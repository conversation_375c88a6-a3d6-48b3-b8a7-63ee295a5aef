# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import os
from pathlib import Path
from typing import Any, Dict, Union

from langchain_openai import ChatOpenAI
from langchain_community.llms.ollama import Ollama

from src.config import load_yaml_config
from src.config.agents import LLMType

# Cache for LLM instances
_llm_cache: dict[Union[LLMType, str], Any] = {}

# Default configuration file path
CONFIG_FILE_PATH = os.path.join(os.getcwd(), "conf.yaml")


def _create_llm_use_conf(llm_type: Union[LLMType, Dict[str, Any]], conf: Dict[str, Any]) -> Any:
    # If llm_type is a dictionary, it's a direct configuration
    if isinstance(llm_type, dict):
        model_type = llm_type.get("type")
        # Create a copy without the type parameter
        config_without_type = {k: v for k, v in llm_type.items() if k != "type"}
        
        if model_type == "ollama":
            return Ollama(
                model=config_without_type.get("model"),
                base_url=config_without_type.get("base_url"),
            )
        # Default to OpenAI-compatible models
        return ChatOpenAI(**config_without_type)
    
    # Otherwise, use the mapping from the configuration
    llm_type_map = {
        "reasoning": conf.get("REASONING_MODEL"),
        "basic": conf.get("BASIC_MODEL"),
        "vision": conf.get("VISION_MODEL"),
    }
    llm_conf = llm_type_map.get(llm_type)
    if not llm_conf:
        raise ValueError(f"Unknown LLM type: {llm_type}")
    
    # Handle the configuration based on type
    model_type = llm_conf.get("type")
    if model_type == "ollama":
        # Create a copy without the type parameter
        config_without_type = {k: v for k, v in llm_conf.items() if k != "type"}
        return Ollama(
            model=config_without_type.get("model"),
            base_url=config_without_type.get("base_url"),
        )
    
    # For other types, remove the type parameter and use ChatOpenAI
    config_without_type = {k: v for k, v in llm_conf.items() if k != "type"}
    return ChatOpenAI(**config_without_type)


def get_llm_by_type(llm_type: Union[LLMType, Dict[str, Any]]) -> Any:
    """Get LLM by type with caching."""
    # For dictionary configurations, we can't use them as cache keys
    # So we'll just create a new instance each time
    if isinstance(llm_type, dict):
        conf = load_yaml_config(CONFIG_FILE_PATH)
        return _create_llm_use_conf(llm_type, conf)
    
    # For string types, use caching
    if llm_type not in _llm_cache:
        conf = load_yaml_config(CONFIG_FILE_PATH)
        _llm_cache[llm_type] = _create_llm_use_conf(llm_type, conf)
    return _llm_cache[llm_type]


# In the future, we will use reasoning_llm and vl_llm for different purposes
# reasoning_llm = get_llm_by_type("reasoning")
# vl_llm = get_llm_by_type("vision")


if __name__ == "__main__":
    # Initialize LLMs for different purposes - now these will be cached
    basic_llm = get_llm_by_type("basic")
    print(basic_llm.invoke("Hello"))
