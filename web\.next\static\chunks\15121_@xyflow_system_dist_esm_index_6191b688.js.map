{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/%40xyflow%2Bsystem%400.0.57/node_modules/%40xyflow/system/dist/esm/index.js"], "sourcesContent": ["import { drag } from 'd3-drag';\nimport { select, pointer } from 'd3-selection';\nimport { zoom, zoomIdentity, zoomTransform } from 'd3-zoom';\n\nconst errorMessages = {\n    error001: () => '[React Flow]: Seems like you have not used zustand provider as an ancestor. Help: https://reactflow.dev/error#001',\n    error002: () => \"It looks like you've created a new nodeTypes or edgeTypes object. If this wasn't on purpose please define the nodeTypes/edgeTypes outside of the component or memoize them.\",\n    error003: (nodeType) => `Node type \"${nodeType}\" not found. Using fallback type \"default\".`,\n    error004: () => 'The React Flow parent container needs a width and a height to render the graph.',\n    error005: () => 'Only child nodes can use a parent extent.',\n    error006: () => \"Can't create edge. An edge needs a source and a target.\",\n    error007: (id) => `The old edge with id=${id} does not exist.`,\n    error009: (type) => `Marker type \"${type}\" doesn't exist.`,\n    error008: (handleType, { id, sourceHandle, targetHandle }) => `Couldn't create edge for ${handleType} handle id: \"${handleType === 'source' ? sourceHandle : targetHandle}\", edge id: ${id}.`,\n    error010: () => 'Handle: No node id found. Make sure to only use a Handle inside a custom Node.',\n    error011: (edgeType) => `Edge type \"${edgeType}\" not found. Using fallback type \"default\".`,\n    error012: (id) => `Node with id \"${id}\" does not exist, it may have been removed. This can happen when a node is deleted before the \"onNodeClick\" handler is called.`,\n    error013: (lib = 'react') => `It seems that you haven't loaded the styles. Please import '@xyflow/${lib}/dist/style.css' or base.css to make sure everything is working properly.`,\n    error014: () => 'useNodeConnections: No node ID found. Call useNodeConnections inside a custom Node or provide a node ID.',\n    error015: () => 'It seems that you are trying to drag a node that is not initialized. Please use onNodesChange as explained in the docs.',\n};\nconst infiniteExtent = [\n    [Number.NEGATIVE_INFINITY, Number.NEGATIVE_INFINITY],\n    [Number.POSITIVE_INFINITY, Number.POSITIVE_INFINITY],\n];\nconst elementSelectionKeys = ['Enter', ' ', 'Escape'];\n\n/**\n * The `ConnectionMode` is used to set the mode of connection between nodes.\n * The `Strict` mode is the default one and only allows source to target edges.\n * `Loose` mode allows source to source and target to target edges as well.\n *\n * @public\n */\nvar ConnectionMode;\n(function (ConnectionMode) {\n    ConnectionMode[\"Strict\"] = \"strict\";\n    ConnectionMode[\"Loose\"] = \"loose\";\n})(ConnectionMode || (ConnectionMode = {}));\n/**\n * This enum is used to set the different modes of panning the viewport when the\n * user scrolls. The `Free` mode allows the user to pan in any direction by scrolling\n * with a device like a trackpad. The `Vertical` and `Horizontal` modes restrict\n * scroll panning to only the vertical or horizontal axis, respectively.\n *\n * @public\n */\nvar PanOnScrollMode;\n(function (PanOnScrollMode) {\n    PanOnScrollMode[\"Free\"] = \"free\";\n    PanOnScrollMode[\"Vertical\"] = \"vertical\";\n    PanOnScrollMode[\"Horizontal\"] = \"horizontal\";\n})(PanOnScrollMode || (PanOnScrollMode = {}));\nvar SelectionMode;\n(function (SelectionMode) {\n    SelectionMode[\"Partial\"] = \"partial\";\n    SelectionMode[\"Full\"] = \"full\";\n})(SelectionMode || (SelectionMode = {}));\nconst initialConnection = {\n    inProgress: false,\n    isValid: null,\n    from: null,\n    fromHandle: null,\n    fromPosition: null,\n    fromNode: null,\n    to: null,\n    toHandle: null,\n    toPosition: null,\n    toNode: null,\n};\n\n/**\n * If you set the `connectionLineType` prop on your [`<ReactFlow />`](/api-reference/react-flow#connection-connectionLineType)\n *component, it will dictate the style of connection line rendered when creating\n *new edges.\n *\n * @public\n *\n * @remarks If you choose to render a custom connection line component, this value will be\n *passed to your component as part of its [`ConnectionLineComponentProps`](/api-reference/types/connection-line-component-props).\n */\nvar ConnectionLineType;\n(function (ConnectionLineType) {\n    ConnectionLineType[\"Bezier\"] = \"default\";\n    ConnectionLineType[\"Straight\"] = \"straight\";\n    ConnectionLineType[\"Step\"] = \"step\";\n    ConnectionLineType[\"SmoothStep\"] = \"smoothstep\";\n    ConnectionLineType[\"SimpleBezier\"] = \"simplebezier\";\n})(ConnectionLineType || (ConnectionLineType = {}));\n/**\n * Edges may optionally have a marker on either end. The MarkerType type enumerates\n * the options available to you when configuring a given marker.\n *\n * @public\n */\nvar MarkerType;\n(function (MarkerType) {\n    MarkerType[\"Arrow\"] = \"arrow\";\n    MarkerType[\"ArrowClosed\"] = \"arrowclosed\";\n})(MarkerType || (MarkerType = {}));\n\n/**\n * While [`PanelPosition`](/api-reference/types/panel-position) can be used to place a\n * component in the corners of a container, the `Position` enum is less precise and used\n * primarily in relation to edges and handles.\n *\n * @public\n */\nvar Position;\n(function (Position) {\n    Position[\"Left\"] = \"left\";\n    Position[\"Top\"] = \"top\";\n    Position[\"Right\"] = \"right\";\n    Position[\"Bottom\"] = \"bottom\";\n})(Position || (Position = {}));\nconst oppositePosition = {\n    [Position.Left]: Position.Right,\n    [Position.Right]: Position.Left,\n    [Position.Top]: Position.Bottom,\n    [Position.Bottom]: Position.Top,\n};\n\n/**\n * @internal\n */\nfunction areConnectionMapsEqual(a, b) {\n    if (!a && !b) {\n        return true;\n    }\n    if (!a || !b || a.size !== b.size) {\n        return false;\n    }\n    if (!a.size && !b.size) {\n        return true;\n    }\n    for (const key of a.keys()) {\n        if (!b.has(key)) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * We call the callback for all connections in a that are not in b\n *\n * @internal\n */\nfunction handleConnectionChange(a, b, cb) {\n    if (!cb) {\n        return;\n    }\n    const diff = [];\n    a.forEach((connection, key) => {\n        if (!b?.has(key)) {\n            diff.push(connection);\n        }\n    });\n    if (diff.length) {\n        cb(diff);\n    }\n}\nfunction getConnectionStatus(isValid) {\n    return isValid === null ? null : isValid ? 'valid' : 'invalid';\n}\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/**\n * Test whether an object is usable as an Edge\n * @public\n * @remarks In TypeScript this is a type guard that will narrow the type of whatever you pass in to Edge if it returns true\n * @param element - The element to test\n * @returns A boolean indicating whether the element is an Edge\n */\nconst isEdgeBase = (element) => 'id' in element && 'source' in element && 'target' in element;\n/**\n * Test whether an object is usable as a Node\n * @public\n * @remarks In TypeScript this is a type guard that will narrow the type of whatever you pass in to Node if it returns true\n * @param element - The element to test\n * @returns A boolean indicating whether the element is an Node\n */\nconst isNodeBase = (element) => 'id' in element && 'position' in element && !('source' in element) && !('target' in element);\nconst isInternalNodeBase = (element) => 'id' in element && 'internals' in element && !('source' in element) && !('target' in element);\n/**\n * This util is used to tell you what nodes, if any, are connected to the given node\n * as the _target_ of an edge.\n * @public\n * @param node - The node to get the connected nodes from.\n * @param nodes - The array of all nodes.\n * @param edges - The array of all edges.\n * @returns An array of nodes that are connected over edges where the source is the given node.\n *\n * @example\n * ```ts\n *import { getOutgoers } from '@xyflow/react';\n *\n *const nodes = [];\n *const edges = [];\n *\n *const outgoers = getOutgoers(\n *  { id: '1', position: { x: 0, y: 0 }, data: { label: 'node' } },\n *  nodes,\n *  edges,\n *);\n *```\n */\nconst getOutgoers = (node, nodes, edges) => {\n    if (!node.id) {\n        return [];\n    }\n    const outgoerIds = new Set();\n    edges.forEach((edge) => {\n        if (edge.source === node.id) {\n            outgoerIds.add(edge.target);\n        }\n    });\n    return nodes.filter((n) => outgoerIds.has(n.id));\n};\n/**\n * This util is used to tell you what nodes, if any, are connected to the given node\n * as the _source_ of an edge.\n * @public\n * @param node - The node to get the connected nodes from.\n * @param nodes - The array of all nodes.\n * @param edges - The array of all edges.\n * @returns An array of nodes that are connected over edges where the target is the given node.\n *\n * @example\n * ```ts\n *import { getIncomers } from '@xyflow/react';\n *\n *const nodes = [];\n *const edges = [];\n *\n *const incomers = getIncomers(\n *  { id: '1', position: { x: 0, y: 0 }, data: { label: 'node' } },\n *  nodes,\n *  edges,\n *);\n *```\n */\nconst getIncomers = (node, nodes, edges) => {\n    if (!node.id) {\n        return [];\n    }\n    const incomersIds = new Set();\n    edges.forEach((edge) => {\n        if (edge.target === node.id) {\n            incomersIds.add(edge.source);\n        }\n    });\n    return nodes.filter((n) => incomersIds.has(n.id));\n};\nconst getNodePositionWithOrigin = (node, nodeOrigin = [0, 0]) => {\n    const { width, height } = getNodeDimensions(node);\n    const origin = node.origin ?? nodeOrigin;\n    const offsetX = width * origin[0];\n    const offsetY = height * origin[1];\n    return {\n        x: node.position.x - offsetX,\n        y: node.position.y - offsetY,\n    };\n};\n/**\n * Returns the bounding box that contains all the given nodes in an array. This can\n * be useful when combined with [`getViewportForBounds`](/api-reference/utils/get-viewport-for-bounds)\n * to calculate the correct transform to fit the given nodes in a viewport.\n * @public\n * @remarks Useful when combined with {@link getViewportForBounds} to calculate the correct transform to fit the given nodes in a viewport.\n * @param nodes - Nodes to calculate the bounds for.\n * @returns Bounding box enclosing all nodes.\n *\n * @remarks This function was previously called `getRectOfNodes`\n *\n * @example\n * ```js\n *import { getNodesBounds } from '@xyflow/react';\n *\n *const nodes = [\n *  {\n *    id: 'a',\n *    position: { x: 0, y: 0 },\n *    data: { label: 'a' },\n *    width: 50,\n *    height: 25,\n *  },\n *  {\n *    id: 'b',\n *    position: { x: 100, y: 100 },\n *    data: { label: 'b' },\n *    width: 50,\n *    height: 25,\n *  },\n *];\n *\n *const bounds = getNodesBounds(nodes);\n *```\n */\nconst getNodesBounds = (nodes, params = { nodeOrigin: [0, 0] }) => {\n    if (process.env.NODE_ENV === 'development' && !params.nodeLookup) {\n        console.warn('Please use `getNodesBounds` from `useReactFlow`/`useSvelteFlow` hook to ensure correct values for sub flows. If not possible, you have to provide a nodeLookup to support sub flows.');\n    }\n    if (nodes.length === 0) {\n        return { x: 0, y: 0, width: 0, height: 0 };\n    }\n    const box = nodes.reduce((currBox, nodeOrId) => {\n        const isId = typeof nodeOrId === 'string';\n        let currentNode = !params.nodeLookup && !isId ? nodeOrId : undefined;\n        if (params.nodeLookup) {\n            currentNode = isId\n                ? params.nodeLookup.get(nodeOrId)\n                : !isInternalNodeBase(nodeOrId)\n                    ? params.nodeLookup.get(nodeOrId.id)\n                    : nodeOrId;\n        }\n        const nodeBox = currentNode ? nodeToBox(currentNode, params.nodeOrigin) : { x: 0, y: 0, x2: 0, y2: 0 };\n        return getBoundsOfBoxes(currBox, nodeBox);\n    }, { x: Infinity, y: Infinity, x2: -Infinity, y2: -Infinity });\n    return boxToRect(box);\n};\n/**\n * Determines a bounding box that contains all given nodes in an array\n * @internal\n */\nconst getInternalNodesBounds = (nodeLookup, params = {}) => {\n    if (nodeLookup.size === 0) {\n        return { x: 0, y: 0, width: 0, height: 0 };\n    }\n    let box = { x: Infinity, y: Infinity, x2: -Infinity, y2: -Infinity };\n    nodeLookup.forEach((node) => {\n        if (params.filter === undefined || params.filter(node)) {\n            const nodeBox = nodeToBox(node);\n            box = getBoundsOfBoxes(box, nodeBox);\n        }\n    });\n    return boxToRect(box);\n};\nconst getNodesInside = (nodes, rect, [tx, ty, tScale] = [0, 0, 1], partially = false, \n// set excludeNonSelectableNodes if you want to pay attention to the nodes \"selectable\" attribute\nexcludeNonSelectableNodes = false) => {\n    const paneRect = {\n        ...pointToRendererPoint(rect, [tx, ty, tScale]),\n        width: rect.width / tScale,\n        height: rect.height / tScale,\n    };\n    const visibleNodes = [];\n    for (const node of nodes.values()) {\n        const { measured, selectable = true, hidden = false } = node;\n        if ((excludeNonSelectableNodes && !selectable) || hidden) {\n            continue;\n        }\n        const width = measured.width ?? node.width ?? node.initialWidth ?? null;\n        const height = measured.height ?? node.height ?? node.initialHeight ?? null;\n        const overlappingArea = getOverlappingArea(paneRect, nodeToRect(node));\n        const area = (width ?? 0) * (height ?? 0);\n        const partiallyVisible = partially && overlappingArea > 0;\n        const forceInitialRender = !node.internals.handleBounds;\n        const isVisible = forceInitialRender || partiallyVisible || overlappingArea >= area;\n        if (isVisible || node.dragging) {\n            visibleNodes.push(node);\n        }\n    }\n    return visibleNodes;\n};\n/**\n * This utility filters an array of edges, keeping only those where either the source or target\n * node is present in the given array of nodes.\n * @public\n * @param nodes - Nodes you want to get the connected edges for.\n * @param edges - All edges.\n * @returns Array of edges that connect any of the given nodes with each other.\n *\n * @example\n * ```js\n *import { getConnectedEdges } from '@xyflow/react';\n *\n *const nodes = [\n *  { id: 'a', position: { x: 0, y: 0 } },\n *  { id: 'b', position: { x: 100, y: 0 } },\n *];\n *\n *const edges = [\n *  { id: 'a->c', source: 'a', target: 'c' },\n *  { id: 'c->d', source: 'c', target: 'd' },\n *];\n *\n *const connectedEdges = getConnectedEdges(nodes, edges);\n * // => [{ id: 'a->c', source: 'a', target: 'c' }]\n *```\n */\nconst getConnectedEdges = (nodes, edges) => {\n    const nodeIds = new Set();\n    nodes.forEach((node) => {\n        nodeIds.add(node.id);\n    });\n    return edges.filter((edge) => nodeIds.has(edge.source) || nodeIds.has(edge.target));\n};\nfunction getFitViewNodes(nodeLookup, options) {\n    const fitViewNodes = new Map();\n    const optionNodeIds = options?.nodes ? new Set(options.nodes.map((node) => node.id)) : null;\n    nodeLookup.forEach((n) => {\n        const isVisible = n.measured.width && n.measured.height && (options?.includeHiddenNodes || !n.hidden);\n        if (isVisible && (!optionNodeIds || optionNodeIds.has(n.id))) {\n            fitViewNodes.set(n.id, n);\n        }\n    });\n    return fitViewNodes;\n}\nasync function fitViewport({ nodes, width, height, panZoom, minZoom, maxZoom }, options) {\n    if (nodes.size === 0) {\n        return Promise.resolve(true);\n    }\n    const nodesToFit = getFitViewNodes(nodes, options);\n    const bounds = getInternalNodesBounds(nodesToFit);\n    const viewport = getViewportForBounds(bounds, width, height, options?.minZoom ?? minZoom, options?.maxZoom ?? maxZoom, options?.padding ?? 0.1);\n    await panZoom.setViewport(viewport, { duration: options?.duration });\n    return Promise.resolve(true);\n}\n/**\n * This function calculates the next position of a node, taking into account the node's extent, parent node, and origin.\n *\n * @internal\n * @returns position, positionAbsolute\n */\nfunction calculateNodePosition({ nodeId, nextPosition, nodeLookup, nodeOrigin = [0, 0], nodeExtent, onError, }) {\n    const node = nodeLookup.get(nodeId);\n    const parentNode = node.parentId ? nodeLookup.get(node.parentId) : undefined;\n    const { x: parentX, y: parentY } = parentNode ? parentNode.internals.positionAbsolute : { x: 0, y: 0 };\n    const origin = node.origin ?? nodeOrigin;\n    let extent = nodeExtent;\n    if (node.extent === 'parent' && !node.expandParent) {\n        if (!parentNode) {\n            onError?.('005', errorMessages['error005']());\n        }\n        else {\n            const parentWidth = parentNode.measured.width;\n            const parentHeight = parentNode.measured.height;\n            if (parentWidth && parentHeight) {\n                extent = [\n                    [parentX, parentY],\n                    [parentX + parentWidth, parentY + parentHeight],\n                ];\n            }\n        }\n    }\n    else if (parentNode && isCoordinateExtent(node.extent)) {\n        extent = [\n            [node.extent[0][0] + parentX, node.extent[0][1] + parentY],\n            [node.extent[1][0] + parentX, node.extent[1][1] + parentY],\n        ];\n    }\n    const positionAbsolute = isCoordinateExtent(extent)\n        ? clampPosition(nextPosition, extent, node.measured)\n        : nextPosition;\n    if (node.measured.width === undefined || node.measured.height === undefined) {\n        onError?.('015', errorMessages['error015']());\n    }\n    return {\n        position: {\n            x: positionAbsolute.x - parentX + (node.measured.width ?? 0) * origin[0],\n            y: positionAbsolute.y - parentY + (node.measured.height ?? 0) * origin[1],\n        },\n        positionAbsolute,\n    };\n}\n/**\n * Pass in nodes & edges to delete, get arrays of nodes and edges that actually can be deleted\n * @internal\n * @param param.nodesToRemove - The nodes to remove\n * @param param.edgesToRemove - The edges to remove\n * @param param.nodes - All nodes\n * @param param.edges - All edges\n * @param param.onBeforeDelete - Callback to check which nodes and edges can be deleted\n * @returns nodes: nodes that can be deleted, edges: edges that can be deleted\n */\nasync function getElementsToRemove({ nodesToRemove = [], edgesToRemove = [], nodes, edges, onBeforeDelete, }) {\n    const nodeIds = new Set(nodesToRemove.map((node) => node.id));\n    const matchingNodes = [];\n    for (const node of nodes) {\n        if (node.deletable === false) {\n            continue;\n        }\n        const isIncluded = nodeIds.has(node.id);\n        const parentHit = !isIncluded && node.parentId && matchingNodes.find((n) => n.id === node.parentId);\n        if (isIncluded || parentHit) {\n            matchingNodes.push(node);\n        }\n    }\n    const edgeIds = new Set(edgesToRemove.map((edge) => edge.id));\n    const deletableEdges = edges.filter((edge) => edge.deletable !== false);\n    const connectedEdges = getConnectedEdges(matchingNodes, deletableEdges);\n    const matchingEdges = connectedEdges;\n    for (const edge of deletableEdges) {\n        const isIncluded = edgeIds.has(edge.id);\n        if (isIncluded && !matchingEdges.find((e) => e.id === edge.id)) {\n            matchingEdges.push(edge);\n        }\n    }\n    if (!onBeforeDelete) {\n        return {\n            edges: matchingEdges,\n            nodes: matchingNodes,\n        };\n    }\n    const onBeforeDeleteResult = await onBeforeDelete({\n        nodes: matchingNodes,\n        edges: matchingEdges,\n    });\n    if (typeof onBeforeDeleteResult === 'boolean') {\n        return onBeforeDeleteResult ? { edges: matchingEdges, nodes: matchingNodes } : { edges: [], nodes: [] };\n    }\n    return onBeforeDeleteResult;\n}\n\nconst clamp = (val, min = 0, max = 1) => Math.min(Math.max(val, min), max);\nconst clampPosition = (position = { x: 0, y: 0 }, extent, dimensions) => ({\n    x: clamp(position.x, extent[0][0], extent[1][0] - (dimensions?.width ?? 0)),\n    y: clamp(position.y, extent[0][1], extent[1][1] - (dimensions?.height ?? 0)),\n});\nfunction clampPositionToParent(childPosition, childDimensions, parent) {\n    const { width: parentWidth, height: parentHeight } = getNodeDimensions(parent);\n    const { x: parentX, y: parentY } = parent.internals.positionAbsolute;\n    return clampPosition(childPosition, [\n        [parentX, parentY],\n        [parentX + parentWidth, parentY + parentHeight],\n    ], childDimensions);\n}\n/**\n * Calculates the velocity of panning when the mouse is close to the edge of the canvas\n * @internal\n * @param value - One dimensional poition of the mouse (x or y)\n * @param min - Minimal position on canvas before panning starts\n * @param max - Maximal position on canvas before panning starts\n * @returns - A number between 0 and 1 that represents the velocity of panning\n */\nconst calcAutoPanVelocity = (value, min, max) => {\n    if (value < min) {\n        return clamp(Math.abs(value - min), 1, min) / min;\n    }\n    else if (value > max) {\n        return -clamp(Math.abs(value - max), 1, min) / min;\n    }\n    return 0;\n};\nconst calcAutoPan = (pos, bounds, speed = 15, distance = 40) => {\n    const xMovement = calcAutoPanVelocity(pos.x, distance, bounds.width - distance) * speed;\n    const yMovement = calcAutoPanVelocity(pos.y, distance, bounds.height - distance) * speed;\n    return [xMovement, yMovement];\n};\nconst getBoundsOfBoxes = (box1, box2) => ({\n    x: Math.min(box1.x, box2.x),\n    y: Math.min(box1.y, box2.y),\n    x2: Math.max(box1.x2, box2.x2),\n    y2: Math.max(box1.y2, box2.y2),\n});\nconst rectToBox = ({ x, y, width, height }) => ({\n    x,\n    y,\n    x2: x + width,\n    y2: y + height,\n});\nconst boxToRect = ({ x, y, x2, y2 }) => ({\n    x,\n    y,\n    width: x2 - x,\n    height: y2 - y,\n});\nconst nodeToRect = (node, nodeOrigin = [0, 0]) => {\n    const { x, y } = isInternalNodeBase(node)\n        ? node.internals.positionAbsolute\n        : getNodePositionWithOrigin(node, nodeOrigin);\n    return {\n        x,\n        y,\n        width: node.measured?.width ?? node.width ?? node.initialWidth ?? 0,\n        height: node.measured?.height ?? node.height ?? node.initialHeight ?? 0,\n    };\n};\nconst nodeToBox = (node, nodeOrigin = [0, 0]) => {\n    const { x, y } = isInternalNodeBase(node)\n        ? node.internals.positionAbsolute\n        : getNodePositionWithOrigin(node, nodeOrigin);\n    return {\n        x,\n        y,\n        x2: x + (node.measured?.width ?? node.width ?? node.initialWidth ?? 0),\n        y2: y + (node.measured?.height ?? node.height ?? node.initialHeight ?? 0),\n    };\n};\nconst getBoundsOfRects = (rect1, rect2) => boxToRect(getBoundsOfBoxes(rectToBox(rect1), rectToBox(rect2)));\nconst getOverlappingArea = (rectA, rectB) => {\n    const xOverlap = Math.max(0, Math.min(rectA.x + rectA.width, rectB.x + rectB.width) - Math.max(rectA.x, rectB.x));\n    const yOverlap = Math.max(0, Math.min(rectA.y + rectA.height, rectB.y + rectB.height) - Math.max(rectA.y, rectB.y));\n    return Math.ceil(xOverlap * yOverlap);\n};\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst isRectObject = (obj) => isNumeric(obj.width) && isNumeric(obj.height) && isNumeric(obj.x) && isNumeric(obj.y);\n/* eslint-disable-next-line @typescript-eslint/no-explicit-any */\nconst isNumeric = (n) => !isNaN(n) && isFinite(n);\n// used for a11y key board controls for nodes and edges\nconst devWarn = (id, message) => {\n    if (process.env.NODE_ENV === 'development') {\n        console.warn(`[React Flow]: ${message} Help: https://reactflow.dev/error#${id}`);\n    }\n};\nconst snapPosition = (position, snapGrid = [1, 1]) => {\n    return {\n        x: snapGrid[0] * Math.round(position.x / snapGrid[0]),\n        y: snapGrid[1] * Math.round(position.y / snapGrid[1]),\n    };\n};\nconst pointToRendererPoint = ({ x, y }, [tx, ty, tScale], snapToGrid = false, snapGrid = [1, 1]) => {\n    const position = {\n        x: (x - tx) / tScale,\n        y: (y - ty) / tScale,\n    };\n    return snapToGrid ? snapPosition(position, snapGrid) : position;\n};\nconst rendererPointToPoint = ({ x, y }, [tx, ty, tScale]) => {\n    return {\n        x: x * tScale + tx,\n        y: y * tScale + ty,\n    };\n};\n/**\n * Parses a single padding value to a number\n * @internal\n * @param padding - Padding to parse\n * @param viewport - Width or height of the viewport\n * @returns The padding in pixels\n */\nfunction parsePadding(padding, viewport) {\n    if (typeof padding === 'number') {\n        return Math.floor((viewport - viewport / (1 + padding)) * 0.5);\n    }\n    if (typeof padding === 'string' && padding.endsWith('px')) {\n        const paddingValue = parseFloat(padding);\n        if (!Number.isNaN(paddingValue)) {\n            return Math.floor(paddingValue);\n        }\n    }\n    if (typeof padding === 'string' && padding.endsWith('%')) {\n        const paddingValue = parseFloat(padding);\n        if (!Number.isNaN(paddingValue)) {\n            return Math.floor(viewport * paddingValue * 0.01);\n        }\n    }\n    console.error(`[React Flow] The padding value \"${padding}\" is invalid. Please provide a number or a string with a valid unit (px or %).`);\n    return 0;\n}\n/**\n * Parses the paddings to an object with top, right, bottom, left, x and y paddings\n * @internal\n * @param padding - Padding to parse\n * @param width - Width of the viewport\n * @param height - Height of the viewport\n * @returns An object with the paddings in pixels\n */\nfunction parsePaddings(padding, width, height) {\n    if (typeof padding === 'string' || typeof padding === 'number') {\n        const paddingY = parsePadding(padding, height);\n        const paddingX = parsePadding(padding, width);\n        return {\n            top: paddingY,\n            right: paddingX,\n            bottom: paddingY,\n            left: paddingX,\n            x: paddingX * 2,\n            y: paddingY * 2,\n        };\n    }\n    if (typeof padding === 'object') {\n        const top = parsePadding(padding.top ?? padding.y ?? 0, height);\n        const bottom = parsePadding(padding.bottom ?? padding.y ?? 0, height);\n        const left = parsePadding(padding.left ?? padding.x ?? 0, width);\n        const right = parsePadding(padding.right ?? padding.x ?? 0, width);\n        return { top, right, bottom, left, x: left + right, y: top + bottom };\n    }\n    return { top: 0, right: 0, bottom: 0, left: 0, x: 0, y: 0 };\n}\n/**\n * Calculates the resulting paddings if the new viewport is applied\n * @internal\n * @param bounds - Bounds to fit inside viewport\n * @param x - X position of the viewport\n * @param y - Y position of the viewport\n * @param zoom - Zoom level of the viewport\n * @param width - Width of the viewport\n * @param height - Height of the viewport\n * @returns An object with the minimum padding required to fit the bounds inside the viewport\n */\nfunction calculateAppliedPaddings(bounds, x, y, zoom, width, height) {\n    const { x: left, y: top } = rendererPointToPoint(bounds, [x, y, zoom]);\n    const { x: boundRight, y: boundBottom } = rendererPointToPoint({ x: bounds.x + bounds.width, y: bounds.y + bounds.height }, [x, y, zoom]);\n    const right = width - boundRight;\n    const bottom = height - boundBottom;\n    return {\n        left: Math.floor(left),\n        top: Math.floor(top),\n        right: Math.floor(right),\n        bottom: Math.floor(bottom),\n    };\n}\n/**\n * Returns a viewport that encloses the given bounds with padding.\n * @public\n * @remarks You can determine bounds of nodes with {@link getNodesBounds} and {@link getBoundsOfRects}\n * @param bounds - Bounds to fit inside viewport.\n * @param width - Width of the viewport.\n * @param height  - Height of the viewport.\n * @param minZoom - Minimum zoom level of the resulting viewport.\n * @param maxZoom - Maximum zoom level of the resulting viewport.\n * @param padding - Padding around the bounds.\n * @returns A transformed {@link Viewport} that encloses the given bounds which you can pass to e.g. {@link setViewport}.\n * @example\n * const { x, y, zoom } = getViewportForBounds(\n * { x: 0, y: 0, width: 100, height: 100},\n * 1200, 800, 0.5, 2);\n */\nconst getViewportForBounds = (bounds, width, height, minZoom, maxZoom, padding) => {\n    // First we resolve all the paddings to actual pixel values\n    const p = parsePaddings(padding, width, height);\n    const xZoom = (width - p.x) / bounds.width;\n    const yZoom = (height - p.y) / bounds.height;\n    // We calculate the new x, y, zoom for a centered view\n    const zoom = Math.min(xZoom, yZoom);\n    const clampedZoom = clamp(zoom, minZoom, maxZoom);\n    const boundsCenterX = bounds.x + bounds.width / 2;\n    const boundsCenterY = bounds.y + bounds.height / 2;\n    const x = width / 2 - boundsCenterX * clampedZoom;\n    const y = height / 2 - boundsCenterY * clampedZoom;\n    // Then we calculate the minimum padding, to respect asymmetric paddings\n    const newPadding = calculateAppliedPaddings(bounds, x, y, clampedZoom, width, height);\n    // We only want to have an offset if the newPadding is smaller than the required padding\n    const offset = {\n        left: Math.min(newPadding.left - p.left, 0),\n        top: Math.min(newPadding.top - p.top, 0),\n        right: Math.min(newPadding.right - p.right, 0),\n        bottom: Math.min(newPadding.bottom - p.bottom, 0),\n    };\n    return {\n        x: x - offset.left + offset.right,\n        y: y - offset.top + offset.bottom,\n        zoom: clampedZoom,\n    };\n};\nconst isMacOs = () => typeof navigator !== 'undefined' && navigator?.userAgent?.indexOf('Mac') >= 0;\nfunction isCoordinateExtent(extent) {\n    return extent !== undefined && extent !== 'parent';\n}\nfunction getNodeDimensions(node) {\n    return {\n        width: node.measured?.width ?? node.width ?? node.initialWidth ?? 0,\n        height: node.measured?.height ?? node.height ?? node.initialHeight ?? 0,\n    };\n}\nfunction nodeHasDimensions(node) {\n    return ((node.measured?.width ?? node.width ?? node.initialWidth) !== undefined &&\n        (node.measured?.height ?? node.height ?? node.initialHeight) !== undefined);\n}\n/**\n * Convert child position to aboslute position\n *\n * @internal\n * @param position\n * @param parentId\n * @param nodeLookup\n * @param nodeOrigin\n * @returns an internal node with an absolute position\n */\nfunction evaluateAbsolutePosition(position, dimensions = { width: 0, height: 0 }, parentId, nodeLookup, nodeOrigin) {\n    const positionAbsolute = { ...position };\n    const parent = nodeLookup.get(parentId);\n    if (parent) {\n        const origin = parent.origin || nodeOrigin;\n        positionAbsolute.x += parent.internals.positionAbsolute.x - (dimensions.width ?? 0) * origin[0];\n        positionAbsolute.y += parent.internals.positionAbsolute.y - (dimensions.height ?? 0) * origin[1];\n    }\n    return positionAbsolute;\n}\nfunction areSetsEqual(a, b) {\n    if (a.size !== b.size) {\n        return false;\n    }\n    for (const item of a) {\n        if (!b.has(item)) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Polyfill for Promise.withResolvers until we can use it in all browsers\n * @internal\n */\nfunction withResolvers() {\n    let resolve;\n    let reject;\n    const promise = new Promise((res, rej) => {\n        resolve = res;\n        reject = rej;\n    });\n    return { promise, resolve, reject };\n}\n\nfunction getPointerPosition(event, { snapGrid = [0, 0], snapToGrid = false, transform, containerBounds }) {\n    const { x, y } = getEventPosition(event);\n    const pointerPos = pointToRendererPoint({ x: x - (containerBounds?.left ?? 0), y: y - (containerBounds?.top ?? 0) }, transform);\n    const { x: xSnapped, y: ySnapped } = snapToGrid ? snapPosition(pointerPos, snapGrid) : pointerPos;\n    // we need the snapped position in order to be able to skip unnecessary drag events\n    return {\n        xSnapped,\n        ySnapped,\n        ...pointerPos,\n    };\n}\nconst getDimensions = (node) => ({\n    width: node.offsetWidth,\n    height: node.offsetHeight,\n});\nconst getHostForElement = (element) => element?.getRootNode?.() || window?.document;\nconst inputTags = ['INPUT', 'SELECT', 'TEXTAREA'];\nfunction isInputDOMNode(event) {\n    // using composed path for handling shadow dom\n    const target = (event.composedPath?.()?.[0] || event.target);\n    if (target?.nodeType !== 1 /* Node.ELEMENT_NODE */)\n        return false;\n    const isInput = inputTags.includes(target.nodeName) || target.hasAttribute('contenteditable');\n    // when an input field is focused we don't want to trigger deletion or movement of nodes\n    return isInput || !!target.closest('.nokey');\n}\nconst isMouseEvent = (event) => 'clientX' in event;\nconst getEventPosition = (event, bounds) => {\n    const isMouse = isMouseEvent(event);\n    const evtX = isMouse ? event.clientX : event.touches?.[0].clientX;\n    const evtY = isMouse ? event.clientY : event.touches?.[0].clientY;\n    return {\n        x: evtX - (bounds?.left ?? 0),\n        y: evtY - (bounds?.top ?? 0),\n    };\n};\n/*\n * The handle bounds are calculated relative to the node element.\n * We store them in the internals object of the node in order to avoid\n * unnecessary recalculations.\n */\nconst getHandleBounds = (type, nodeElement, nodeBounds, zoom, nodeId) => {\n    const handles = nodeElement.querySelectorAll(`.${type}`);\n    if (!handles || !handles.length) {\n        return null;\n    }\n    return Array.from(handles).map((handle) => {\n        const handleBounds = handle.getBoundingClientRect();\n        return {\n            id: handle.getAttribute('data-handleid'),\n            type,\n            nodeId,\n            position: handle.getAttribute('data-handlepos'),\n            x: (handleBounds.left - nodeBounds.left) / zoom,\n            y: (handleBounds.top - nodeBounds.top) / zoom,\n            ...getDimensions(handle),\n        };\n    });\n};\n\nfunction getBezierEdgeCenter({ sourceX, sourceY, targetX, targetY, sourceControlX, sourceControlY, targetControlX, targetControlY, }) {\n    /*\n     * cubic bezier t=0.5 mid point, not the actual mid point, but easy to calculate\n     * https://stackoverflow.com/questions/67516101/how-to-find-distance-mid-point-of-bezier-curve\n     */\n    const centerX = sourceX * 0.125 + sourceControlX * 0.375 + targetControlX * 0.375 + targetX * 0.125;\n    const centerY = sourceY * 0.125 + sourceControlY * 0.375 + targetControlY * 0.375 + targetY * 0.125;\n    const offsetX = Math.abs(centerX - sourceX);\n    const offsetY = Math.abs(centerY - sourceY);\n    return [centerX, centerY, offsetX, offsetY];\n}\nfunction calculateControlOffset(distance, curvature) {\n    if (distance >= 0) {\n        return 0.5 * distance;\n    }\n    return curvature * 25 * Math.sqrt(-distance);\n}\nfunction getControlWithCurvature({ pos, x1, y1, x2, y2, c }) {\n    switch (pos) {\n        case Position.Left:\n            return [x1 - calculateControlOffset(x1 - x2, c), y1];\n        case Position.Right:\n            return [x1 + calculateControlOffset(x2 - x1, c), y1];\n        case Position.Top:\n            return [x1, y1 - calculateControlOffset(y1 - y2, c)];\n        case Position.Bottom:\n            return [x1, y1 + calculateControlOffset(y2 - y1, c)];\n    }\n}\n/**\n * The `getBezierPath` util returns everything you need to render a bezier edge\n *between two nodes.\n * @public\n * @returns A path string you can use in an SVG, the `labelX` and `labelY` position (center of path)\n * and `offsetX`, `offsetY` between source handle and label.\n * - `path`: the path to use in an SVG `<path>` element.\n * - `labelX`: the `x` position you can use to render a label for this edge.\n * - `labelY`: the `y` position you can use to render a label for this edge.\n * - `offsetX`: the absolute difference between the source `x` position and the `x` position of the\n * middle of this path.\n * - `offsetY`: the absolute difference between the source `y` position and the `y` position of the\n * middle of this path.\n * @example\n * ```js\n *  const source = { x: 0, y: 20 };\n *  const target = { x: 150, y: 100 };\n *\n *  const [path, labelX, labelY, offsetX, offsetY] = getBezierPath({\n *    sourceX: source.x,\n *    sourceY: source.y,\n *    sourcePosition: Position.Right,\n *    targetX: target.x,\n *    targetY: target.y,\n *    targetPosition: Position.Left,\n *});\n *```\n *\n * @remarks This function returns a tuple (aka a fixed-size array) to make it easier to\n *work with multiple edge paths at once.\n */\nfunction getBezierPath({ sourceX, sourceY, sourcePosition = Position.Bottom, targetX, targetY, targetPosition = Position.Top, curvature = 0.25, }) {\n    const [sourceControlX, sourceControlY] = getControlWithCurvature({\n        pos: sourcePosition,\n        x1: sourceX,\n        y1: sourceY,\n        x2: targetX,\n        y2: targetY,\n        c: curvature,\n    });\n    const [targetControlX, targetControlY] = getControlWithCurvature({\n        pos: targetPosition,\n        x1: targetX,\n        y1: targetY,\n        x2: sourceX,\n        y2: sourceY,\n        c: curvature,\n    });\n    const [labelX, labelY, offsetX, offsetY] = getBezierEdgeCenter({\n        sourceX,\n        sourceY,\n        targetX,\n        targetY,\n        sourceControlX,\n        sourceControlY,\n        targetControlX,\n        targetControlY,\n    });\n    return [\n        `M${sourceX},${sourceY} C${sourceControlX},${sourceControlY} ${targetControlX},${targetControlY} ${targetX},${targetY}`,\n        labelX,\n        labelY,\n        offsetX,\n        offsetY,\n    ];\n}\n\n// this is used for straight edges and simple smoothstep edges (LTR, RTL, BTT, TTB)\nfunction getEdgeCenter({ sourceX, sourceY, targetX, targetY, }) {\n    const xOffset = Math.abs(targetX - sourceX) / 2;\n    const centerX = targetX < sourceX ? targetX + xOffset : targetX - xOffset;\n    const yOffset = Math.abs(targetY - sourceY) / 2;\n    const centerY = targetY < sourceY ? targetY + yOffset : targetY - yOffset;\n    return [centerX, centerY, xOffset, yOffset];\n}\nfunction getElevatedEdgeZIndex({ sourceNode, targetNode, selected = false, zIndex = 0, elevateOnSelect = false, }) {\n    if (!elevateOnSelect) {\n        return zIndex;\n    }\n    const edgeOrConnectedNodeSelected = selected || targetNode.selected || sourceNode.selected;\n    const selectedZIndex = Math.max(sourceNode.internals.z || 0, targetNode.internals.z || 0, 1000);\n    return zIndex + (edgeOrConnectedNodeSelected ? selectedZIndex : 0);\n}\nfunction isEdgeVisible({ sourceNode, targetNode, width, height, transform }) {\n    const edgeBox = getBoundsOfBoxes(nodeToBox(sourceNode), nodeToBox(targetNode));\n    if (edgeBox.x === edgeBox.x2) {\n        edgeBox.x2 += 1;\n    }\n    if (edgeBox.y === edgeBox.y2) {\n        edgeBox.y2 += 1;\n    }\n    const viewRect = {\n        x: -transform[0] / transform[2],\n        y: -transform[1] / transform[2],\n        width: width / transform[2],\n        height: height / transform[2],\n    };\n    return getOverlappingArea(viewRect, boxToRect(edgeBox)) > 0;\n}\nconst getEdgeId = ({ source, sourceHandle, target, targetHandle }) => `xy-edge__${source}${sourceHandle || ''}-${target}${targetHandle || ''}`;\nconst connectionExists = (edge, edges) => {\n    return edges.some((el) => el.source === edge.source &&\n        el.target === edge.target &&\n        (el.sourceHandle === edge.sourceHandle || (!el.sourceHandle && !edge.sourceHandle)) &&\n        (el.targetHandle === edge.targetHandle || (!el.targetHandle && !edge.targetHandle)));\n};\n/**\n * This util is a convenience function to add a new Edge to an array of edges. It also performs some validation to make sure you don't add an invalid edge or duplicate an existing one.\n * @public\n * @param edgeParams - Either an `Edge` or a `Connection` you want to add.\n * @param edges - The array of all current edges.\n * @returns A new array of edges with the new edge added.\n *\n * @remarks If an edge with the same `target` and `source` already exists (and the same\n *`targetHandle` and `sourceHandle` if those are set), then this util won't add\n *a new edge even if the `id` property is different.\n *\n */\nconst addEdge = (edgeParams, edges) => {\n    if (!edgeParams.source || !edgeParams.target) {\n        devWarn('006', errorMessages['error006']());\n        return edges;\n    }\n    let edge;\n    if (isEdgeBase(edgeParams)) {\n        edge = { ...edgeParams };\n    }\n    else {\n        edge = {\n            ...edgeParams,\n            id: getEdgeId(edgeParams),\n        };\n    }\n    if (connectionExists(edge, edges)) {\n        return edges;\n    }\n    if (edge.sourceHandle === null) {\n        delete edge.sourceHandle;\n    }\n    if (edge.targetHandle === null) {\n        delete edge.targetHandle;\n    }\n    return edges.concat(edge);\n};\n/**\n * A handy utility to update an existing [`Edge`](/api-reference/types/edge) with new properties.\n *This searches your edge array for an edge with a matching `id` and updates its\n *properties with the connection you provide.\n * @public\n * @param oldEdge - The edge you want to update.\n * @param newConnection - The new connection you want to update the edge with.\n * @param edges - The array of all current edges.\n * @returns The updated edges array.\n *\n * @example\n * ```js\n *const onReconnect = useCallback(\n *  (oldEdge: Edge, newConnection: Connection) => setEdges((els) => reconnectEdge(oldEdge, newConnection, els)),[]);\n *```\n */\nconst reconnectEdge = (oldEdge, newConnection, edges, options = { shouldReplaceId: true }) => {\n    const { id: oldEdgeId, ...rest } = oldEdge;\n    if (!newConnection.source || !newConnection.target) {\n        devWarn('006', errorMessages['error006']());\n        return edges;\n    }\n    const foundEdge = edges.find((e) => e.id === oldEdge.id);\n    if (!foundEdge) {\n        devWarn('007', errorMessages['error007'](oldEdgeId));\n        return edges;\n    }\n    // Remove old edge and create the new edge with parameters of old edge.\n    const edge = {\n        ...rest,\n        id: options.shouldReplaceId ? getEdgeId(newConnection) : oldEdgeId,\n        source: newConnection.source,\n        target: newConnection.target,\n        sourceHandle: newConnection.sourceHandle,\n        targetHandle: newConnection.targetHandle,\n    };\n    return edges.filter((e) => e.id !== oldEdgeId).concat(edge);\n};\n\n/**\n * Calculates the straight line path between two points.\n * @public\n * @returns A path string you can use in an SVG, the `labelX` and `labelY` position (center of path)\n * and `offsetX`, `offsetY` between source handle and label.\n *\n * - `path`: the path to use in an SVG `<path>` element.\n * - `labelX`: the `x` position you can use to render a label for this edge.\n * - `labelY`: the `y` position you can use to render a label for this edge.\n * - `offsetX`: the absolute difference between the source `x` position and the `x` position of the\n * middle of this path.\n * - `offsetY`: the absolute difference between the source `y` position and the `y` position of the\n * middle of this path.\n * @example\n * ```js\n *  const source = { x: 0, y: 20 };\n *  const target = { x: 150, y: 100 };\n *\n *  const [path, labelX, labelY, offsetX, offsetY] = getStraightPath({\n *    sourceX: source.x,\n *    sourceY: source.y,\n *    sourcePosition: Position.Right,\n *    targetX: target.x,\n *    targetY: target.y,\n *    targetPosition: Position.Left,\n *  });\n * ```\n * @remarks This function returns a tuple (aka a fixed-size array) to make it easier to work with multiple edge paths at once.\n */\nfunction getStraightPath({ sourceX, sourceY, targetX, targetY, }) {\n    const [labelX, labelY, offsetX, offsetY] = getEdgeCenter({\n        sourceX,\n        sourceY,\n        targetX,\n        targetY,\n    });\n    return [`M ${sourceX},${sourceY}L ${targetX},${targetY}`, labelX, labelY, offsetX, offsetY];\n}\n\nconst handleDirections = {\n    [Position.Left]: { x: -1, y: 0 },\n    [Position.Right]: { x: 1, y: 0 },\n    [Position.Top]: { x: 0, y: -1 },\n    [Position.Bottom]: { x: 0, y: 1 },\n};\nconst getDirection = ({ source, sourcePosition = Position.Bottom, target, }) => {\n    if (sourcePosition === Position.Left || sourcePosition === Position.Right) {\n        return source.x < target.x ? { x: 1, y: 0 } : { x: -1, y: 0 };\n    }\n    return source.y < target.y ? { x: 0, y: 1 } : { x: 0, y: -1 };\n};\nconst distance = (a, b) => Math.sqrt(Math.pow(b.x - a.x, 2) + Math.pow(b.y - a.y, 2));\n/*\n * With this function we try to mimic an orthogonal edge routing behaviour\n * It's not as good as a real orthogonal edge routing, but it's faster and good enough as a default for step and smooth step edges\n */\nfunction getPoints({ source, sourcePosition = Position.Bottom, target, targetPosition = Position.Top, center, offset, }) {\n    const sourceDir = handleDirections[sourcePosition];\n    const targetDir = handleDirections[targetPosition];\n    const sourceGapped = { x: source.x + sourceDir.x * offset, y: source.y + sourceDir.y * offset };\n    const targetGapped = { x: target.x + targetDir.x * offset, y: target.y + targetDir.y * offset };\n    const dir = getDirection({\n        source: sourceGapped,\n        sourcePosition,\n        target: targetGapped,\n    });\n    const dirAccessor = dir.x !== 0 ? 'x' : 'y';\n    const currDir = dir[dirAccessor];\n    let points = [];\n    let centerX, centerY;\n    const sourceGapOffset = { x: 0, y: 0 };\n    const targetGapOffset = { x: 0, y: 0 };\n    const [defaultCenterX, defaultCenterY, defaultOffsetX, defaultOffsetY] = getEdgeCenter({\n        sourceX: source.x,\n        sourceY: source.y,\n        targetX: target.x,\n        targetY: target.y,\n    });\n    // opposite handle positions, default case\n    if (sourceDir[dirAccessor] * targetDir[dirAccessor] === -1) {\n        centerX = center.x ?? defaultCenterX;\n        centerY = center.y ?? defaultCenterY;\n        /*\n         *    --->\n         *    |\n         * >---\n         */\n        const verticalSplit = [\n            { x: centerX, y: sourceGapped.y },\n            { x: centerX, y: targetGapped.y },\n        ];\n        /*\n         *    |\n         *  ---\n         *  |\n         */\n        const horizontalSplit = [\n            { x: sourceGapped.x, y: centerY },\n            { x: targetGapped.x, y: centerY },\n        ];\n        if (sourceDir[dirAccessor] === currDir) {\n            points = dirAccessor === 'x' ? verticalSplit : horizontalSplit;\n        }\n        else {\n            points = dirAccessor === 'x' ? horizontalSplit : verticalSplit;\n        }\n    }\n    else {\n        // sourceTarget means we take x from source and y from target, targetSource is the opposite\n        const sourceTarget = [{ x: sourceGapped.x, y: targetGapped.y }];\n        const targetSource = [{ x: targetGapped.x, y: sourceGapped.y }];\n        // this handles edges with same handle positions\n        if (dirAccessor === 'x') {\n            points = sourceDir.x === currDir ? targetSource : sourceTarget;\n        }\n        else {\n            points = sourceDir.y === currDir ? sourceTarget : targetSource;\n        }\n        if (sourcePosition === targetPosition) {\n            const diff = Math.abs(source[dirAccessor] - target[dirAccessor]);\n            // if an edge goes from right to right for example (sourcePosition === targetPosition) and the distance between source.x and target.x is less than the offset, the added point and the gapped source/target will overlap. This leads to a weird edge path. To avoid this we add a gapOffset to the source/target\n            if (diff <= offset) {\n                const gapOffset = Math.min(offset - 1, offset - diff);\n                if (sourceDir[dirAccessor] === currDir) {\n                    sourceGapOffset[dirAccessor] = (sourceGapped[dirAccessor] > source[dirAccessor] ? -1 : 1) * gapOffset;\n                }\n                else {\n                    targetGapOffset[dirAccessor] = (targetGapped[dirAccessor] > target[dirAccessor] ? -1 : 1) * gapOffset;\n                }\n            }\n        }\n        // these are conditions for handling mixed handle positions like Right -> Bottom for example\n        if (sourcePosition !== targetPosition) {\n            const dirAccessorOpposite = dirAccessor === 'x' ? 'y' : 'x';\n            const isSameDir = sourceDir[dirAccessor] === targetDir[dirAccessorOpposite];\n            const sourceGtTargetOppo = sourceGapped[dirAccessorOpposite] > targetGapped[dirAccessorOpposite];\n            const sourceLtTargetOppo = sourceGapped[dirAccessorOpposite] < targetGapped[dirAccessorOpposite];\n            const flipSourceTarget = (sourceDir[dirAccessor] === 1 && ((!isSameDir && sourceGtTargetOppo) || (isSameDir && sourceLtTargetOppo))) ||\n                (sourceDir[dirAccessor] !== 1 && ((!isSameDir && sourceLtTargetOppo) || (isSameDir && sourceGtTargetOppo)));\n            if (flipSourceTarget) {\n                points = dirAccessor === 'x' ? sourceTarget : targetSource;\n            }\n        }\n        const sourceGapPoint = { x: sourceGapped.x + sourceGapOffset.x, y: sourceGapped.y + sourceGapOffset.y };\n        const targetGapPoint = { x: targetGapped.x + targetGapOffset.x, y: targetGapped.y + targetGapOffset.y };\n        const maxXDistance = Math.max(Math.abs(sourceGapPoint.x - points[0].x), Math.abs(targetGapPoint.x - points[0].x));\n        const maxYDistance = Math.max(Math.abs(sourceGapPoint.y - points[0].y), Math.abs(targetGapPoint.y - points[0].y));\n        // we want to place the label on the longest segment of the edge\n        if (maxXDistance >= maxYDistance) {\n            centerX = (sourceGapPoint.x + targetGapPoint.x) / 2;\n            centerY = points[0].y;\n        }\n        else {\n            centerX = points[0].x;\n            centerY = (sourceGapPoint.y + targetGapPoint.y) / 2;\n        }\n    }\n    const pathPoints = [\n        source,\n        { x: sourceGapped.x + sourceGapOffset.x, y: sourceGapped.y + sourceGapOffset.y },\n        ...points,\n        { x: targetGapped.x + targetGapOffset.x, y: targetGapped.y + targetGapOffset.y },\n        target,\n    ];\n    return [pathPoints, centerX, centerY, defaultOffsetX, defaultOffsetY];\n}\nfunction getBend(a, b, c, size) {\n    const bendSize = Math.min(distance(a, b) / 2, distance(b, c) / 2, size);\n    const { x, y } = b;\n    // no bend\n    if ((a.x === x && x === c.x) || (a.y === y && y === c.y)) {\n        return `L${x} ${y}`;\n    }\n    // first segment is horizontal\n    if (a.y === y) {\n        const xDir = a.x < c.x ? -1 : 1;\n        const yDir = a.y < c.y ? 1 : -1;\n        return `L ${x + bendSize * xDir},${y}Q ${x},${y} ${x},${y + bendSize * yDir}`;\n    }\n    const xDir = a.x < c.x ? 1 : -1;\n    const yDir = a.y < c.y ? -1 : 1;\n    return `L ${x},${y + bendSize * yDir}Q ${x},${y} ${x + bendSize * xDir},${y}`;\n}\n/**\n * The `getSmoothStepPath` util returns everything you need to render a stepped path\n * between two nodes. The `borderRadius` property can be used to choose how rounded\n * the corners of those steps are.\n * @public\n * @returns A path string you can use in an SVG, the `labelX` and `labelY` position (center of path)\n * and `offsetX`, `offsetY` between source handle and label.\n *\n * - `path`: the path to use in an SVG `<path>` element.\n * - `labelX`: the `x` position you can use to render a label for this edge.\n * - `labelY`: the `y` position you can use to render a label for this edge.\n * - `offsetX`: the absolute difference between the source `x` position and the `x` position of the\n * middle of this path.\n * - `offsetY`: the absolute difference between the source `y` position and the `y` position of the\n * middle of this path.\n * @example\n * ```js\n *  const source = { x: 0, y: 20 };\n *  const target = { x: 150, y: 100 };\n *\n *  const [path, labelX, labelY, offsetX, offsetY] = getSmoothStepPath({\n *    sourceX: source.x,\n *    sourceY: source.y,\n *    sourcePosition: Position.Right,\n *    targetX: target.x,\n *    targetY: target.y,\n *    targetPosition: Position.Left,\n *  });\n * ```\n * @remarks This function returns a tuple (aka a fixed-size array) to make it easier to work with multiple edge paths at once.\n */\nfunction getSmoothStepPath({ sourceX, sourceY, sourcePosition = Position.Bottom, targetX, targetY, targetPosition = Position.Top, borderRadius = 5, centerX, centerY, offset = 20, }) {\n    const [points, labelX, labelY, offsetX, offsetY] = getPoints({\n        source: { x: sourceX, y: sourceY },\n        sourcePosition,\n        target: { x: targetX, y: targetY },\n        targetPosition,\n        center: { x: centerX, y: centerY },\n        offset,\n    });\n    const path = points.reduce((res, p, i) => {\n        let segment = '';\n        if (i > 0 && i < points.length - 1) {\n            segment = getBend(points[i - 1], p, points[i + 1], borderRadius);\n        }\n        else {\n            segment = `${i === 0 ? 'M' : 'L'}${p.x} ${p.y}`;\n        }\n        res += segment;\n        return res;\n    }, '');\n    return [path, labelX, labelY, offsetX, offsetY];\n}\n\nfunction isNodeInitialized(node) {\n    return (node &&\n        !!(node.internals.handleBounds || node.handles?.length) &&\n        !!(node.measured.width || node.width || node.initialWidth));\n}\nfunction getEdgePosition(params) {\n    const { sourceNode, targetNode } = params;\n    if (!isNodeInitialized(sourceNode) || !isNodeInitialized(targetNode)) {\n        return null;\n    }\n    const sourceHandleBounds = sourceNode.internals.handleBounds || toHandleBounds(sourceNode.handles);\n    const targetHandleBounds = targetNode.internals.handleBounds || toHandleBounds(targetNode.handles);\n    const sourceHandle = getHandle$1(sourceHandleBounds?.source ?? [], params.sourceHandle);\n    const targetHandle = getHandle$1(\n    // when connection type is loose we can define all handles as sources and connect source -> source\n    params.connectionMode === ConnectionMode.Strict\n        ? targetHandleBounds?.target ?? []\n        : (targetHandleBounds?.target ?? []).concat(targetHandleBounds?.source ?? []), params.targetHandle);\n    if (!sourceHandle || !targetHandle) {\n        params.onError?.('008', errorMessages['error008'](!sourceHandle ? 'source' : 'target', {\n            id: params.id,\n            sourceHandle: params.sourceHandle,\n            targetHandle: params.targetHandle,\n        }));\n        return null;\n    }\n    const sourcePosition = sourceHandle?.position || Position.Bottom;\n    const targetPosition = targetHandle?.position || Position.Top;\n    const source = getHandlePosition(sourceNode, sourceHandle, sourcePosition);\n    const target = getHandlePosition(targetNode, targetHandle, targetPosition);\n    return {\n        sourceX: source.x,\n        sourceY: source.y,\n        targetX: target.x,\n        targetY: target.y,\n        sourcePosition,\n        targetPosition,\n    };\n}\nfunction toHandleBounds(handles) {\n    if (!handles) {\n        return null;\n    }\n    const source = [];\n    const target = [];\n    for (const handle of handles) {\n        handle.width = handle.width ?? 1;\n        handle.height = handle.height ?? 1;\n        if (handle.type === 'source') {\n            source.push(handle);\n        }\n        else if (handle.type === 'target') {\n            target.push(handle);\n        }\n    }\n    return {\n        source,\n        target,\n    };\n}\nfunction getHandlePosition(node, handle, fallbackPosition = Position.Left, center = false) {\n    const x = (handle?.x ?? 0) + node.internals.positionAbsolute.x;\n    const y = (handle?.y ?? 0) + node.internals.positionAbsolute.y;\n    const { width, height } = handle ?? getNodeDimensions(node);\n    if (center) {\n        return { x: x + width / 2, y: y + height / 2 };\n    }\n    const position = handle?.position ?? fallbackPosition;\n    switch (position) {\n        case Position.Top:\n            return { x: x + width / 2, y };\n        case Position.Right:\n            return { x: x + width, y: y + height / 2 };\n        case Position.Bottom:\n            return { x: x + width / 2, y: y + height };\n        case Position.Left:\n            return { x, y: y + height / 2 };\n    }\n}\nfunction getHandle$1(bounds, handleId) {\n    if (!bounds) {\n        return null;\n    }\n    // if no handleId is given, we use the first handle, otherwise we check for the id\n    return (!handleId ? bounds[0] : bounds.find((d) => d.id === handleId)) || null;\n}\n\nfunction getMarkerId(marker, id) {\n    if (!marker) {\n        return '';\n    }\n    if (typeof marker === 'string') {\n        return marker;\n    }\n    const idPrefix = id ? `${id}__` : '';\n    return `${idPrefix}${Object.keys(marker)\n        .sort()\n        .map((key) => `${key}=${marker[key]}`)\n        .join('&')}`;\n}\nfunction createMarkerIds(edges, { id, defaultColor, defaultMarkerStart, defaultMarkerEnd, }) {\n    const ids = new Set();\n    return edges\n        .reduce((markers, edge) => {\n        [edge.markerStart || defaultMarkerStart, edge.markerEnd || defaultMarkerEnd].forEach((marker) => {\n            if (marker && typeof marker === 'object') {\n                const markerId = getMarkerId(marker, id);\n                if (!ids.has(markerId)) {\n                    markers.push({ id: markerId, color: marker.color || defaultColor, ...marker });\n                    ids.add(markerId);\n                }\n            }\n        });\n        return markers;\n    }, [])\n        .sort((a, b) => a.id.localeCompare(b.id));\n}\n\nfunction getNodeToolbarTransform(nodeRect, viewport, position, offset, align) {\n    let alignmentOffset = 0.5;\n    if (align === 'start') {\n        alignmentOffset = 0;\n    }\n    else if (align === 'end') {\n        alignmentOffset = 1;\n    }\n    /*\n     * position === Position.Top\n     * we set the x any y position of the toolbar based on the nodes position\n     */\n    let pos = [\n        (nodeRect.x + nodeRect.width * alignmentOffset) * viewport.zoom + viewport.x,\n        nodeRect.y * viewport.zoom + viewport.y - offset,\n    ];\n    // and than shift it based on the alignment. The shift values are in %.\n    let shift = [-100 * alignmentOffset, -100];\n    switch (position) {\n        case Position.Right:\n            pos = [\n                (nodeRect.x + nodeRect.width) * viewport.zoom + viewport.x + offset,\n                (nodeRect.y + nodeRect.height * alignmentOffset) * viewport.zoom + viewport.y,\n            ];\n            shift = [0, -100 * alignmentOffset];\n            break;\n        case Position.Bottom:\n            pos[1] = (nodeRect.y + nodeRect.height) * viewport.zoom + viewport.y + offset;\n            shift[1] = 0;\n            break;\n        case Position.Left:\n            pos = [\n                nodeRect.x * viewport.zoom + viewport.x - offset,\n                (nodeRect.y + nodeRect.height * alignmentOffset) * viewport.zoom + viewport.y,\n            ];\n            shift = [-100, -100 * alignmentOffset];\n            break;\n    }\n    return `translate(${pos[0]}px, ${pos[1]}px) translate(${shift[0]}%, ${shift[1]}%)`;\n}\n\nconst defaultOptions = {\n    nodeOrigin: [0, 0],\n    nodeExtent: infiniteExtent,\n    elevateNodesOnSelect: true,\n    defaults: {},\n};\nconst adoptUserNodesDefaultOptions = {\n    ...defaultOptions,\n    checkEquality: true,\n};\nfunction mergeObjects(base, incoming) {\n    const result = { ...base };\n    for (const key in incoming) {\n        if (incoming[key] !== undefined) {\n            // typecast is safe here, because we check for undefined\n            result[key] = incoming[key];\n        }\n    }\n    return result;\n}\nfunction updateAbsolutePositions(nodeLookup, parentLookup, options) {\n    const _options = mergeObjects(defaultOptions, options);\n    for (const node of nodeLookup.values()) {\n        if (node.parentId) {\n            updateChildNode(node, nodeLookup, parentLookup, _options);\n        }\n        else {\n            const positionWithOrigin = getNodePositionWithOrigin(node, _options.nodeOrigin);\n            const extent = isCoordinateExtent(node.extent) ? node.extent : _options.nodeExtent;\n            const clampedPosition = clampPosition(positionWithOrigin, extent, getNodeDimensions(node));\n            node.internals.positionAbsolute = clampedPosition;\n        }\n    }\n}\nfunction adoptUserNodes(nodes, nodeLookup, parentLookup, options) {\n    const _options = mergeObjects(adoptUserNodesDefaultOptions, options);\n    let nodesInitialized = nodes.length > 0;\n    const tmpLookup = new Map(nodeLookup);\n    const selectedNodeZ = _options?.elevateNodesOnSelect ? 1000 : 0;\n    nodeLookup.clear();\n    parentLookup.clear();\n    for (const userNode of nodes) {\n        let internalNode = tmpLookup.get(userNode.id);\n        if (_options.checkEquality && userNode === internalNode?.internals.userNode) {\n            nodeLookup.set(userNode.id, internalNode);\n        }\n        else {\n            const positionWithOrigin = getNodePositionWithOrigin(userNode, _options.nodeOrigin);\n            const extent = isCoordinateExtent(userNode.extent) ? userNode.extent : _options.nodeExtent;\n            const clampedPosition = clampPosition(positionWithOrigin, extent, getNodeDimensions(userNode));\n            internalNode = {\n                ..._options.defaults,\n                ...userNode,\n                measured: {\n                    width: userNode.measured?.width,\n                    height: userNode.measured?.height,\n                },\n                internals: {\n                    positionAbsolute: clampedPosition,\n                    // if user re-initializes the node or removes `measured` for whatever reason, we reset the handleBounds so that the node gets re-measured\n                    handleBounds: !userNode.measured ? undefined : internalNode?.internals.handleBounds,\n                    z: calculateZ(userNode, selectedNodeZ),\n                    userNode,\n                },\n            };\n            nodeLookup.set(userNode.id, internalNode);\n        }\n        if ((internalNode.measured === undefined ||\n            internalNode.measured.width === undefined ||\n            internalNode.measured.height === undefined) &&\n            !internalNode.hidden) {\n            nodesInitialized = false;\n        }\n        if (userNode.parentId) {\n            updateChildNode(internalNode, nodeLookup, parentLookup, options);\n        }\n    }\n    return nodesInitialized;\n}\nfunction updateParentLookup(node, parentLookup) {\n    if (!node.parentId) {\n        return;\n    }\n    const childNodes = parentLookup.get(node.parentId);\n    if (childNodes) {\n        childNodes.set(node.id, node);\n    }\n    else {\n        parentLookup.set(node.parentId, new Map([[node.id, node]]));\n    }\n}\n/**\n * Updates positionAbsolute and zIndex of a child node and the parentLookup.\n */\nfunction updateChildNode(node, nodeLookup, parentLookup, options) {\n    const { elevateNodesOnSelect, nodeOrigin, nodeExtent } = mergeObjects(defaultOptions, options);\n    const parentId = node.parentId;\n    const parentNode = nodeLookup.get(parentId);\n    if (!parentNode) {\n        console.warn(`Parent node ${parentId} not found. Please make sure that parent nodes are in front of their child nodes in the nodes array.`);\n        return;\n    }\n    updateParentLookup(node, parentLookup);\n    const selectedNodeZ = elevateNodesOnSelect ? 1000 : 0;\n    const { x, y, z } = calculateChildXYZ(node, parentNode, nodeOrigin, nodeExtent, selectedNodeZ);\n    const { positionAbsolute } = node.internals;\n    const positionChanged = x !== positionAbsolute.x || y !== positionAbsolute.y;\n    if (positionChanged || z !== node.internals.z) {\n        // we create a new object to mark the node as updated\n        nodeLookup.set(node.id, {\n            ...node,\n            internals: {\n                ...node.internals,\n                positionAbsolute: positionChanged ? { x, y } : positionAbsolute,\n                z,\n            },\n        });\n    }\n}\nfunction calculateZ(node, selectedNodeZ) {\n    return (isNumeric(node.zIndex) ? node.zIndex : 0) + (node.selected ? selectedNodeZ : 0);\n}\nfunction calculateChildXYZ(childNode, parentNode, nodeOrigin, nodeExtent, selectedNodeZ) {\n    const { x: parentX, y: parentY } = parentNode.internals.positionAbsolute;\n    const childDimensions = getNodeDimensions(childNode);\n    const positionWithOrigin = getNodePositionWithOrigin(childNode, nodeOrigin);\n    const clampedPosition = isCoordinateExtent(childNode.extent)\n        ? clampPosition(positionWithOrigin, childNode.extent, childDimensions)\n        : positionWithOrigin;\n    let absolutePosition = clampPosition({ x: parentX + clampedPosition.x, y: parentY + clampedPosition.y }, nodeExtent, childDimensions);\n    if (childNode.extent === 'parent') {\n        absolutePosition = clampPositionToParent(absolutePosition, childDimensions, parentNode);\n    }\n    const childZ = calculateZ(childNode, selectedNodeZ);\n    const parentZ = parentNode.internals.z ?? 0;\n    return {\n        x: absolutePosition.x,\n        y: absolutePosition.y,\n        z: parentZ > childZ ? parentZ : childZ,\n    };\n}\nfunction handleExpandParent(children, nodeLookup, parentLookup, nodeOrigin = [0, 0]) {\n    const changes = [];\n    const parentExpansions = new Map();\n    // determine the expanded rectangle the child nodes would take for each parent\n    for (const child of children) {\n        const parent = nodeLookup.get(child.parentId);\n        if (!parent) {\n            continue;\n        }\n        const parentRect = parentExpansions.get(child.parentId)?.expandedRect ?? nodeToRect(parent);\n        const expandedRect = getBoundsOfRects(parentRect, child.rect);\n        parentExpansions.set(child.parentId, { expandedRect, parent });\n    }\n    if (parentExpansions.size > 0) {\n        parentExpansions.forEach(({ expandedRect, parent }, parentId) => {\n            // determine the position & dimensions of the parent\n            const positionAbsolute = parent.internals.positionAbsolute;\n            const dimensions = getNodeDimensions(parent);\n            const origin = parent.origin ?? nodeOrigin;\n            // determine how much the parent expands in width and position\n            const xChange = expandedRect.x < positionAbsolute.x ? Math.round(Math.abs(positionAbsolute.x - expandedRect.x)) : 0;\n            const yChange = expandedRect.y < positionAbsolute.y ? Math.round(Math.abs(positionAbsolute.y - expandedRect.y)) : 0;\n            const newWidth = Math.max(dimensions.width, Math.round(expandedRect.width));\n            const newHeight = Math.max(dimensions.height, Math.round(expandedRect.height));\n            const widthChange = (newWidth - dimensions.width) * origin[0];\n            const heightChange = (newHeight - dimensions.height) * origin[1];\n            // We need to correct the position of the parent node if the origin is not [0,0]\n            if (xChange > 0 || yChange > 0 || widthChange || heightChange) {\n                changes.push({\n                    id: parentId,\n                    type: 'position',\n                    position: {\n                        x: parent.position.x - xChange + widthChange,\n                        y: parent.position.y - yChange + heightChange,\n                    },\n                });\n                /*\n                 * We move all child nodes in the oppsite direction\n                 * so the x,y changes of the parent do not move the children\n                 */\n                parentLookup.get(parentId)?.forEach((childNode) => {\n                    if (!children.some((child) => child.id === childNode.id)) {\n                        changes.push({\n                            id: childNode.id,\n                            type: 'position',\n                            position: {\n                                x: childNode.position.x + xChange,\n                                y: childNode.position.y + yChange,\n                            },\n                        });\n                    }\n                });\n            }\n            // We need to correct the dimensions of the parent node if the origin is not [0,0]\n            if (dimensions.width < expandedRect.width || dimensions.height < expandedRect.height || xChange || yChange) {\n                changes.push({\n                    id: parentId,\n                    type: 'dimensions',\n                    setAttributes: true,\n                    dimensions: {\n                        width: newWidth + (xChange ? origin[0] * xChange - widthChange : 0),\n                        height: newHeight + (yChange ? origin[1] * yChange - heightChange : 0),\n                    },\n                });\n            }\n        });\n    }\n    return changes;\n}\nfunction updateNodeInternals(updates, nodeLookup, parentLookup, domNode, nodeOrigin, nodeExtent) {\n    const viewportNode = domNode?.querySelector('.xyflow__viewport');\n    let updatedInternals = false;\n    if (!viewportNode) {\n        return { changes: [], updatedInternals };\n    }\n    const changes = [];\n    const style = window.getComputedStyle(viewportNode);\n    const { m22: zoom } = new window.DOMMatrixReadOnly(style.transform);\n    // in this array we collect nodes, that might trigger changes (like expanding parent)\n    const parentExpandChildren = [];\n    for (const update of updates.values()) {\n        const node = nodeLookup.get(update.id);\n        if (!node) {\n            continue;\n        }\n        if (node.hidden) {\n            nodeLookup.set(node.id, {\n                ...node,\n                internals: {\n                    ...node.internals,\n                    handleBounds: undefined,\n                },\n            });\n            updatedInternals = true;\n            continue;\n        }\n        const dimensions = getDimensions(update.nodeElement);\n        const dimensionChanged = node.measured.width !== dimensions.width || node.measured.height !== dimensions.height;\n        const doUpdate = !!(dimensions.width &&\n            dimensions.height &&\n            (dimensionChanged || !node.internals.handleBounds || update.force));\n        if (doUpdate) {\n            const nodeBounds = update.nodeElement.getBoundingClientRect();\n            const extent = isCoordinateExtent(node.extent) ? node.extent : nodeExtent;\n            let { positionAbsolute } = node.internals;\n            if (node.parentId && node.extent === 'parent') {\n                positionAbsolute = clampPositionToParent(positionAbsolute, dimensions, nodeLookup.get(node.parentId));\n            }\n            else if (extent) {\n                positionAbsolute = clampPosition(positionAbsolute, extent, dimensions);\n            }\n            const newNode = {\n                ...node,\n                measured: dimensions,\n                internals: {\n                    ...node.internals,\n                    positionAbsolute,\n                    handleBounds: {\n                        source: getHandleBounds('source', update.nodeElement, nodeBounds, zoom, node.id),\n                        target: getHandleBounds('target', update.nodeElement, nodeBounds, zoom, node.id),\n                    },\n                },\n            };\n            nodeLookup.set(node.id, newNode);\n            if (node.parentId) {\n                updateChildNode(newNode, nodeLookup, parentLookup, { nodeOrigin });\n            }\n            updatedInternals = true;\n            if (dimensionChanged) {\n                changes.push({\n                    id: node.id,\n                    type: 'dimensions',\n                    dimensions,\n                });\n                if (node.expandParent && node.parentId) {\n                    parentExpandChildren.push({\n                        id: node.id,\n                        parentId: node.parentId,\n                        rect: nodeToRect(newNode, nodeOrigin),\n                    });\n                }\n            }\n        }\n    }\n    if (parentExpandChildren.length > 0) {\n        const parentExpandChanges = handleExpandParent(parentExpandChildren, nodeLookup, parentLookup, nodeOrigin);\n        changes.push(...parentExpandChanges);\n    }\n    return { changes, updatedInternals };\n}\nasync function panBy({ delta, panZoom, transform, translateExtent, width, height, }) {\n    if (!panZoom || (!delta.x && !delta.y)) {\n        return Promise.resolve(false);\n    }\n    const nextViewport = await panZoom.setViewportConstrained({\n        x: transform[0] + delta.x,\n        y: transform[1] + delta.y,\n        zoom: transform[2],\n    }, [\n        [0, 0],\n        [width, height],\n    ], translateExtent);\n    const transformChanged = !!nextViewport &&\n        (nextViewport.x !== transform[0] || nextViewport.y !== transform[1] || nextViewport.k !== transform[2]);\n    return Promise.resolve(transformChanged);\n}\n/**\n * this function adds the connection to the connectionLookup\n * at the following keys: nodeId-type-handleId, nodeId-type and nodeId\n * @param type type of the connection\n * @param connection connection that should be added to the lookup\n * @param connectionKey at which key the connection should be added\n * @param connectionLookup reference to the connection lookup\n * @param nodeId nodeId of the connection\n * @param handleId handleId of the conneciton\n */\nfunction addConnectionToLookup(type, connection, connectionKey, connectionLookup, nodeId, handleId) {\n    /*\n     * We add the connection to the connectionLookup at the following keys\n     * 1. nodeId, 2. nodeId-type, 3. nodeId-type-handleId\n     * If the key already exists, we add the connection to the existing map\n     */\n    let key = nodeId;\n    const nodeMap = connectionLookup.get(key) || new Map();\n    connectionLookup.set(key, nodeMap.set(connectionKey, connection));\n    key = `${nodeId}-${type}`;\n    const typeMap = connectionLookup.get(key) || new Map();\n    connectionLookup.set(key, typeMap.set(connectionKey, connection));\n    if (handleId) {\n        key = `${nodeId}-${type}-${handleId}`;\n        const handleMap = connectionLookup.get(key) || new Map();\n        connectionLookup.set(key, handleMap.set(connectionKey, connection));\n    }\n}\nfunction updateConnectionLookup(connectionLookup, edgeLookup, edges) {\n    connectionLookup.clear();\n    edgeLookup.clear();\n    for (const edge of edges) {\n        const { source: sourceNode, target: targetNode, sourceHandle = null, targetHandle = null } = edge;\n        const connection = { edgeId: edge.id, source: sourceNode, target: targetNode, sourceHandle, targetHandle };\n        const sourceKey = `${sourceNode}-${sourceHandle}--${targetNode}-${targetHandle}`;\n        const targetKey = `${targetNode}-${targetHandle}--${sourceNode}-${sourceHandle}`;\n        addConnectionToLookup('source', connection, targetKey, connectionLookup, sourceNode, sourceHandle);\n        addConnectionToLookup('target', connection, sourceKey, connectionLookup, targetNode, targetHandle);\n        edgeLookup.set(edge.id, edge);\n    }\n}\n\nfunction shallowNodeData(a, b) {\n    if (a === null || b === null) {\n        return false;\n    }\n    const _a = Array.isArray(a) ? a : [a];\n    const _b = Array.isArray(b) ? b : [b];\n    if (_a.length !== _b.length) {\n        return false;\n    }\n    for (let i = 0; i < _a.length; i++) {\n        if (_a[i].id !== _b[i].id || _a[i].type !== _b[i].type || !Object.is(_a[i].data, _b[i].data)) {\n            return false;\n        }\n    }\n    return true;\n}\n\nfunction isParentSelected(node, nodeLookup) {\n    if (!node.parentId) {\n        return false;\n    }\n    const parentNode = nodeLookup.get(node.parentId);\n    if (!parentNode) {\n        return false;\n    }\n    if (parentNode.selected) {\n        return true;\n    }\n    return isParentSelected(parentNode, nodeLookup);\n}\nfunction hasSelector(target, selector, domNode) {\n    let current = target;\n    do {\n        if (current?.matches?.(selector))\n            return true;\n        if (current === domNode)\n            return false;\n        current = current?.parentElement;\n    } while (current);\n    return false;\n}\n// looks for all selected nodes and created a NodeDragItem for each of them\nfunction getDragItems(nodeLookup, nodesDraggable, mousePos, nodeId) {\n    const dragItems = new Map();\n    for (const [id, node] of nodeLookup) {\n        if ((node.selected || node.id === nodeId) &&\n            (!node.parentId || !isParentSelected(node, nodeLookup)) &&\n            (node.draggable || (nodesDraggable && typeof node.draggable === 'undefined'))) {\n            const internalNode = nodeLookup.get(id);\n            if (internalNode) {\n                dragItems.set(id, {\n                    id,\n                    position: internalNode.position || { x: 0, y: 0 },\n                    distance: {\n                        x: mousePos.x - internalNode.internals.positionAbsolute.x,\n                        y: mousePos.y - internalNode.internals.positionAbsolute.y,\n                    },\n                    extent: internalNode.extent,\n                    parentId: internalNode.parentId,\n                    origin: internalNode.origin,\n                    expandParent: internalNode.expandParent,\n                    internals: {\n                        positionAbsolute: internalNode.internals.positionAbsolute || { x: 0, y: 0 },\n                    },\n                    measured: {\n                        width: internalNode.measured.width ?? 0,\n                        height: internalNode.measured.height ?? 0,\n                    },\n                });\n            }\n        }\n    }\n    return dragItems;\n}\n/*\n * returns two params:\n * 1. the dragged node (or the first of the list, if we are dragging a node selection)\n * 2. array of selected nodes (for multi selections)\n */\nfunction getEventHandlerParams({ nodeId, dragItems, nodeLookup, dragging = true, }) {\n    const nodesFromDragItems = [];\n    for (const [id, dragItem] of dragItems) {\n        const node = nodeLookup.get(id)?.internals.userNode;\n        if (node) {\n            nodesFromDragItems.push({\n                ...node,\n                position: dragItem.position,\n                dragging,\n            });\n        }\n    }\n    if (!nodeId) {\n        return [nodesFromDragItems[0], nodesFromDragItems];\n    }\n    const node = nodeLookup.get(nodeId)?.internals.userNode;\n    return [\n        !node\n            ? nodesFromDragItems[0]\n            : {\n                ...node,\n                position: dragItems.get(nodeId)?.position || node.position,\n                dragging,\n            },\n        nodesFromDragItems,\n    ];\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction XYDrag({ onNodeMouseDown, getStoreItems, onDragStart, onDrag, onDragStop, }) {\n    let lastPos = { x: null, y: null };\n    let autoPanId = 0;\n    let dragItems = new Map();\n    let autoPanStarted = false;\n    let mousePosition = { x: 0, y: 0 };\n    let containerBounds = null;\n    let dragStarted = false;\n    let d3Selection = null;\n    let abortDrag = false; // prevents unintentional dragging on multitouch\n    // public functions\n    function update({ noDragClassName, handleSelector, domNode, isSelectable, nodeId, nodeClickDistance = 0, }) {\n        d3Selection = select(domNode);\n        function updateNodes({ x, y }, dragEvent) {\n            const { nodeLookup, nodeExtent, snapGrid, snapToGrid, nodeOrigin, onNodeDrag, onSelectionDrag, onError, updateNodePositions, } = getStoreItems();\n            lastPos = { x, y };\n            let hasChange = false;\n            let nodesBox = { x: 0, y: 0, x2: 0, y2: 0 };\n            if (dragItems.size > 1 && nodeExtent) {\n                const rect = getInternalNodesBounds(dragItems);\n                nodesBox = rectToBox(rect);\n            }\n            for (const [id, dragItem] of dragItems) {\n                if (!nodeLookup.has(id)) {\n                    /*\n                     * if the node is not in the nodeLookup anymore, it was probably deleted while dragging\n                     * and we don't need to update it anymore\n                     */\n                    continue;\n                }\n                let nextPosition = { x: x - dragItem.distance.x, y: y - dragItem.distance.y };\n                if (snapToGrid) {\n                    nextPosition = snapPosition(nextPosition, snapGrid);\n                }\n                /*\n                 * if there is selection with multiple nodes and a node extent is set, we need to adjust the node extent for each node\n                 * based on its position so that the node stays at it's position relative to the selection.\n                 */\n                let adjustedNodeExtent = [\n                    [nodeExtent[0][0], nodeExtent[0][1]],\n                    [nodeExtent[1][0], nodeExtent[1][1]],\n                ];\n                if (dragItems.size > 1 && nodeExtent && !dragItem.extent) {\n                    const { positionAbsolute } = dragItem.internals;\n                    const x1 = positionAbsolute.x - nodesBox.x + nodeExtent[0][0];\n                    const x2 = positionAbsolute.x + dragItem.measured.width - nodesBox.x2 + nodeExtent[1][0];\n                    const y1 = positionAbsolute.y - nodesBox.y + nodeExtent[0][1];\n                    const y2 = positionAbsolute.y + dragItem.measured.height - nodesBox.y2 + nodeExtent[1][1];\n                    adjustedNodeExtent = [\n                        [x1, y1],\n                        [x2, y2],\n                    ];\n                }\n                const { position, positionAbsolute } = calculateNodePosition({\n                    nodeId: id,\n                    nextPosition,\n                    nodeLookup,\n                    nodeExtent: adjustedNodeExtent,\n                    nodeOrigin,\n                    onError,\n                });\n                // we want to make sure that we only fire a change event when there is a change\n                hasChange = hasChange || dragItem.position.x !== position.x || dragItem.position.y !== position.y;\n                dragItem.position = position;\n                dragItem.internals.positionAbsolute = positionAbsolute;\n            }\n            if (!hasChange) {\n                return;\n            }\n            updateNodePositions(dragItems, true);\n            if (dragEvent && (onDrag || onNodeDrag || (!nodeId && onSelectionDrag))) {\n                const [currentNode, currentNodes] = getEventHandlerParams({\n                    nodeId,\n                    dragItems,\n                    nodeLookup,\n                });\n                onDrag?.(dragEvent, dragItems, currentNode, currentNodes);\n                onNodeDrag?.(dragEvent, currentNode, currentNodes);\n                if (!nodeId) {\n                    onSelectionDrag?.(dragEvent, currentNodes);\n                }\n            }\n        }\n        async function autoPan() {\n            if (!containerBounds) {\n                return;\n            }\n            const { transform, panBy, autoPanSpeed, autoPanOnNodeDrag } = getStoreItems();\n            if (!autoPanOnNodeDrag) {\n                autoPanStarted = false;\n                cancelAnimationFrame(autoPanId);\n                return;\n            }\n            const [xMovement, yMovement] = calcAutoPan(mousePosition, containerBounds, autoPanSpeed);\n            if (xMovement !== 0 || yMovement !== 0) {\n                lastPos.x = (lastPos.x ?? 0) - xMovement / transform[2];\n                lastPos.y = (lastPos.y ?? 0) - yMovement / transform[2];\n                if (await panBy({ x: xMovement, y: yMovement })) {\n                    updateNodes(lastPos, null);\n                }\n            }\n            autoPanId = requestAnimationFrame(autoPan);\n        }\n        function startDrag(event) {\n            const { nodeLookup, multiSelectionActive, nodesDraggable, transform, snapGrid, snapToGrid, selectNodesOnDrag, onNodeDragStart, onSelectionDragStart, unselectNodesAndEdges, } = getStoreItems();\n            dragStarted = true;\n            if ((!selectNodesOnDrag || !isSelectable) && !multiSelectionActive && nodeId) {\n                if (!nodeLookup.get(nodeId)?.selected) {\n                    // we need to reset selected nodes when selectNodesOnDrag=false\n                    unselectNodesAndEdges();\n                }\n            }\n            if (isSelectable && selectNodesOnDrag && nodeId) {\n                onNodeMouseDown?.(nodeId);\n            }\n            const pointerPos = getPointerPosition(event.sourceEvent, { transform, snapGrid, snapToGrid, containerBounds });\n            lastPos = pointerPos;\n            dragItems = getDragItems(nodeLookup, nodesDraggable, pointerPos, nodeId);\n            if (dragItems.size > 0 && (onDragStart || onNodeDragStart || (!nodeId && onSelectionDragStart))) {\n                const [currentNode, currentNodes] = getEventHandlerParams({\n                    nodeId,\n                    dragItems,\n                    nodeLookup,\n                });\n                onDragStart?.(event.sourceEvent, dragItems, currentNode, currentNodes);\n                onNodeDragStart?.(event.sourceEvent, currentNode, currentNodes);\n                if (!nodeId) {\n                    onSelectionDragStart?.(event.sourceEvent, currentNodes);\n                }\n            }\n        }\n        const d3DragInstance = drag()\n            .clickDistance(nodeClickDistance)\n            .on('start', (event) => {\n            const { domNode, nodeDragThreshold, transform, snapGrid, snapToGrid } = getStoreItems();\n            containerBounds = domNode?.getBoundingClientRect() || null;\n            abortDrag = false;\n            if (nodeDragThreshold === 0) {\n                startDrag(event);\n            }\n            const pointerPos = getPointerPosition(event.sourceEvent, { transform, snapGrid, snapToGrid, containerBounds });\n            lastPos = pointerPos;\n            mousePosition = getEventPosition(event.sourceEvent, containerBounds);\n        })\n            .on('drag', (event) => {\n            const { autoPanOnNodeDrag, transform, snapGrid, snapToGrid, nodeDragThreshold, nodeLookup } = getStoreItems();\n            const pointerPos = getPointerPosition(event.sourceEvent, { transform, snapGrid, snapToGrid, containerBounds });\n            if ((event.sourceEvent.type === 'touchmove' && event.sourceEvent.touches.length > 1) ||\n                // if user deletes a node while dragging, we need to abort the drag to prevent errors\n                (nodeId && !nodeLookup.has(nodeId))) {\n                abortDrag = true;\n            }\n            if (abortDrag) {\n                return;\n            }\n            if (!autoPanStarted && autoPanOnNodeDrag && dragStarted) {\n                autoPanStarted = true;\n                autoPan();\n            }\n            if (!dragStarted) {\n                const x = pointerPos.xSnapped - (lastPos.x ?? 0);\n                const y = pointerPos.ySnapped - (lastPos.y ?? 0);\n                const distance = Math.sqrt(x * x + y * y);\n                if (distance > nodeDragThreshold) {\n                    startDrag(event);\n                }\n            }\n            // skip events without movement\n            if ((lastPos.x !== pointerPos.xSnapped || lastPos.y !== pointerPos.ySnapped) && dragItems && dragStarted) {\n                // dragEvent = event.sourceEvent as MouseEvent;\n                mousePosition = getEventPosition(event.sourceEvent, containerBounds);\n                updateNodes(pointerPos, event.sourceEvent);\n            }\n        })\n            .on('end', (event) => {\n            if (!dragStarted || abortDrag) {\n                return;\n            }\n            autoPanStarted = false;\n            dragStarted = false;\n            cancelAnimationFrame(autoPanId);\n            if (dragItems.size > 0) {\n                const { nodeLookup, updateNodePositions, onNodeDragStop, onSelectionDragStop } = getStoreItems();\n                updateNodePositions(dragItems, false);\n                if (onDragStop || onNodeDragStop || (!nodeId && onSelectionDragStop)) {\n                    const [currentNode, currentNodes] = getEventHandlerParams({\n                        nodeId,\n                        dragItems,\n                        nodeLookup,\n                        dragging: false,\n                    });\n                    onDragStop?.(event.sourceEvent, dragItems, currentNode, currentNodes);\n                    onNodeDragStop?.(event.sourceEvent, currentNode, currentNodes);\n                    if (!nodeId) {\n                        onSelectionDragStop?.(event.sourceEvent, currentNodes);\n                    }\n                }\n            }\n        })\n            .filter((event) => {\n            const target = event.target;\n            const isDraggable = !event.button &&\n                (!noDragClassName || !hasSelector(target, `.${noDragClassName}`, domNode)) &&\n                (!handleSelector || hasSelector(target, handleSelector, domNode));\n            return isDraggable;\n        });\n        d3Selection.call(d3DragInstance);\n    }\n    function destroy() {\n        d3Selection?.on('.drag', null);\n    }\n    return {\n        update,\n        destroy,\n    };\n}\n\nfunction getNodesWithinDistance(position, nodeLookup, distance) {\n    const nodes = [];\n    const rect = {\n        x: position.x - distance,\n        y: position.y - distance,\n        width: distance * 2,\n        height: distance * 2,\n    };\n    for (const node of nodeLookup.values()) {\n        if (getOverlappingArea(rect, nodeToRect(node)) > 0) {\n            nodes.push(node);\n        }\n    }\n    return nodes;\n}\n/*\n * this distance is used for the area around the user pointer\n * while doing a connection for finding the closest nodes\n */\nconst ADDITIONAL_DISTANCE = 250;\nfunction getClosestHandle(position, connectionRadius, nodeLookup, fromHandle) {\n    let closestHandles = [];\n    let minDistance = Infinity;\n    const closeNodes = getNodesWithinDistance(position, nodeLookup, connectionRadius + ADDITIONAL_DISTANCE);\n    for (const node of closeNodes) {\n        const allHandles = [...(node.internals.handleBounds?.source ?? []), ...(node.internals.handleBounds?.target ?? [])];\n        for (const handle of allHandles) {\n            // if the handle is the same as the fromHandle we skip it\n            if (fromHandle.nodeId === handle.nodeId && fromHandle.type === handle.type && fromHandle.id === handle.id) {\n                continue;\n            }\n            // determine absolute position of the handle\n            const { x, y } = getHandlePosition(node, handle, handle.position, true);\n            const distance = Math.sqrt(Math.pow(x - position.x, 2) + Math.pow(y - position.y, 2));\n            if (distance > connectionRadius) {\n                continue;\n            }\n            if (distance < minDistance) {\n                closestHandles = [{ ...handle, x, y }];\n                minDistance = distance;\n            }\n            else if (distance === minDistance) {\n                // when multiple handles are on the same distance we collect all of them\n                closestHandles.push({ ...handle, x, y });\n            }\n        }\n    }\n    if (!closestHandles.length) {\n        return null;\n    }\n    // when multiple handles overlay each other we prefer the opposite handle\n    if (closestHandles.length > 1) {\n        const oppositeHandleType = fromHandle.type === 'source' ? 'target' : 'source';\n        return closestHandles.find((handle) => handle.type === oppositeHandleType) ?? closestHandles[0];\n    }\n    return closestHandles[0];\n}\nfunction getHandle(nodeId, handleType, handleId, nodeLookup, connectionMode, withAbsolutePosition = false) {\n    const node = nodeLookup.get(nodeId);\n    if (!node) {\n        return null;\n    }\n    const handles = connectionMode === 'strict'\n        ? node.internals.handleBounds?.[handleType]\n        : [...(node.internals.handleBounds?.source ?? []), ...(node.internals.handleBounds?.target ?? [])];\n    const handle = (handleId ? handles?.find((h) => h.id === handleId) : handles?.[0]) ?? null;\n    return handle && withAbsolutePosition\n        ? { ...handle, ...getHandlePosition(node, handle, handle.position, true) }\n        : handle;\n}\nfunction getHandleType(edgeUpdaterType, handleDomNode) {\n    if (edgeUpdaterType) {\n        return edgeUpdaterType;\n    }\n    else if (handleDomNode?.classList.contains('target')) {\n        return 'target';\n    }\n    else if (handleDomNode?.classList.contains('source')) {\n        return 'source';\n    }\n    return null;\n}\nfunction isConnectionValid(isInsideConnectionRadius, isHandleValid) {\n    let isValid = null;\n    if (isHandleValid) {\n        isValid = true;\n    }\n    else if (isInsideConnectionRadius && !isHandleValid) {\n        isValid = false;\n    }\n    return isValid;\n}\n\nconst alwaysValid = () => true;\nfunction onPointerDown(event, { connectionMode, connectionRadius, handleId, nodeId, edgeUpdaterType, isTarget, domNode, nodeLookup, lib, autoPanOnConnect, flowId, panBy, cancelConnection, onConnectStart, onConnect, onConnectEnd, isValidConnection = alwaysValid, onReconnectEnd, updateConnection, getTransform, getFromHandle, autoPanSpeed, }) {\n    // when xyflow is used inside a shadow root we can't use document\n    const doc = getHostForElement(event.target);\n    let autoPanId = 0;\n    let closestHandle;\n    const { x, y } = getEventPosition(event);\n    const clickedHandle = doc?.elementFromPoint(x, y);\n    const handleType = getHandleType(edgeUpdaterType, clickedHandle);\n    const containerBounds = domNode?.getBoundingClientRect();\n    if (!containerBounds || !handleType) {\n        return;\n    }\n    const fromHandleInternal = getHandle(nodeId, handleType, handleId, nodeLookup, connectionMode);\n    if (!fromHandleInternal) {\n        return;\n    }\n    let position = getEventPosition(event, containerBounds);\n    let autoPanStarted = false;\n    let connection = null;\n    let isValid = false;\n    let handleDomNode = null;\n    // when the user is moving the mouse close to the edge of the canvas while connecting we move the canvas\n    function autoPan() {\n        if (!autoPanOnConnect || !containerBounds) {\n            return;\n        }\n        const [x, y] = calcAutoPan(position, containerBounds, autoPanSpeed);\n        panBy({ x, y });\n        autoPanId = requestAnimationFrame(autoPan);\n    }\n    // Stays the same for all consecutive pointermove events\n    const fromHandle = {\n        ...fromHandleInternal,\n        nodeId,\n        type: handleType,\n        position: fromHandleInternal.position,\n    };\n    const fromNodeInternal = nodeLookup.get(nodeId);\n    const from = getHandlePosition(fromNodeInternal, fromHandle, Position.Left, true);\n    const newConnection = {\n        inProgress: true,\n        isValid: null,\n        from,\n        fromHandle,\n        fromPosition: fromHandle.position,\n        fromNode: fromNodeInternal,\n        to: position,\n        toHandle: null,\n        toPosition: oppositePosition[fromHandle.position],\n        toNode: null,\n    };\n    updateConnection(newConnection);\n    let previousConnection = newConnection;\n    onConnectStart?.(event, { nodeId, handleId, handleType });\n    function onPointerMove(event) {\n        if (!getFromHandle() || !fromHandle) {\n            onPointerUp(event);\n            return;\n        }\n        const transform = getTransform();\n        position = getEventPosition(event, containerBounds);\n        closestHandle = getClosestHandle(pointToRendererPoint(position, transform, false, [1, 1]), connectionRadius, nodeLookup, fromHandle);\n        if (!autoPanStarted) {\n            autoPan();\n            autoPanStarted = true;\n        }\n        const result = isValidHandle(event, {\n            handle: closestHandle,\n            connectionMode,\n            fromNodeId: nodeId,\n            fromHandleId: handleId,\n            fromType: isTarget ? 'target' : 'source',\n            isValidConnection,\n            doc,\n            lib,\n            flowId,\n            nodeLookup,\n        });\n        handleDomNode = result.handleDomNode;\n        connection = result.connection;\n        isValid = isConnectionValid(!!closestHandle, result.isValid);\n        const newConnection = {\n            // from stays the same\n            ...previousConnection,\n            isValid,\n            to: closestHandle && isValid\n                ? rendererPointToPoint({ x: closestHandle.x, y: closestHandle.y }, transform)\n                : position,\n            toHandle: result.toHandle,\n            toPosition: isValid && result.toHandle ? result.toHandle.position : oppositePosition[fromHandle.position],\n            toNode: result.toHandle ? nodeLookup.get(result.toHandle.nodeId) : null,\n        };\n        /*\n         * we don't want to trigger an update when the connection\n         * is snapped to the same handle as before\n         */\n        if (isValid &&\n            closestHandle &&\n            previousConnection.toHandle &&\n            newConnection.toHandle &&\n            previousConnection.toHandle.type === newConnection.toHandle.type &&\n            previousConnection.toHandle.nodeId === newConnection.toHandle.nodeId &&\n            previousConnection.toHandle.id === newConnection.toHandle.id &&\n            previousConnection.to.x === newConnection.to.x &&\n            previousConnection.to.y === newConnection.to.y) {\n            return;\n        }\n        updateConnection(newConnection);\n        previousConnection = newConnection;\n    }\n    function onPointerUp(event) {\n        if ((closestHandle || handleDomNode) && connection && isValid) {\n            onConnect?.(connection);\n        }\n        /*\n         * it's important to get a fresh reference from the store here\n         * in order to get the latest state of onConnectEnd\n         */\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const { inProgress, ...connectionState } = previousConnection;\n        const finalConnectionState = {\n            ...connectionState,\n            toPosition: previousConnection.toHandle ? previousConnection.toPosition : null,\n        };\n        onConnectEnd?.(event, finalConnectionState);\n        if (edgeUpdaterType) {\n            onReconnectEnd?.(event, finalConnectionState);\n        }\n        cancelConnection();\n        cancelAnimationFrame(autoPanId);\n        autoPanStarted = false;\n        isValid = false;\n        connection = null;\n        handleDomNode = null;\n        doc.removeEventListener('mousemove', onPointerMove);\n        doc.removeEventListener('mouseup', onPointerUp);\n        doc.removeEventListener('touchmove', onPointerMove);\n        doc.removeEventListener('touchend', onPointerUp);\n    }\n    doc.addEventListener('mousemove', onPointerMove);\n    doc.addEventListener('mouseup', onPointerUp);\n    doc.addEventListener('touchmove', onPointerMove);\n    doc.addEventListener('touchend', onPointerUp);\n}\n// checks if  and returns connection in fom of an object { source: 123, target: 312 }\nfunction isValidHandle(event, { handle, connectionMode, fromNodeId, fromHandleId, fromType, doc, lib, flowId, isValidConnection = alwaysValid, nodeLookup, }) {\n    const isTarget = fromType === 'target';\n    const handleDomNode = handle\n        ? doc.querySelector(`.${lib}-flow__handle[data-id=\"${flowId}-${handle?.nodeId}-${handle?.id}-${handle?.type}\"]`)\n        : null;\n    const { x, y } = getEventPosition(event);\n    const handleBelow = doc.elementFromPoint(x, y);\n    /*\n     * we always want to prioritize the handle below the mouse cursor over the closest distance handle,\n     * because it could be that the center of another handle is closer to the mouse pointer than the handle below the cursor\n     */\n    const handleToCheck = handleBelow?.classList.contains(`${lib}-flow__handle`) ? handleBelow : handleDomNode;\n    const result = {\n        handleDomNode: handleToCheck,\n        isValid: false,\n        connection: null,\n        toHandle: null,\n    };\n    if (handleToCheck) {\n        const handleType = getHandleType(undefined, handleToCheck);\n        const handleNodeId = handleToCheck.getAttribute('data-nodeid');\n        const handleId = handleToCheck.getAttribute('data-handleid');\n        const connectable = handleToCheck.classList.contains('connectable');\n        const connectableEnd = handleToCheck.classList.contains('connectableend');\n        if (!handleNodeId || !handleType) {\n            return result;\n        }\n        const connection = {\n            source: isTarget ? handleNodeId : fromNodeId,\n            sourceHandle: isTarget ? handleId : fromHandleId,\n            target: isTarget ? fromNodeId : handleNodeId,\n            targetHandle: isTarget ? fromHandleId : handleId,\n        };\n        result.connection = connection;\n        const isConnectable = connectable && connectableEnd;\n        // in strict mode we don't allow target to target or source to source connections\n        const isValid = isConnectable &&\n            (connectionMode === ConnectionMode.Strict\n                ? (isTarget && handleType === 'source') || (!isTarget && handleType === 'target')\n                : handleNodeId !== fromNodeId || handleId !== fromHandleId);\n        result.isValid = isValid && isValidConnection(connection);\n        result.toHandle = getHandle(handleNodeId, handleType, handleId, nodeLookup, connectionMode, false);\n    }\n    return result;\n}\nconst XYHandle = {\n    onPointerDown,\n    isValid: isValidHandle,\n};\n\nfunction XYMinimap({ domNode, panZoom, getTransform, getViewScale }) {\n    const selection = select(domNode);\n    function update({ translateExtent, width, height, zoomStep = 10, pannable = true, zoomable = true, inversePan = false, }) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const zoomHandler = (event) => {\n            const transform = getTransform();\n            if (event.sourceEvent.type !== 'wheel' || !panZoom) {\n                return;\n            }\n            const pinchDelta = -event.sourceEvent.deltaY *\n                (event.sourceEvent.deltaMode === 1 ? 0.05 : event.sourceEvent.deltaMode ? 1 : 0.002) *\n                zoomStep;\n            const nextZoom = transform[2] * Math.pow(2, pinchDelta);\n            panZoom.scaleTo(nextZoom);\n        };\n        let panStart = [0, 0];\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const panStartHandler = (event) => {\n            if (event.sourceEvent.type === 'mousedown' || event.sourceEvent.type === 'touchstart') {\n                panStart = [\n                    event.sourceEvent.clientX ?? event.sourceEvent.touches[0].clientX,\n                    event.sourceEvent.clientY ?? event.sourceEvent.touches[0].clientY,\n                ];\n            }\n        };\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const panHandler = (event) => {\n            const transform = getTransform();\n            if ((event.sourceEvent.type !== 'mousemove' && event.sourceEvent.type !== 'touchmove') || !panZoom) {\n                return;\n            }\n            const panCurrent = [\n                event.sourceEvent.clientX ?? event.sourceEvent.touches[0].clientX,\n                event.sourceEvent.clientY ?? event.sourceEvent.touches[0].clientY,\n            ];\n            const panDelta = [panCurrent[0] - panStart[0], panCurrent[1] - panStart[1]];\n            panStart = panCurrent;\n            const moveScale = getViewScale() * Math.max(transform[2], Math.log(transform[2])) * (inversePan ? -1 : 1);\n            const position = {\n                x: transform[0] - panDelta[0] * moveScale,\n                y: transform[1] - panDelta[1] * moveScale,\n            };\n            const extent = [\n                [0, 0],\n                [width, height],\n            ];\n            panZoom.setViewportConstrained({\n                x: position.x,\n                y: position.y,\n                zoom: transform[2],\n            }, extent, translateExtent);\n        };\n        const zoomAndPanHandler = zoom()\n            .on('start', panStartHandler)\n            // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n            // @ts-ignore\n            .on('zoom', pannable ? panHandler : null)\n            // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n            // @ts-ignore\n            .on('zoom.wheel', zoomable ? zoomHandler : null);\n        selection.call(zoomAndPanHandler, {});\n    }\n    function destroy() {\n        selection.on('zoom', null);\n    }\n    return {\n        update,\n        destroy,\n        pointer,\n    };\n}\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\nconst viewChanged = (prevViewport, eventViewport) => prevViewport.x !== eventViewport.x || prevViewport.y !== eventViewport.y || prevViewport.zoom !== eventViewport.k;\nconst transformToViewport = (transform) => ({\n    x: transform.x,\n    y: transform.y,\n    zoom: transform.k,\n});\nconst viewportToTransform = ({ x, y, zoom }) => zoomIdentity.translate(x, y).scale(zoom);\nconst isWrappedWithClass = (event, className) => event.target.closest(`.${className}`);\nconst isRightClickPan = (panOnDrag, usedButton) => usedButton === 2 && Array.isArray(panOnDrag) && panOnDrag.includes(2);\nconst getD3Transition = (selection, duration = 0, onEnd = () => { }) => {\n    const hasDuration = typeof duration === 'number' && duration > 0;\n    if (!hasDuration) {\n        onEnd();\n    }\n    return hasDuration ? selection.transition().duration(duration).on('end', onEnd) : selection;\n};\nconst wheelDelta = (event) => {\n    const factor = event.ctrlKey && isMacOs() ? 10 : 1;\n    return -event.deltaY * (event.deltaMode === 1 ? 0.05 : event.deltaMode ? 1 : 0.002) * factor;\n};\n\nfunction createPanOnScrollHandler({ zoomPanValues, noWheelClassName, d3Selection, d3Zoom, panOnScrollMode, panOnScrollSpeed, zoomOnPinch, onPanZoomStart, onPanZoom, onPanZoomEnd, }) {\n    return (event) => {\n        if (isWrappedWithClass(event, noWheelClassName)) {\n            return false;\n        }\n        event.preventDefault();\n        event.stopImmediatePropagation();\n        const currentZoom = d3Selection.property('__zoom').k || 1;\n        // macos sets ctrlKey=true for pinch gesture on a trackpad\n        if (event.ctrlKey && zoomOnPinch) {\n            const point = pointer(event);\n            const pinchDelta = wheelDelta(event);\n            const zoom = currentZoom * Math.pow(2, pinchDelta);\n            // @ts-ignore\n            d3Zoom.scaleTo(d3Selection, zoom, point, event);\n            return;\n        }\n        /*\n         * increase scroll speed in firefox\n         * firefox: deltaMode === 1; chrome: deltaMode === 0\n         */\n        const deltaNormalize = event.deltaMode === 1 ? 20 : 1;\n        let deltaX = panOnScrollMode === PanOnScrollMode.Vertical ? 0 : event.deltaX * deltaNormalize;\n        let deltaY = panOnScrollMode === PanOnScrollMode.Horizontal ? 0 : event.deltaY * deltaNormalize;\n        // this enables vertical scrolling with shift + scroll on windows\n        if (!isMacOs() && event.shiftKey && panOnScrollMode !== PanOnScrollMode.Vertical) {\n            deltaX = event.deltaY * deltaNormalize;\n            deltaY = 0;\n        }\n        d3Zoom.translateBy(d3Selection, -(deltaX / currentZoom) * panOnScrollSpeed, -(deltaY / currentZoom) * panOnScrollSpeed, \n        // @ts-ignore\n        { internal: true });\n        const nextViewport = transformToViewport(d3Selection.property('__zoom'));\n        clearTimeout(zoomPanValues.panScrollTimeout);\n        /*\n         * for pan on scroll we need to handle the event calls on our own\n         * we can't use the start, zoom and end events from d3-zoom\n         * because start and move gets called on every scroll event and not once at the beginning\n         */\n        if (!zoomPanValues.isPanScrolling) {\n            zoomPanValues.isPanScrolling = true;\n            onPanZoomStart?.(event, nextViewport);\n        }\n        if (zoomPanValues.isPanScrolling) {\n            onPanZoom?.(event, nextViewport);\n            zoomPanValues.panScrollTimeout = setTimeout(() => {\n                onPanZoomEnd?.(event, nextViewport);\n                zoomPanValues.isPanScrolling = false;\n            }, 150);\n        }\n    };\n}\nfunction createZoomOnScrollHandler({ noWheelClassName, preventScrolling, d3ZoomHandler }) {\n    return function (event, d) {\n        const isWheel = event.type === 'wheel';\n        // we still want to enable pinch zooming even if preventScrolling is set to false\n        const preventZoom = !preventScrolling && isWheel && !event.ctrlKey;\n        const hasNoWheelClass = isWrappedWithClass(event, noWheelClassName);\n        // if user is pinch zooming above a nowheel element, we don't want the browser to zoom\n        if (event.ctrlKey && isWheel && hasNoWheelClass) {\n            event.preventDefault();\n        }\n        if (preventZoom || hasNoWheelClass) {\n            return null;\n        }\n        event.preventDefault();\n        d3ZoomHandler.call(this, event, d);\n    };\n}\nfunction createPanZoomStartHandler({ zoomPanValues, onDraggingChange, onPanZoomStart }) {\n    return (event) => {\n        if (event.sourceEvent?.internal) {\n            return;\n        }\n        const viewport = transformToViewport(event.transform);\n        // we need to remember it here, because it's always 0 in the \"zoom\" event\n        zoomPanValues.mouseButton = event.sourceEvent?.button || 0;\n        zoomPanValues.isZoomingOrPanning = true;\n        zoomPanValues.prevViewport = viewport;\n        if (event.sourceEvent?.type === 'mousedown') {\n            onDraggingChange(true);\n        }\n        if (onPanZoomStart) {\n            onPanZoomStart?.(event.sourceEvent, viewport);\n        }\n    };\n}\nfunction createPanZoomHandler({ zoomPanValues, panOnDrag, onPaneContextMenu, onTransformChange, onPanZoom, }) {\n    return (event) => {\n        zoomPanValues.usedRightMouseButton = !!(onPaneContextMenu && isRightClickPan(panOnDrag, zoomPanValues.mouseButton ?? 0));\n        if (!event.sourceEvent?.sync) {\n            onTransformChange([event.transform.x, event.transform.y, event.transform.k]);\n        }\n        if (onPanZoom && !event.sourceEvent?.internal) {\n            onPanZoom?.(event.sourceEvent, transformToViewport(event.transform));\n        }\n    };\n}\nfunction createPanZoomEndHandler({ zoomPanValues, panOnDrag, panOnScroll, onDraggingChange, onPanZoomEnd, onPaneContextMenu, }) {\n    return (event) => {\n        if (event.sourceEvent?.internal) {\n            return;\n        }\n        zoomPanValues.isZoomingOrPanning = false;\n        if (onPaneContextMenu &&\n            isRightClickPan(panOnDrag, zoomPanValues.mouseButton ?? 0) &&\n            !zoomPanValues.usedRightMouseButton &&\n            event.sourceEvent) {\n            onPaneContextMenu(event.sourceEvent);\n        }\n        zoomPanValues.usedRightMouseButton = false;\n        onDraggingChange(false);\n        if (onPanZoomEnd && viewChanged(zoomPanValues.prevViewport, event.transform)) {\n            const viewport = transformToViewport(event.transform);\n            zoomPanValues.prevViewport = viewport;\n            clearTimeout(zoomPanValues.timerId);\n            zoomPanValues.timerId = setTimeout(() => {\n                onPanZoomEnd?.(event.sourceEvent, viewport);\n            }, \n            // we need a setTimeout for panOnScroll to supress multiple end events fired during scroll\n            panOnScroll ? 150 : 0);\n        }\n    };\n}\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\nfunction createFilter({ zoomActivationKeyPressed, zoomOnScroll, zoomOnPinch, panOnDrag, panOnScroll, zoomOnDoubleClick, userSelectionActive, noWheelClassName, noPanClassName, lib, }) {\n    return (event) => {\n        const zoomScroll = zoomActivationKeyPressed || zoomOnScroll;\n        const pinchZoom = zoomOnPinch && event.ctrlKey;\n        if (event.button === 1 &&\n            event.type === 'mousedown' &&\n            (isWrappedWithClass(event, `${lib}-flow__node`) || isWrappedWithClass(event, `${lib}-flow__edge`))) {\n            return true;\n        }\n        // if all interactions are disabled, we prevent all zoom events\n        if (!panOnDrag && !zoomScroll && !panOnScroll && !zoomOnDoubleClick && !zoomOnPinch) {\n            return false;\n        }\n        // during a selection we prevent all other interactions\n        if (userSelectionActive) {\n            return false;\n        }\n        // if the target element is inside an element with the nowheel class, we prevent zooming\n        if (isWrappedWithClass(event, noWheelClassName) && event.type === 'wheel') {\n            return false;\n        }\n        // if the target element is inside an element with the nopan class, we prevent panning\n        if (isWrappedWithClass(event, noPanClassName) &&\n            (event.type !== 'wheel' || (panOnScroll && event.type === 'wheel' && !zoomActivationKeyPressed))) {\n            return false;\n        }\n        if (!zoomOnPinch && event.ctrlKey && event.type === 'wheel') {\n            return false;\n        }\n        if (!zoomOnPinch && event.type === 'touchstart' && event.touches?.length > 1) {\n            event.preventDefault(); // if you manage to start with 2 touches, we prevent native zoom\n            return false;\n        }\n        // when there is no scroll handling enabled, we prevent all wheel events\n        if (!zoomScroll && !panOnScroll && !pinchZoom && event.type === 'wheel') {\n            return false;\n        }\n        // if the pane is not movable, we prevent dragging it with mousestart or touchstart\n        if (!panOnDrag && (event.type === 'mousedown' || event.type === 'touchstart')) {\n            return false;\n        }\n        // if the pane is only movable using allowed clicks\n        if (Array.isArray(panOnDrag) && !panOnDrag.includes(event.button) && event.type === 'mousedown') {\n            return false;\n        }\n        // We only allow right clicks if pan on drag is set to right click\n        const buttonAllowed = (Array.isArray(panOnDrag) && panOnDrag.includes(event.button)) || !event.button || event.button <= 1;\n        // default filter for d3-zoom\n        return (!event.ctrlKey || event.type === 'wheel') && buttonAllowed;\n    };\n}\n\nfunction XYPanZoom({ domNode, minZoom, maxZoom, paneClickDistance, translateExtent, viewport, onPanZoom, onPanZoomStart, onPanZoomEnd, onDraggingChange, }) {\n    const zoomPanValues = {\n        isZoomingOrPanning: false,\n        usedRightMouseButton: false,\n        prevViewport: { x: 0, y: 0, zoom: 0 },\n        mouseButton: 0,\n        timerId: undefined,\n        panScrollTimeout: undefined,\n        isPanScrolling: false,\n    };\n    const bbox = domNode.getBoundingClientRect();\n    const d3ZoomInstance = zoom()\n        .clickDistance(!isNumeric(paneClickDistance) || paneClickDistance < 0 ? 0 : paneClickDistance)\n        .scaleExtent([minZoom, maxZoom])\n        .translateExtent(translateExtent);\n    const d3Selection = select(domNode).call(d3ZoomInstance);\n    setViewportConstrained({\n        x: viewport.x,\n        y: viewport.y,\n        zoom: clamp(viewport.zoom, minZoom, maxZoom),\n    }, [\n        [0, 0],\n        [bbox.width, bbox.height],\n    ], translateExtent);\n    const d3ZoomHandler = d3Selection.on('wheel.zoom');\n    const d3DblClickZoomHandler = d3Selection.on('dblclick.zoom');\n    d3ZoomInstance.wheelDelta(wheelDelta);\n    function setTransform(transform, options) {\n        if (d3Selection) {\n            return new Promise((resolve) => {\n                d3ZoomInstance?.transform(getD3Transition(d3Selection, options?.duration, () => resolve(true)), transform);\n            });\n        }\n        return Promise.resolve(false);\n    }\n    // public functions\n    function update({ noWheelClassName, noPanClassName, onPaneContextMenu, userSelectionActive, panOnScroll, panOnDrag, panOnScrollMode, panOnScrollSpeed, preventScrolling, zoomOnPinch, zoomOnScroll, zoomOnDoubleClick, zoomActivationKeyPressed, lib, onTransformChange, }) {\n        if (userSelectionActive && !zoomPanValues.isZoomingOrPanning) {\n            destroy();\n        }\n        const isPanOnScroll = panOnScroll && !zoomActivationKeyPressed && !userSelectionActive;\n        const wheelHandler = isPanOnScroll\n            ? createPanOnScrollHandler({\n                zoomPanValues,\n                noWheelClassName,\n                d3Selection,\n                d3Zoom: d3ZoomInstance,\n                panOnScrollMode,\n                panOnScrollSpeed,\n                zoomOnPinch,\n                onPanZoomStart,\n                onPanZoom,\n                onPanZoomEnd,\n            })\n            : createZoomOnScrollHandler({\n                noWheelClassName,\n                preventScrolling,\n                d3ZoomHandler,\n            });\n        d3Selection.on('wheel.zoom', wheelHandler, { passive: false });\n        if (!userSelectionActive) {\n            // pan zoom start\n            const startHandler = createPanZoomStartHandler({\n                zoomPanValues,\n                onDraggingChange,\n                onPanZoomStart,\n            });\n            d3ZoomInstance.on('start', startHandler);\n            // pan zoom\n            const panZoomHandler = createPanZoomHandler({\n                zoomPanValues,\n                panOnDrag,\n                onPaneContextMenu: !!onPaneContextMenu,\n                onPanZoom,\n                onTransformChange,\n            });\n            d3ZoomInstance.on('zoom', panZoomHandler);\n            // pan zoom end\n            const panZoomEndHandler = createPanZoomEndHandler({\n                zoomPanValues,\n                panOnDrag,\n                panOnScroll,\n                onPaneContextMenu,\n                onPanZoomEnd,\n                onDraggingChange,\n            });\n            d3ZoomInstance.on('end', panZoomEndHandler);\n        }\n        const filter = createFilter({\n            zoomActivationKeyPressed,\n            panOnDrag,\n            zoomOnScroll,\n            panOnScroll,\n            zoomOnDoubleClick,\n            zoomOnPinch,\n            userSelectionActive,\n            noPanClassName,\n            noWheelClassName,\n            lib,\n        });\n        d3ZoomInstance.filter(filter);\n        /*\n         * We cannot add zoomOnDoubleClick to the filter above because\n         * double tapping on touch screens circumvents the filter and\n         * dblclick.zoom is fired on the selection directly\n         */\n        if (zoomOnDoubleClick) {\n            d3Selection.on('dblclick.zoom', d3DblClickZoomHandler);\n        }\n        else {\n            d3Selection.on('dblclick.zoom', null);\n        }\n    }\n    function destroy() {\n        d3ZoomInstance.on('zoom', null);\n    }\n    async function setViewportConstrained(viewport, extent, translateExtent) {\n        const nextTransform = viewportToTransform(viewport);\n        const contrainedTransform = d3ZoomInstance?.constrain()(nextTransform, extent, translateExtent);\n        if (contrainedTransform) {\n            await setTransform(contrainedTransform);\n        }\n        return new Promise((resolve) => resolve(contrainedTransform));\n    }\n    async function setViewport(viewport, options) {\n        const nextTransform = viewportToTransform(viewport);\n        await setTransform(nextTransform, options);\n        return new Promise((resolve) => resolve(nextTransform));\n    }\n    function syncViewport(viewport) {\n        if (d3Selection) {\n            const nextTransform = viewportToTransform(viewport);\n            const currentTransform = d3Selection.property('__zoom');\n            if (currentTransform.k !== viewport.zoom ||\n                currentTransform.x !== viewport.x ||\n                currentTransform.y !== viewport.y) {\n                // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n                // @ts-ignore\n                d3ZoomInstance?.transform(d3Selection, nextTransform, null, { sync: true });\n            }\n        }\n    }\n    function getViewport() {\n        const transform = d3Selection ? zoomTransform(d3Selection.node()) : { x: 0, y: 0, k: 1 };\n        return { x: transform.x, y: transform.y, zoom: transform.k };\n    }\n    function scaleTo(zoom, options) {\n        if (d3Selection) {\n            return new Promise((resolve) => {\n                d3ZoomInstance?.scaleTo(getD3Transition(d3Selection, options?.duration, () => resolve(true)), zoom);\n            });\n        }\n        return Promise.resolve(false);\n    }\n    function scaleBy(factor, options) {\n        if (d3Selection) {\n            return new Promise((resolve) => {\n                d3ZoomInstance?.scaleBy(getD3Transition(d3Selection, options?.duration, () => resolve(true)), factor);\n            });\n        }\n        return Promise.resolve(false);\n    }\n    function setScaleExtent(scaleExtent) {\n        d3ZoomInstance?.scaleExtent(scaleExtent);\n    }\n    function setTranslateExtent(translateExtent) {\n        d3ZoomInstance?.translateExtent(translateExtent);\n    }\n    function setClickDistance(distance) {\n        const validDistance = !isNumeric(distance) || distance < 0 ? 0 : distance;\n        d3ZoomInstance?.clickDistance(validDistance);\n    }\n    return {\n        update,\n        destroy,\n        setViewport,\n        setViewportConstrained,\n        getViewport,\n        scaleTo,\n        scaleBy,\n        setScaleExtent,\n        setTranslateExtent,\n        syncViewport,\n        setClickDistance,\n    };\n}\n\n/**\n * Used to determine the variant of the resize control\n *\n * @public\n */\nvar ResizeControlVariant;\n(function (ResizeControlVariant) {\n    ResizeControlVariant[\"Line\"] = \"line\";\n    ResizeControlVariant[\"Handle\"] = \"handle\";\n})(ResizeControlVariant || (ResizeControlVariant = {}));\nconst XY_RESIZER_HANDLE_POSITIONS = ['top-left', 'top-right', 'bottom-left', 'bottom-right'];\nconst XY_RESIZER_LINE_POSITIONS = ['top', 'right', 'bottom', 'left'];\n\n/**\n * Get all connecting edges for a given set of nodes\n * @param width - new width of the node\n * @param prevWidth - previous width of the node\n * @param height - new height of the node\n * @param prevHeight - previous height of the node\n * @param affectsX - whether to invert the resize direction for the x axis\n * @param affectsY - whether to invert the resize direction for the y axis\n * @returns array of two numbers representing the direction of the resize for each axis, 0 = no change, 1 = increase, -1 = decrease\n */\nfunction getResizeDirection({ width, prevWidth, height, prevHeight, affectsX, affectsY, }) {\n    const deltaWidth = width - prevWidth;\n    const deltaHeight = height - prevHeight;\n    const direction = [deltaWidth > 0 ? 1 : deltaWidth < 0 ? -1 : 0, deltaHeight > 0 ? 1 : deltaHeight < 0 ? -1 : 0];\n    if (deltaWidth && affectsX) {\n        direction[0] = direction[0] * -1;\n    }\n    if (deltaHeight && affectsY) {\n        direction[1] = direction[1] * -1;\n    }\n    return direction;\n}\n/**\n * Parses the control position that is being dragged to dimensions that are being resized\n * @param controlPosition - position of the control that is being dragged\n * @returns isHorizontal, isVertical, affectsX, affectsY,\n */\nfunction getControlDirection(controlPosition) {\n    const isHorizontal = controlPosition.includes('right') || controlPosition.includes('left');\n    const isVertical = controlPosition.includes('bottom') || controlPosition.includes('top');\n    const affectsX = controlPosition.includes('left');\n    const affectsY = controlPosition.includes('top');\n    return {\n        isHorizontal,\n        isVertical,\n        affectsX,\n        affectsY,\n    };\n}\nfunction getLowerExtentClamp(lowerExtent, lowerBound) {\n    return Math.max(0, lowerBound - lowerExtent);\n}\nfunction getUpperExtentClamp(upperExtent, upperBound) {\n    return Math.max(0, upperExtent - upperBound);\n}\nfunction getSizeClamp(size, minSize, maxSize) {\n    return Math.max(0, minSize - size, size - maxSize);\n}\nfunction xor(a, b) {\n    return a ? !b : b;\n}\n/**\n * Calculates new width & height and x & y of node after resize based on pointer position\n * @description - Buckle up, this is a chunky one... If you want to determine the new dimensions of a node after a resize,\n * you have to account for all possible restrictions: min/max width/height of the node, the maximum extent the node is allowed\n * to move in (in this case: resize into) determined by the parent node, the minimal extent determined by child nodes\n * with expandParent or extent: 'parent' set and oh yeah, these things also have to work with keepAspectRatio!\n * The way this is done is by determining how much each of these restricting actually restricts the resize and then applying the\n * strongest restriction. Because the resize affects x, y and width, height and width, height of a opposing side with keepAspectRatio,\n * the resize amount is always kept in distX & distY amount (the distance in mouse movement)\n * Instead of clamping each value, we first calculate the biggest 'clamp' (for the lack of a better name) and then apply it to all values.\n * To complicate things nodeOrigin has to be taken into account as well. This is done by offsetting the nodes as if their origin is [0, 0],\n * then calculating the restrictions as usual\n * @param startValues - starting values of resize\n * @param controlDirection - dimensions affected by the resize\n * @param pointerPosition - the current pointer position corrected for snapping\n * @param boundaries - minimum and maximum dimensions of the node\n * @param keepAspectRatio - prevent changes of asprect ratio\n * @returns x, y, width and height of the node after resize\n */\nfunction getDimensionsAfterResize(startValues, controlDirection, pointerPosition, boundaries, keepAspectRatio, nodeOrigin, extent, childExtent) {\n    let { affectsX, affectsY } = controlDirection;\n    const { isHorizontal, isVertical } = controlDirection;\n    const isDiagonal = isHorizontal && isVertical;\n    const { xSnapped, ySnapped } = pointerPosition;\n    const { minWidth, maxWidth, minHeight, maxHeight } = boundaries;\n    const { x: startX, y: startY, width: startWidth, height: startHeight, aspectRatio } = startValues;\n    let distX = Math.floor(isHorizontal ? xSnapped - startValues.pointerX : 0);\n    let distY = Math.floor(isVertical ? ySnapped - startValues.pointerY : 0);\n    const newWidth = startWidth + (affectsX ? -distX : distX);\n    const newHeight = startHeight + (affectsY ? -distY : distY);\n    const originOffsetX = -nodeOrigin[0] * startWidth;\n    const originOffsetY = -nodeOrigin[1] * startHeight;\n    // Check if maxWidth, minWWidth, maxHeight, minHeight are restricting the resize\n    let clampX = getSizeClamp(newWidth, minWidth, maxWidth);\n    let clampY = getSizeClamp(newHeight, minHeight, maxHeight);\n    // Check if extent is restricting the resize\n    if (extent) {\n        let xExtentClamp = 0;\n        let yExtentClamp = 0;\n        if (affectsX && distX < 0) {\n            xExtentClamp = getLowerExtentClamp(startX + distX + originOffsetX, extent[0][0]);\n        }\n        else if (!affectsX && distX > 0) {\n            xExtentClamp = getUpperExtentClamp(startX + newWidth + originOffsetX, extent[1][0]);\n        }\n        if (affectsY && distY < 0) {\n            yExtentClamp = getLowerExtentClamp(startY + distY + originOffsetY, extent[0][1]);\n        }\n        else if (!affectsY && distY > 0) {\n            yExtentClamp = getUpperExtentClamp(startY + newHeight + originOffsetY, extent[1][1]);\n        }\n        clampX = Math.max(clampX, xExtentClamp);\n        clampY = Math.max(clampY, yExtentClamp);\n    }\n    // Check if the child extent is restricting the resize\n    if (childExtent) {\n        let xExtentClamp = 0;\n        let yExtentClamp = 0;\n        if (affectsX && distX > 0) {\n            xExtentClamp = getUpperExtentClamp(startX + distX, childExtent[0][0]);\n        }\n        else if (!affectsX && distX < 0) {\n            xExtentClamp = getLowerExtentClamp(startX + newWidth, childExtent[1][0]);\n        }\n        if (affectsY && distY > 0) {\n            yExtentClamp = getUpperExtentClamp(startY + distY, childExtent[0][1]);\n        }\n        else if (!affectsY && distY < 0) {\n            yExtentClamp = getLowerExtentClamp(startY + newHeight, childExtent[1][1]);\n        }\n        clampX = Math.max(clampX, xExtentClamp);\n        clampY = Math.max(clampY, yExtentClamp);\n    }\n    // Check if the aspect ratio resizing of the other side is restricting the resize\n    if (keepAspectRatio) {\n        if (isHorizontal) {\n            // Check if the max dimensions might be restricting the resize\n            const aspectHeightClamp = getSizeClamp(newWidth / aspectRatio, minHeight, maxHeight) * aspectRatio;\n            clampX = Math.max(clampX, aspectHeightClamp);\n            // Check if the extent is restricting the resize\n            if (extent) {\n                let aspectExtentClamp = 0;\n                if ((!affectsX && !affectsY) || (affectsX && !affectsY && isDiagonal)) {\n                    aspectExtentClamp =\n                        getUpperExtentClamp(startY + originOffsetY + newWidth / aspectRatio, extent[1][1]) * aspectRatio;\n                }\n                else {\n                    aspectExtentClamp =\n                        getLowerExtentClamp(startY + originOffsetY + (affectsX ? distX : -distX) / aspectRatio, extent[0][1]) *\n                            aspectRatio;\n                }\n                clampX = Math.max(clampX, aspectExtentClamp);\n            }\n            // Check if the child extent is restricting the resize\n            if (childExtent) {\n                let aspectExtentClamp = 0;\n                if ((!affectsX && !affectsY) || (affectsX && !affectsY && isDiagonal)) {\n                    aspectExtentClamp = getLowerExtentClamp(startY + newWidth / aspectRatio, childExtent[1][1]) * aspectRatio;\n                }\n                else {\n                    aspectExtentClamp =\n                        getUpperExtentClamp(startY + (affectsX ? distX : -distX) / aspectRatio, childExtent[0][1]) * aspectRatio;\n                }\n                clampX = Math.max(clampX, aspectExtentClamp);\n            }\n        }\n        // Do the same thing for vertical resizing\n        if (isVertical) {\n            const aspectWidthClamp = getSizeClamp(newHeight * aspectRatio, minWidth, maxWidth) / aspectRatio;\n            clampY = Math.max(clampY, aspectWidthClamp);\n            if (extent) {\n                let aspectExtentClamp = 0;\n                if ((!affectsX && !affectsY) || (affectsY && !affectsX && isDiagonal)) {\n                    aspectExtentClamp =\n                        getUpperExtentClamp(startX + newHeight * aspectRatio + originOffsetX, extent[1][0]) / aspectRatio;\n                }\n                else {\n                    aspectExtentClamp =\n                        getLowerExtentClamp(startX + (affectsY ? distY : -distY) * aspectRatio + originOffsetX, extent[0][0]) /\n                            aspectRatio;\n                }\n                clampY = Math.max(clampY, aspectExtentClamp);\n            }\n            if (childExtent) {\n                let aspectExtentClamp = 0;\n                if ((!affectsX && !affectsY) || (affectsY && !affectsX && isDiagonal)) {\n                    aspectExtentClamp = getLowerExtentClamp(startX + newHeight * aspectRatio, childExtent[1][0]) / aspectRatio;\n                }\n                else {\n                    aspectExtentClamp =\n                        getUpperExtentClamp(startX + (affectsY ? distY : -distY) * aspectRatio, childExtent[0][0]) / aspectRatio;\n                }\n                clampY = Math.max(clampY, aspectExtentClamp);\n            }\n        }\n    }\n    distY = distY + (distY < 0 ? clampY : -clampY);\n    distX = distX + (distX < 0 ? clampX : -clampX);\n    if (keepAspectRatio) {\n        if (isDiagonal) {\n            if (newWidth > newHeight * aspectRatio) {\n                distY = (xor(affectsX, affectsY) ? -distX : distX) / aspectRatio;\n            }\n            else {\n                distX = (xor(affectsX, affectsY) ? -distY : distY) * aspectRatio;\n            }\n        }\n        else {\n            if (isHorizontal) {\n                distY = distX / aspectRatio;\n                affectsY = affectsX;\n            }\n            else {\n                distX = distY * aspectRatio;\n                affectsX = affectsY;\n            }\n        }\n    }\n    const x = affectsX ? startX + distX : startX;\n    const y = affectsY ? startY + distY : startY;\n    return {\n        width: startWidth + (affectsX ? -distX : distX),\n        height: startHeight + (affectsY ? -distY : distY),\n        x: nodeOrigin[0] * distX * (!affectsX ? 1 : -1) + x,\n        y: nodeOrigin[1] * distY * (!affectsY ? 1 : -1) + y,\n    };\n}\n\nconst initPrevValues = { width: 0, height: 0, x: 0, y: 0 };\nconst initStartValues = {\n    ...initPrevValues,\n    pointerX: 0,\n    pointerY: 0,\n    aspectRatio: 1,\n};\nfunction nodeToParentExtent(node) {\n    return [\n        [0, 0],\n        [node.measured.width, node.measured.height],\n    ];\n}\nfunction nodeToChildExtent(child, parent, nodeOrigin) {\n    const x = parent.position.x + child.position.x;\n    const y = parent.position.y + child.position.y;\n    const width = child.measured.width ?? 0;\n    const height = child.measured.height ?? 0;\n    const originOffsetX = nodeOrigin[0] * width;\n    const originOffsetY = nodeOrigin[1] * height;\n    return [\n        [x - originOffsetX, y - originOffsetY],\n        [x + width - originOffsetX, y + height - originOffsetY],\n    ];\n}\nfunction XYResizer({ domNode, nodeId, getStoreItems, onChange, onEnd }) {\n    const selection = select(domNode);\n    function update({ controlPosition, boundaries, keepAspectRatio, resizeDirection, onResizeStart, onResize, onResizeEnd, shouldResize, }) {\n        let prevValues = { ...initPrevValues };\n        let startValues = { ...initStartValues };\n        const controlDirection = getControlDirection(controlPosition);\n        let node = undefined;\n        let containerBounds = null;\n        let childNodes = [];\n        let parentNode = undefined; // Needed to fix expandParent\n        let parentExtent = undefined;\n        let childExtent = undefined;\n        const dragHandler = drag()\n            .on('start', (event) => {\n            const { nodeLookup, transform, snapGrid, snapToGrid, nodeOrigin, paneDomNode } = getStoreItems();\n            node = nodeLookup.get(nodeId);\n            if (!node) {\n                return;\n            }\n            containerBounds = paneDomNode?.getBoundingClientRect() ?? null;\n            const { xSnapped, ySnapped } = getPointerPosition(event.sourceEvent, {\n                transform,\n                snapGrid,\n                snapToGrid,\n                containerBounds,\n            });\n            prevValues = {\n                width: node.measured.width ?? 0,\n                height: node.measured.height ?? 0,\n                x: node.position.x ?? 0,\n                y: node.position.y ?? 0,\n            };\n            startValues = {\n                ...prevValues,\n                pointerX: xSnapped,\n                pointerY: ySnapped,\n                aspectRatio: prevValues.width / prevValues.height,\n            };\n            parentNode = undefined;\n            if (node.parentId && (node.extent === 'parent' || node.expandParent)) {\n                parentNode = nodeLookup.get(node.parentId);\n                parentExtent = parentNode && node.extent === 'parent' ? nodeToParentExtent(parentNode) : undefined;\n            }\n            /*\n             * Collect all child nodes to correct their relative positions when top/left changes\n             * Determine largest minimal extent the parent node is allowed to resize to\n             */\n            childNodes = [];\n            childExtent = undefined;\n            for (const [childId, child] of nodeLookup) {\n                if (child.parentId === nodeId) {\n                    childNodes.push({\n                        id: childId,\n                        position: { ...child.position },\n                        extent: child.extent,\n                    });\n                    if (child.extent === 'parent' || child.expandParent) {\n                        const extent = nodeToChildExtent(child, node, child.origin ?? nodeOrigin);\n                        if (childExtent) {\n                            childExtent = [\n                                [Math.min(extent[0][0], childExtent[0][0]), Math.min(extent[0][1], childExtent[0][1])],\n                                [Math.max(extent[1][0], childExtent[1][0]), Math.max(extent[1][1], childExtent[1][1])],\n                            ];\n                        }\n                        else {\n                            childExtent = extent;\n                        }\n                    }\n                }\n            }\n            onResizeStart?.(event, { ...prevValues });\n        })\n            .on('drag', (event) => {\n            const { transform, snapGrid, snapToGrid, nodeOrigin: storeNodeOrigin } = getStoreItems();\n            const pointerPosition = getPointerPosition(event.sourceEvent, {\n                transform,\n                snapGrid,\n                snapToGrid,\n                containerBounds,\n            });\n            const childChanges = [];\n            if (!node) {\n                return;\n            }\n            const { x: prevX, y: prevY, width: prevWidth, height: prevHeight } = prevValues;\n            const change = {};\n            const nodeOrigin = node.origin ?? storeNodeOrigin;\n            const { width, height, x, y } = getDimensionsAfterResize(startValues, controlDirection, pointerPosition, boundaries, keepAspectRatio, nodeOrigin, parentExtent, childExtent);\n            const isWidthChange = width !== prevWidth;\n            const isHeightChange = height !== prevHeight;\n            const isXPosChange = x !== prevX && isWidthChange;\n            const isYPosChange = y !== prevY && isHeightChange;\n            if (!isXPosChange && !isYPosChange && !isWidthChange && !isHeightChange) {\n                return;\n            }\n            if (isXPosChange || isYPosChange || nodeOrigin[0] === 1 || nodeOrigin[1] === 1) {\n                change.x = isXPosChange ? x : prevValues.x;\n                change.y = isYPosChange ? y : prevValues.y;\n                prevValues.x = change.x;\n                prevValues.y = change.y;\n                /*\n                 * when top/left changes, correct the relative positions of child nodes\n                 * so that they stay in the same position\n                 */\n                if (childNodes.length > 0) {\n                    const xChange = x - prevX;\n                    const yChange = y - prevY;\n                    for (const childNode of childNodes) {\n                        childNode.position = {\n                            x: childNode.position.x - xChange + nodeOrigin[0] * (width - prevWidth),\n                            y: childNode.position.y - yChange + nodeOrigin[1] * (height - prevHeight),\n                        };\n                        childChanges.push(childNode);\n                    }\n                }\n            }\n            if (isWidthChange || isHeightChange) {\n                change.width =\n                    isWidthChange && (!resizeDirection || resizeDirection === 'horizontal') ? width : prevValues.width;\n                change.height =\n                    isHeightChange && (!resizeDirection || resizeDirection === 'vertical') ? height : prevValues.height;\n                prevValues.width = change.width;\n                prevValues.height = change.height;\n            }\n            // Fix expandParent when resizing from top/left\n            if (parentNode && node.expandParent) {\n                const xLimit = nodeOrigin[0] * (change.width ?? 0);\n                if (change.x && change.x < xLimit) {\n                    prevValues.x = xLimit;\n                    startValues.x = startValues.x - (change.x - xLimit);\n                }\n                const yLimit = nodeOrigin[1] * (change.height ?? 0);\n                if (change.y && change.y < yLimit) {\n                    prevValues.y = yLimit;\n                    startValues.y = startValues.y - (change.y - yLimit);\n                }\n            }\n            const direction = getResizeDirection({\n                width: prevValues.width,\n                prevWidth,\n                height: prevValues.height,\n                prevHeight,\n                affectsX: controlDirection.affectsX,\n                affectsY: controlDirection.affectsY,\n            });\n            const nextValues = { ...prevValues, direction };\n            const callResize = shouldResize?.(event, nextValues);\n            if (callResize === false) {\n                return;\n            }\n            onResize?.(event, nextValues);\n            onChange(change, childChanges);\n        })\n            .on('end', (event) => {\n            onResizeEnd?.(event, { ...prevValues });\n            onEnd?.({ ...prevValues });\n        });\n        selection.call(dragHandler);\n    }\n    function destroy() {\n        selection.on('.drag', null);\n    }\n    return {\n        update,\n        destroy,\n    };\n}\n\nexport { ConnectionLineType, ConnectionMode, MarkerType, PanOnScrollMode, Position, ResizeControlVariant, SelectionMode, XYDrag, XYHandle, XYMinimap, XYPanZoom, XYResizer, XY_RESIZER_HANDLE_POSITIONS, XY_RESIZER_LINE_POSITIONS, addEdge, adoptUserNodes, areConnectionMapsEqual, areSetsEqual, boxToRect, calcAutoPan, calculateNodePosition, clamp, clampPosition, clampPositionToParent, createMarkerIds, devWarn, elementSelectionKeys, errorMessages, evaluateAbsolutePosition, fitViewport, getBezierEdgeCenter, getBezierPath, getBoundsOfBoxes, getBoundsOfRects, getConnectedEdges, getConnectionStatus, getDimensions, getEdgeCenter, getEdgePosition, getElementsToRemove, getElevatedEdgeZIndex, getEventPosition, getHandleBounds, getHandlePosition, getHostForElement, getIncomers, getInternalNodesBounds, getMarkerId, getNodeDimensions, getNodePositionWithOrigin, getNodeToolbarTransform, getNodesBounds, getNodesInside, getOutgoers, getOverlappingArea, getPointerPosition, getSmoothStepPath, getStraightPath, getViewportForBounds, handleConnectionChange, handleExpandParent, infiniteExtent, initialConnection, isCoordinateExtent, isEdgeBase, isEdgeVisible, isInputDOMNode, isInternalNodeBase, isMacOs, isMouseEvent, isNodeBase, isNumeric, isRectObject, nodeHasDimensions, nodeToBox, nodeToRect, oppositePosition, panBy, pointToRendererPoint, reconnectEdge, rectToBox, rendererPointToPoint, shallowNodeData, snapPosition, updateAbsolutePositions, updateConnectionLookup, updateNodeInternals, withResolvers };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2SQ;AA3SR;AACA;AAAA;AACA;AAAA;AAAA;AAAA;;;;AAEA,MAAM,gBAAgB;IAClB,UAAU,IAAM;IAChB,UAAU,IAAM;IAChB,UAAU,CAAC,WAAa,CAAC,WAAW,EAAE,SAAS,2CAA2C,CAAC;IAC3F,UAAU,IAAM;IAChB,UAAU,IAAM;IAChB,UAAU,IAAM;IAChB,UAAU,CAAC,KAAO,CAAC,qBAAqB,EAAE,GAAG,gBAAgB,CAAC;IAC9D,UAAU,CAAC,OAAS,CAAC,aAAa,EAAE,KAAK,gBAAgB,CAAC;IAC1D,UAAU,CAAC,YAAY,EAAE,EAAE,EAAE,YAAY,EAAE,YAAY,EAAE,GAAK,CAAC,yBAAyB,EAAE,WAAW,aAAa,EAAE,eAAe,WAAW,eAAe,aAAa,YAAY,EAAE,GAAG,CAAC,CAAC;IAC7L,UAAU,IAAM;IAChB,UAAU,CAAC,WAAa,CAAC,WAAW,EAAE,SAAS,2CAA2C,CAAC;IAC3F,UAAU,CAAC,KAAO,CAAC,cAAc,EAAE,GAAG,8HAA8H,CAAC;IACrK,UAAU,CAAC,MAAM,OAAO,GAAK,CAAC,oEAAoE,EAAE,IAAI,yEAAyE,CAAC;IAClL,UAAU,IAAM;IAChB,UAAU,IAAM;AACpB;AACA,MAAM,iBAAiB;IACnB;QAAC,OAAO,iBAAiB;QAAE,OAAO,iBAAiB;KAAC;IACpD;QAAC,OAAO,iBAAiB;QAAE,OAAO,iBAAiB;KAAC;CACvD;AACD,MAAM,uBAAuB;IAAC;IAAS;IAAK;CAAS;AAErD;;;;;;CAMC,GACD,IAAI;AACJ,CAAC,SAAU,cAAc;IACrB,cAAc,CAAC,SAAS,GAAG;IAC3B,cAAc,CAAC,QAAQ,GAAG;AAC9B,CAAC,EAAE,kBAAkB,CAAC,iBAAiB,CAAC,CAAC;AACzC;;;;;;;CAOC,GACD,IAAI;AACJ,CAAC,SAAU,eAAe;IACtB,eAAe,CAAC,OAAO,GAAG;IAC1B,eAAe,CAAC,WAAW,GAAG;IAC9B,eAAe,CAAC,aAAa,GAAG;AACpC,CAAC,EAAE,mBAAmB,CAAC,kBAAkB,CAAC,CAAC;AAC3C,IAAI;AACJ,CAAC,SAAU,aAAa;IACpB,aAAa,CAAC,UAAU,GAAG;IAC3B,aAAa,CAAC,OAAO,GAAG;AAC5B,CAAC,EAAE,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;AACvC,MAAM,oBAAoB;IACtB,YAAY;IACZ,SAAS;IACT,MAAM;IACN,YAAY;IACZ,cAAc;IACd,UAAU;IACV,IAAI;IACJ,UAAU;IACV,YAAY;IACZ,QAAQ;AACZ;AAEA;;;;;;;;;CASC,GACD,IAAI;AACJ,CAAC,SAAU,kBAAkB;IACzB,kBAAkB,CAAC,SAAS,GAAG;IAC/B,kBAAkB,CAAC,WAAW,GAAG;IACjC,kBAAkB,CAAC,OAAO,GAAG;IAC7B,kBAAkB,CAAC,aAAa,GAAG;IACnC,kBAAkB,CAAC,eAAe,GAAG;AACzC,CAAC,EAAE,sBAAsB,CAAC,qBAAqB,CAAC,CAAC;AACjD;;;;;CAKC,GACD,IAAI;AACJ,CAAC,SAAU,UAAU;IACjB,UAAU,CAAC,QAAQ,GAAG;IACtB,UAAU,CAAC,cAAc,GAAG;AAChC,CAAC,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC;AAEjC;;;;;;CAMC,GACD,IAAI;AACJ,CAAC,SAAU,QAAQ;IACf,QAAQ,CAAC,OAAO,GAAG;IACnB,QAAQ,CAAC,MAAM,GAAG;IAClB,QAAQ,CAAC,QAAQ,GAAG;IACpB,QAAQ,CAAC,SAAS,GAAG;AACzB,CAAC,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC;AAC7B,MAAM,mBAAmB;IACrB,CAAC,SAAS,IAAI,CAAC,EAAE,SAAS,KAAK;IAC/B,CAAC,SAAS,KAAK,CAAC,EAAE,SAAS,IAAI;IAC/B,CAAC,SAAS,GAAG,CAAC,EAAE,SAAS,MAAM;IAC/B,CAAC,SAAS,MAAM,CAAC,EAAE,SAAS,GAAG;AACnC;AAEA;;CAEC,GACD,SAAS,uBAAuB,CAAC,EAAE,CAAC;IAChC,IAAI,CAAC,KAAK,CAAC,GAAG;QACV,OAAO;IACX;IACA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,KAAK,EAAE,IAAI,EAAE;QAC/B,OAAO;IACX;IACA,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,IAAI,EAAE;QACpB,OAAO;IACX;IACA,KAAK,MAAM,OAAO,EAAE,IAAI,GAAI;QACxB,IAAI,CAAC,EAAE,GAAG,CAAC,MAAM;YACb,OAAO;QACX;IACJ;IACA,OAAO;AACX;AACA;;;;CAIC,GACD,SAAS,uBAAuB,CAAC,EAAE,CAAC,EAAE,EAAE;IACpC,IAAI,CAAC,IAAI;QACL;IACJ;IACA,MAAM,OAAO,EAAE;IACf,EAAE,OAAO,CAAC,CAAC,YAAY;QACnB,IAAI,CAAC,GAAG,IAAI,MAAM;YACd,KAAK,IAAI,CAAC;QACd;IACJ;IACA,IAAI,KAAK,MAAM,EAAE;QACb,GAAG;IACP;AACJ;AACA,SAAS,oBAAoB,OAAO;IAChC,OAAO,YAAY,OAAO,OAAO,UAAU,UAAU;AACzD;AAEA,qDAAqD,GACrD;;;;;;CAMC,GACD,MAAM,aAAa,CAAC,UAAY,QAAQ,WAAW,YAAY,WAAW,YAAY;AACtF;;;;;;CAMC,GACD,MAAM,aAAa,CAAC,UAAY,QAAQ,WAAW,cAAc,WAAW,CAAC,CAAC,YAAY,OAAO,KAAK,CAAC,CAAC,YAAY,OAAO;AAC3H,MAAM,qBAAqB,CAAC,UAAY,QAAQ,WAAW,eAAe,WAAW,CAAC,CAAC,YAAY,OAAO,KAAK,CAAC,CAAC,YAAY,OAAO;AACpI;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,MAAM,cAAc,CAAC,MAAM,OAAO;IAC9B,IAAI,CAAC,KAAK,EAAE,EAAE;QACV,OAAO,EAAE;IACb;IACA,MAAM,aAAa,IAAI;IACvB,MAAM,OAAO,CAAC,CAAC;QACX,IAAI,KAAK,MAAM,KAAK,KAAK,EAAE,EAAE;YACzB,WAAW,GAAG,CAAC,KAAK,MAAM;QAC9B;IACJ;IACA,OAAO,MAAM,MAAM,CAAC,CAAC,IAAM,WAAW,GAAG,CAAC,EAAE,EAAE;AAClD;AACA;;;;;;;;;;;;;;;;;;;;;;CAsBC,GACD,MAAM,cAAc,CAAC,MAAM,OAAO;IAC9B,IAAI,CAAC,KAAK,EAAE,EAAE;QACV,OAAO,EAAE;IACb;IACA,MAAM,cAAc,IAAI;IACxB,MAAM,OAAO,CAAC,CAAC;QACX,IAAI,KAAK,MAAM,KAAK,KAAK,EAAE,EAAE;YACzB,YAAY,GAAG,CAAC,KAAK,MAAM;QAC/B;IACJ;IACA,OAAO,MAAM,MAAM,CAAC,CAAC,IAAM,YAAY,GAAG,CAAC,EAAE,EAAE;AACnD;AACA,MAAM,4BAA4B,CAAC,MAAM,aAAa;IAAC;IAAG;CAAE;IACxD,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,kBAAkB;IAC5C,MAAM,SAAS,KAAK,MAAM,IAAI;IAC9B,MAAM,UAAU,QAAQ,MAAM,CAAC,EAAE;IACjC,MAAM,UAAU,SAAS,MAAM,CAAC,EAAE;IAClC,OAAO;QACH,GAAG,KAAK,QAAQ,CAAC,CAAC,GAAG;QACrB,GAAG,KAAK,QAAQ,CAAC,CAAC,GAAG;IACzB;AACJ;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAkCC,GACD,MAAM,iBAAiB,CAAC,OAAO,SAAS;IAAE,YAAY;QAAC;QAAG;KAAE;AAAC,CAAC;IAC1D,IAAI,oDAAyB,iBAAiB,CAAC,OAAO,UAAU,EAAE;QAC9D,QAAQ,IAAI,CAAC;IACjB;IACA,IAAI,MAAM,MAAM,KAAK,GAAG;QACpB,OAAO;YAAE,GAAG;YAAG,GAAG;YAAG,OAAO;YAAG,QAAQ;QAAE;IAC7C;IACA,MAAM,MAAM,MAAM,MAAM,CAAC,CAAC,SAAS;QAC/B,MAAM,OAAO,OAAO,aAAa;QACjC,IAAI,cAAc,CAAC,OAAO,UAAU,IAAI,CAAC,OAAO,WAAW;QAC3D,IAAI,OAAO,UAAU,EAAE;YACnB,cAAc,OACR,OAAO,UAAU,CAAC,GAAG,CAAC,YACtB,CAAC,mBAAmB,YAChB,OAAO,UAAU,CAAC,GAAG,CAAC,SAAS,EAAE,IACjC;QACd;QACA,MAAM,UAAU,cAAc,UAAU,aAAa,OAAO,UAAU,IAAI;YAAE,GAAG;YAAG,GAAG;YAAG,IAAI;YAAG,IAAI;QAAE;QACrG,OAAO,iBAAiB,SAAS;IACrC,GAAG;QAAE,GAAG;QAAU,GAAG;QAAU,IAAI,CAAC;QAAU,IAAI,CAAC;IAAS;IAC5D,OAAO,UAAU;AACrB;AACA;;;CAGC,GACD,MAAM,yBAAyB,CAAC,YAAY,SAAS,CAAC,CAAC;IACnD,IAAI,WAAW,IAAI,KAAK,GAAG;QACvB,OAAO;YAAE,GAAG;YAAG,GAAG;YAAG,OAAO;YAAG,QAAQ;QAAE;IAC7C;IACA,IAAI,MAAM;QAAE,GAAG;QAAU,GAAG;QAAU,IAAI,CAAC;QAAU,IAAI,CAAC;IAAS;IACnE,WAAW,OAAO,CAAC,CAAC;QAChB,IAAI,OAAO,MAAM,KAAK,aAAa,OAAO,MAAM,CAAC,OAAO;YACpD,MAAM,UAAU,UAAU;YAC1B,MAAM,iBAAiB,KAAK;QAChC;IACJ;IACA,OAAO,UAAU;AACrB;AACA,MAAM,iBAAiB,CAAC,OAAO,MAAM,CAAC,IAAI,IAAI,OAAO,GAAG;IAAC;IAAG;IAAG;CAAE,EAAE,YAAY,KAAK,EACpF,iGAAiG;AACjG,4BAA4B,KAAK;IAC7B,MAAM,WAAW;QACb,GAAG,qBAAqB,MAAM;YAAC;YAAI;YAAI;SAAO,CAAC;QAC/C,OAAO,KAAK,KAAK,GAAG;QACpB,QAAQ,KAAK,MAAM,GAAG;IAC1B;IACA,MAAM,eAAe,EAAE;IACvB,KAAK,MAAM,QAAQ,MAAM,MAAM,GAAI;QAC/B,MAAM,EAAE,QAAQ,EAAE,aAAa,IAAI,EAAE,SAAS,KAAK,EAAE,GAAG;QACxD,IAAI,AAAC,6BAA6B,CAAC,cAAe,QAAQ;YACtD;QACJ;QACA,MAAM,QAAQ,SAAS,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,YAAY,IAAI;QACnE,MAAM,SAAS,SAAS,MAAM,IAAI,KAAK,MAAM,IAAI,KAAK,aAAa,IAAI;QACvE,MAAM,kBAAkB,mBAAmB,UAAU,WAAW;QAChE,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;QACxC,MAAM,mBAAmB,aAAa,kBAAkB;QACxD,MAAM,qBAAqB,CAAC,KAAK,SAAS,CAAC,YAAY;QACvD,MAAM,YAAY,sBAAsB,oBAAoB,mBAAmB;QAC/E,IAAI,aAAa,KAAK,QAAQ,EAAE;YAC5B,aAAa,IAAI,CAAC;QACtB;IACJ;IACA,OAAO;AACX;AACA;;;;;;;;;;;;;;;;;;;;;;;;;CAyBC,GACD,MAAM,oBAAoB,CAAC,OAAO;IAC9B,MAAM,UAAU,IAAI;IACpB,MAAM,OAAO,CAAC,CAAC;QACX,QAAQ,GAAG,CAAC,KAAK,EAAE;IACvB;IACA,OAAO,MAAM,MAAM,CAAC,CAAC,OAAS,QAAQ,GAAG,CAAC,KAAK,MAAM,KAAK,QAAQ,GAAG,CAAC,KAAK,MAAM;AACrF;AACA,SAAS,gBAAgB,UAAU,EAAE,OAAO;IACxC,MAAM,eAAe,IAAI;IACzB,MAAM,gBAAgB,SAAS,QAAQ,IAAI,IAAI,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;IACvF,WAAW,OAAO,CAAC,CAAC;QAChB,MAAM,YAAY,EAAE,QAAQ,CAAC,KAAK,IAAI,EAAE,QAAQ,CAAC,MAAM,IAAI,CAAC,SAAS,sBAAsB,CAAC,EAAE,MAAM;QACpG,IAAI,aAAa,CAAC,CAAC,iBAAiB,cAAc,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG;YAC1D,aAAa,GAAG,CAAC,EAAE,EAAE,EAAE;QAC3B;IACJ;IACA,OAAO;AACX;AACA,eAAe,YAAY,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,OAAO;IACnF,IAAI,MAAM,IAAI,KAAK,GAAG;QAClB,OAAO,QAAQ,OAAO,CAAC;IAC3B;IACA,MAAM,aAAa,gBAAgB,OAAO;IAC1C,MAAM,SAAS,uBAAuB;IACtC,MAAM,WAAW,qBAAqB,QAAQ,OAAO,QAAQ,SAAS,WAAW,SAAS,SAAS,WAAW,SAAS,SAAS,WAAW;IAC3I,MAAM,QAAQ,WAAW,CAAC,UAAU;QAAE,UAAU,SAAS;IAAS;IAClE,OAAO,QAAQ,OAAO,CAAC;AAC3B;AACA;;;;;CAKC,GACD,SAAS,sBAAsB,EAAE,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,aAAa;IAAC;IAAG;CAAE,EAAE,UAAU,EAAE,OAAO,EAAG;IAC1G,MAAM,OAAO,WAAW,GAAG,CAAC;IAC5B,MAAM,aAAa,KAAK,QAAQ,GAAG,WAAW,GAAG,CAAC,KAAK,QAAQ,IAAI;IACnE,MAAM,EAAE,GAAG,OAAO,EAAE,GAAG,OAAO,EAAE,GAAG,aAAa,WAAW,SAAS,CAAC,gBAAgB,GAAG;QAAE,GAAG;QAAG,GAAG;IAAE;IACrG,MAAM,SAAS,KAAK,MAAM,IAAI;IAC9B,IAAI,SAAS;IACb,IAAI,KAAK,MAAM,KAAK,YAAY,CAAC,KAAK,YAAY,EAAE;QAChD,IAAI,CAAC,YAAY;YACb,UAAU,OAAO,aAAa,CAAC,WAAW;QAC9C,OACK;YACD,MAAM,cAAc,WAAW,QAAQ,CAAC,KAAK;YAC7C,MAAM,eAAe,WAAW,QAAQ,CAAC,MAAM;YAC/C,IAAI,eAAe,cAAc;gBAC7B,SAAS;oBACL;wBAAC;wBAAS;qBAAQ;oBAClB;wBAAC,UAAU;wBAAa,UAAU;qBAAa;iBAClD;YACL;QACJ;IACJ,OACK,IAAI,cAAc,mBAAmB,KAAK,MAAM,GAAG;QACpD,SAAS;YACL;gBAAC,KAAK,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG;gBAAS,KAAK,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG;aAAQ;YAC1D;gBAAC,KAAK,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG;gBAAS,KAAK,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG;aAAQ;SAC7D;IACL;IACA,MAAM,mBAAmB,mBAAmB,UACtC,cAAc,cAAc,QAAQ,KAAK,QAAQ,IACjD;IACN,IAAI,KAAK,QAAQ,CAAC,KAAK,KAAK,aAAa,KAAK,QAAQ,CAAC,MAAM,KAAK,WAAW;QACzE,UAAU,OAAO,aAAa,CAAC,WAAW;IAC9C;IACA,OAAO;QACH,UAAU;YACN,GAAG,iBAAiB,CAAC,GAAG,UAAU,CAAC,KAAK,QAAQ,CAAC,KAAK,IAAI,CAAC,IAAI,MAAM,CAAC,EAAE;YACxE,GAAG,iBAAiB,CAAC,GAAG,UAAU,CAAC,KAAK,QAAQ,CAAC,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC,EAAE;QAC7E;QACA;IACJ;AACJ;AACA;;;;;;;;;CASC,GACD,eAAe,oBAAoB,EAAE,gBAAgB,EAAE,EAAE,gBAAgB,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,cAAc,EAAG;IACxG,MAAM,UAAU,IAAI,IAAI,cAAc,GAAG,CAAC,CAAC,OAAS,KAAK,EAAE;IAC3D,MAAM,gBAAgB,EAAE;IACxB,KAAK,MAAM,QAAQ,MAAO;QACtB,IAAI,KAAK,SAAS,KAAK,OAAO;YAC1B;QACJ;QACA,MAAM,aAAa,QAAQ,GAAG,CAAC,KAAK,EAAE;QACtC,MAAM,YAAY,CAAC,cAAc,KAAK,QAAQ,IAAI,cAAc,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,KAAK,QAAQ;QAClG,IAAI,cAAc,WAAW;YACzB,cAAc,IAAI,CAAC;QACvB;IACJ;IACA,MAAM,UAAU,IAAI,IAAI,cAAc,GAAG,CAAC,CAAC,OAAS,KAAK,EAAE;IAC3D,MAAM,iBAAiB,MAAM,MAAM,CAAC,CAAC,OAAS,KAAK,SAAS,KAAK;IACjE,MAAM,iBAAiB,kBAAkB,eAAe;IACxD,MAAM,gBAAgB;IACtB,KAAK,MAAM,QAAQ,eAAgB;QAC/B,MAAM,aAAa,QAAQ,GAAG,CAAC,KAAK,EAAE;QACtC,IAAI,cAAc,CAAC,cAAc,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,KAAK,EAAE,GAAG;YAC5D,cAAc,IAAI,CAAC;QACvB;IACJ;IACA,IAAI,CAAC,gBAAgB;QACjB,OAAO;YACH,OAAO;YACP,OAAO;QACX;IACJ;IACA,MAAM,uBAAuB,MAAM,eAAe;QAC9C,OAAO;QACP,OAAO;IACX;IACA,IAAI,OAAO,yBAAyB,WAAW;QAC3C,OAAO,uBAAuB;YAAE,OAAO;YAAe,OAAO;QAAc,IAAI;YAAE,OAAO,EAAE;YAAE,OAAO,EAAE;QAAC;IAC1G;IACA,OAAO;AACX;AAEA,MAAM,QAAQ,CAAC,KAAK,MAAM,CAAC,EAAE,MAAM,CAAC,GAAK,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,MAAM;AACtE,MAAM,gBAAgB,CAAC,WAAW;IAAE,GAAG;IAAG,GAAG;AAAE,CAAC,EAAE,QAAQ,aAAe,CAAC;QACtE,GAAG,MAAM,SAAS,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,YAAY,SAAS,CAAC;QACzE,GAAG,MAAM,SAAS,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,YAAY,UAAU,CAAC;IAC9E,CAAC;AACD,SAAS,sBAAsB,aAAa,EAAE,eAAe,EAAE,MAAM;IACjE,MAAM,EAAE,OAAO,WAAW,EAAE,QAAQ,YAAY,EAAE,GAAG,kBAAkB;IACvE,MAAM,EAAE,GAAG,OAAO,EAAE,GAAG,OAAO,EAAE,GAAG,OAAO,SAAS,CAAC,gBAAgB;IACpE,OAAO,cAAc,eAAe;QAChC;YAAC;YAAS;SAAQ;QAClB;YAAC,UAAU;YAAa,UAAU;SAAa;KAClD,EAAE;AACP;AACA;;;;;;;CAOC,GACD,MAAM,sBAAsB,CAAC,OAAO,KAAK;IACrC,IAAI,QAAQ,KAAK;QACb,OAAO,MAAM,KAAK,GAAG,CAAC,QAAQ,MAAM,GAAG,OAAO;IAClD,OACK,IAAI,QAAQ,KAAK;QAClB,OAAO,CAAC,MAAM,KAAK,GAAG,CAAC,QAAQ,MAAM,GAAG,OAAO;IACnD;IACA,OAAO;AACX;AACA,MAAM,cAAc,CAAC,KAAK,QAAQ,QAAQ,EAAE,EAAE,WAAW,EAAE;IACvD,MAAM,YAAY,oBAAoB,IAAI,CAAC,EAAE,UAAU,OAAO,KAAK,GAAG,YAAY;IAClF,MAAM,YAAY,oBAAoB,IAAI,CAAC,EAAE,UAAU,OAAO,MAAM,GAAG,YAAY;IACnF,OAAO;QAAC;QAAW;KAAU;AACjC;AACA,MAAM,mBAAmB,CAAC,MAAM,OAAS,CAAC;QACtC,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC;QAC1B,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC;QAC1B,IAAI,KAAK,GAAG,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE;QAC7B,IAAI,KAAK,GAAG,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE;IACjC,CAAC;AACD,MAAM,YAAY,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,GAAK,CAAC;QAC5C;QACA;QACA,IAAI,IAAI;QACR,IAAI,IAAI;IACZ,CAAC;AACD,MAAM,YAAY,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAAK,CAAC;QACrC;QACA;QACA,OAAO,KAAK;QACZ,QAAQ,KAAK;IACjB,CAAC;AACD,MAAM,aAAa,CAAC,MAAM,aAAa;IAAC;IAAG;CAAE;IACzC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,mBAAmB,QAC9B,KAAK,SAAS,CAAC,gBAAgB,GAC/B,0BAA0B,MAAM;IACtC,OAAO;QACH;QACA;QACA,OAAO,KAAK,QAAQ,EAAE,SAAS,KAAK,KAAK,IAAI,KAAK,YAAY,IAAI;QAClE,QAAQ,KAAK,QAAQ,EAAE,UAAU,KAAK,MAAM,IAAI,KAAK,aAAa,IAAI;IAC1E;AACJ;AACA,MAAM,YAAY,CAAC,MAAM,aAAa;IAAC;IAAG;CAAE;IACxC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,mBAAmB,QAC9B,KAAK,SAAS,CAAC,gBAAgB,GAC/B,0BAA0B,MAAM;IACtC,OAAO;QACH;QACA;QACA,IAAI,IAAI,CAAC,KAAK,QAAQ,EAAE,SAAS,KAAK,KAAK,IAAI,KAAK,YAAY,IAAI,CAAC;QACrE,IAAI,IAAI,CAAC,KAAK,QAAQ,EAAE,UAAU,KAAK,MAAM,IAAI,KAAK,aAAa,IAAI,CAAC;IAC5E;AACJ;AACA,MAAM,mBAAmB,CAAC,OAAO,QAAU,UAAU,iBAAiB,UAAU,QAAQ,UAAU;AAClG,MAAM,qBAAqB,CAAC,OAAO;IAC/B,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,CAAC,GAAG,MAAM,KAAK,EAAE,MAAM,CAAC,GAAG,MAAM,KAAK,IAAI,KAAK,GAAG,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC;IAC/G,MAAM,WAAW,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,CAAC,GAAG,MAAM,MAAM,EAAE,MAAM,CAAC,GAAG,MAAM,MAAM,IAAI,KAAK,GAAG,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC;IACjH,OAAO,KAAK,IAAI,CAAC,WAAW;AAChC;AACA,8DAA8D;AAC9D,MAAM,eAAe,CAAC,MAAQ,UAAU,IAAI,KAAK,KAAK,UAAU,IAAI,MAAM,KAAK,UAAU,IAAI,CAAC,KAAK,UAAU,IAAI,CAAC;AAClH,+DAA+D,GAC/D,MAAM,YAAY,CAAC,IAAM,CAAC,MAAM,MAAM,SAAS;AAC/C,uDAAuD;AACvD,MAAM,UAAU,CAAC,IAAI;IACjB,wCAA4C;QACxC,QAAQ,IAAI,CAAC,CAAC,cAAc,EAAE,QAAQ,mCAAmC,EAAE,IAAI;IACnF;AACJ;AACA,MAAM,eAAe,CAAC,UAAU,WAAW;IAAC;IAAG;CAAE;IAC7C,OAAO;QACH,GAAG,QAAQ,CAAC,EAAE,GAAG,KAAK,KAAK,CAAC,SAAS,CAAC,GAAG,QAAQ,CAAC,EAAE;QACpD,GAAG,QAAQ,CAAC,EAAE,GAAG,KAAK,KAAK,CAAC,SAAS,CAAC,GAAG,QAAQ,CAAC,EAAE;IACxD;AACJ;AACA,MAAM,uBAAuB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,OAAO,EAAE,aAAa,KAAK,EAAE,WAAW;IAAC;IAAG;CAAE;IAC3F,MAAM,WAAW;QACb,GAAG,CAAC,IAAI,EAAE,IAAI;QACd,GAAG,CAAC,IAAI,EAAE,IAAI;IAClB;IACA,OAAO,aAAa,aAAa,UAAU,YAAY;AAC3D;AACA,MAAM,uBAAuB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,OAAO;IACpD,OAAO;QACH,GAAG,IAAI,SAAS;QAChB,GAAG,IAAI,SAAS;IACpB;AACJ;AACA;;;;;;CAMC,GACD,SAAS,aAAa,OAAO,EAAE,QAAQ;IACnC,IAAI,OAAO,YAAY,UAAU;QAC7B,OAAO,KAAK,KAAK,CAAC,CAAC,WAAW,WAAW,CAAC,IAAI,OAAO,CAAC,IAAI;IAC9D;IACA,IAAI,OAAO,YAAY,YAAY,QAAQ,QAAQ,CAAC,OAAO;QACvD,MAAM,eAAe,WAAW;QAChC,IAAI,CAAC,OAAO,KAAK,CAAC,eAAe;YAC7B,OAAO,KAAK,KAAK,CAAC;QACtB;IACJ;IACA,IAAI,OAAO,YAAY,YAAY,QAAQ,QAAQ,CAAC,MAAM;QACtD,MAAM,eAAe,WAAW;QAChC,IAAI,CAAC,OAAO,KAAK,CAAC,eAAe;YAC7B,OAAO,KAAK,KAAK,CAAC,WAAW,eAAe;QAChD;IACJ;IACA,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,QAAQ,8EAA8E,CAAC;IACxI,OAAO;AACX;AACA;;;;;;;CAOC,GACD,SAAS,cAAc,OAAO,EAAE,KAAK,EAAE,MAAM;IACzC,IAAI,OAAO,YAAY,YAAY,OAAO,YAAY,UAAU;QAC5D,MAAM,WAAW,aAAa,SAAS;QACvC,MAAM,WAAW,aAAa,SAAS;QACvC,OAAO;YACH,KAAK;YACL,OAAO;YACP,QAAQ;YACR,MAAM;YACN,GAAG,WAAW;YACd,GAAG,WAAW;QAClB;IACJ;IACA,IAAI,OAAO,YAAY,UAAU;QAC7B,MAAM,MAAM,aAAa,QAAQ,GAAG,IAAI,QAAQ,CAAC,IAAI,GAAG;QACxD,MAAM,SAAS,aAAa,QAAQ,MAAM,IAAI,QAAQ,CAAC,IAAI,GAAG;QAC9D,MAAM,OAAO,aAAa,QAAQ,IAAI,IAAI,QAAQ,CAAC,IAAI,GAAG;QAC1D,MAAM,QAAQ,aAAa,QAAQ,KAAK,IAAI,QAAQ,CAAC,IAAI,GAAG;QAC5D,OAAO;YAAE;YAAK;YAAO;YAAQ;YAAM,GAAG,OAAO;YAAO,GAAG,MAAM;QAAO;IACxE;IACA,OAAO;QAAE,KAAK;QAAG,OAAO;QAAG,QAAQ;QAAG,MAAM;QAAG,GAAG;QAAG,GAAG;IAAE;AAC9D;AACA;;;;;;;;;;CAUC,GACD,SAAS,yBAAyB,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM;IAC/D,MAAM,EAAE,GAAG,IAAI,EAAE,GAAG,GAAG,EAAE,GAAG,qBAAqB,QAAQ;QAAC;QAAG;QAAG;KAAK;IACrE,MAAM,EAAE,GAAG,UAAU,EAAE,GAAG,WAAW,EAAE,GAAG,qBAAqB;QAAE,GAAG,OAAO,CAAC,GAAG,OAAO,KAAK;QAAE,GAAG,OAAO,CAAC,GAAG,OAAO,MAAM;IAAC,GAAG;QAAC;QAAG;QAAG;KAAK;IACxI,MAAM,QAAQ,QAAQ;IACtB,MAAM,SAAS,SAAS;IACxB,OAAO;QACH,MAAM,KAAK,KAAK,CAAC;QACjB,KAAK,KAAK,KAAK,CAAC;QAChB,OAAO,KAAK,KAAK,CAAC;QAClB,QAAQ,KAAK,KAAK,CAAC;IACvB;AACJ;AACA;;;;;;;;;;;;;;;CAeC,GACD,MAAM,uBAAuB,CAAC,QAAQ,OAAO,QAAQ,SAAS,SAAS;IACnE,2DAA2D;IAC3D,MAAM,IAAI,cAAc,SAAS,OAAO;IACxC,MAAM,QAAQ,CAAC,QAAQ,EAAE,CAAC,IAAI,OAAO,KAAK;IAC1C,MAAM,QAAQ,CAAC,SAAS,EAAE,CAAC,IAAI,OAAO,MAAM;IAC5C,sDAAsD;IACtD,MAAM,OAAO,KAAK,GAAG,CAAC,OAAO;IAC7B,MAAM,cAAc,MAAM,MAAM,SAAS;IACzC,MAAM,gBAAgB,OAAO,CAAC,GAAG,OAAO,KAAK,GAAG;IAChD,MAAM,gBAAgB,OAAO,CAAC,GAAG,OAAO,MAAM,GAAG;IACjD,MAAM,IAAI,QAAQ,IAAI,gBAAgB;IACtC,MAAM,IAAI,SAAS,IAAI,gBAAgB;IACvC,wEAAwE;IACxE,MAAM,aAAa,yBAAyB,QAAQ,GAAG,GAAG,aAAa,OAAO;IAC9E,wFAAwF;IACxF,MAAM,SAAS;QACX,MAAM,KAAK,GAAG,CAAC,WAAW,IAAI,GAAG,EAAE,IAAI,EAAE;QACzC,KAAK,KAAK,GAAG,CAAC,WAAW,GAAG,GAAG,EAAE,GAAG,EAAE;QACtC,OAAO,KAAK,GAAG,CAAC,WAAW,KAAK,GAAG,EAAE,KAAK,EAAE;QAC5C,QAAQ,KAAK,GAAG,CAAC,WAAW,MAAM,GAAG,EAAE,MAAM,EAAE;IACnD;IACA,OAAO;QACH,GAAG,IAAI,OAAO,IAAI,GAAG,OAAO,KAAK;QACjC,GAAG,IAAI,OAAO,GAAG,GAAG,OAAO,MAAM;QACjC,MAAM;IACV;AACJ;AACA,MAAM,UAAU,IAAM,OAAO,cAAc,eAAe,WAAW,WAAW,QAAQ,UAAU;AAClG,SAAS,mBAAmB,MAAM;IAC9B,OAAO,WAAW,aAAa,WAAW;AAC9C;AACA,SAAS,kBAAkB,IAAI;IAC3B,OAAO;QACH,OAAO,KAAK,QAAQ,EAAE,SAAS,KAAK,KAAK,IAAI,KAAK,YAAY,IAAI;QAClE,QAAQ,KAAK,QAAQ,EAAE,UAAU,KAAK,MAAM,IAAI,KAAK,aAAa,IAAI;IAC1E;AACJ;AACA,SAAS,kBAAkB,IAAI;IAC3B,OAAQ,CAAC,KAAK,QAAQ,EAAE,SAAS,KAAK,KAAK,IAAI,KAAK,YAAY,MAAM,aAClE,CAAC,KAAK,QAAQ,EAAE,UAAU,KAAK,MAAM,IAAI,KAAK,aAAa,MAAM;AACzE;AACA;;;;;;;;;CASC,GACD,SAAS,yBAAyB,QAAQ,EAAE,aAAa;IAAE,OAAO;IAAG,QAAQ;AAAE,CAAC,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU;IAC9G,MAAM,mBAAmB;QAAE,GAAG,QAAQ;IAAC;IACvC,MAAM,SAAS,WAAW,GAAG,CAAC;IAC9B,IAAI,QAAQ;QACR,MAAM,SAAS,OAAO,MAAM,IAAI;QAChC,iBAAiB,CAAC,IAAI,OAAO,SAAS,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,WAAW,KAAK,IAAI,CAAC,IAAI,MAAM,CAAC,EAAE;QAC/F,iBAAiB,CAAC,IAAI,OAAO,SAAS,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAAC,WAAW,MAAM,IAAI,CAAC,IAAI,MAAM,CAAC,EAAE;IACpG;IACA,OAAO;AACX;AACA,SAAS,aAAa,CAAC,EAAE,CAAC;IACtB,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI,EAAE;QACnB,OAAO;IACX;IACA,KAAK,MAAM,QAAQ,EAAG;QAClB,IAAI,CAAC,EAAE,GAAG,CAAC,OAAO;YACd,OAAO;QACX;IACJ;IACA,OAAO;AACX;AACA;;;CAGC,GACD,SAAS;IACL,IAAI;IACJ,IAAI;IACJ,MAAM,UAAU,IAAI,QAAQ,CAAC,KAAK;QAC9B,UAAU;QACV,SAAS;IACb;IACA,OAAO;QAAE;QAAS;QAAS;IAAO;AACtC;AAEA,SAAS,mBAAmB,KAAK,EAAE,EAAE,WAAW;IAAC;IAAG;CAAE,EAAE,aAAa,KAAK,EAAE,SAAS,EAAE,eAAe,EAAE;IACpG,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,iBAAiB;IAClC,MAAM,aAAa,qBAAqB;QAAE,GAAG,IAAI,CAAC,iBAAiB,QAAQ,CAAC;QAAG,GAAG,IAAI,CAAC,iBAAiB,OAAO,CAAC;IAAE,GAAG;IACrH,MAAM,EAAE,GAAG,QAAQ,EAAE,GAAG,QAAQ,EAAE,GAAG,aAAa,aAAa,YAAY,YAAY;IACvF,mFAAmF;IACnF,OAAO;QACH;QACA;QACA,GAAG,UAAU;IACjB;AACJ;AACA,MAAM,gBAAgB,CAAC,OAAS,CAAC;QAC7B,OAAO,KAAK,WAAW;QACvB,QAAQ,KAAK,YAAY;IAC7B,CAAC;AACD,MAAM,oBAAoB,CAAC,UAAY,SAAS,mBAAmB,QAAQ;AAC3E,MAAM,YAAY;IAAC;IAAS;IAAU;CAAW;AACjD,SAAS,eAAe,KAAK;IACzB,8CAA8C;IAC9C,MAAM,SAAU,MAAM,YAAY,MAAM,CAAC,EAAE,IAAI,MAAM,MAAM;IAC3D,IAAI,QAAQ,aAAa,EAAE,qBAAqB,KAC5C,OAAO;IACX,MAAM,UAAU,UAAU,QAAQ,CAAC,OAAO,QAAQ,KAAK,OAAO,YAAY,CAAC;IAC3E,wFAAwF;IACxF,OAAO,WAAW,CAAC,CAAC,OAAO,OAAO,CAAC;AACvC;AACA,MAAM,eAAe,CAAC,QAAU,aAAa;AAC7C,MAAM,mBAAmB,CAAC,OAAO;IAC7B,MAAM,UAAU,aAAa;IAC7B,MAAM,OAAO,UAAU,MAAM,OAAO,GAAG,MAAM,OAAO,EAAE,CAAC,EAAE,CAAC;IAC1D,MAAM,OAAO,UAAU,MAAM,OAAO,GAAG,MAAM,OAAO,EAAE,CAAC,EAAE,CAAC;IAC1D,OAAO;QACH,GAAG,OAAO,CAAC,QAAQ,QAAQ,CAAC;QAC5B,GAAG,OAAO,CAAC,QAAQ,OAAO,CAAC;IAC/B;AACJ;AACA;;;;CAIC,GACD,MAAM,kBAAkB,CAAC,MAAM,aAAa,YAAY,MAAM;IAC1D,MAAM,UAAU,YAAY,gBAAgB,CAAC,CAAC,CAAC,EAAE,MAAM;IACvD,IAAI,CAAC,WAAW,CAAC,QAAQ,MAAM,EAAE;QAC7B,OAAO;IACX;IACA,OAAO,MAAM,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;QAC5B,MAAM,eAAe,OAAO,qBAAqB;QACjD,OAAO;YACH,IAAI,OAAO,YAAY,CAAC;YACxB;YACA;YACA,UAAU,OAAO,YAAY,CAAC;YAC9B,GAAG,CAAC,aAAa,IAAI,GAAG,WAAW,IAAI,IAAI;YAC3C,GAAG,CAAC,aAAa,GAAG,GAAG,WAAW,GAAG,IAAI;YACzC,GAAG,cAAc,OAAO;QAC5B;IACJ;AACJ;AAEA,SAAS,oBAAoB,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAG;IAChI;;;KAGC,GACD,MAAM,UAAU,UAAU,QAAQ,iBAAiB,QAAQ,iBAAiB,QAAQ,UAAU;IAC9F,MAAM,UAAU,UAAU,QAAQ,iBAAiB,QAAQ,iBAAiB,QAAQ,UAAU;IAC9F,MAAM,UAAU,KAAK,GAAG,CAAC,UAAU;IACnC,MAAM,UAAU,KAAK,GAAG,CAAC,UAAU;IACnC,OAAO;QAAC;QAAS;QAAS;QAAS;KAAQ;AAC/C;AACA,SAAS,uBAAuB,QAAQ,EAAE,SAAS;IAC/C,IAAI,YAAY,GAAG;QACf,OAAO,MAAM;IACjB;IACA,OAAO,YAAY,KAAK,KAAK,IAAI,CAAC,CAAC;AACvC;AACA,SAAS,wBAAwB,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE;IACvD,OAAQ;QACJ,KAAK,SAAS,IAAI;YACd,OAAO;gBAAC,KAAK,uBAAuB,KAAK,IAAI;gBAAI;aAAG;QACxD,KAAK,SAAS,KAAK;YACf,OAAO;gBAAC,KAAK,uBAAuB,KAAK,IAAI;gBAAI;aAAG;QACxD,KAAK,SAAS,GAAG;YACb,OAAO;gBAAC;gBAAI,KAAK,uBAAuB,KAAK,IAAI;aAAG;QACxD,KAAK,SAAS,MAAM;YAChB,OAAO;gBAAC;gBAAI,KAAK,uBAAuB,KAAK,IAAI;aAAG;IAC5D;AACJ;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA8BC,GACD,SAAS,cAAc,EAAE,OAAO,EAAE,OAAO,EAAE,iBAAiB,SAAS,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,iBAAiB,SAAS,GAAG,EAAE,YAAY,IAAI,EAAG;IAC7I,MAAM,CAAC,gBAAgB,eAAe,GAAG,wBAAwB;QAC7D,KAAK;QACL,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,GAAG;IACP;IACA,MAAM,CAAC,gBAAgB,eAAe,GAAG,wBAAwB;QAC7D,KAAK;QACL,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,GAAG;IACP;IACA,MAAM,CAAC,QAAQ,QAAQ,SAAS,QAAQ,GAAG,oBAAoB;QAC3D;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACJ;IACA,OAAO;QACH,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE,QAAQ,EAAE,EAAE,eAAe,CAAC,EAAE,eAAe,CAAC,EAAE,eAAe,CAAC,EAAE,eAAe,CAAC,EAAE,QAAQ,CAAC,EAAE,SAAS;QACvH;QACA;QACA;QACA;KACH;AACL;AAEA,mFAAmF;AACnF,SAAS,cAAc,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAG;IAC1D,MAAM,UAAU,KAAK,GAAG,CAAC,UAAU,WAAW;IAC9C,MAAM,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU;IAClE,MAAM,UAAU,KAAK,GAAG,CAAC,UAAU,WAAW;IAC9C,MAAM,UAAU,UAAU,UAAU,UAAU,UAAU,UAAU;IAClE,OAAO;QAAC;QAAS;QAAS;QAAS;KAAQ;AAC/C;AACA,SAAS,sBAAsB,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,KAAK,EAAE,SAAS,CAAC,EAAE,kBAAkB,KAAK,EAAG;IAC7G,IAAI,CAAC,iBAAiB;QAClB,OAAO;IACX;IACA,MAAM,8BAA8B,YAAY,WAAW,QAAQ,IAAI,WAAW,QAAQ;IAC1F,MAAM,iBAAiB,KAAK,GAAG,CAAC,WAAW,SAAS,CAAC,CAAC,IAAI,GAAG,WAAW,SAAS,CAAC,CAAC,IAAI,GAAG;IAC1F,OAAO,SAAS,CAAC,8BAA8B,iBAAiB,CAAC;AACrE;AACA,SAAS,cAAc,EAAE,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE;IACvE,MAAM,UAAU,iBAAiB,UAAU,aAAa,UAAU;IAClE,IAAI,QAAQ,CAAC,KAAK,QAAQ,EAAE,EAAE;QAC1B,QAAQ,EAAE,IAAI;IAClB;IACA,IAAI,QAAQ,CAAC,KAAK,QAAQ,EAAE,EAAE;QAC1B,QAAQ,EAAE,IAAI;IAClB;IACA,MAAM,WAAW;QACb,GAAG,CAAC,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE;QAC/B,GAAG,CAAC,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE;QAC/B,OAAO,QAAQ,SAAS,CAAC,EAAE;QAC3B,QAAQ,SAAS,SAAS,CAAC,EAAE;IACjC;IACA,OAAO,mBAAmB,UAAU,UAAU,YAAY;AAC9D;AACA,MAAM,YAAY,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,YAAY,EAAE,GAAK,CAAC,SAAS,EAAE,SAAS,gBAAgB,GAAG,CAAC,EAAE,SAAS,gBAAgB,IAAI;AAC9I,MAAM,mBAAmB,CAAC,MAAM;IAC5B,OAAO,MAAM,IAAI,CAAC,CAAC,KAAO,GAAG,MAAM,KAAK,KAAK,MAAM,IAC/C,GAAG,MAAM,KAAK,KAAK,MAAM,IACzB,CAAC,GAAG,YAAY,KAAK,KAAK,YAAY,IAAK,CAAC,GAAG,YAAY,IAAI,CAAC,KAAK,YAAY,AAAC,KAClF,CAAC,GAAG,YAAY,KAAK,KAAK,YAAY,IAAK,CAAC,GAAG,YAAY,IAAI,CAAC,KAAK,YAAY,AAAC;AAC1F;AACA;;;;;;;;;;;CAWC,GACD,MAAM,UAAU,CAAC,YAAY;IACzB,IAAI,CAAC,WAAW,MAAM,IAAI,CAAC,WAAW,MAAM,EAAE;QAC1C,QAAQ,OAAO,aAAa,CAAC,WAAW;QACxC,OAAO;IACX;IACA,IAAI;IACJ,IAAI,WAAW,aAAa;QACxB,OAAO;YAAE,GAAG,UAAU;QAAC;IAC3B,OACK;QACD,OAAO;YACH,GAAG,UAAU;YACb,IAAI,UAAU;QAClB;IACJ;IACA,IAAI,iBAAiB,MAAM,QAAQ;QAC/B,OAAO;IACX;IACA,IAAI,KAAK,YAAY,KAAK,MAAM;QAC5B,OAAO,KAAK,YAAY;IAC5B;IACA,IAAI,KAAK,YAAY,KAAK,MAAM;QAC5B,OAAO,KAAK,YAAY;IAC5B;IACA,OAAO,MAAM,MAAM,CAAC;AACxB;AACA;;;;;;;;;;;;;;;CAeC,GACD,MAAM,gBAAgB,CAAC,SAAS,eAAe,OAAO,UAAU;IAAE,iBAAiB;AAAK,CAAC;IACrF,MAAM,EAAE,IAAI,SAAS,EAAE,GAAG,MAAM,GAAG;IACnC,IAAI,CAAC,cAAc,MAAM,IAAI,CAAC,cAAc,MAAM,EAAE;QAChD,QAAQ,OAAO,aAAa,CAAC,WAAW;QACxC,OAAO;IACX;IACA,MAAM,YAAY,MAAM,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,QAAQ,EAAE;IACvD,IAAI,CAAC,WAAW;QACZ,QAAQ,OAAO,aAAa,CAAC,WAAW,CAAC;QACzC,OAAO;IACX;IACA,uEAAuE;IACvE,MAAM,OAAO;QACT,GAAG,IAAI;QACP,IAAI,QAAQ,eAAe,GAAG,UAAU,iBAAiB;QACzD,QAAQ,cAAc,MAAM;QAC5B,QAAQ,cAAc,MAAM;QAC5B,cAAc,cAAc,YAAY;QACxC,cAAc,cAAc,YAAY;IAC5C;IACA,OAAO,MAAM,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,WAAW,MAAM,CAAC;AAC1D;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA4BC,GACD,SAAS,gBAAgB,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAG;IAC5D,MAAM,CAAC,QAAQ,QAAQ,SAAS,QAAQ,GAAG,cAAc;QACrD;QACA;QACA;QACA;IACJ;IACA,OAAO;QAAC,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,QAAQ,EAAE,EAAE,QAAQ,CAAC,EAAE,SAAS;QAAE;QAAQ;QAAQ;QAAS;KAAQ;AAC/F;AAEA,MAAM,mBAAmB;IACrB,CAAC,SAAS,IAAI,CAAC,EAAE;QAAE,GAAG,CAAC;QAAG,GAAG;IAAE;IAC/B,CAAC,SAAS,KAAK,CAAC,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAC/B,CAAC,SAAS,GAAG,CAAC,EAAE;QAAE,GAAG;QAAG,GAAG,CAAC;IAAE;IAC9B,CAAC,SAAS,MAAM,CAAC,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;AACpC;AACA,MAAM,eAAe,CAAC,EAAE,MAAM,EAAE,iBAAiB,SAAS,MAAM,EAAE,MAAM,EAAG;IACvE,IAAI,mBAAmB,SAAS,IAAI,IAAI,mBAAmB,SAAS,KAAK,EAAE;QACvE,OAAO,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG;YAAE,GAAG;YAAG,GAAG;QAAE,IAAI;YAAE,GAAG,CAAC;YAAG,GAAG;QAAE;IAChE;IACA,OAAO,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG;QAAE,GAAG;QAAG,GAAG;IAAE,IAAI;QAAE,GAAG;QAAG,GAAG,CAAC;IAAE;AAChE;AACA,MAAM,WAAW,CAAC,GAAG,IAAM,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,KAAK,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE;AAClF;;;CAGC,GACD,SAAS,UAAU,EAAE,MAAM,EAAE,iBAAiB,SAAS,MAAM,EAAE,MAAM,EAAE,iBAAiB,SAAS,GAAG,EAAE,MAAM,EAAE,MAAM,EAAG;IACnH,MAAM,YAAY,gBAAgB,CAAC,eAAe;IAClD,MAAM,YAAY,gBAAgB,CAAC,eAAe;IAClD,MAAM,eAAe;QAAE,GAAG,OAAO,CAAC,GAAG,UAAU,CAAC,GAAG;QAAQ,GAAG,OAAO,CAAC,GAAG,UAAU,CAAC,GAAG;IAAO;IAC9F,MAAM,eAAe;QAAE,GAAG,OAAO,CAAC,GAAG,UAAU,CAAC,GAAG;QAAQ,GAAG,OAAO,CAAC,GAAG,UAAU,CAAC,GAAG;IAAO;IAC9F,MAAM,MAAM,aAAa;QACrB,QAAQ;QACR;QACA,QAAQ;IACZ;IACA,MAAM,cAAc,IAAI,CAAC,KAAK,IAAI,MAAM;IACxC,MAAM,UAAU,GAAG,CAAC,YAAY;IAChC,IAAI,SAAS,EAAE;IACf,IAAI,SAAS;IACb,MAAM,kBAAkB;QAAE,GAAG;QAAG,GAAG;IAAE;IACrC,MAAM,kBAAkB;QAAE,GAAG;QAAG,GAAG;IAAE;IACrC,MAAM,CAAC,gBAAgB,gBAAgB,gBAAgB,eAAe,GAAG,cAAc;QACnF,SAAS,OAAO,CAAC;QACjB,SAAS,OAAO,CAAC;QACjB,SAAS,OAAO,CAAC;QACjB,SAAS,OAAO,CAAC;IACrB;IACA,0CAA0C;IAC1C,IAAI,SAAS,CAAC,YAAY,GAAG,SAAS,CAAC,YAAY,KAAK,CAAC,GAAG;QACxD,UAAU,OAAO,CAAC,IAAI;QACtB,UAAU,OAAO,CAAC,IAAI;QACtB;;;;SAIC,GACD,MAAM,gBAAgB;YAClB;gBAAE,GAAG;gBAAS,GAAG,aAAa,CAAC;YAAC;YAChC;gBAAE,GAAG;gBAAS,GAAG,aAAa,CAAC;YAAC;SACnC;QACD;;;;SAIC,GACD,MAAM,kBAAkB;YACpB;gBAAE,GAAG,aAAa,CAAC;gBAAE,GAAG;YAAQ;YAChC;gBAAE,GAAG,aAAa,CAAC;gBAAE,GAAG;YAAQ;SACnC;QACD,IAAI,SAAS,CAAC,YAAY,KAAK,SAAS;YACpC,SAAS,gBAAgB,MAAM,gBAAgB;QACnD,OACK;YACD,SAAS,gBAAgB,MAAM,kBAAkB;QACrD;IACJ,OACK;QACD,2FAA2F;QAC3F,MAAM,eAAe;YAAC;gBAAE,GAAG,aAAa,CAAC;gBAAE,GAAG,aAAa,CAAC;YAAC;SAAE;QAC/D,MAAM,eAAe;YAAC;gBAAE,GAAG,aAAa,CAAC;gBAAE,GAAG,aAAa,CAAC;YAAC;SAAE;QAC/D,gDAAgD;QAChD,IAAI,gBAAgB,KAAK;YACrB,SAAS,UAAU,CAAC,KAAK,UAAU,eAAe;QACtD,OACK;YACD,SAAS,UAAU,CAAC,KAAK,UAAU,eAAe;QACtD;QACA,IAAI,mBAAmB,gBAAgB;YACnC,MAAM,OAAO,KAAK,GAAG,CAAC,MAAM,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY;YAC/D,gTAAgT;YAChT,IAAI,QAAQ,QAAQ;gBAChB,MAAM,YAAY,KAAK,GAAG,CAAC,SAAS,GAAG,SAAS;gBAChD,IAAI,SAAS,CAAC,YAAY,KAAK,SAAS;oBACpC,eAAe,CAAC,YAAY,GAAG,CAAC,YAAY,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC,IAAI;gBAChG,OACK;oBACD,eAAe,CAAC,YAAY,GAAG,CAAC,YAAY,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC,IAAI;gBAChG;YACJ;QACJ;QACA,4FAA4F;QAC5F,IAAI,mBAAmB,gBAAgB;YACnC,MAAM,sBAAsB,gBAAgB,MAAM,MAAM;YACxD,MAAM,YAAY,SAAS,CAAC,YAAY,KAAK,SAAS,CAAC,oBAAoB;YAC3E,MAAM,qBAAqB,YAAY,CAAC,oBAAoB,GAAG,YAAY,CAAC,oBAAoB;YAChG,MAAM,qBAAqB,YAAY,CAAC,oBAAoB,GAAG,YAAY,CAAC,oBAAoB;YAChG,MAAM,mBAAmB,AAAC,SAAS,CAAC,YAAY,KAAK,KAAK,CAAC,AAAC,CAAC,aAAa,sBAAwB,aAAa,kBAAmB,KAC7H,SAAS,CAAC,YAAY,KAAK,KAAK,CAAC,AAAC,CAAC,aAAa,sBAAwB,aAAa,kBAAmB;YAC7G,IAAI,kBAAkB;gBAClB,SAAS,gBAAgB,MAAM,eAAe;YAClD;QACJ;QACA,MAAM,iBAAiB;YAAE,GAAG,aAAa,CAAC,GAAG,gBAAgB,CAAC;YAAE,GAAG,aAAa,CAAC,GAAG,gBAAgB,CAAC;QAAC;QACtG,MAAM,iBAAiB;YAAE,GAAG,aAAa,CAAC,GAAG,gBAAgB,CAAC;YAAE,GAAG,aAAa,CAAC,GAAG,gBAAgB,CAAC;QAAC;QACtG,MAAM,eAAe,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,eAAe,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,eAAe,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;QAC/G,MAAM,eAAe,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,eAAe,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,eAAe,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;QAC/G,gEAAgE;QAChE,IAAI,gBAAgB,cAAc;YAC9B,UAAU,CAAC,eAAe,CAAC,GAAG,eAAe,CAAC,IAAI;YAClD,UAAU,MAAM,CAAC,EAAE,CAAC,CAAC;QACzB,OACK;YACD,UAAU,MAAM,CAAC,EAAE,CAAC,CAAC;YACrB,UAAU,CAAC,eAAe,CAAC,GAAG,eAAe,CAAC,IAAI;QACtD;IACJ;IACA,MAAM,aAAa;QACf;QACA;YAAE,GAAG,aAAa,CAAC,GAAG,gBAAgB,CAAC;YAAE,GAAG,aAAa,CAAC,GAAG,gBAAgB,CAAC;QAAC;WAC5E;QACH;YAAE,GAAG,aAAa,CAAC,GAAG,gBAAgB,CAAC;YAAE,GAAG,aAAa,CAAC,GAAG,gBAAgB,CAAC;QAAC;QAC/E;KACH;IACD,OAAO;QAAC;QAAY;QAAS;QAAS;QAAgB;KAAe;AACzE;AACA,SAAS,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI;IAC1B,MAAM,WAAW,KAAK,GAAG,CAAC,SAAS,GAAG,KAAK,GAAG,SAAS,GAAG,KAAK,GAAG;IAClE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG;IACjB,UAAU;IACV,IAAI,AAAC,EAAE,CAAC,KAAK,KAAK,MAAM,EAAE,CAAC,IAAM,EAAE,CAAC,KAAK,KAAK,MAAM,EAAE,CAAC,EAAG;QACtD,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG;IACvB;IACA,8BAA8B;IAC9B,IAAI,EAAE,CAAC,KAAK,GAAG;QACX,MAAM,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI;QAC9B,MAAM,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;QAC9B,OAAO,CAAC,EAAE,EAAE,IAAI,WAAW,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,WAAW,MAAM;IACjF;IACA,MAAM,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC;IAC9B,MAAM,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI;IAC9B,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,WAAW,KAAK,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,WAAW,KAAK,CAAC,EAAE,GAAG;AACjF;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA8BC,GACD,SAAS,kBAAkB,EAAE,OAAO,EAAE,OAAO,EAAE,iBAAiB,SAAS,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,iBAAiB,SAAS,GAAG,EAAE,eAAe,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,EAAG;IAChL,MAAM,CAAC,QAAQ,QAAQ,QAAQ,SAAS,QAAQ,GAAG,UAAU;QACzD,QAAQ;YAAE,GAAG;YAAS,GAAG;QAAQ;QACjC;QACA,QAAQ;YAAE,GAAG;YAAS,GAAG;QAAQ;QACjC;QACA,QAAQ;YAAE,GAAG;YAAS,GAAG;QAAQ;QACjC;IACJ;IACA,MAAM,OAAO,OAAO,MAAM,CAAC,CAAC,KAAK,GAAG;QAChC,IAAI,UAAU;QACd,IAAI,IAAI,KAAK,IAAI,OAAO,MAAM,GAAG,GAAG;YAChC,UAAU,QAAQ,MAAM,CAAC,IAAI,EAAE,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,EAAE;QACvD,OACK;YACD,UAAU,GAAG,MAAM,IAAI,MAAM,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;QACnD;QACA,OAAO;QACP,OAAO;IACX,GAAG;IACH,OAAO;QAAC;QAAM;QAAQ;QAAQ;QAAS;KAAQ;AACnD;AAEA,SAAS,kBAAkB,IAAI;IAC3B,OAAQ,QACJ,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,YAAY,IAAI,KAAK,OAAO,EAAE,MAAM,KACtD,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,YAAY;AACjE;AACA,SAAS,gBAAgB,MAAM;IAC3B,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG;IACnC,IAAI,CAAC,kBAAkB,eAAe,CAAC,kBAAkB,aAAa;QAClE,OAAO;IACX;IACA,MAAM,qBAAqB,WAAW,SAAS,CAAC,YAAY,IAAI,eAAe,WAAW,OAAO;IACjG,MAAM,qBAAqB,WAAW,SAAS,CAAC,YAAY,IAAI,eAAe,WAAW,OAAO;IACjG,MAAM,eAAe,YAAY,oBAAoB,UAAU,EAAE,EAAE,OAAO,YAAY;IACtF,MAAM,eAAe,YACrB,kGAAkG;IAClG,OAAO,cAAc,KAAK,eAAe,MAAM,GACzC,oBAAoB,UAAU,EAAE,GAChC,CAAC,oBAAoB,UAAU,EAAE,EAAE,MAAM,CAAC,oBAAoB,UAAU,EAAE,GAAG,OAAO,YAAY;IACtG,IAAI,CAAC,gBAAgB,CAAC,cAAc;QAChC,OAAO,OAAO,GAAG,OAAO,aAAa,CAAC,WAAW,CAAC,CAAC,eAAe,WAAW,UAAU;YACnF,IAAI,OAAO,EAAE;YACb,cAAc,OAAO,YAAY;YACjC,cAAc,OAAO,YAAY;QACrC;QACA,OAAO;IACX;IACA,MAAM,iBAAiB,cAAc,YAAY,SAAS,MAAM;IAChE,MAAM,iBAAiB,cAAc,YAAY,SAAS,GAAG;IAC7D,MAAM,SAAS,kBAAkB,YAAY,cAAc;IAC3D,MAAM,SAAS,kBAAkB,YAAY,cAAc;IAC3D,OAAO;QACH,SAAS,OAAO,CAAC;QACjB,SAAS,OAAO,CAAC;QACjB,SAAS,OAAO,CAAC;QACjB,SAAS,OAAO,CAAC;QACjB;QACA;IACJ;AACJ;AACA,SAAS,eAAe,OAAO;IAC3B,IAAI,CAAC,SAAS;QACV,OAAO;IACX;IACA,MAAM,SAAS,EAAE;IACjB,MAAM,SAAS,EAAE;IACjB,KAAK,MAAM,UAAU,QAAS;QAC1B,OAAO,KAAK,GAAG,OAAO,KAAK,IAAI;QAC/B,OAAO,MAAM,GAAG,OAAO,MAAM,IAAI;QACjC,IAAI,OAAO,IAAI,KAAK,UAAU;YAC1B,OAAO,IAAI,CAAC;QAChB,OACK,IAAI,OAAO,IAAI,KAAK,UAAU;YAC/B,OAAO,IAAI,CAAC;QAChB;IACJ;IACA,OAAO;QACH;QACA;IACJ;AACJ;AACA,SAAS,kBAAkB,IAAI,EAAE,MAAM,EAAE,mBAAmB,SAAS,IAAI,EAAE,SAAS,KAAK;IACrF,MAAM,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,gBAAgB,CAAC,CAAC;IAC9D,MAAM,IAAI,CAAC,QAAQ,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,gBAAgB,CAAC,CAAC;IAC9D,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,UAAU,kBAAkB;IACtD,IAAI,QAAQ;QACR,OAAO;YAAE,GAAG,IAAI,QAAQ;YAAG,GAAG,IAAI,SAAS;QAAE;IACjD;IACA,MAAM,WAAW,QAAQ,YAAY;IACrC,OAAQ;QACJ,KAAK,SAAS,GAAG;YACb,OAAO;gBAAE,GAAG,IAAI,QAAQ;gBAAG;YAAE;QACjC,KAAK,SAAS,KAAK;YACf,OAAO;gBAAE,GAAG,IAAI;gBAAO,GAAG,IAAI,SAAS;YAAE;QAC7C,KAAK,SAAS,MAAM;YAChB,OAAO;gBAAE,GAAG,IAAI,QAAQ;gBAAG,GAAG,IAAI;YAAO;QAC7C,KAAK,SAAS,IAAI;YACd,OAAO;gBAAE;gBAAG,GAAG,IAAI,SAAS;YAAE;IACtC;AACJ;AACA,SAAS,YAAY,MAAM,EAAE,QAAQ;IACjC,IAAI,CAAC,QAAQ;QACT,OAAO;IACX;IACA,kFAAkF;IAClF,OAAO,CAAC,CAAC,WAAW,MAAM,CAAC,EAAE,GAAG,OAAO,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,SAAS,KAAK;AAC9E;AAEA,SAAS,YAAY,MAAM,EAAE,EAAE;IAC3B,IAAI,CAAC,QAAQ;QACT,OAAO;IACX;IACA,IAAI,OAAO,WAAW,UAAU;QAC5B,OAAO;IACX;IACA,MAAM,WAAW,KAAK,GAAG,GAAG,EAAE,CAAC,GAAG;IAClC,OAAO,GAAG,WAAW,OAAO,IAAI,CAAC,QAC5B,IAAI,GACJ,GAAG,CAAC,CAAC,MAAQ,GAAG,IAAI,CAAC,EAAE,MAAM,CAAC,IAAI,EAAE,EACpC,IAAI,CAAC,MAAM;AACpB;AACA,SAAS,gBAAgB,KAAK,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE,kBAAkB,EAAE,gBAAgB,EAAG;IACvF,MAAM,MAAM,IAAI;IAChB,OAAO,MACF,MAAM,CAAC,CAAC,SAAS;QAClB;YAAC,KAAK,WAAW,IAAI;YAAoB,KAAK,SAAS,IAAI;SAAiB,CAAC,OAAO,CAAC,CAAC;YAClF,IAAI,UAAU,OAAO,WAAW,UAAU;gBACtC,MAAM,WAAW,YAAY,QAAQ;gBACrC,IAAI,CAAC,IAAI,GAAG,CAAC,WAAW;oBACpB,QAAQ,IAAI,CAAC;wBAAE,IAAI;wBAAU,OAAO,OAAO,KAAK,IAAI;wBAAc,GAAG,MAAM;oBAAC;oBAC5E,IAAI,GAAG,CAAC;gBACZ;YACJ;QACJ;QACA,OAAO;IACX,GAAG,EAAE,EACA,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,EAAE,CAAC,aAAa,CAAC,EAAE,EAAE;AAC/C;AAEA,SAAS,wBAAwB,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK;IACxE,IAAI,kBAAkB;IACtB,IAAI,UAAU,SAAS;QACnB,kBAAkB;IACtB,OACK,IAAI,UAAU,OAAO;QACtB,kBAAkB;IACtB;IACA;;;KAGC,GACD,IAAI,MAAM;QACN,CAAC,SAAS,CAAC,GAAG,SAAS,KAAK,GAAG,eAAe,IAAI,SAAS,IAAI,GAAG,SAAS,CAAC;QAC5E,SAAS,CAAC,GAAG,SAAS,IAAI,GAAG,SAAS,CAAC,GAAG;KAC7C;IACD,uEAAuE;IACvE,IAAI,QAAQ;QAAC,CAAC,MAAM;QAAiB,CAAC;KAAI;IAC1C,OAAQ;QACJ,KAAK,SAAS,KAAK;YACf,MAAM;gBACF,CAAC,SAAS,CAAC,GAAG,SAAS,KAAK,IAAI,SAAS,IAAI,GAAG,SAAS,CAAC,GAAG;gBAC7D,CAAC,SAAS,CAAC,GAAG,SAAS,MAAM,GAAG,eAAe,IAAI,SAAS,IAAI,GAAG,SAAS,CAAC;aAChF;YACD,QAAQ;gBAAC;gBAAG,CAAC,MAAM;aAAgB;YACnC;QACJ,KAAK,SAAS,MAAM;YAChB,GAAG,CAAC,EAAE,GAAG,CAAC,SAAS,CAAC,GAAG,SAAS,MAAM,IAAI,SAAS,IAAI,GAAG,SAAS,CAAC,GAAG;YACvE,KAAK,CAAC,EAAE,GAAG;YACX;QACJ,KAAK,SAAS,IAAI;YACd,MAAM;gBACF,SAAS,CAAC,GAAG,SAAS,IAAI,GAAG,SAAS,CAAC,GAAG;gBAC1C,CAAC,SAAS,CAAC,GAAG,SAAS,MAAM,GAAG,eAAe,IAAI,SAAS,IAAI,GAAG,SAAS,CAAC;aAChF;YACD,QAAQ;gBAAC,CAAC;gBAAK,CAAC,MAAM;aAAgB;YACtC;IACR;IACA,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC,cAAc,EAAE,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;AACtF;AAEA,MAAM,iBAAiB;IACnB,YAAY;QAAC;QAAG;KAAE;IAClB,YAAY;IACZ,sBAAsB;IACtB,UAAU,CAAC;AACf;AACA,MAAM,+BAA+B;IACjC,GAAG,cAAc;IACjB,eAAe;AACnB;AACA,SAAS,aAAa,IAAI,EAAE,QAAQ;IAChC,MAAM,SAAS;QAAE,GAAG,IAAI;IAAC;IACzB,IAAK,MAAM,OAAO,SAAU;QACxB,IAAI,QAAQ,CAAC,IAAI,KAAK,WAAW;YAC7B,wDAAwD;YACxD,MAAM,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI;QAC/B;IACJ;IACA,OAAO;AACX;AACA,SAAS,wBAAwB,UAAU,EAAE,YAAY,EAAE,OAAO;IAC9D,MAAM,WAAW,aAAa,gBAAgB;IAC9C,KAAK,MAAM,QAAQ,WAAW,MAAM,GAAI;QACpC,IAAI,KAAK,QAAQ,EAAE;YACf,gBAAgB,MAAM,YAAY,cAAc;QACpD,OACK;YACD,MAAM,qBAAqB,0BAA0B,MAAM,SAAS,UAAU;YAC9E,MAAM,SAAS,mBAAmB,KAAK,MAAM,IAAI,KAAK,MAAM,GAAG,SAAS,UAAU;YAClF,MAAM,kBAAkB,cAAc,oBAAoB,QAAQ,kBAAkB;YACpF,KAAK,SAAS,CAAC,gBAAgB,GAAG;QACtC;IACJ;AACJ;AACA,SAAS,eAAe,KAAK,EAAE,UAAU,EAAE,YAAY,EAAE,OAAO;IAC5D,MAAM,WAAW,aAAa,8BAA8B;IAC5D,IAAI,mBAAmB,MAAM,MAAM,GAAG;IACtC,MAAM,YAAY,IAAI,IAAI;IAC1B,MAAM,gBAAgB,UAAU,uBAAuB,OAAO;IAC9D,WAAW,KAAK;IAChB,aAAa,KAAK;IAClB,KAAK,MAAM,YAAY,MAAO;QAC1B,IAAI,eAAe,UAAU,GAAG,CAAC,SAAS,EAAE;QAC5C,IAAI,SAAS,aAAa,IAAI,aAAa,cAAc,UAAU,UAAU;YACzE,WAAW,GAAG,CAAC,SAAS,EAAE,EAAE;QAChC,OACK;YACD,MAAM,qBAAqB,0BAA0B,UAAU,SAAS,UAAU;YAClF,MAAM,SAAS,mBAAmB,SAAS,MAAM,IAAI,SAAS,MAAM,GAAG,SAAS,UAAU;YAC1F,MAAM,kBAAkB,cAAc,oBAAoB,QAAQ,kBAAkB;YACpF,eAAe;gBACX,GAAG,SAAS,QAAQ;gBACpB,GAAG,QAAQ;gBACX,UAAU;oBACN,OAAO,SAAS,QAAQ,EAAE;oBAC1B,QAAQ,SAAS,QAAQ,EAAE;gBAC/B;gBACA,WAAW;oBACP,kBAAkB;oBAClB,yIAAyI;oBACzI,cAAc,CAAC,SAAS,QAAQ,GAAG,YAAY,cAAc,UAAU;oBACvE,GAAG,WAAW,UAAU;oBACxB;gBACJ;YACJ;YACA,WAAW,GAAG,CAAC,SAAS,EAAE,EAAE;QAChC;QACA,IAAI,CAAC,aAAa,QAAQ,KAAK,aAC3B,aAAa,QAAQ,CAAC,KAAK,KAAK,aAChC,aAAa,QAAQ,CAAC,MAAM,KAAK,SAAS,KAC1C,CAAC,aAAa,MAAM,EAAE;YACtB,mBAAmB;QACvB;QACA,IAAI,SAAS,QAAQ,EAAE;YACnB,gBAAgB,cAAc,YAAY,cAAc;QAC5D;IACJ;IACA,OAAO;AACX;AACA,SAAS,mBAAmB,IAAI,EAAE,YAAY;IAC1C,IAAI,CAAC,KAAK,QAAQ,EAAE;QAChB;IACJ;IACA,MAAM,aAAa,aAAa,GAAG,CAAC,KAAK,QAAQ;IACjD,IAAI,YAAY;QACZ,WAAW,GAAG,CAAC,KAAK,EAAE,EAAE;IAC5B,OACK;QACD,aAAa,GAAG,CAAC,KAAK,QAAQ,EAAE,IAAI,IAAI;YAAC;gBAAC,KAAK,EAAE;gBAAE;aAAK;SAAC;IAC7D;AACJ;AACA;;CAEC,GACD,SAAS,gBAAgB,IAAI,EAAE,UAAU,EAAE,YAAY,EAAE,OAAO;IAC5D,MAAM,EAAE,oBAAoB,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,aAAa,gBAAgB;IACtF,MAAM,WAAW,KAAK,QAAQ;IAC9B,MAAM,aAAa,WAAW,GAAG,CAAC;IAClC,IAAI,CAAC,YAAY;QACb,QAAQ,IAAI,CAAC,CAAC,YAAY,EAAE,SAAS,oGAAoG,CAAC;QAC1I;IACJ;IACA,mBAAmB,MAAM;IACzB,MAAM,gBAAgB,uBAAuB,OAAO;IACpD,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,kBAAkB,MAAM,YAAY,YAAY,YAAY;IAChF,MAAM,EAAE,gBAAgB,EAAE,GAAG,KAAK,SAAS;IAC3C,MAAM,kBAAkB,MAAM,iBAAiB,CAAC,IAAI,MAAM,iBAAiB,CAAC;IAC5E,IAAI,mBAAmB,MAAM,KAAK,SAAS,CAAC,CAAC,EAAE;QAC3C,qDAAqD;QACrD,WAAW,GAAG,CAAC,KAAK,EAAE,EAAE;YACpB,GAAG,IAAI;YACP,WAAW;gBACP,GAAG,KAAK,SAAS;gBACjB,kBAAkB,kBAAkB;oBAAE;oBAAG;gBAAE,IAAI;gBAC/C;YACJ;QACJ;IACJ;AACJ;AACA,SAAS,WAAW,IAAI,EAAE,aAAa;IACnC,OAAO,CAAC,UAAU,KAAK,MAAM,IAAI,KAAK,MAAM,GAAG,CAAC,IAAI,CAAC,KAAK,QAAQ,GAAG,gBAAgB,CAAC;AAC1F;AACA,SAAS,kBAAkB,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,aAAa;IACnF,MAAM,EAAE,GAAG,OAAO,EAAE,GAAG,OAAO,EAAE,GAAG,WAAW,SAAS,CAAC,gBAAgB;IACxE,MAAM,kBAAkB,kBAAkB;IAC1C,MAAM,qBAAqB,0BAA0B,WAAW;IAChE,MAAM,kBAAkB,mBAAmB,UAAU,MAAM,IACrD,cAAc,oBAAoB,UAAU,MAAM,EAAE,mBACpD;IACN,IAAI,mBAAmB,cAAc;QAAE,GAAG,UAAU,gBAAgB,CAAC;QAAE,GAAG,UAAU,gBAAgB,CAAC;IAAC,GAAG,YAAY;IACrH,IAAI,UAAU,MAAM,KAAK,UAAU;QAC/B,mBAAmB,sBAAsB,kBAAkB,iBAAiB;IAChF;IACA,MAAM,SAAS,WAAW,WAAW;IACrC,MAAM,UAAU,WAAW,SAAS,CAAC,CAAC,IAAI;IAC1C,OAAO;QACH,GAAG,iBAAiB,CAAC;QACrB,GAAG,iBAAiB,CAAC;QACrB,GAAG,UAAU,SAAS,UAAU;IACpC;AACJ;AACA,SAAS,mBAAmB,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,aAAa;IAAC;IAAG;CAAE;IAC/E,MAAM,UAAU,EAAE;IAClB,MAAM,mBAAmB,IAAI;IAC7B,8EAA8E;IAC9E,KAAK,MAAM,SAAS,SAAU;QAC1B,MAAM,SAAS,WAAW,GAAG,CAAC,MAAM,QAAQ;QAC5C,IAAI,CAAC,QAAQ;YACT;QACJ;QACA,MAAM,aAAa,iBAAiB,GAAG,CAAC,MAAM,QAAQ,GAAG,gBAAgB,WAAW;QACpF,MAAM,eAAe,iBAAiB,YAAY,MAAM,IAAI;QAC5D,iBAAiB,GAAG,CAAC,MAAM,QAAQ,EAAE;YAAE;YAAc;QAAO;IAChE;IACA,IAAI,iBAAiB,IAAI,GAAG,GAAG;QAC3B,iBAAiB,OAAO,CAAC,CAAC,EAAE,YAAY,EAAE,MAAM,EAAE,EAAE;YAChD,oDAAoD;YACpD,MAAM,mBAAmB,OAAO,SAAS,CAAC,gBAAgB;YAC1D,MAAM,aAAa,kBAAkB;YACrC,MAAM,SAAS,OAAO,MAAM,IAAI;YAChC,8DAA8D;YAC9D,MAAM,UAAU,aAAa,CAAC,GAAG,iBAAiB,CAAC,GAAG,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,iBAAiB,CAAC,GAAG,aAAa,CAAC,KAAK;YAClH,MAAM,UAAU,aAAa,CAAC,GAAG,iBAAiB,CAAC,GAAG,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,iBAAiB,CAAC,GAAG,aAAa,CAAC,KAAK;YAClH,MAAM,WAAW,KAAK,GAAG,CAAC,WAAW,KAAK,EAAE,KAAK,KAAK,CAAC,aAAa,KAAK;YACzE,MAAM,YAAY,KAAK,GAAG,CAAC,WAAW,MAAM,EAAE,KAAK,KAAK,CAAC,aAAa,MAAM;YAC5E,MAAM,cAAc,CAAC,WAAW,WAAW,KAAK,IAAI,MAAM,CAAC,EAAE;YAC7D,MAAM,eAAe,CAAC,YAAY,WAAW,MAAM,IAAI,MAAM,CAAC,EAAE;YAChE,gFAAgF;YAChF,IAAI,UAAU,KAAK,UAAU,KAAK,eAAe,cAAc;gBAC3D,QAAQ,IAAI,CAAC;oBACT,IAAI;oBACJ,MAAM;oBACN,UAAU;wBACN,GAAG,OAAO,QAAQ,CAAC,CAAC,GAAG,UAAU;wBACjC,GAAG,OAAO,QAAQ,CAAC,CAAC,GAAG,UAAU;oBACrC;gBACJ;gBACA;;;iBAGC,GACD,aAAa,GAAG,CAAC,WAAW,QAAQ,CAAC;oBACjC,IAAI,CAAC,SAAS,IAAI,CAAC,CAAC,QAAU,MAAM,EAAE,KAAK,UAAU,EAAE,GAAG;wBACtD,QAAQ,IAAI,CAAC;4BACT,IAAI,UAAU,EAAE;4BAChB,MAAM;4BACN,UAAU;gCACN,GAAG,UAAU,QAAQ,CAAC,CAAC,GAAG;gCAC1B,GAAG,UAAU,QAAQ,CAAC,CAAC,GAAG;4BAC9B;wBACJ;oBACJ;gBACJ;YACJ;YACA,kFAAkF;YAClF,IAAI,WAAW,KAAK,GAAG,aAAa,KAAK,IAAI,WAAW,MAAM,GAAG,aAAa,MAAM,IAAI,WAAW,SAAS;gBACxG,QAAQ,IAAI,CAAC;oBACT,IAAI;oBACJ,MAAM;oBACN,eAAe;oBACf,YAAY;wBACR,OAAO,WAAW,CAAC,UAAU,MAAM,CAAC,EAAE,GAAG,UAAU,cAAc,CAAC;wBAClE,QAAQ,YAAY,CAAC,UAAU,MAAM,CAAC,EAAE,GAAG,UAAU,eAAe,CAAC;oBACzE;gBACJ;YACJ;QACJ;IACJ;IACA,OAAO;AACX;AACA,SAAS,oBAAoB,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU;IAC3F,MAAM,eAAe,SAAS,cAAc;IAC5C,IAAI,mBAAmB;IACvB,IAAI,CAAC,cAAc;QACf,OAAO;YAAE,SAAS,EAAE;YAAE;QAAiB;IAC3C;IACA,MAAM,UAAU,EAAE;IAClB,MAAM,QAAQ,OAAO,gBAAgB,CAAC;IACtC,MAAM,EAAE,KAAK,IAAI,EAAE,GAAG,IAAI,OAAO,iBAAiB,CAAC,MAAM,SAAS;IAClE,qFAAqF;IACrF,MAAM,uBAAuB,EAAE;IAC/B,KAAK,MAAM,UAAU,QAAQ,MAAM,GAAI;QACnC,MAAM,OAAO,WAAW,GAAG,CAAC,OAAO,EAAE;QACrC,IAAI,CAAC,MAAM;YACP;QACJ;QACA,IAAI,KAAK,MAAM,EAAE;YACb,WAAW,GAAG,CAAC,KAAK,EAAE,EAAE;gBACpB,GAAG,IAAI;gBACP,WAAW;oBACP,GAAG,KAAK,SAAS;oBACjB,cAAc;gBAClB;YACJ;YACA,mBAAmB;YACnB;QACJ;QACA,MAAM,aAAa,cAAc,OAAO,WAAW;QACnD,MAAM,mBAAmB,KAAK,QAAQ,CAAC,KAAK,KAAK,WAAW,KAAK,IAAI,KAAK,QAAQ,CAAC,MAAM,KAAK,WAAW,MAAM;QAC/G,MAAM,WAAW,CAAC,CAAC,CAAC,WAAW,KAAK,IAChC,WAAW,MAAM,IACjB,CAAC,oBAAoB,CAAC,KAAK,SAAS,CAAC,YAAY,IAAI,OAAO,KAAK,CAAC;QACtE,IAAI,UAAU;YACV,MAAM,aAAa,OAAO,WAAW,CAAC,qBAAqB;YAC3D,MAAM,SAAS,mBAAmB,KAAK,MAAM,IAAI,KAAK,MAAM,GAAG;YAC/D,IAAI,EAAE,gBAAgB,EAAE,GAAG,KAAK,SAAS;YACzC,IAAI,KAAK,QAAQ,IAAI,KAAK,MAAM,KAAK,UAAU;gBAC3C,mBAAmB,sBAAsB,kBAAkB,YAAY,WAAW,GAAG,CAAC,KAAK,QAAQ;YACvG,OACK,IAAI,QAAQ;gBACb,mBAAmB,cAAc,kBAAkB,QAAQ;YAC/D;YACA,MAAM,UAAU;gBACZ,GAAG,IAAI;gBACP,UAAU;gBACV,WAAW;oBACP,GAAG,KAAK,SAAS;oBACjB;oBACA,cAAc;wBACV,QAAQ,gBAAgB,UAAU,OAAO,WAAW,EAAE,YAAY,MAAM,KAAK,EAAE;wBAC/E,QAAQ,gBAAgB,UAAU,OAAO,WAAW,EAAE,YAAY,MAAM,KAAK,EAAE;oBACnF;gBACJ;YACJ;YACA,WAAW,GAAG,CAAC,KAAK,EAAE,EAAE;YACxB,IAAI,KAAK,QAAQ,EAAE;gBACf,gBAAgB,SAAS,YAAY,cAAc;oBAAE;gBAAW;YACpE;YACA,mBAAmB;YACnB,IAAI,kBAAkB;gBAClB,QAAQ,IAAI,CAAC;oBACT,IAAI,KAAK,EAAE;oBACX,MAAM;oBACN;gBACJ;gBACA,IAAI,KAAK,YAAY,IAAI,KAAK,QAAQ,EAAE;oBACpC,qBAAqB,IAAI,CAAC;wBACtB,IAAI,KAAK,EAAE;wBACX,UAAU,KAAK,QAAQ;wBACvB,MAAM,WAAW,SAAS;oBAC9B;gBACJ;YACJ;QACJ;IACJ;IACA,IAAI,qBAAqB,MAAM,GAAG,GAAG;QACjC,MAAM,sBAAsB,mBAAmB,sBAAsB,YAAY,cAAc;QAC/F,QAAQ,IAAI,IAAI;IACpB;IACA,OAAO;QAAE;QAAS;IAAiB;AACvC;AACA,eAAe,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,eAAe,EAAE,KAAK,EAAE,MAAM,EAAG;IAC/E,IAAI,CAAC,WAAY,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAG;QACpC,OAAO,QAAQ,OAAO,CAAC;IAC3B;IACA,MAAM,eAAe,MAAM,QAAQ,sBAAsB,CAAC;QACtD,GAAG,SAAS,CAAC,EAAE,GAAG,MAAM,CAAC;QACzB,GAAG,SAAS,CAAC,EAAE,GAAG,MAAM,CAAC;QACzB,MAAM,SAAS,CAAC,EAAE;IACtB,GAAG;QACC;YAAC;YAAG;SAAE;QACN;YAAC;YAAO;SAAO;KAClB,EAAE;IACH,MAAM,mBAAmB,CAAC,CAAC,gBACvB,CAAC,aAAa,CAAC,KAAK,SAAS,CAAC,EAAE,IAAI,aAAa,CAAC,KAAK,SAAS,CAAC,EAAE,IAAI,aAAa,CAAC,KAAK,SAAS,CAAC,EAAE;IAC1G,OAAO,QAAQ,OAAO,CAAC;AAC3B;AACA;;;;;;;;;CASC,GACD,SAAS,sBAAsB,IAAI,EAAE,UAAU,EAAE,aAAa,EAAE,gBAAgB,EAAE,MAAM,EAAE,QAAQ;IAC9F;;;;KAIC,GACD,IAAI,MAAM;IACV,MAAM,UAAU,iBAAiB,GAAG,CAAC,QAAQ,IAAI;IACjD,iBAAiB,GAAG,CAAC,KAAK,QAAQ,GAAG,CAAC,eAAe;IACrD,MAAM,GAAG,OAAO,CAAC,EAAE,MAAM;IACzB,MAAM,UAAU,iBAAiB,GAAG,CAAC,QAAQ,IAAI;IACjD,iBAAiB,GAAG,CAAC,KAAK,QAAQ,GAAG,CAAC,eAAe;IACrD,IAAI,UAAU;QACV,MAAM,GAAG,OAAO,CAAC,EAAE,KAAK,CAAC,EAAE,UAAU;QACrC,MAAM,YAAY,iBAAiB,GAAG,CAAC,QAAQ,IAAI;QACnD,iBAAiB,GAAG,CAAC,KAAK,UAAU,GAAG,CAAC,eAAe;IAC3D;AACJ;AACA,SAAS,uBAAuB,gBAAgB,EAAE,UAAU,EAAE,KAAK;IAC/D,iBAAiB,KAAK;IACtB,WAAW,KAAK;IAChB,KAAK,MAAM,QAAQ,MAAO;QACtB,MAAM,EAAE,QAAQ,UAAU,EAAE,QAAQ,UAAU,EAAE,eAAe,IAAI,EAAE,eAAe,IAAI,EAAE,GAAG;QAC7F,MAAM,aAAa;YAAE,QAAQ,KAAK,EAAE;YAAE,QAAQ;YAAY,QAAQ;YAAY;YAAc;QAAa;QACzG,MAAM,YAAY,GAAG,WAAW,CAAC,EAAE,aAAa,EAAE,EAAE,WAAW,CAAC,EAAE,cAAc;QAChF,MAAM,YAAY,GAAG,WAAW,CAAC,EAAE,aAAa,EAAE,EAAE,WAAW,CAAC,EAAE,cAAc;QAChF,sBAAsB,UAAU,YAAY,WAAW,kBAAkB,YAAY;QACrF,sBAAsB,UAAU,YAAY,WAAW,kBAAkB,YAAY;QACrF,WAAW,GAAG,CAAC,KAAK,EAAE,EAAE;IAC5B;AACJ;AAEA,SAAS,gBAAgB,CAAC,EAAE,CAAC;IACzB,IAAI,MAAM,QAAQ,MAAM,MAAM;QAC1B,OAAO;IACX;IACA,MAAM,KAAK,MAAM,OAAO,CAAC,KAAK,IAAI;QAAC;KAAE;IACrC,MAAM,KAAK,MAAM,OAAO,CAAC,KAAK,IAAI;QAAC;KAAE;IACrC,IAAI,GAAG,MAAM,KAAK,GAAG,MAAM,EAAE;QACzB,OAAO;IACX;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,MAAM,EAAE,IAAK;QAChC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,GAAG;YAC1F,OAAO;QACX;IACJ;IACA,OAAO;AACX;AAEA,SAAS,iBAAiB,IAAI,EAAE,UAAU;IACtC,IAAI,CAAC,KAAK,QAAQ,EAAE;QAChB,OAAO;IACX;IACA,MAAM,aAAa,WAAW,GAAG,CAAC,KAAK,QAAQ;IAC/C,IAAI,CAAC,YAAY;QACb,OAAO;IACX;IACA,IAAI,WAAW,QAAQ,EAAE;QACrB,OAAO;IACX;IACA,OAAO,iBAAiB,YAAY;AACxC;AACA,SAAS,YAAY,MAAM,EAAE,QAAQ,EAAE,OAAO;IAC1C,IAAI,UAAU;IACd,GAAG;QACC,IAAI,SAAS,UAAU,WACnB,OAAO;QACX,IAAI,YAAY,SACZ,OAAO;QACX,UAAU,SAAS;IACvB,QAAS,QAAS;IAClB,OAAO;AACX;AACA,2EAA2E;AAC3E,SAAS,aAAa,UAAU,EAAE,cAAc,EAAE,QAAQ,EAAE,MAAM;IAC9D,MAAM,YAAY,IAAI;IACtB,KAAK,MAAM,CAAC,IAAI,KAAK,IAAI,WAAY;QACjC,IAAI,CAAC,KAAK,QAAQ,IAAI,KAAK,EAAE,KAAK,MAAM,KACpC,CAAC,CAAC,KAAK,QAAQ,IAAI,CAAC,iBAAiB,MAAM,WAAW,KACtD,CAAC,KAAK,SAAS,IAAK,kBAAkB,OAAO,KAAK,SAAS,KAAK,WAAY,GAAG;YAC/E,MAAM,eAAe,WAAW,GAAG,CAAC;YACpC,IAAI,cAAc;gBACd,UAAU,GAAG,CAAC,IAAI;oBACd;oBACA,UAAU,aAAa,QAAQ,IAAI;wBAAE,GAAG;wBAAG,GAAG;oBAAE;oBAChD,UAAU;wBACN,GAAG,SAAS,CAAC,GAAG,aAAa,SAAS,CAAC,gBAAgB,CAAC,CAAC;wBACzD,GAAG,SAAS,CAAC,GAAG,aAAa,SAAS,CAAC,gBAAgB,CAAC,CAAC;oBAC7D;oBACA,QAAQ,aAAa,MAAM;oBAC3B,UAAU,aAAa,QAAQ;oBAC/B,QAAQ,aAAa,MAAM;oBAC3B,cAAc,aAAa,YAAY;oBACvC,WAAW;wBACP,kBAAkB,aAAa,SAAS,CAAC,gBAAgB,IAAI;4BAAE,GAAG;4BAAG,GAAG;wBAAE;oBAC9E;oBACA,UAAU;wBACN,OAAO,aAAa,QAAQ,CAAC,KAAK,IAAI;wBACtC,QAAQ,aAAa,QAAQ,CAAC,MAAM,IAAI;oBAC5C;gBACJ;YACJ;QACJ;IACJ;IACA,OAAO;AACX;AACA;;;;CAIC,GACD,SAAS,sBAAsB,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,IAAI,EAAG;IAC9E,MAAM,qBAAqB,EAAE;IAC7B,KAAK,MAAM,CAAC,IAAI,SAAS,IAAI,UAAW;QACpC,MAAM,OAAO,WAAW,GAAG,CAAC,KAAK,UAAU;QAC3C,IAAI,MAAM;YACN,mBAAmB,IAAI,CAAC;gBACpB,GAAG,IAAI;gBACP,UAAU,SAAS,QAAQ;gBAC3B;YACJ;QACJ;IACJ;IACA,IAAI,CAAC,QAAQ;QACT,OAAO;YAAC,kBAAkB,CAAC,EAAE;YAAE;SAAmB;IACtD;IACA,MAAM,OAAO,WAAW,GAAG,CAAC,SAAS,UAAU;IAC/C,OAAO;QACH,CAAC,OACK,kBAAkB,CAAC,EAAE,GACrB;YACE,GAAG,IAAI;YACP,UAAU,UAAU,GAAG,CAAC,SAAS,YAAY,KAAK,QAAQ;YAC1D;QACJ;QACJ;KACH;AACL;AAEA,8DAA8D;AAC9D,SAAS,OAAO,EAAE,eAAe,EAAE,aAAa,EAAE,WAAW,EAAE,MAAM,EAAE,UAAU,EAAG;IAChF,IAAI,UAAU;QAAE,GAAG;QAAM,GAAG;IAAK;IACjC,IAAI,YAAY;IAChB,IAAI,YAAY,IAAI;IACpB,IAAI,iBAAiB;IACrB,IAAI,gBAAgB;QAAE,GAAG;QAAG,GAAG;IAAE;IACjC,IAAI,kBAAkB;IACtB,IAAI,cAAc;IAClB,IAAI,cAAc;IAClB,IAAI,YAAY,OAAO,gDAAgD;IACvE,mBAAmB;IACnB,SAAS,OAAO,EAAE,eAAe,EAAE,cAAc,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,EAAE,oBAAoB,CAAC,EAAG;QACtG,cAAc,CAAA,GAAA,oPAAA,CAAA,SAAM,AAAD,EAAE;QACrB,SAAS,YAAY,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,SAAS;YACpC,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,EAAE,OAAO,EAAE,mBAAmB,EAAG,GAAG;YACjI,UAAU;gBAAE;gBAAG;YAAE;YACjB,IAAI,YAAY;YAChB,IAAI,WAAW;gBAAE,GAAG;gBAAG,GAAG;gBAAG,IAAI;gBAAG,IAAI;YAAE;YAC1C,IAAI,UAAU,IAAI,GAAG,KAAK,YAAY;gBAClC,MAAM,OAAO,uBAAuB;gBACpC,WAAW,UAAU;YACzB;YACA,KAAK,MAAM,CAAC,IAAI,SAAS,IAAI,UAAW;gBACpC,IAAI,CAAC,WAAW,GAAG,CAAC,KAAK;oBAKrB;gBACJ;gBACA,IAAI,eAAe;oBAAE,GAAG,IAAI,SAAS,QAAQ,CAAC,CAAC;oBAAE,GAAG,IAAI,SAAS,QAAQ,CAAC,CAAC;gBAAC;gBAC5E,IAAI,YAAY;oBACZ,eAAe,aAAa,cAAc;gBAC9C;gBACA;;;iBAGC,GACD,IAAI,qBAAqB;oBACrB;wBAAC,UAAU,CAAC,EAAE,CAAC,EAAE;wBAAE,UAAU,CAAC,EAAE,CAAC,EAAE;qBAAC;oBACpC;wBAAC,UAAU,CAAC,EAAE,CAAC,EAAE;wBAAE,UAAU,CAAC,EAAE,CAAC,EAAE;qBAAC;iBACvC;gBACD,IAAI,UAAU,IAAI,GAAG,KAAK,cAAc,CAAC,SAAS,MAAM,EAAE;oBACtD,MAAM,EAAE,gBAAgB,EAAE,GAAG,SAAS,SAAS;oBAC/C,MAAM,KAAK,iBAAiB,CAAC,GAAG,SAAS,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC,EAAE;oBAC7D,MAAM,KAAK,iBAAiB,CAAC,GAAG,SAAS,QAAQ,CAAC,KAAK,GAAG,SAAS,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC,EAAE;oBACxF,MAAM,KAAK,iBAAiB,CAAC,GAAG,SAAS,CAAC,GAAG,UAAU,CAAC,EAAE,CAAC,EAAE;oBAC7D,MAAM,KAAK,iBAAiB,CAAC,GAAG,SAAS,QAAQ,CAAC,MAAM,GAAG,SAAS,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC,EAAE;oBACzF,qBAAqB;wBACjB;4BAAC;4BAAI;yBAAG;wBACR;4BAAC;4BAAI;yBAAG;qBACX;gBACL;gBACA,MAAM,EAAE,QAAQ,EAAE,gBAAgB,EAAE,GAAG,sBAAsB;oBACzD,QAAQ;oBACR;oBACA;oBACA,YAAY;oBACZ;oBACA;gBACJ;gBACA,+EAA+E;gBAC/E,YAAY,aAAa,SAAS,QAAQ,CAAC,CAAC,KAAK,SAAS,CAAC,IAAI,SAAS,QAAQ,CAAC,CAAC,KAAK,SAAS,CAAC;gBACjG,SAAS,QAAQ,GAAG;gBACpB,SAAS,SAAS,CAAC,gBAAgB,GAAG;YAC1C;YACA,IAAI,CAAC,WAAW;gBACZ;YACJ;YACA,oBAAoB,WAAW;YAC/B,IAAI,aAAa,CAAC,UAAU,cAAe,CAAC,UAAU,eAAgB,GAAG;gBACrE,MAAM,CAAC,aAAa,aAAa,GAAG,sBAAsB;oBACtD;oBACA;oBACA;gBACJ;gBACA,SAAS,WAAW,WAAW,aAAa;gBAC5C,aAAa,WAAW,aAAa;gBACrC,IAAI,CAAC,QAAQ;oBACT,kBAAkB,WAAW;gBACjC;YACJ;QACJ;QACA,eAAe;YACX,IAAI,CAAC,iBAAiB;gBAClB;YACJ;YACA,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,YAAY,EAAE,iBAAiB,EAAE,GAAG;YAC9D,IAAI,CAAC,mBAAmB;gBACpB,iBAAiB;gBACjB,qBAAqB;gBACrB;YACJ;YACA,MAAM,CAAC,WAAW,UAAU,GAAG,YAAY,eAAe,iBAAiB;YAC3E,IAAI,cAAc,KAAK,cAAc,GAAG;gBACpC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,YAAY,SAAS,CAAC,EAAE;gBACvD,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,YAAY,SAAS,CAAC,EAAE;gBACvD,IAAI,MAAM,MAAM;oBAAE,GAAG;oBAAW,GAAG;gBAAU,IAAI;oBAC7C,YAAY,SAAS;gBACzB;YACJ;YACA,YAAY,sBAAsB;QACtC;QACA,SAAS,UAAU,KAAK;YACpB,MAAM,EAAE,UAAU,EAAE,oBAAoB,EAAE,cAAc,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,iBAAiB,EAAE,eAAe,EAAE,oBAAoB,EAAE,qBAAqB,EAAG,GAAG;YAChL,cAAc;YACd,IAAI,CAAC,CAAC,qBAAqB,CAAC,YAAY,KAAK,CAAC,wBAAwB,QAAQ;gBAC1E,IAAI,CAAC,WAAW,GAAG,CAAC,SAAS,UAAU;oBACnC,+DAA+D;oBAC/D;gBACJ;YACJ;YACA,IAAI,gBAAgB,qBAAqB,QAAQ;gBAC7C,kBAAkB;YACtB;YACA,MAAM,aAAa,mBAAmB,MAAM,WAAW,EAAE;gBAAE;gBAAW;gBAAU;gBAAY;YAAgB;YAC5G,UAAU;YACV,YAAY,aAAa,YAAY,gBAAgB,YAAY;YACjE,IAAI,UAAU,IAAI,GAAG,KAAK,CAAC,eAAe,mBAAoB,CAAC,UAAU,oBAAqB,GAAG;gBAC7F,MAAM,CAAC,aAAa,aAAa,GAAG,sBAAsB;oBACtD;oBACA;oBACA;gBACJ;gBACA,cAAc,MAAM,WAAW,EAAE,WAAW,aAAa;gBACzD,kBAAkB,MAAM,WAAW,EAAE,aAAa;gBAClD,IAAI,CAAC,QAAQ;oBACT,uBAAuB,MAAM,WAAW,EAAE;gBAC9C;YACJ;QACJ;QACA,MAAM,iBAAiB,CAAA,GAAA,sOAAA,CAAA,OAAI,AAAD,IACrB,aAAa,CAAC,mBACd,EAAE,CAAC,SAAS,CAAC;YACd,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG;YACxE,kBAAkB,SAAS,2BAA2B;YACtD,YAAY;YACZ,IAAI,sBAAsB,GAAG;gBACzB,UAAU;YACd;YACA,MAAM,aAAa,mBAAmB,MAAM,WAAW,EAAE;gBAAE;gBAAW;gBAAU;gBAAY;YAAgB;YAC5G,UAAU;YACV,gBAAgB,iBAAiB,MAAM,WAAW,EAAE;QACxD,GACK,EAAE,CAAC,QAAQ,CAAC;YACb,MAAM,EAAE,iBAAiB,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,iBAAiB,EAAE,UAAU,EAAE,GAAG;YAC9F,MAAM,aAAa,mBAAmB,MAAM,WAAW,EAAE;gBAAE;gBAAW;gBAAU;gBAAY;YAAgB;YAC5G,IAAI,AAAC,MAAM,WAAW,CAAC,IAAI,KAAK,eAAe,MAAM,WAAW,CAAC,OAAO,CAAC,MAAM,GAAG,KAE7E,UAAU,CAAC,WAAW,GAAG,CAAC,SAAU;gBACrC,YAAY;YAChB;YACA,IAAI,WAAW;gBACX;YACJ;YACA,IAAI,CAAC,kBAAkB,qBAAqB,aAAa;gBACrD,iBAAiB;gBACjB;YACJ;YACA,IAAI,CAAC,aAAa;gBACd,MAAM,IAAI,WAAW,QAAQ,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC;gBAC/C,MAAM,IAAI,WAAW,QAAQ,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC;gBAC/C,MAAM,WAAW,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI;gBACvC,IAAI,WAAW,mBAAmB;oBAC9B,UAAU;gBACd;YACJ;YACA,+BAA+B;YAC/B,IAAI,CAAC,QAAQ,CAAC,KAAK,WAAW,QAAQ,IAAI,QAAQ,CAAC,KAAK,WAAW,QAAQ,KAAK,aAAa,aAAa;gBACtG,+CAA+C;gBAC/C,gBAAgB,iBAAiB,MAAM,WAAW,EAAE;gBACpD,YAAY,YAAY,MAAM,WAAW;YAC7C;QACJ,GACK,EAAE,CAAC,OAAO,CAAC;YACZ,IAAI,CAAC,eAAe,WAAW;gBAC3B;YACJ;YACA,iBAAiB;YACjB,cAAc;YACd,qBAAqB;YACrB,IAAI,UAAU,IAAI,GAAG,GAAG;gBACpB,MAAM,EAAE,UAAU,EAAE,mBAAmB,EAAE,cAAc,EAAE,mBAAmB,EAAE,GAAG;gBACjF,oBAAoB,WAAW;gBAC/B,IAAI,cAAc,kBAAmB,CAAC,UAAU,qBAAsB;oBAClE,MAAM,CAAC,aAAa,aAAa,GAAG,sBAAsB;wBACtD;wBACA;wBACA;wBACA,UAAU;oBACd;oBACA,aAAa,MAAM,WAAW,EAAE,WAAW,aAAa;oBACxD,iBAAiB,MAAM,WAAW,EAAE,aAAa;oBACjD,IAAI,CAAC,QAAQ;wBACT,sBAAsB,MAAM,WAAW,EAAE;oBAC7C;gBACJ;YACJ;QACJ,GACK,MAAM,CAAC,CAAC;YACT,MAAM,SAAS,MAAM,MAAM;YAC3B,MAAM,cAAc,CAAC,MAAM,MAAM,IAC7B,CAAC,CAAC,mBAAmB,CAAC,YAAY,QAAQ,CAAC,CAAC,EAAE,iBAAiB,EAAE,QAAQ,KACzE,CAAC,CAAC,kBAAkB,YAAY,QAAQ,gBAAgB,QAAQ;YACpE,OAAO;QACX;QACA,YAAY,IAAI,CAAC;IACrB;IACA,SAAS;QACL,aAAa,GAAG,SAAS;IAC7B;IACA,OAAO;QACH;QACA;IACJ;AACJ;AAEA,SAAS,uBAAuB,QAAQ,EAAE,UAAU,EAAE,QAAQ;IAC1D,MAAM,QAAQ,EAAE;IAChB,MAAM,OAAO;QACT,GAAG,SAAS,CAAC,GAAG;QAChB,GAAG,SAAS,CAAC,GAAG;QAChB,OAAO,WAAW;QAClB,QAAQ,WAAW;IACvB;IACA,KAAK,MAAM,QAAQ,WAAW,MAAM,GAAI;QACpC,IAAI,mBAAmB,MAAM,WAAW,SAAS,GAAG;YAChD,MAAM,IAAI,CAAC;QACf;IACJ;IACA,OAAO;AACX;AACA;;;CAGC,GACD,MAAM,sBAAsB;AAC5B,SAAS,iBAAiB,QAAQ,EAAE,gBAAgB,EAAE,UAAU,EAAE,UAAU;IACxE,IAAI,iBAAiB,EAAE;IACvB,IAAI,cAAc;IAClB,MAAM,aAAa,uBAAuB,UAAU,YAAY,mBAAmB;IACnF,KAAK,MAAM,QAAQ,WAAY;QAC3B,MAAM,aAAa;eAAK,KAAK,SAAS,CAAC,YAAY,EAAE,UAAU,EAAE;eAAO,KAAK,SAAS,CAAC,YAAY,EAAE,UAAU,EAAE;SAAE;QACnH,KAAK,MAAM,UAAU,WAAY;YAC7B,yDAAyD;YACzD,IAAI,WAAW,MAAM,KAAK,OAAO,MAAM,IAAI,WAAW,IAAI,KAAK,OAAO,IAAI,IAAI,WAAW,EAAE,KAAK,OAAO,EAAE,EAAE;gBACvG;YACJ;YACA,4CAA4C;YAC5C,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,EAAE;YAClE,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,SAAS,CAAC,EAAE,KAAK,KAAK,GAAG,CAAC,IAAI,SAAS,CAAC,EAAE;YAClF,IAAI,WAAW,kBAAkB;gBAC7B;YACJ;YACA,IAAI,WAAW,aAAa;gBACxB,iBAAiB;oBAAC;wBAAE,GAAG,MAAM;wBAAE;wBAAG;oBAAE;iBAAE;gBACtC,cAAc;YAClB,OACK,IAAI,aAAa,aAAa;gBAC/B,wEAAwE;gBACxE,eAAe,IAAI,CAAC;oBAAE,GAAG,MAAM;oBAAE;oBAAG;gBAAE;YAC1C;QACJ;IACJ;IACA,IAAI,CAAC,eAAe,MAAM,EAAE;QACxB,OAAO;IACX;IACA,yEAAyE;IACzE,IAAI,eAAe,MAAM,GAAG,GAAG;QAC3B,MAAM,qBAAqB,WAAW,IAAI,KAAK,WAAW,WAAW;QACrE,OAAO,eAAe,IAAI,CAAC,CAAC,SAAW,OAAO,IAAI,KAAK,uBAAuB,cAAc,CAAC,EAAE;IACnG;IACA,OAAO,cAAc,CAAC,EAAE;AAC5B;AACA,SAAS,UAAU,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,cAAc,EAAE,uBAAuB,KAAK;IACrG,MAAM,OAAO,WAAW,GAAG,CAAC;IAC5B,IAAI,CAAC,MAAM;QACP,OAAO;IACX;IACA,MAAM,UAAU,mBAAmB,WAC7B,KAAK,SAAS,CAAC,YAAY,EAAE,CAAC,WAAW,GACzC;WAAK,KAAK,SAAS,CAAC,YAAY,EAAE,UAAU,EAAE;WAAO,KAAK,SAAS,CAAC,YAAY,EAAE,UAAU,EAAE;KAAE;IACtG,MAAM,SAAS,CAAC,WAAW,SAAS,KAAK,CAAC,IAAM,EAAE,EAAE,KAAK,YAAY,SAAS,CAAC,EAAE,KAAK;IACtF,OAAO,UAAU,uBACX;QAAE,GAAG,MAAM;QAAE,GAAG,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,EAAE,KAAK;IAAC,IACvE;AACV;AACA,SAAS,cAAc,eAAe,EAAE,aAAa;IACjD,IAAI,iBAAiB;QACjB,OAAO;IACX,OACK,IAAI,eAAe,UAAU,SAAS,WAAW;QAClD,OAAO;IACX,OACK,IAAI,eAAe,UAAU,SAAS,WAAW;QAClD,OAAO;IACX;IACA,OAAO;AACX;AACA,SAAS,kBAAkB,wBAAwB,EAAE,aAAa;IAC9D,IAAI,UAAU;IACd,IAAI,eAAe;QACf,UAAU;IACd,OACK,IAAI,4BAA4B,CAAC,eAAe;QACjD,UAAU;IACd;IACA,OAAO;AACX;AAEA,MAAM,cAAc,IAAM;AAC1B,SAAS,cAAc,KAAK,EAAE,EAAE,cAAc,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,EAAE,eAAe,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,EAAE,gBAAgB,EAAE,MAAM,EAAE,KAAK,EAAE,gBAAgB,EAAE,cAAc,EAAE,SAAS,EAAE,YAAY,EAAE,oBAAoB,WAAW,EAAE,cAAc,EAAE,gBAAgB,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAG;IAChV,iEAAiE;IACjE,MAAM,MAAM,kBAAkB,MAAM,MAAM;IAC1C,IAAI,YAAY;IAChB,IAAI;IACJ,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,iBAAiB;IAClC,MAAM,gBAAgB,KAAK,iBAAiB,GAAG;IAC/C,MAAM,aAAa,cAAc,iBAAiB;IAClD,MAAM,kBAAkB,SAAS;IACjC,IAAI,CAAC,mBAAmB,CAAC,YAAY;QACjC;IACJ;IACA,MAAM,qBAAqB,UAAU,QAAQ,YAAY,UAAU,YAAY;IAC/E,IAAI,CAAC,oBAAoB;QACrB;IACJ;IACA,IAAI,WAAW,iBAAiB,OAAO;IACvC,IAAI,iBAAiB;IACrB,IAAI,aAAa;IACjB,IAAI,UAAU;IACd,IAAI,gBAAgB;IACpB,wGAAwG;IACxG,SAAS;QACL,IAAI,CAAC,oBAAoB,CAAC,iBAAiB;YACvC;QACJ;QACA,MAAM,CAAC,GAAG,EAAE,GAAG,YAAY,UAAU,iBAAiB;QACtD,MAAM;YAAE;YAAG;QAAE;QACb,YAAY,sBAAsB;IACtC;IACA,wDAAwD;IACxD,MAAM,aAAa;QACf,GAAG,kBAAkB;QACrB;QACA,MAAM;QACN,UAAU,mBAAmB,QAAQ;IACzC;IACA,MAAM,mBAAmB,WAAW,GAAG,CAAC;IACxC,MAAM,OAAO,kBAAkB,kBAAkB,YAAY,SAAS,IAAI,EAAE;IAC5E,MAAM,gBAAgB;QAClB,YAAY;QACZ,SAAS;QACT;QACA;QACA,cAAc,WAAW,QAAQ;QACjC,UAAU;QACV,IAAI;QACJ,UAAU;QACV,YAAY,gBAAgB,CAAC,WAAW,QAAQ,CAAC;QACjD,QAAQ;IACZ;IACA,iBAAiB;IACjB,IAAI,qBAAqB;IACzB,iBAAiB,OAAO;QAAE;QAAQ;QAAU;IAAW;IACvD,SAAS,cAAc,KAAK;QACxB,IAAI,CAAC,mBAAmB,CAAC,YAAY;YACjC,YAAY;YACZ;QACJ;QACA,MAAM,YAAY;QAClB,WAAW,iBAAiB,OAAO;QACnC,gBAAgB,iBAAiB,qBAAqB,UAAU,WAAW,OAAO;YAAC;YAAG;SAAE,GAAG,kBAAkB,YAAY;QACzH,IAAI,CAAC,gBAAgB;YACjB;YACA,iBAAiB;QACrB;QACA,MAAM,SAAS,cAAc,OAAO;YAChC,QAAQ;YACR;YACA,YAAY;YACZ,cAAc;YACd,UAAU,WAAW,WAAW;YAChC;YACA;YACA;YACA;YACA;QACJ;QACA,gBAAgB,OAAO,aAAa;QACpC,aAAa,OAAO,UAAU;QAC9B,UAAU,kBAAkB,CAAC,CAAC,eAAe,OAAO,OAAO;QAC3D,MAAM,gBAAgB;YAClB,sBAAsB;YACtB,GAAG,kBAAkB;YACrB;YACA,IAAI,iBAAiB,UACf,qBAAqB;gBAAE,GAAG,cAAc,CAAC;gBAAE,GAAG,cAAc,CAAC;YAAC,GAAG,aACjE;YACN,UAAU,OAAO,QAAQ;YACzB,YAAY,WAAW,OAAO,QAAQ,GAAG,OAAO,QAAQ,CAAC,QAAQ,GAAG,gBAAgB,CAAC,WAAW,QAAQ,CAAC;YACzG,QAAQ,OAAO,QAAQ,GAAG,WAAW,GAAG,CAAC,OAAO,QAAQ,CAAC,MAAM,IAAI;QACvE;QACA;;;SAGC,GACD,IAAI,WACA,iBACA,mBAAmB,QAAQ,IAC3B,cAAc,QAAQ,IACtB,mBAAmB,QAAQ,CAAC,IAAI,KAAK,cAAc,QAAQ,CAAC,IAAI,IAChE,mBAAmB,QAAQ,CAAC,MAAM,KAAK,cAAc,QAAQ,CAAC,MAAM,IACpE,mBAAmB,QAAQ,CAAC,EAAE,KAAK,cAAc,QAAQ,CAAC,EAAE,IAC5D,mBAAmB,EAAE,CAAC,CAAC,KAAK,cAAc,EAAE,CAAC,CAAC,IAC9C,mBAAmB,EAAE,CAAC,CAAC,KAAK,cAAc,EAAE,CAAC,CAAC,EAAE;YAChD;QACJ;QACA,iBAAiB;QACjB,qBAAqB;IACzB;IACA,SAAS,YAAY,KAAK;QACtB,IAAI,CAAC,iBAAiB,aAAa,KAAK,cAAc,SAAS;YAC3D,YAAY;QAChB;QACA;;;SAGC,GACD,6DAA6D;QAC7D,MAAM,EAAE,UAAU,EAAE,GAAG,iBAAiB,GAAG;QAC3C,MAAM,uBAAuB;YACzB,GAAG,eAAe;YAClB,YAAY,mBAAmB,QAAQ,GAAG,mBAAmB,UAAU,GAAG;QAC9E;QACA,eAAe,OAAO;QACtB,IAAI,iBAAiB;YACjB,iBAAiB,OAAO;QAC5B;QACA;QACA,qBAAqB;QACrB,iBAAiB;QACjB,UAAU;QACV,aAAa;QACb,gBAAgB;QAChB,IAAI,mBAAmB,CAAC,aAAa;QACrC,IAAI,mBAAmB,CAAC,WAAW;QACnC,IAAI,mBAAmB,CAAC,aAAa;QACrC,IAAI,mBAAmB,CAAC,YAAY;IACxC;IACA,IAAI,gBAAgB,CAAC,aAAa;IAClC,IAAI,gBAAgB,CAAC,WAAW;IAChC,IAAI,gBAAgB,CAAC,aAAa;IAClC,IAAI,gBAAgB,CAAC,YAAY;AACrC;AACA,qFAAqF;AACrF,SAAS,cAAc,KAAK,EAAE,EAAE,MAAM,EAAE,cAAc,EAAE,UAAU,EAAE,YAAY,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,oBAAoB,WAAW,EAAE,UAAU,EAAG;IACxJ,MAAM,WAAW,aAAa;IAC9B,MAAM,gBAAgB,SAChB,IAAI,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,uBAAuB,EAAE,OAAO,CAAC,EAAE,QAAQ,OAAO,CAAC,EAAE,QAAQ,GAAG,CAAC,EAAE,QAAQ,KAAK,EAAE,CAAC,IAC7G;IACN,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,iBAAiB;IAClC,MAAM,cAAc,IAAI,gBAAgB,CAAC,GAAG;IAC5C;;;KAGC,GACD,MAAM,gBAAgB,aAAa,UAAU,SAAS,GAAG,IAAI,aAAa,CAAC,IAAI,cAAc;IAC7F,MAAM,SAAS;QACX,eAAe;QACf,SAAS;QACT,YAAY;QACZ,UAAU;IACd;IACA,IAAI,eAAe;QACf,MAAM,aAAa,cAAc,WAAW;QAC5C,MAAM,eAAe,cAAc,YAAY,CAAC;QAChD,MAAM,WAAW,cAAc,YAAY,CAAC;QAC5C,MAAM,cAAc,cAAc,SAAS,CAAC,QAAQ,CAAC;QACrD,MAAM,iBAAiB,cAAc,SAAS,CAAC,QAAQ,CAAC;QACxD,IAAI,CAAC,gBAAgB,CAAC,YAAY;YAC9B,OAAO;QACX;QACA,MAAM,aAAa;YACf,QAAQ,WAAW,eAAe;YAClC,cAAc,WAAW,WAAW;YACpC,QAAQ,WAAW,aAAa;YAChC,cAAc,WAAW,eAAe;QAC5C;QACA,OAAO,UAAU,GAAG;QACpB,MAAM,gBAAgB,eAAe;QACrC,iFAAiF;QACjF,MAAM,UAAU,iBACZ,CAAC,mBAAmB,eAAe,MAAM,GACnC,AAAC,YAAY,eAAe,YAAc,CAAC,YAAY,eAAe,WACtE,iBAAiB,cAAc,aAAa,YAAY;QAClE,OAAO,OAAO,GAAG,WAAW,kBAAkB;QAC9C,OAAO,QAAQ,GAAG,UAAU,cAAc,YAAY,UAAU,YAAY,gBAAgB;IAChG;IACA,OAAO;AACX;AACA,MAAM,WAAW;IACb;IACA,SAAS;AACb;AAEA,SAAS,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE;IAC/D,MAAM,YAAY,CAAA,GAAA,oPAAA,CAAA,SAAM,AAAD,EAAE;IACzB,SAAS,OAAO,EAAE,eAAe,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,WAAW,IAAI,EAAE,WAAW,IAAI,EAAE,aAAa,KAAK,EAAG;QACpH,8DAA8D;QAC9D,MAAM,cAAc,CAAC;YACjB,MAAM,YAAY;YAClB,IAAI,MAAM,WAAW,CAAC,IAAI,KAAK,WAAW,CAAC,SAAS;gBAChD;YACJ;YACA,MAAM,aAAa,CAAC,MAAM,WAAW,CAAC,MAAM,GACxC,CAAC,MAAM,WAAW,CAAC,SAAS,KAAK,IAAI,OAAO,MAAM,WAAW,CAAC,SAAS,GAAG,IAAI,KAAK,IACnF;YACJ,MAAM,WAAW,SAAS,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG;YAC5C,QAAQ,OAAO,CAAC;QACpB;QACA,IAAI,WAAW;YAAC;YAAG;SAAE;QACrB,8DAA8D;QAC9D,MAAM,kBAAkB,CAAC;YACrB,IAAI,MAAM,WAAW,CAAC,IAAI,KAAK,eAAe,MAAM,WAAW,CAAC,IAAI,KAAK,cAAc;gBACnF,WAAW;oBACP,MAAM,WAAW,CAAC,OAAO,IAAI,MAAM,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO;oBACjE,MAAM,WAAW,CAAC,OAAO,IAAI,MAAM,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO;iBACpE;YACL;QACJ;QACA,8DAA8D;QAC9D,MAAM,aAAa,CAAC;YAChB,MAAM,YAAY;YAClB,IAAI,AAAC,MAAM,WAAW,CAAC,IAAI,KAAK,eAAe,MAAM,WAAW,CAAC,IAAI,KAAK,eAAgB,CAAC,SAAS;gBAChG;YACJ;YACA,MAAM,aAAa;gBACf,MAAM,WAAW,CAAC,OAAO,IAAI,MAAM,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO;gBACjE,MAAM,WAAW,CAAC,OAAO,IAAI,MAAM,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO;aACpE;YACD,MAAM,WAAW;gBAAC,UAAU,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE;gBAAE,UAAU,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE;aAAC;YAC3E,WAAW;YACX,MAAM,YAAY,iBAAiB,KAAK,GAAG,CAAC,SAAS,CAAC,EAAE,EAAE,KAAK,GAAG,CAAC,SAAS,CAAC,EAAE,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC;YACxG,MAAM,WAAW;gBACb,GAAG,SAAS,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,GAAG;gBAChC,GAAG,SAAS,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,GAAG;YACpC;YACA,MAAM,SAAS;gBACX;oBAAC;oBAAG;iBAAE;gBACN;oBAAC;oBAAO;iBAAO;aAClB;YACD,QAAQ,sBAAsB,CAAC;gBAC3B,GAAG,SAAS,CAAC;gBACb,GAAG,SAAS,CAAC;gBACb,MAAM,SAAS,CAAC,EAAE;YACtB,GAAG,QAAQ;QACf;QACA,MAAM,oBAAoB,CAAA,GAAA,sOAAA,CAAA,OAAI,AAAD,IACxB,EAAE,CAAC,SAAS,gBACb,6DAA6D;QAC7D,aAAa;SACZ,EAAE,CAAC,QAAQ,WAAW,aAAa,KACpC,6DAA6D;QAC7D,aAAa;SACZ,EAAE,CAAC,cAAc,WAAW,cAAc;QAC/C,UAAU,IAAI,CAAC,mBAAmB,CAAC;IACvC;IACA,SAAS;QACL,UAAU,EAAE,CAAC,QAAQ;IACzB;IACA,OAAO;QACH;QACA;QACA,SAAA,sPAAA,CAAA,UAAO;IACX;AACJ;AAEA,qDAAqD,GACrD,MAAM,cAAc,CAAC,cAAc,gBAAkB,aAAa,CAAC,KAAK,cAAc,CAAC,IAAI,aAAa,CAAC,KAAK,cAAc,CAAC,IAAI,aAAa,IAAI,KAAK,cAAc,CAAC;AACtK,MAAM,sBAAsB,CAAC,YAAc,CAAC;QACxC,GAAG,UAAU,CAAC;QACd,GAAG,UAAU,CAAC;QACd,MAAM,UAAU,CAAC;IACrB,CAAC;AACD,MAAM,sBAAsB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,GAAK,oPAAA,CAAA,eAAY,CAAC,SAAS,CAAC,GAAG,GAAG,KAAK,CAAC;AACnF,MAAM,qBAAqB,CAAC,OAAO,YAAc,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,WAAW;AACrF,MAAM,kBAAkB,CAAC,WAAW,aAAe,eAAe,KAAK,MAAM,OAAO,CAAC,cAAc,UAAU,QAAQ,CAAC;AACtH,MAAM,kBAAkB,CAAC,WAAW,WAAW,CAAC,EAAE,QAAQ,KAAQ,CAAC;IAC/D,MAAM,cAAc,OAAO,aAAa,YAAY,WAAW;IAC/D,IAAI,CAAC,aAAa;QACd;IACJ;IACA,OAAO,cAAc,UAAU,UAAU,GAAG,QAAQ,CAAC,UAAU,EAAE,CAAC,OAAO,SAAS;AACtF;AACA,MAAM,aAAa,CAAC;IAChB,MAAM,SAAS,MAAM,OAAO,IAAI,YAAY,KAAK;IACjD,OAAO,CAAC,MAAM,MAAM,GAAG,CAAC,MAAM,SAAS,KAAK,IAAI,OAAO,MAAM,SAAS,GAAG,IAAI,KAAK,IAAI;AAC1F;AAEA,SAAS,yBAAyB,EAAE,aAAa,EAAE,gBAAgB,EAAE,WAAW,EAAE,MAAM,EAAE,eAAe,EAAE,gBAAgB,EAAE,WAAW,EAAE,cAAc,EAAE,SAAS,EAAE,YAAY,EAAG;IAChL,OAAO,CAAC;QACJ,IAAI,mBAAmB,OAAO,mBAAmB;YAC7C,OAAO;QACX;QACA,MAAM,cAAc;QACpB,MAAM,wBAAwB;QAC9B,MAAM,cAAc,YAAY,QAAQ,CAAC,UAAU,CAAC,IAAI;QACxD,0DAA0D;QAC1D,IAAI,MAAM,OAAO,IAAI,aAAa;YAC9B,MAAM,QAAQ,CAAA,GAAA,sPAAA,CAAA,UAAO,AAAD,EAAE;YACtB,MAAM,aAAa,WAAW;YAC9B,MAAM,OAAO,cAAc,KAAK,GAAG,CAAC,GAAG;YACvC,aAAa;YACb,OAAO,OAAO,CAAC,aAAa,MAAM,OAAO;YACzC;QACJ;QACA;;;SAGC,GACD,MAAM,iBAAiB,MAAM,SAAS,KAAK,IAAI,KAAK;QACpD,IAAI,SAAS,oBAAoB,gBAAgB,QAAQ,GAAG,IAAI,MAAM,MAAM,GAAG;QAC/E,IAAI,SAAS,oBAAoB,gBAAgB,UAAU,GAAG,IAAI,MAAM,MAAM,GAAG;QACjF,iEAAiE;QACjE,IAAI,CAAC,aAAa,MAAM,QAAQ,IAAI,oBAAoB,gBAAgB,QAAQ,EAAE;YAC9E,SAAS,MAAM,MAAM,GAAG;YACxB,SAAS;QACb;QACA,OAAO,WAAW,CAAC,aAAa,CAAC,CAAC,SAAS,WAAW,IAAI,kBAAkB,CAAC,CAAC,SAAS,WAAW,IAAI,kBACtG,aAAa;QACb;YAAE,UAAU;QAAK;QACjB,MAAM,eAAe,oBAAoB,YAAY,QAAQ,CAAC;QAC9D,aAAa,cAAc,gBAAgB;QAC3C;;;;SAIC,GACD,IAAI,CAAC,cAAc,cAAc,EAAE;YAC/B,cAAc,cAAc,GAAG;YAC/B,iBAAiB,OAAO;QAC5B;QACA,IAAI,cAAc,cAAc,EAAE;YAC9B,YAAY,OAAO;YACnB,cAAc,gBAAgB,GAAG,WAAW;gBACxC,eAAe,OAAO;gBACtB,cAAc,cAAc,GAAG;YACnC,GAAG;QACP;IACJ;AACJ;AACA,SAAS,0BAA0B,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,aAAa,EAAE;IACpF,OAAO,SAAU,KAAK,EAAE,CAAC;QACrB,MAAM,UAAU,MAAM,IAAI,KAAK;QAC/B,iFAAiF;QACjF,MAAM,cAAc,CAAC,oBAAoB,WAAW,CAAC,MAAM,OAAO;QAClE,MAAM,kBAAkB,mBAAmB,OAAO;QAClD,sFAAsF;QACtF,IAAI,MAAM,OAAO,IAAI,WAAW,iBAAiB;YAC7C,MAAM,cAAc;QACxB;QACA,IAAI,eAAe,iBAAiB;YAChC,OAAO;QACX;QACA,MAAM,cAAc;QACpB,cAAc,IAAI,CAAC,IAAI,EAAE,OAAO;IACpC;AACJ;AACA,SAAS,0BAA0B,EAAE,aAAa,EAAE,gBAAgB,EAAE,cAAc,EAAE;IAClF,OAAO,CAAC;QACJ,IAAI,MAAM,WAAW,EAAE,UAAU;YAC7B;QACJ;QACA,MAAM,WAAW,oBAAoB,MAAM,SAAS;QACpD,yEAAyE;QACzE,cAAc,WAAW,GAAG,MAAM,WAAW,EAAE,UAAU;QACzD,cAAc,kBAAkB,GAAG;QACnC,cAAc,YAAY,GAAG;QAC7B,IAAI,MAAM,WAAW,EAAE,SAAS,aAAa;YACzC,iBAAiB;QACrB;QACA,IAAI,gBAAgB;YAChB,iBAAiB,MAAM,WAAW,EAAE;QACxC;IACJ;AACJ;AACA,SAAS,qBAAqB,EAAE,aAAa,EAAE,SAAS,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,SAAS,EAAG;IACxG,OAAO,CAAC;QACJ,cAAc,oBAAoB,GAAG,CAAC,CAAC,CAAC,qBAAqB,gBAAgB,WAAW,cAAc,WAAW,IAAI,EAAE;QACvH,IAAI,CAAC,MAAM,WAAW,EAAE,MAAM;YAC1B,kBAAkB;gBAAC,MAAM,SAAS,CAAC,CAAC;gBAAE,MAAM,SAAS,CAAC,CAAC;gBAAE,MAAM,SAAS,CAAC,CAAC;aAAC;QAC/E;QACA,IAAI,aAAa,CAAC,MAAM,WAAW,EAAE,UAAU;YAC3C,YAAY,MAAM,WAAW,EAAE,oBAAoB,MAAM,SAAS;QACtE;IACJ;AACJ;AACA,SAAS,wBAAwB,EAAE,aAAa,EAAE,SAAS,EAAE,WAAW,EAAE,gBAAgB,EAAE,YAAY,EAAE,iBAAiB,EAAG;IAC1H,OAAO,CAAC;QACJ,IAAI,MAAM,WAAW,EAAE,UAAU;YAC7B;QACJ;QACA,cAAc,kBAAkB,GAAG;QACnC,IAAI,qBACA,gBAAgB,WAAW,cAAc,WAAW,IAAI,MACxD,CAAC,cAAc,oBAAoB,IACnC,MAAM,WAAW,EAAE;YACnB,kBAAkB,MAAM,WAAW;QACvC;QACA,cAAc,oBAAoB,GAAG;QACrC,iBAAiB;QACjB,IAAI,gBAAgB,YAAY,cAAc,YAAY,EAAE,MAAM,SAAS,GAAG;YAC1E,MAAM,WAAW,oBAAoB,MAAM,SAAS;YACpD,cAAc,YAAY,GAAG;YAC7B,aAAa,cAAc,OAAO;YAClC,cAAc,OAAO,GAAG,WAAW;gBAC/B,eAAe,MAAM,WAAW,EAAE;YACtC,GACA,0FAA0F;YAC1F,cAAc,MAAM;QACxB;IACJ;AACJ;AAEA,qDAAqD,GACrD,SAAS,aAAa,EAAE,wBAAwB,EAAE,YAAY,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,cAAc,EAAE,GAAG,EAAG;IACjL,OAAO,CAAC;QACJ,MAAM,aAAa,4BAA4B;QAC/C,MAAM,YAAY,eAAe,MAAM,OAAO;QAC9C,IAAI,MAAM,MAAM,KAAK,KACjB,MAAM,IAAI,KAAK,eACf,CAAC,mBAAmB,OAAO,GAAG,IAAI,WAAW,CAAC,KAAK,mBAAmB,OAAO,GAAG,IAAI,WAAW,CAAC,CAAC,GAAG;YACpG,OAAO;QACX;QACA,+DAA+D;QAC/D,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,eAAe,CAAC,qBAAqB,CAAC,aAAa;YACjF,OAAO;QACX;QACA,uDAAuD;QACvD,IAAI,qBAAqB;YACrB,OAAO;QACX;QACA,wFAAwF;QACxF,IAAI,mBAAmB,OAAO,qBAAqB,MAAM,IAAI,KAAK,SAAS;YACvE,OAAO;QACX;QACA,sFAAsF;QACtF,IAAI,mBAAmB,OAAO,mBAC1B,CAAC,MAAM,IAAI,KAAK,WAAY,eAAe,MAAM,IAAI,KAAK,WAAW,CAAC,wBAAyB,GAAG;YAClG,OAAO;QACX;QACA,IAAI,CAAC,eAAe,MAAM,OAAO,IAAI,MAAM,IAAI,KAAK,SAAS;YACzD,OAAO;QACX;QACA,IAAI,CAAC,eAAe,MAAM,IAAI,KAAK,gBAAgB,MAAM,OAAO,EAAE,SAAS,GAAG;YAC1E,MAAM,cAAc,IAAI,gEAAgE;YACxF,OAAO;QACX;QACA,wEAAwE;QACxE,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,aAAa,MAAM,IAAI,KAAK,SAAS;YACrE,OAAO;QACX;QACA,mFAAmF;QACnF,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,KAAK,eAAe,MAAM,IAAI,KAAK,YAAY,GAAG;YAC3E,OAAO;QACX;QACA,mDAAmD;QACnD,IAAI,MAAM,OAAO,CAAC,cAAc,CAAC,UAAU,QAAQ,CAAC,MAAM,MAAM,KAAK,MAAM,IAAI,KAAK,aAAa;YAC7F,OAAO;QACX;QACA,kEAAkE;QAClE,MAAM,gBAAgB,AAAC,MAAM,OAAO,CAAC,cAAc,UAAU,QAAQ,CAAC,MAAM,MAAM,KAAM,CAAC,MAAM,MAAM,IAAI,MAAM,MAAM,IAAI;QACzH,6BAA6B;QAC7B,OAAO,CAAC,CAAC,MAAM,OAAO,IAAI,MAAM,IAAI,KAAK,OAAO,KAAK;IACzD;AACJ;AAEA,SAAS,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,iBAAiB,EAAE,eAAe,EAAE,QAAQ,EAAE,SAAS,EAAE,cAAc,EAAE,YAAY,EAAE,gBAAgB,EAAG;IACtJ,MAAM,gBAAgB;QAClB,oBAAoB;QACpB,sBAAsB;QACtB,cAAc;YAAE,GAAG;YAAG,GAAG;YAAG,MAAM;QAAE;QACpC,aAAa;QACb,SAAS;QACT,kBAAkB;QAClB,gBAAgB;IACpB;IACA,MAAM,OAAO,QAAQ,qBAAqB;IAC1C,MAAM,iBAAiB,CAAA,GAAA,sOAAA,CAAA,OAAI,AAAD,IACrB,aAAa,CAAC,CAAC,UAAU,sBAAsB,oBAAoB,IAAI,IAAI,mBAC3E,WAAW,CAAC;QAAC;QAAS;KAAQ,EAC9B,eAAe,CAAC;IACrB,MAAM,cAAc,CAAA,GAAA,oPAAA,CAAA,SAAM,AAAD,EAAE,SAAS,IAAI,CAAC;IACzC,uBAAuB;QACnB,GAAG,SAAS,CAAC;QACb,GAAG,SAAS,CAAC;QACb,MAAM,MAAM,SAAS,IAAI,EAAE,SAAS;IACxC,GAAG;QACC;YAAC;YAAG;SAAE;QACN;YAAC,KAAK,KAAK;YAAE,KAAK,MAAM;SAAC;KAC5B,EAAE;IACH,MAAM,gBAAgB,YAAY,EAAE,CAAC;IACrC,MAAM,wBAAwB,YAAY,EAAE,CAAC;IAC7C,eAAe,UAAU,CAAC;IAC1B,SAAS,aAAa,SAAS,EAAE,OAAO;QACpC,IAAI,aAAa;YACb,OAAO,IAAI,QAAQ,CAAC;gBAChB,gBAAgB,UAAU,gBAAgB,aAAa,SAAS,UAAU,IAAM,QAAQ,QAAQ;YACpG;QACJ;QACA,OAAO,QAAQ,OAAO,CAAC;IAC3B;IACA,mBAAmB;IACnB,SAAS,OAAO,EAAE,gBAAgB,EAAE,cAAc,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,WAAW,EAAE,SAAS,EAAE,eAAe,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAiB,EAAE,wBAAwB,EAAE,GAAG,EAAE,iBAAiB,EAAG;QACtQ,IAAI,uBAAuB,CAAC,cAAc,kBAAkB,EAAE;YAC1D;QACJ;QACA,MAAM,gBAAgB,eAAe,CAAC,4BAA4B,CAAC;QACnE,MAAM,eAAe,gBACf,yBAAyB;YACvB;YACA;YACA;YACA,QAAQ;YACR;YACA;YACA;YACA;YACA;YACA;QACJ,KACE,0BAA0B;YACxB;YACA;YACA;QACJ;QACJ,YAAY,EAAE,CAAC,cAAc,cAAc;YAAE,SAAS;QAAM;QAC5D,IAAI,CAAC,qBAAqB;YACtB,iBAAiB;YACjB,MAAM,eAAe,0BAA0B;gBAC3C;gBACA;gBACA;YACJ;YACA,eAAe,EAAE,CAAC,SAAS;YAC3B,WAAW;YACX,MAAM,iBAAiB,qBAAqB;gBACxC;gBACA;gBACA,mBAAmB,CAAC,CAAC;gBACrB;gBACA;YACJ;YACA,eAAe,EAAE,CAAC,QAAQ;YAC1B,eAAe;YACf,MAAM,oBAAoB,wBAAwB;gBAC9C;gBACA;gBACA;gBACA;gBACA;gBACA;YACJ;YACA,eAAe,EAAE,CAAC,OAAO;QAC7B;QACA,MAAM,SAAS,aAAa;YACxB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACJ;QACA,eAAe,MAAM,CAAC;QACtB;;;;SAIC,GACD,IAAI,mBAAmB;YACnB,YAAY,EAAE,CAAC,iBAAiB;QACpC,OACK;YACD,YAAY,EAAE,CAAC,iBAAiB;QACpC;IACJ;IACA,SAAS;QACL,eAAe,EAAE,CAAC,QAAQ;IAC9B;IACA,eAAe,uBAAuB,QAAQ,EAAE,MAAM,EAAE,eAAe;QACnE,MAAM,gBAAgB,oBAAoB;QAC1C,MAAM,sBAAsB,gBAAgB,YAAY,eAAe,QAAQ;QAC/E,IAAI,qBAAqB;YACrB,MAAM,aAAa;QACvB;QACA,OAAO,IAAI,QAAQ,CAAC,UAAY,QAAQ;IAC5C;IACA,eAAe,YAAY,QAAQ,EAAE,OAAO;QACxC,MAAM,gBAAgB,oBAAoB;QAC1C,MAAM,aAAa,eAAe;QAClC,OAAO,IAAI,QAAQ,CAAC,UAAY,QAAQ;IAC5C;IACA,SAAS,aAAa,QAAQ;QAC1B,IAAI,aAAa;YACb,MAAM,gBAAgB,oBAAoB;YAC1C,MAAM,mBAAmB,YAAY,QAAQ,CAAC;YAC9C,IAAI,iBAAiB,CAAC,KAAK,SAAS,IAAI,IACpC,iBAAiB,CAAC,KAAK,SAAS,CAAC,IACjC,iBAAiB,CAAC,KAAK,SAAS,CAAC,EAAE;gBACnC,6DAA6D;gBAC7D,aAAa;gBACb,gBAAgB,UAAU,aAAa,eAAe,MAAM;oBAAE,MAAM;gBAAK;YAC7E;QACJ;IACJ;IACA,SAAS;QACL,MAAM,YAAY,cAAc,CAAA,GAAA,oPAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,IAAI,MAAM;YAAE,GAAG;YAAG,GAAG;YAAG,GAAG;QAAE;QACvF,OAAO;YAAE,GAAG,UAAU,CAAC;YAAE,GAAG,UAAU,CAAC;YAAE,MAAM,UAAU,CAAC;QAAC;IAC/D;IACA,SAAS,QAAQ,IAAI,EAAE,OAAO;QAC1B,IAAI,aAAa;YACb,OAAO,IAAI,QAAQ,CAAC;gBAChB,gBAAgB,QAAQ,gBAAgB,aAAa,SAAS,UAAU,IAAM,QAAQ,QAAQ;YAClG;QACJ;QACA,OAAO,QAAQ,OAAO,CAAC;IAC3B;IACA,SAAS,QAAQ,MAAM,EAAE,OAAO;QAC5B,IAAI,aAAa;YACb,OAAO,IAAI,QAAQ,CAAC;gBAChB,gBAAgB,QAAQ,gBAAgB,aAAa,SAAS,UAAU,IAAM,QAAQ,QAAQ;YAClG;QACJ;QACA,OAAO,QAAQ,OAAO,CAAC;IAC3B;IACA,SAAS,eAAe,WAAW;QAC/B,gBAAgB,YAAY;IAChC;IACA,SAAS,mBAAmB,eAAe;QACvC,gBAAgB,gBAAgB;IACpC;IACA,SAAS,iBAAiB,QAAQ;QAC9B,MAAM,gBAAgB,CAAC,UAAU,aAAa,WAAW,IAAI,IAAI;QACjE,gBAAgB,cAAc;IAClC;IACA,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACJ;AACJ;AAEA;;;;CAIC,GACD,IAAI;AACJ,CAAC,SAAU,oBAAoB;IAC3B,oBAAoB,CAAC,OAAO,GAAG;IAC/B,oBAAoB,CAAC,SAAS,GAAG;AACrC,CAAC,EAAE,wBAAwB,CAAC,uBAAuB,CAAC,CAAC;AACrD,MAAM,8BAA8B;IAAC;IAAY;IAAa;IAAe;CAAe;AAC5F,MAAM,4BAA4B;IAAC;IAAO;IAAS;IAAU;CAAO;AAEpE;;;;;;;;;CASC,GACD,SAAS,mBAAmB,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAG;IACrF,MAAM,aAAa,QAAQ;IAC3B,MAAM,cAAc,SAAS;IAC7B,MAAM,YAAY;QAAC,aAAa,IAAI,IAAI,aAAa,IAAI,CAAC,IAAI;QAAG,cAAc,IAAI,IAAI,cAAc,IAAI,CAAC,IAAI;KAAE;IAChH,IAAI,cAAc,UAAU;QACxB,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;IACnC;IACA,IAAI,eAAe,UAAU;QACzB,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;IACnC;IACA,OAAO;AACX;AACA;;;;CAIC,GACD,SAAS,oBAAoB,eAAe;IACxC,MAAM,eAAe,gBAAgB,QAAQ,CAAC,YAAY,gBAAgB,QAAQ,CAAC;IACnF,MAAM,aAAa,gBAAgB,QAAQ,CAAC,aAAa,gBAAgB,QAAQ,CAAC;IAClF,MAAM,WAAW,gBAAgB,QAAQ,CAAC;IAC1C,MAAM,WAAW,gBAAgB,QAAQ,CAAC;IAC1C,OAAO;QACH;QACA;QACA;QACA;IACJ;AACJ;AACA,SAAS,oBAAoB,WAAW,EAAE,UAAU;IAChD,OAAO,KAAK,GAAG,CAAC,GAAG,aAAa;AACpC;AACA,SAAS,oBAAoB,WAAW,EAAE,UAAU;IAChD,OAAO,KAAK,GAAG,CAAC,GAAG,cAAc;AACrC;AACA,SAAS,aAAa,IAAI,EAAE,OAAO,EAAE,OAAO;IACxC,OAAO,KAAK,GAAG,CAAC,GAAG,UAAU,MAAM,OAAO;AAC9C;AACA,SAAS,IAAI,CAAC,EAAE,CAAC;IACb,OAAO,IAAI,CAAC,IAAI;AACpB;AACA;;;;;;;;;;;;;;;;;;CAkBC,GACD,SAAS,yBAAyB,WAAW,EAAE,gBAAgB,EAAE,eAAe,EAAE,UAAU,EAAE,eAAe,EAAE,UAAU,EAAE,MAAM,EAAE,WAAW;IAC1I,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG;IAC7B,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,GAAG;IACrC,MAAM,aAAa,gBAAgB;IACnC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG;IAC/B,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG;IACrD,MAAM,EAAE,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE,OAAO,UAAU,EAAE,QAAQ,WAAW,EAAE,WAAW,EAAE,GAAG;IACtF,IAAI,QAAQ,KAAK,KAAK,CAAC,eAAe,WAAW,YAAY,QAAQ,GAAG;IACxE,IAAI,QAAQ,KAAK,KAAK,CAAC,aAAa,WAAW,YAAY,QAAQ,GAAG;IACtE,MAAM,WAAW,aAAa,CAAC,WAAW,CAAC,QAAQ,KAAK;IACxD,MAAM,YAAY,cAAc,CAAC,WAAW,CAAC,QAAQ,KAAK;IAC1D,MAAM,gBAAgB,CAAC,UAAU,CAAC,EAAE,GAAG;IACvC,MAAM,gBAAgB,CAAC,UAAU,CAAC,EAAE,GAAG;IACvC,gFAAgF;IAChF,IAAI,SAAS,aAAa,UAAU,UAAU;IAC9C,IAAI,SAAS,aAAa,WAAW,WAAW;IAChD,4CAA4C;IAC5C,IAAI,QAAQ;QACR,IAAI,eAAe;QACnB,IAAI,eAAe;QACnB,IAAI,YAAY,QAAQ,GAAG;YACvB,eAAe,oBAAoB,SAAS,QAAQ,eAAe,MAAM,CAAC,EAAE,CAAC,EAAE;QACnF,OACK,IAAI,CAAC,YAAY,QAAQ,GAAG;YAC7B,eAAe,oBAAoB,SAAS,WAAW,eAAe,MAAM,CAAC,EAAE,CAAC,EAAE;QACtF;QACA,IAAI,YAAY,QAAQ,GAAG;YACvB,eAAe,oBAAoB,SAAS,QAAQ,eAAe,MAAM,CAAC,EAAE,CAAC,EAAE;QACnF,OACK,IAAI,CAAC,YAAY,QAAQ,GAAG;YAC7B,eAAe,oBAAoB,SAAS,YAAY,eAAe,MAAM,CAAC,EAAE,CAAC,EAAE;QACvF;QACA,SAAS,KAAK,GAAG,CAAC,QAAQ;QAC1B,SAAS,KAAK,GAAG,CAAC,QAAQ;IAC9B;IACA,sDAAsD;IACtD,IAAI,aAAa;QACb,IAAI,eAAe;QACnB,IAAI,eAAe;QACnB,IAAI,YAAY,QAAQ,GAAG;YACvB,eAAe,oBAAoB,SAAS,OAAO,WAAW,CAAC,EAAE,CAAC,EAAE;QACxE,OACK,IAAI,CAAC,YAAY,QAAQ,GAAG;YAC7B,eAAe,oBAAoB,SAAS,UAAU,WAAW,CAAC,EAAE,CAAC,EAAE;QAC3E;QACA,IAAI,YAAY,QAAQ,GAAG;YACvB,eAAe,oBAAoB,SAAS,OAAO,WAAW,CAAC,EAAE,CAAC,EAAE;QACxE,OACK,IAAI,CAAC,YAAY,QAAQ,GAAG;YAC7B,eAAe,oBAAoB,SAAS,WAAW,WAAW,CAAC,EAAE,CAAC,EAAE;QAC5E;QACA,SAAS,KAAK,GAAG,CAAC,QAAQ;QAC1B,SAAS,KAAK,GAAG,CAAC,QAAQ;IAC9B;IACA,iFAAiF;IACjF,IAAI,iBAAiB;QACjB,IAAI,cAAc;YACd,8DAA8D;YAC9D,MAAM,oBAAoB,aAAa,WAAW,aAAa,WAAW,aAAa;YACvF,SAAS,KAAK,GAAG,CAAC,QAAQ;YAC1B,gDAAgD;YAChD,IAAI,QAAQ;gBACR,IAAI,oBAAoB;gBACxB,IAAI,AAAC,CAAC,YAAY,CAAC,YAAc,YAAY,CAAC,YAAY,YAAa;oBACnE,oBACI,oBAAoB,SAAS,gBAAgB,WAAW,aAAa,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI;gBAC7F,OACK;oBACD,oBACI,oBAAoB,SAAS,gBAAgB,CAAC,WAAW,QAAQ,CAAC,KAAK,IAAI,aAAa,MAAM,CAAC,EAAE,CAAC,EAAE,IAChG;gBACZ;gBACA,SAAS,KAAK,GAAG,CAAC,QAAQ;YAC9B;YACA,sDAAsD;YACtD,IAAI,aAAa;gBACb,IAAI,oBAAoB;gBACxB,IAAI,AAAC,CAAC,YAAY,CAAC,YAAc,YAAY,CAAC,YAAY,YAAa;oBACnE,oBAAoB,oBAAoB,SAAS,WAAW,aAAa,WAAW,CAAC,EAAE,CAAC,EAAE,IAAI;gBAClG,OACK;oBACD,oBACI,oBAAoB,SAAS,CAAC,WAAW,QAAQ,CAAC,KAAK,IAAI,aAAa,WAAW,CAAC,EAAE,CAAC,EAAE,IAAI;gBACrG;gBACA,SAAS,KAAK,GAAG,CAAC,QAAQ;YAC9B;QACJ;QACA,0CAA0C;QAC1C,IAAI,YAAY;YACZ,MAAM,mBAAmB,aAAa,YAAY,aAAa,UAAU,YAAY;YACrF,SAAS,KAAK,GAAG,CAAC,QAAQ;YAC1B,IAAI,QAAQ;gBACR,IAAI,oBAAoB;gBACxB,IAAI,AAAC,CAAC,YAAY,CAAC,YAAc,YAAY,CAAC,YAAY,YAAa;oBACnE,oBACI,oBAAoB,SAAS,YAAY,cAAc,eAAe,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI;gBAC9F,OACK;oBACD,oBACI,oBAAoB,SAAS,CAAC,WAAW,QAAQ,CAAC,KAAK,IAAI,cAAc,eAAe,MAAM,CAAC,EAAE,CAAC,EAAE,IAChG;gBACZ;gBACA,SAAS,KAAK,GAAG,CAAC,QAAQ;YAC9B;YACA,IAAI,aAAa;gBACb,IAAI,oBAAoB;gBACxB,IAAI,AAAC,CAAC,YAAY,CAAC,YAAc,YAAY,CAAC,YAAY,YAAa;oBACnE,oBAAoB,oBAAoB,SAAS,YAAY,aAAa,WAAW,CAAC,EAAE,CAAC,EAAE,IAAI;gBACnG,OACK;oBACD,oBACI,oBAAoB,SAAS,CAAC,WAAW,QAAQ,CAAC,KAAK,IAAI,aAAa,WAAW,CAAC,EAAE,CAAC,EAAE,IAAI;gBACrG;gBACA,SAAS,KAAK,GAAG,CAAC,QAAQ;YAC9B;QACJ;IACJ;IACA,QAAQ,QAAQ,CAAC,QAAQ,IAAI,SAAS,CAAC,MAAM;IAC7C,QAAQ,QAAQ,CAAC,QAAQ,IAAI,SAAS,CAAC,MAAM;IAC7C,IAAI,iBAAiB;QACjB,IAAI,YAAY;YACZ,IAAI,WAAW,YAAY,aAAa;gBACpC,QAAQ,CAAC,IAAI,UAAU,YAAY,CAAC,QAAQ,KAAK,IAAI;YACzD,OACK;gBACD,QAAQ,CAAC,IAAI,UAAU,YAAY,CAAC,QAAQ,KAAK,IAAI;YACzD;QACJ,OACK;YACD,IAAI,cAAc;gBACd,QAAQ,QAAQ;gBAChB,WAAW;YACf,OACK;gBACD,QAAQ,QAAQ;gBAChB,WAAW;YACf;QACJ;IACJ;IACA,MAAM,IAAI,WAAW,SAAS,QAAQ;IACtC,MAAM,IAAI,WAAW,SAAS,QAAQ;IACtC,OAAO;QACH,OAAO,aAAa,CAAC,WAAW,CAAC,QAAQ,KAAK;QAC9C,QAAQ,cAAc,CAAC,WAAW,CAAC,QAAQ,KAAK;QAChD,GAAG,UAAU,CAAC,EAAE,GAAG,QAAQ,CAAC,CAAC,WAAW,IAAI,CAAC,CAAC,IAAI;QAClD,GAAG,UAAU,CAAC,EAAE,GAAG,QAAQ,CAAC,CAAC,WAAW,IAAI,CAAC,CAAC,IAAI;IACtD;AACJ;AAEA,MAAM,iBAAiB;IAAE,OAAO;IAAG,QAAQ;IAAG,GAAG;IAAG,GAAG;AAAE;AACzD,MAAM,kBAAkB;IACpB,GAAG,cAAc;IACjB,UAAU;IACV,UAAU;IACV,aAAa;AACjB;AACA,SAAS,mBAAmB,IAAI;IAC5B,OAAO;QACH;YAAC;YAAG;SAAE;QACN;YAAC,KAAK,QAAQ,CAAC,KAAK;YAAE,KAAK,QAAQ,CAAC,MAAM;SAAC;KAC9C;AACL;AACA,SAAS,kBAAkB,KAAK,EAAE,MAAM,EAAE,UAAU;IAChD,MAAM,IAAI,OAAO,QAAQ,CAAC,CAAC,GAAG,MAAM,QAAQ,CAAC,CAAC;IAC9C,MAAM,IAAI,OAAO,QAAQ,CAAC,CAAC,GAAG,MAAM,QAAQ,CAAC,CAAC;IAC9C,MAAM,QAAQ,MAAM,QAAQ,CAAC,KAAK,IAAI;IACtC,MAAM,SAAS,MAAM,QAAQ,CAAC,MAAM,IAAI;IACxC,MAAM,gBAAgB,UAAU,CAAC,EAAE,GAAG;IACtC,MAAM,gBAAgB,UAAU,CAAC,EAAE,GAAG;IACtC,OAAO;QACH;YAAC,IAAI;YAAe,IAAI;SAAc;QACtC;YAAC,IAAI,QAAQ;YAAe,IAAI,SAAS;SAAc;KAC1D;AACL;AACA,SAAS,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAE;IAClE,MAAM,YAAY,CAAA,GAAA,oPAAA,CAAA,SAAM,AAAD,EAAE;IACzB,SAAS,OAAO,EAAE,eAAe,EAAE,UAAU,EAAE,eAAe,EAAE,eAAe,EAAE,aAAa,EAAE,QAAQ,EAAE,WAAW,EAAE,YAAY,EAAG;QAClI,IAAI,aAAa;YAAE,GAAG,cAAc;QAAC;QACrC,IAAI,cAAc;YAAE,GAAG,eAAe;QAAC;QACvC,MAAM,mBAAmB,oBAAoB;QAC7C,IAAI,OAAO;QACX,IAAI,kBAAkB;QACtB,IAAI,aAAa,EAAE;QACnB,IAAI,aAAa,WAAW,6BAA6B;QACzD,IAAI,eAAe;QACnB,IAAI,cAAc;QAClB,MAAM,cAAc,CAAA,GAAA,sOAAA,CAAA,OAAI,AAAD,IAClB,EAAE,CAAC,SAAS,CAAC;YACd,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG;YACjF,OAAO,WAAW,GAAG,CAAC;YACtB,IAAI,CAAC,MAAM;gBACP;YACJ;YACA,kBAAkB,aAAa,2BAA2B;YAC1D,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,mBAAmB,MAAM,WAAW,EAAE;gBACjE;gBACA;gBACA;gBACA;YACJ;YACA,aAAa;gBACT,OAAO,KAAK,QAAQ,CAAC,KAAK,IAAI;gBAC9B,QAAQ,KAAK,QAAQ,CAAC,MAAM,IAAI;gBAChC,GAAG,KAAK,QAAQ,CAAC,CAAC,IAAI;gBACtB,GAAG,KAAK,QAAQ,CAAC,CAAC,IAAI;YAC1B;YACA,cAAc;gBACV,GAAG,UAAU;gBACb,UAAU;gBACV,UAAU;gBACV,aAAa,WAAW,KAAK,GAAG,WAAW,MAAM;YACrD;YACA,aAAa;YACb,IAAI,KAAK,QAAQ,IAAI,CAAC,KAAK,MAAM,KAAK,YAAY,KAAK,YAAY,GAAG;gBAClE,aAAa,WAAW,GAAG,CAAC,KAAK,QAAQ;gBACzC,eAAe,cAAc,KAAK,MAAM,KAAK,WAAW,mBAAmB,cAAc;YAC7F;YACA;;;aAGC,GACD,aAAa,EAAE;YACf,cAAc;YACd,KAAK,MAAM,CAAC,SAAS,MAAM,IAAI,WAAY;gBACvC,IAAI,MAAM,QAAQ,KAAK,QAAQ;oBAC3B,WAAW,IAAI,CAAC;wBACZ,IAAI;wBACJ,UAAU;4BAAE,GAAG,MAAM,QAAQ;wBAAC;wBAC9B,QAAQ,MAAM,MAAM;oBACxB;oBACA,IAAI,MAAM,MAAM,KAAK,YAAY,MAAM,YAAY,EAAE;wBACjD,MAAM,SAAS,kBAAkB,OAAO,MAAM,MAAM,MAAM,IAAI;wBAC9D,IAAI,aAAa;4BACb,cAAc;gCACV;oCAAC,KAAK,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,WAAW,CAAC,EAAE,CAAC,EAAE;oCAAG,KAAK,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,WAAW,CAAC,EAAE,CAAC,EAAE;iCAAE;gCACtF;oCAAC,KAAK,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,WAAW,CAAC,EAAE,CAAC,EAAE;oCAAG,KAAK,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,WAAW,CAAC,EAAE,CAAC,EAAE;iCAAE;6BACzF;wBACL,OACK;4BACD,cAAc;wBAClB;oBACJ;gBACJ;YACJ;YACA,gBAAgB,OAAO;gBAAE,GAAG,UAAU;YAAC;QAC3C,GACK,EAAE,CAAC,QAAQ,CAAC;YACb,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,eAAe,EAAE,GAAG;YACzE,MAAM,kBAAkB,mBAAmB,MAAM,WAAW,EAAE;gBAC1D;gBACA;gBACA;gBACA;YACJ;YACA,MAAM,eAAe,EAAE;YACvB,IAAI,CAAC,MAAM;gBACP;YACJ;YACA,MAAM,EAAE,GAAG,KAAK,EAAE,GAAG,KAAK,EAAE,OAAO,SAAS,EAAE,QAAQ,UAAU,EAAE,GAAG;YACrE,MAAM,SAAS,CAAC;YAChB,MAAM,aAAa,KAAK,MAAM,IAAI;YAClC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,yBAAyB,aAAa,kBAAkB,iBAAiB,YAAY,iBAAiB,YAAY,cAAc;YAChK,MAAM,gBAAgB,UAAU;YAChC,MAAM,iBAAiB,WAAW;YAClC,MAAM,eAAe,MAAM,SAAS;YACpC,MAAM,eAAe,MAAM,SAAS;YACpC,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,gBAAgB;gBACrE;YACJ;YACA,IAAI,gBAAgB,gBAAgB,UAAU,CAAC,EAAE,KAAK,KAAK,UAAU,CAAC,EAAE,KAAK,GAAG;gBAC5E,OAAO,CAAC,GAAG,eAAe,IAAI,WAAW,CAAC;gBAC1C,OAAO,CAAC,GAAG,eAAe,IAAI,WAAW,CAAC;gBAC1C,WAAW,CAAC,GAAG,OAAO,CAAC;gBACvB,WAAW,CAAC,GAAG,OAAO,CAAC;gBACvB;;;iBAGC,GACD,IAAI,WAAW,MAAM,GAAG,GAAG;oBACvB,MAAM,UAAU,IAAI;oBACpB,MAAM,UAAU,IAAI;oBACpB,KAAK,MAAM,aAAa,WAAY;wBAChC,UAAU,QAAQ,GAAG;4BACjB,GAAG,UAAU,QAAQ,CAAC,CAAC,GAAG,UAAU,UAAU,CAAC,EAAE,GAAG,CAAC,QAAQ,SAAS;4BACtE,GAAG,UAAU,QAAQ,CAAC,CAAC,GAAG,UAAU,UAAU,CAAC,EAAE,GAAG,CAAC,SAAS,UAAU;wBAC5E;wBACA,aAAa,IAAI,CAAC;oBACtB;gBACJ;YACJ;YACA,IAAI,iBAAiB,gBAAgB;gBACjC,OAAO,KAAK,GACR,iBAAiB,CAAC,CAAC,mBAAmB,oBAAoB,YAAY,IAAI,QAAQ,WAAW,KAAK;gBACtG,OAAO,MAAM,GACT,kBAAkB,CAAC,CAAC,mBAAmB,oBAAoB,UAAU,IAAI,SAAS,WAAW,MAAM;gBACvG,WAAW,KAAK,GAAG,OAAO,KAAK;gBAC/B,WAAW,MAAM,GAAG,OAAO,MAAM;YACrC;YACA,+CAA+C;YAC/C,IAAI,cAAc,KAAK,YAAY,EAAE;gBACjC,MAAM,SAAS,UAAU,CAAC,EAAE,GAAG,CAAC,OAAO,KAAK,IAAI,CAAC;gBACjD,IAAI,OAAO,CAAC,IAAI,OAAO,CAAC,GAAG,QAAQ;oBAC/B,WAAW,CAAC,GAAG;oBACf,YAAY,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,MAAM;gBACtD;gBACA,MAAM,SAAS,UAAU,CAAC,EAAE,GAAG,CAAC,OAAO,MAAM,IAAI,CAAC;gBAClD,IAAI,OAAO,CAAC,IAAI,OAAO,CAAC,GAAG,QAAQ;oBAC/B,WAAW,CAAC,GAAG;oBACf,YAAY,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,MAAM;gBACtD;YACJ;YACA,MAAM,YAAY,mBAAmB;gBACjC,OAAO,WAAW,KAAK;gBACvB;gBACA,QAAQ,WAAW,MAAM;gBACzB;gBACA,UAAU,iBAAiB,QAAQ;gBACnC,UAAU,iBAAiB,QAAQ;YACvC;YACA,MAAM,aAAa;gBAAE,GAAG,UAAU;gBAAE;YAAU;YAC9C,MAAM,aAAa,eAAe,OAAO;YACzC,IAAI,eAAe,OAAO;gBACtB;YACJ;YACA,WAAW,OAAO;YAClB,SAAS,QAAQ;QACrB,GACK,EAAE,CAAC,OAAO,CAAC;YACZ,cAAc,OAAO;gBAAE,GAAG,UAAU;YAAC;YACrC,QAAQ;gBAAE,GAAG,UAAU;YAAC;QAC5B;QACA,UAAU,IAAI,CAAC;IACnB;IACA,SAAS;QACL,UAAU,EAAE,CAAC,SAAS;IAC1B;IACA,OAAO;QACH;QACA;IACJ;AACJ", "ignoreList": [0], "debugId": null}}]}