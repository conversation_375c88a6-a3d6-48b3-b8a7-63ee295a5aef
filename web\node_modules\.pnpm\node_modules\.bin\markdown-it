#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/repo/deer-flow/web/node_modules/.pnpm/markdown-it@14.1.0/node_modules/markdown-it/bin/node_modules:/mnt/c/repo/deer-flow/web/node_modules/.pnpm/markdown-it@14.1.0/node_modules/markdown-it/node_modules:/mnt/c/repo/deer-flow/web/node_modules/.pnpm/markdown-it@14.1.0/node_modules:/mnt/c/repo/deer-flow/web/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/repo/deer-flow/web/node_modules/.pnpm/markdown-it@14.1.0/node_modules/markdown-it/bin/node_modules:/mnt/c/repo/deer-flow/web/node_modules/.pnpm/markdown-it@14.1.0/node_modules/markdown-it/node_modules:/mnt/c/repo/deer-flow/web/node_modules/.pnpm/markdown-it@14.1.0/node_modules:/mnt/c/repo/deer-flow/web/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../markdown-it/bin/markdown-it.mjs" "$@"
else
  exec node  "$basedir/../markdown-it/bin/markdown-it.mjs" "$@"
fi
