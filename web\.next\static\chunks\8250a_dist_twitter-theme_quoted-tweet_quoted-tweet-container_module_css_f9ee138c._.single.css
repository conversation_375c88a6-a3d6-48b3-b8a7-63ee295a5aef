/* [project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.module.css [app-client] (css) */
.quoted-tweet-container-module__NjboUa__root {
  border: var(--tweet-border);
  width: 100%;
  margin: var(--tweet-quoted-container-margin);
  cursor: pointer;
  border-radius: 12px;
  transition-property: background-color, box-shadow;
  transition-duration: .2s;
  overflow: hidden;
}

.quoted-tweet-container-module__NjboUa__root:hover {
  background-color: var(--tweet-quoted-bg-color-hover);
}

.quoted-tweet-container-module__NjboUa__article {
  box-sizing: inherit;
  position: relative;
}

/*# sourceMappingURL=8250a_dist_twitter-theme_quoted-tweet_quoted-tweet-container_module_css_f9ee138c._.single.css.map*/