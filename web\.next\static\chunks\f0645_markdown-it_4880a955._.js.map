{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/common/utils.mjs"], "sourcesContent": ["// Utilities\n//\n\nimport * as mdurl from 'mdurl'\nimport * as ucmicro from 'uc.micro'\nimport { decodeHTML } from 'entities'\n\nfunction _class (obj) { return Object.prototype.toString.call(obj) }\n\nfunction isString (obj) { return _class(obj) === '[object String]' }\n\nconst _hasOwnProperty = Object.prototype.hasOwnProperty\n\nfunction has (object, key) {\n  return _hasOwnProperty.call(object, key)\n}\n\n// Merge objects\n//\nfunction assign (obj /* from1, from2, from3, ... */) {\n  const sources = Array.prototype.slice.call(arguments, 1)\n\n  sources.forEach(function (source) {\n    if (!source) { return }\n\n    if (typeof source !== 'object') {\n      throw new TypeError(source + 'must be object')\n    }\n\n    Object.keys(source).forEach(function (key) {\n      obj[key] = source[key]\n    })\n  })\n\n  return obj\n}\n\n// Remove element from array and put another array at those position.\n// Useful for some operations with tokens\nfunction arrayReplaceAt (src, pos, newElements) {\n  return [].concat(src.slice(0, pos), newElements, src.slice(pos + 1))\n}\n\nfunction isValidEntityCode (c) {\n  /* eslint no-bitwise:0 */\n  // broken sequence\n  if (c >= 0xD800 && c <= 0xDFFF) { return false }\n  // never used\n  if (c >= 0xFDD0 && c <= 0xFDEF) { return false }\n  if ((c & 0xFFFF) === 0xFFFF || (c & 0xFFFF) === 0xFFFE) { return false }\n  // control codes\n  if (c >= 0x00 && c <= 0x08) { return false }\n  if (c === 0x0B) { return false }\n  if (c >= 0x0E && c <= 0x1F) { return false }\n  if (c >= 0x7F && c <= 0x9F) { return false }\n  // out of range\n  if (c > 0x10FFFF) { return false }\n  return true\n}\n\nfunction fromCodePoint (c) {\n  /* eslint no-bitwise:0 */\n  if (c > 0xffff) {\n    c -= 0x10000\n    const surrogate1 = 0xd800 + (c >> 10)\n    const surrogate2 = 0xdc00 + (c & 0x3ff)\n\n    return String.fromCharCode(surrogate1, surrogate2)\n  }\n  return String.fromCharCode(c)\n}\n\nconst UNESCAPE_MD_RE  = /\\\\([!\"#$%&'()*+,\\-./:;<=>?@[\\\\\\]^_`{|}~])/g\nconst ENTITY_RE       = /&([a-z#][a-z0-9]{1,31});/gi\nconst UNESCAPE_ALL_RE = new RegExp(UNESCAPE_MD_RE.source + '|' + ENTITY_RE.source, 'gi')\n\nconst DIGITAL_ENTITY_TEST_RE = /^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))$/i\n\nfunction replaceEntityPattern (match, name) {\n  if (name.charCodeAt(0) === 0x23/* # */ && DIGITAL_ENTITY_TEST_RE.test(name)) {\n    const code = name[1].toLowerCase() === 'x'\n      ? parseInt(name.slice(2), 16)\n      : parseInt(name.slice(1), 10)\n\n    if (isValidEntityCode(code)) {\n      return fromCodePoint(code)\n    }\n\n    return match\n  }\n\n  const decoded = decodeHTML(match)\n  if (decoded !== match) {\n    return decoded\n  }\n\n  return match\n}\n\n/* function replaceEntities(str) {\n  if (str.indexOf('&') < 0) { return str; }\n\n  return str.replace(ENTITY_RE, replaceEntityPattern);\n} */\n\nfunction unescapeMd (str) {\n  if (str.indexOf('\\\\') < 0) { return str }\n  return str.replace(UNESCAPE_MD_RE, '$1')\n}\n\nfunction unescapeAll (str) {\n  if (str.indexOf('\\\\') < 0 && str.indexOf('&') < 0) { return str }\n\n  return str.replace(UNESCAPE_ALL_RE, function (match, escaped, entity) {\n    if (escaped) { return escaped }\n    return replaceEntityPattern(match, entity)\n  })\n}\n\nconst HTML_ESCAPE_TEST_RE = /[&<>\"]/\nconst HTML_ESCAPE_REPLACE_RE = /[&<>\"]/g\nconst HTML_REPLACEMENTS = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;'\n}\n\nfunction replaceUnsafeChar (ch) {\n  return HTML_REPLACEMENTS[ch]\n}\n\nfunction escapeHtml (str) {\n  if (HTML_ESCAPE_TEST_RE.test(str)) {\n    return str.replace(HTML_ESCAPE_REPLACE_RE, replaceUnsafeChar)\n  }\n  return str\n}\n\nconst REGEXP_ESCAPE_RE = /[.?*+^$[\\]\\\\(){}|-]/g\n\nfunction escapeRE (str) {\n  return str.replace(REGEXP_ESCAPE_RE, '\\\\$&')\n}\n\nfunction isSpace (code) {\n  switch (code) {\n    case 0x09:\n    case 0x20:\n      return true\n  }\n  return false\n}\n\n// Zs (unicode class) || [\\t\\f\\v\\r\\n]\nfunction isWhiteSpace (code) {\n  if (code >= 0x2000 && code <= 0x200A) { return true }\n  switch (code) {\n    case 0x09: // \\t\n    case 0x0A: // \\n\n    case 0x0B: // \\v\n    case 0x0C: // \\f\n    case 0x0D: // \\r\n    case 0x20:\n    case 0xA0:\n    case 0x1680:\n    case 0x202F:\n    case 0x205F:\n    case 0x3000:\n      return true\n  }\n  return false\n}\n\n/* eslint-disable max-len */\n\n// Currently without astral characters support.\nfunction isPunctChar (ch) {\n  return ucmicro.P.test(ch) || ucmicro.S.test(ch)\n}\n\n// Markdown ASCII punctuation characters.\n//\n// !, \", #, $, %, &, ', (, ), *, +, ,, -, ., /, :, ;, <, =, >, ?, @, [, \\, ], ^, _, `, {, |, }, or ~\n// http://spec.commonmark.org/0.15/#ascii-punctuation-character\n//\n// Don't confuse with unicode punctuation !!! It lacks some chars in ascii range.\n//\nfunction isMdAsciiPunct (ch) {\n  switch (ch) {\n    case 0x21/* ! */:\n    case 0x22/* \" */:\n    case 0x23/* # */:\n    case 0x24/* $ */:\n    case 0x25/* % */:\n    case 0x26/* & */:\n    case 0x27/* ' */:\n    case 0x28/* ( */:\n    case 0x29/* ) */:\n    case 0x2A/* * */:\n    case 0x2B/* + */:\n    case 0x2C/* , */:\n    case 0x2D/* - */:\n    case 0x2E/* . */:\n    case 0x2F/* / */:\n    case 0x3A/* : */:\n    case 0x3B/* ; */:\n    case 0x3C/* < */:\n    case 0x3D/* = */:\n    case 0x3E/* > */:\n    case 0x3F/* ? */:\n    case 0x40/* @ */:\n    case 0x5B/* [ */:\n    case 0x5C/* \\ */:\n    case 0x5D/* ] */:\n    case 0x5E/* ^ */:\n    case 0x5F/* _ */:\n    case 0x60/* ` */:\n    case 0x7B/* { */:\n    case 0x7C/* | */:\n    case 0x7D/* } */:\n    case 0x7E/* ~ */:\n      return true\n    default:\n      return false\n  }\n}\n\n// Hepler to unify [reference labels].\n//\nfunction normalizeReference (str) {\n  // Trim and collapse whitespace\n  //\n  str = str.trim().replace(/\\s+/g, ' ')\n\n  // In node v10 'ẞ'.toLowerCase() === 'Ṿ', which is presumed to be a bug\n  // fixed in v12 (couldn't find any details).\n  //\n  // So treat this one as a special case\n  // (remove this when node v10 is no longer supported).\n  //\n  if ('ẞ'.toLowerCase() === 'Ṿ') {\n    str = str.replace(/ẞ/g, 'ß')\n  }\n\n  // .toLowerCase().toUpperCase() should get rid of all differences\n  // between letter variants.\n  //\n  // Simple .toLowerCase() doesn't normalize 125 code points correctly,\n  // and .toUpperCase doesn't normalize 6 of them (list of exceptions:\n  // İ, ϴ, ẞ, Ω, K, Å - those are already uppercased, but have differently\n  // uppercased versions).\n  //\n  // Here's an example showing how it happens. Lets take greek letter omega:\n  // uppercase U+0398 (Θ), U+03f4 (ϴ) and lowercase U+03b8 (θ), U+03d1 (ϑ)\n  //\n  // Unicode entries:\n  // 0398;GREEK CAPITAL LETTER THETA;Lu;0;L;;;;;N;;;;03B8;\n  // 03B8;GREEK SMALL LETTER THETA;Ll;0;L;;;;;N;;;0398;;0398\n  // 03D1;GREEK THETA SYMBOL;Ll;0;L;<compat> 03B8;;;;N;GREEK SMALL LETTER SCRIPT THETA;;0398;;0398\n  // 03F4;GREEK CAPITAL THETA SYMBOL;Lu;0;L;<compat> 0398;;;;N;;;;03B8;\n  //\n  // Case-insensitive comparison should treat all of them as equivalent.\n  //\n  // But .toLowerCase() doesn't change ϑ (it's already lowercase),\n  // and .toUpperCase() doesn't change ϴ (already uppercase).\n  //\n  // Applying first lower then upper case normalizes any character:\n  // '\\u0398\\u03f4\\u03b8\\u03d1'.toLowerCase().toUpperCase() === '\\u0398\\u0398\\u0398\\u0398'\n  //\n  // Note: this is equivalent to unicode case folding; unicode normalization\n  // is a different step that is not required here.\n  //\n  // Final result should be uppercased, because it's later stored in an object\n  // (this avoid a conflict with Object.prototype members,\n  // most notably, `__proto__`)\n  //\n  return str.toLowerCase().toUpperCase()\n}\n\n// Re-export libraries commonly used in both markdown-it and its plugins,\n// so plugins won't have to depend on them explicitly, which reduces their\n// bundled size (e.g. a browser build).\n//\nconst lib = { mdurl, ucmicro }\n\nexport {\n  lib,\n  assign,\n  isString,\n  has,\n  unescapeMd,\n  unescapeAll,\n  isValidEntityCode,\n  fromCodePoint,\n  escapeHtml,\n  arrayReplaceAt,\n  isSpace,\n  isWhiteSpace,\n  isMdAsciiPunct,\n  isPunctChar,\n  escapeRE,\n  normalizeReference\n}\n"], "names": [], "mappings": "AAAA,YAAY;AACZ,EAAE;;;;;;;;;;;;;;;;;;;AAEF;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;;;;AAEA,SAAS,OAAQ,GAAG;IAAI,OAAO,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;AAAK;AAEnE,SAAS,SAAU,GAAG;IAAI,OAAO,OAAO,SAAS;AAAkB;AAEnE,MAAM,kBAAkB,OAAO,SAAS,CAAC,cAAc;AAEvD,SAAS,IAAK,MAAM,EAAE,GAAG;IACvB,OAAO,gBAAgB,IAAI,CAAC,QAAQ;AACtC;AAEA,gBAAgB;AAChB,EAAE;AACF,SAAS,OAAQ,IAAI,4BAA4B,GAA7B;IAClB,MAAM,UAAU,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW;IAEtD,QAAQ,OAAO,CAAC,SAAU,MAAM;QAC9B,IAAI,CAAC,QAAQ;YAAE;QAAO;QAEtB,IAAI,OAAO,WAAW,UAAU;YAC9B,MAAM,IAAI,UAAU,SAAS;QAC/B;QAEA,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,SAAU,GAAG;YACvC,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QACxB;IACF;IAEA,OAAO;AACT;AAEA,qEAAqE;AACrE,yCAAyC;AACzC,SAAS,eAAgB,GAAG,EAAE,GAAG,EAAE,WAAW;IAC5C,OAAO,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,GAAG,MAAM,aAAa,IAAI,KAAK,CAAC,MAAM;AACnE;AAEA,SAAS,kBAAmB,CAAC;IAC3B,uBAAuB,GACvB,kBAAkB;IAClB,IAAI,KAAK,UAAU,KAAK,QAAQ;QAAE,OAAO;IAAM;IAC/C,aAAa;IACb,IAAI,KAAK,UAAU,KAAK,QAAQ;QAAE,OAAO;IAAM;IAC/C,IAAI,CAAC,IAAI,MAAM,MAAM,UAAU,CAAC,IAAI,MAAM,MAAM,QAAQ;QAAE,OAAO;IAAM;IACvE,gBAAgB;IAChB,IAAI,KAAK,QAAQ,KAAK,MAAM;QAAE,OAAO;IAAM;IAC3C,IAAI,MAAM,MAAM;QAAE,OAAO;IAAM;IAC/B,IAAI,KAAK,QAAQ,KAAK,MAAM;QAAE,OAAO;IAAM;IAC3C,IAAI,KAAK,QAAQ,KAAK,MAAM;QAAE,OAAO;IAAM;IAC3C,eAAe;IACf,IAAI,IAAI,UAAU;QAAE,OAAO;IAAM;IACjC,OAAO;AACT;AAEA,SAAS,cAAe,CAAC;IACvB,uBAAuB,GACvB,IAAI,IAAI,QAAQ;QACd,KAAK;QACL,MAAM,aAAa,SAAS,CAAC,KAAK,EAAE;QACpC,MAAM,aAAa,SAAS,CAAC,IAAI,KAAK;QAEtC,OAAO,OAAO,YAAY,CAAC,YAAY;IACzC;IACA,OAAO,OAAO,YAAY,CAAC;AAC7B;AAEA,MAAM,iBAAkB;AACxB,MAAM,YAAkB;AACxB,MAAM,kBAAkB,IAAI,OAAO,eAAe,MAAM,GAAG,MAAM,UAAU,MAAM,EAAE;AAEnF,MAAM,yBAAyB;AAE/B,SAAS,qBAAsB,KAAK,EAAE,IAAI;IACxC,IAAI,KAAK,UAAU,CAAC,OAAO,KAAI,KAAK,OAAM,uBAAuB,IAAI,CAAC,OAAO;QAC3E,MAAM,OAAO,IAAI,CAAC,EAAE,CAAC,WAAW,OAAO,MACnC,SAAS,KAAK,KAAK,CAAC,IAAI,MACxB,SAAS,KAAK,KAAK,CAAC,IAAI;QAE5B,IAAI,kBAAkB,OAAO;YAC3B,OAAO,cAAc;QACvB;QAEA,OAAO;IACT;IAEA,MAAM,UAAU,CAAA,GAAA,wNAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,OAAO;QACrB,OAAO;IACT;IAEA,OAAO;AACT;AAEA;;;;EAIE,GAEF,SAAS,WAAY,GAAG;IACtB,IAAI,IAAI,OAAO,CAAC,QAAQ,GAAG;QAAE,OAAO;IAAI;IACxC,OAAO,IAAI,OAAO,CAAC,gBAAgB;AACrC;AAEA,SAAS,YAAa,GAAG;IACvB,IAAI,IAAI,OAAO,CAAC,QAAQ,KAAK,IAAI,OAAO,CAAC,OAAO,GAAG;QAAE,OAAO;IAAI;IAEhE,OAAO,IAAI,OAAO,CAAC,iBAAiB,SAAU,KAAK,EAAE,OAAO,EAAE,MAAM;QAClE,IAAI,SAAS;YAAE,OAAO;QAAQ;QAC9B,OAAO,qBAAqB,OAAO;IACrC;AACF;AAEA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB;AAC/B,MAAM,oBAAoB;IACxB,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACP;AAEA,SAAS,kBAAmB,EAAE;IAC5B,OAAO,iBAAiB,CAAC,GAAG;AAC9B;AAEA,SAAS,WAAY,GAAG;IACtB,IAAI,oBAAoB,IAAI,CAAC,MAAM;QACjC,OAAO,IAAI,OAAO,CAAC,wBAAwB;IAC7C;IACA,OAAO;AACT;AAEA,MAAM,mBAAmB;AAEzB,SAAS,SAAU,GAAG;IACpB,OAAO,IAAI,OAAO,CAAC,kBAAkB;AACvC;AAEA,SAAS,QAAS,IAAI;IACpB,OAAQ;QACN,KAAK;QACL,KAAK;YACH,OAAO;IACX;IACA,OAAO;AACT;AAEA,qCAAqC;AACrC,SAAS,aAAc,IAAI;IACzB,IAAI,QAAQ,UAAU,QAAQ,QAAQ;QAAE,OAAO;IAAK;IACpD,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;IACX;IACA,OAAO;AACT;AAEA,0BAA0B,GAE1B,+CAA+C;AAC/C,SAAS,YAAa,EAAE;IACtB,OAAO,mPAAA,CAAA,IAAS,CAAC,IAAI,CAAC,OAAO,mPAAA,CAAA,IAAS,CAAC,IAAI,CAAC;AAC9C;AAEA,yCAAyC;AACzC,EAAE;AACF,oGAAoG;AACpG,+DAA+D;AAC/D,EAAE;AACF,iFAAiF;AACjF,EAAE;AACF,SAAS,eAAgB,EAAE;IACzB,OAAQ;QACN,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;YACZ,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,sCAAsC;AACtC,EAAE;AACF,SAAS,mBAAoB,GAAG;IAC9B,+BAA+B;IAC/B,EAAE;IACF,MAAM,IAAI,IAAI,GAAG,OAAO,CAAC,QAAQ;IAEjC,uEAAuE;IACvE,4CAA4C;IAC5C,EAAE;IACF,sCAAsC;IACtC,sDAAsD;IACtD,EAAE;IACF,IAAI,IAAI,WAAW,OAAO,KAAK;QAC7B,MAAM,IAAI,OAAO,CAAC,MAAM;IAC1B;IAEA,iEAAiE;IACjE,2BAA2B;IAC3B,EAAE;IACF,qEAAqE;IACrE,oEAAoE;IACpE,wEAAwE;IACxE,wBAAwB;IACxB,EAAE;IACF,0EAA0E;IAC1E,wEAAwE;IACxE,EAAE;IACF,mBAAmB;IACnB,wDAAwD;IACxD,0DAA0D;IAC1D,gGAAgG;IAChG,qEAAqE;IACrE,EAAE;IACF,sEAAsE;IACtE,EAAE;IACF,gEAAgE;IAChE,2DAA2D;IAC3D,EAAE;IACF,iEAAiE;IACjE,wFAAwF;IACxF,EAAE;IACF,0EAA0E;IAC1E,iDAAiD;IACjD,EAAE;IACF,4EAA4E;IAC5E,wDAAwD;IACxD,6BAA6B;IAC7B,EAAE;IACF,OAAO,IAAI,WAAW,GAAG,WAAW;AACtC;AAEA,yEAAyE;AACzE,0EAA0E;AAC1E,uCAAuC;AACvC,EAAE;AACF,MAAM,MAAM;IAAE,OAAA;IAAO,SAAA;AAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/helpers/parse_link_label.mjs"], "sourcesContent": ["// Parse link label\n//\n// this function assumes that first character (\"[\") already matches;\n// returns the end of the label\n//\n\nexport default function parseLinkLabel (state, start, disableNested) {\n  let level, found, marker, prevPos\n\n  const max = state.posMax\n  const oldPos = state.pos\n\n  state.pos = start + 1\n  level = 1\n\n  while (state.pos < max) {\n    marker = state.src.charCodeAt(state.pos)\n    if (marker === 0x5D /* ] */) {\n      level--\n      if (level === 0) {\n        found = true\n        break\n      }\n    }\n\n    prevPos = state.pos\n    state.md.inline.skipToken(state)\n    if (marker === 0x5B /* [ */) {\n      if (prevPos === state.pos - 1) {\n        // increase level if we find text `[`, which is not a part of any token\n        level++\n      } else if (disableNested) {\n        state.pos = oldPos\n        return -1\n      }\n    }\n  }\n\n  let labelEnd = -1\n\n  if (found) {\n    labelEnd = state.pos\n  }\n\n  // restore old state\n  state.pos = oldPos\n\n  return labelEnd\n}\n"], "names": [], "mappings": "AAAA,mBAAmB;AACnB,EAAE;AACF,oEAAoE;AACpE,+BAA+B;AAC/B,EAAE;;;;AAEa,SAAS,eAAgB,KAAK,EAAE,KAAK,EAAE,aAAa;IACjE,IAAI,OAAO,OAAO,QAAQ;IAE1B,MAAM,MAAM,MAAM,MAAM;IACxB,MAAM,SAAS,MAAM,GAAG;IAExB,MAAM,GAAG,GAAG,QAAQ;IACpB,QAAQ;IAER,MAAO,MAAM,GAAG,GAAG,IAAK;QACtB,SAAS,MAAM,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG;QACvC,IAAI,WAAW,KAAK,KAAK,KAAI;YAC3B;YACA,IAAI,UAAU,GAAG;gBACf,QAAQ;gBACR;YACF;QACF;QAEA,UAAU,MAAM,GAAG;QACnB,MAAM,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC;QAC1B,IAAI,WAAW,KAAK,KAAK,KAAI;YAC3B,IAAI,YAAY,MAAM,GAAG,GAAG,GAAG;gBAC7B,uEAAuE;gBACvE;YACF,OAAO,IAAI,eAAe;gBACxB,MAAM,GAAG,GAAG;gBACZ,OAAO,CAAC;YACV;QACF;IACF;IAEA,IAAI,WAAW,CAAC;IAEhB,IAAI,OAAO;QACT,WAAW,MAAM,GAAG;IACtB;IAEA,oBAAoB;IACpB,MAAM,GAAG,GAAG;IAEZ,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 362, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/helpers/parse_link_destination.mjs"], "sourcesContent": ["// Parse link destination\n//\n\nimport { unescapeAll } from '../common/utils.mjs'\n\nexport default function parseLinkDestination (str, start, max) {\n  let code\n  let pos = start\n\n  const result = {\n    ok: false,\n    pos: 0,\n    str: ''\n  }\n\n  if (str.charCodeAt(pos) === 0x3C /* < */) {\n    pos++\n    while (pos < max) {\n      code = str.charCodeAt(pos)\n      if (code === 0x0A /* \\n */) { return result }\n      if (code === 0x3C /* < */) { return result }\n      if (code === 0x3E /* > */) {\n        result.pos = pos + 1\n        result.str = unescapeAll(str.slice(start + 1, pos))\n        result.ok = true\n        return result\n      }\n      if (code === 0x5C /* \\ */ && pos + 1 < max) {\n        pos += 2\n        continue\n      }\n\n      pos++\n    }\n\n    // no closing '>'\n    return result\n  }\n\n  // this should be ... } else { ... branch\n\n  let level = 0\n  while (pos < max) {\n    code = str.charCodeAt(pos)\n\n    if (code === 0x20) { break }\n\n    // ascii control characters\n    if (code < 0x20 || code === 0x7F) { break }\n\n    if (code === 0x5C /* \\ */ && pos + 1 < max) {\n      if (str.charCodeAt(pos + 1) === 0x20) { break }\n      pos += 2\n      continue\n    }\n\n    if (code === 0x28 /* ( */) {\n      level++\n      if (level > 32) { return result }\n    }\n\n    if (code === 0x29 /* ) */) {\n      if (level === 0) { break }\n      level--\n    }\n\n    pos++\n  }\n\n  if (start === pos) { return result }\n  if (level !== 0) { return result }\n\n  result.str = unescapeAll(str.slice(start, pos))\n  result.pos = pos\n  result.ok = true\n  return result\n}\n"], "names": [], "mappings": "AAAA,yBAAyB;AACzB,EAAE;;;;AAEF;;AAEe,SAAS,qBAAsB,GAAG,EAAE,KAAK,EAAE,GAAG;IAC3D,IAAI;IACJ,IAAI,MAAM;IAEV,MAAM,SAAS;QACb,IAAI;QACJ,KAAK;QACL,KAAK;IACP;IAEA,IAAI,IAAI,UAAU,CAAC,SAAS,KAAK,KAAK,KAAI;QACxC;QACA,MAAO,MAAM,IAAK;YAChB,OAAO,IAAI,UAAU,CAAC;YACtB,IAAI,SAAS,KAAK,MAAM,KAAI;gBAAE,OAAO;YAAO;YAC5C,IAAI,SAAS,KAAK,KAAK,KAAI;gBAAE,OAAO;YAAO;YAC3C,IAAI,SAAS,KAAK,KAAK,KAAI;gBACzB,OAAO,GAAG,GAAG,MAAM;gBACnB,OAAO,GAAG,GAAG,CAAA,GAAA,wNAAA,CAAA,cAAW,AAAD,EAAE,IAAI,KAAK,CAAC,QAAQ,GAAG;gBAC9C,OAAO,EAAE,GAAG;gBACZ,OAAO;YACT;YACA,IAAI,SAAS,KAAK,KAAK,OAAM,MAAM,IAAI,KAAK;gBAC1C,OAAO;gBACP;YACF;YAEA;QACF;QAEA,iBAAiB;QACjB,OAAO;IACT;IAEA,yCAAyC;IAEzC,IAAI,QAAQ;IACZ,MAAO,MAAM,IAAK;QAChB,OAAO,IAAI,UAAU,CAAC;QAEtB,IAAI,SAAS,MAAM;YAAE;QAAM;QAE3B,2BAA2B;QAC3B,IAAI,OAAO,QAAQ,SAAS,MAAM;YAAE;QAAM;QAE1C,IAAI,SAAS,KAAK,KAAK,OAAM,MAAM,IAAI,KAAK;YAC1C,IAAI,IAAI,UAAU,CAAC,MAAM,OAAO,MAAM;gBAAE;YAAM;YAC9C,OAAO;YACP;QACF;QAEA,IAAI,SAAS,KAAK,KAAK,KAAI;YACzB;YACA,IAAI,QAAQ,IAAI;gBAAE,OAAO;YAAO;QAClC;QAEA,IAAI,SAAS,KAAK,KAAK,KAAI;YACzB,IAAI,UAAU,GAAG;gBAAE;YAAM;YACzB;QACF;QAEA;IACF;IAEA,IAAI,UAAU,KAAK;QAAE,OAAO;IAAO;IACnC,IAAI,UAAU,GAAG;QAAE,OAAO;IAAO;IAEjC,OAAO,GAAG,GAAG,CAAA,GAAA,wNAAA,CAAA,cAAW,AAAD,EAAE,IAAI,KAAK,CAAC,OAAO;IAC1C,OAAO,GAAG,GAAG;IACb,OAAO,EAAE,GAAG;IACZ,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 451, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/helpers/parse_link_title.mjs"], "sourcesContent": ["// Parse link title\n//\n\nimport { unescapeAll } from '../common/utils.mjs'\n\n// Parse link title within `str` in [start, max] range,\n// or continue previous parsing if `prev_state` is defined (equal to result of last execution).\n//\nexport default function parseLinkTitle (str, start, max, prev_state) {\n  let code\n  let pos = start\n\n  const state = {\n    // if `true`, this is a valid link title\n    ok: false,\n    // if `true`, this link can be continued on the next line\n    can_continue: false,\n    // if `ok`, it's the position of the first character after the closing marker\n    pos: 0,\n    // if `ok`, it's the unescaped title\n    str: '',\n    // expected closing marker character code\n    marker: 0\n  }\n\n  if (prev_state) {\n    // this is a continuation of a previous parseLinkTitle call on the next line,\n    // used in reference links only\n    state.str = prev_state.str\n    state.marker = prev_state.marker\n  } else {\n    if (pos >= max) { return state }\n\n    let marker = str.charCodeAt(pos)\n    if (marker !== 0x22 /* \" */ && marker !== 0x27 /* ' */ && marker !== 0x28 /* ( */) { return state }\n\n    start++\n    pos++\n\n    // if opening marker is \"(\", switch it to closing marker \")\"\n    if (marker === 0x28) { marker = 0x29 }\n\n    state.marker = marker\n  }\n\n  while (pos < max) {\n    code = str.charCodeAt(pos)\n    if (code === state.marker) {\n      state.pos = pos + 1\n      state.str += unescapeAll(str.slice(start, pos))\n      state.ok = true\n      return state\n    } else if (code === 0x28 /* ( */ && state.marker === 0x29 /* ) */) {\n      return state\n    } else if (code === 0x5C /* \\ */ && pos + 1 < max) {\n      pos++\n    }\n\n    pos++\n  }\n\n  // no closing marker found, but this link title may continue on the next line (for references)\n  state.can_continue = true\n  state.str += unescapeAll(str.slice(start, pos))\n  return state\n}\n"], "names": [], "mappings": "AAAA,mBAAmB;AACnB,EAAE;;;;AAEF;;AAKe,SAAS,eAAgB,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,UAAU;IACjE,IAAI;IACJ,IAAI,MAAM;IAEV,MAAM,QAAQ;QACZ,wCAAwC;QACxC,IAAI;QACJ,yDAAyD;QACzD,cAAc;QACd,6EAA6E;QAC7E,KAAK;QACL,oCAAoC;QACpC,KAAK;QACL,yCAAyC;QACzC,QAAQ;IACV;IAEA,IAAI,YAAY;QACd,6EAA6E;QAC7E,+BAA+B;QAC/B,MAAM,GAAG,GAAG,WAAW,GAAG;QAC1B,MAAM,MAAM,GAAG,WAAW,MAAM;IAClC,OAAO;QACL,IAAI,OAAO,KAAK;YAAE,OAAO;QAAM;QAE/B,IAAI,SAAS,IAAI,UAAU,CAAC;QAC5B,IAAI,WAAW,KAAK,KAAK,OAAM,WAAW,KAAK,KAAK,OAAM,WAAW,KAAK,KAAK,KAAI;YAAE,OAAO;QAAM;QAElG;QACA;QAEA,4DAA4D;QAC5D,IAAI,WAAW,MAAM;YAAE,SAAS;QAAK;QAErC,MAAM,MAAM,GAAG;IACjB;IAEA,MAAO,MAAM,IAAK;QAChB,OAAO,IAAI,UAAU,CAAC;QACtB,IAAI,SAAS,MAAM,MAAM,EAAE;YACzB,MAAM,GAAG,GAAG,MAAM;YAClB,MAAM,GAAG,IAAI,CAAA,GAAA,wNAAA,CAAA,cAAW,AAAD,EAAE,IAAI,KAAK,CAAC,OAAO;YAC1C,MAAM,EAAE,GAAG;YACX,OAAO;QACT,OAAO,IAAI,SAAS,KAAK,KAAK,OAAM,MAAM,MAAM,KAAK,KAAK,KAAK,KAAI;YACjE,OAAO;QACT,OAAO,IAAI,SAAS,KAAK,KAAK,OAAM,MAAM,IAAI,KAAK;YACjD;QACF;QAEA;IACF;IAEA,8FAA8F;IAC9F,MAAM,YAAY,GAAG;IACrB,MAAM,GAAG,IAAI,CAAA,GAAA,wNAAA,CAAA,cAAW,AAAD,EAAE,IAAI,KAAK,CAAC,OAAO;IAC1C,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 519, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/helpers/index.mjs"], "sourcesContent": ["// Just a shortcut for bulk export\n\nimport parseLinkLabel from './parse_link_label.mjs'\nimport parseLinkDestination from './parse_link_destination.mjs'\nimport parseLinkTitle from './parse_link_title.mjs'\n\nexport {\n  parseLinkLabel,\n  parseLinkDestination,\n  parseLinkTitle\n}\n"], "names": [], "mappings": "AAAA,kCAAkC;;AAElC;AACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 573, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/renderer.mjs"], "sourcesContent": ["/**\n * class Renderer\n *\n * Generates HTML from parsed token stream. Each instance has independent\n * copy of rules. Those can be rewritten with ease. Also, you can add new\n * rules if you create plugin and adds new token types.\n **/\n\nimport { assign, unescapeAll, escapeHtml } from './common/utils.mjs'\n\nconst default_rules = {}\n\ndefault_rules.code_inline = function (tokens, idx, options, env, slf) {\n  const token = tokens[idx]\n\n  return  '<code' + slf.renderAttrs(token) + '>' +\n          escapeHtml(token.content) +\n          '</code>'\n}\n\ndefault_rules.code_block = function (tokens, idx, options, env, slf) {\n  const token = tokens[idx]\n\n  return  '<pre' + slf.renderAttrs(token) + '><code>' +\n          escapeHtml(tokens[idx].content) +\n          '</code></pre>\\n'\n}\n\ndefault_rules.fence = function (tokens, idx, options, env, slf) {\n  const token = tokens[idx]\n  const info = token.info ? unescapeAll(token.info).trim() : ''\n  let langName = ''\n  let langAttrs = ''\n\n  if (info) {\n    const arr = info.split(/(\\s+)/g)\n    langName = arr[0]\n    langAttrs = arr.slice(2).join('')\n  }\n\n  let highlighted\n  if (options.highlight) {\n    highlighted = options.highlight(token.content, langName, langAttrs) || escapeHtml(token.content)\n  } else {\n    highlighted = escapeHtml(token.content)\n  }\n\n  if (highlighted.indexOf('<pre') === 0) {\n    return highlighted + '\\n'\n  }\n\n  // If language exists, inject class gently, without modifying original token.\n  // May be, one day we will add .deepClone() for token and simplify this part, but\n  // now we prefer to keep things local.\n  if (info) {\n    const i = token.attrIndex('class')\n    const tmpAttrs = token.attrs ? token.attrs.slice() : []\n\n    if (i < 0) {\n      tmpAttrs.push(['class', options.langPrefix + langName])\n    } else {\n      tmpAttrs[i] = tmpAttrs[i].slice()\n      tmpAttrs[i][1] += ' ' + options.langPrefix + langName\n    }\n\n    // Fake token just to render attributes\n    const tmpToken = {\n      attrs: tmpAttrs\n    }\n\n    return `<pre><code${slf.renderAttrs(tmpToken)}>${highlighted}</code></pre>\\n`\n  }\n\n  return `<pre><code${slf.renderAttrs(token)}>${highlighted}</code></pre>\\n`\n}\n\ndefault_rules.image = function (tokens, idx, options, env, slf) {\n  const token = tokens[idx]\n\n  // \"alt\" attr MUST be set, even if empty. Because it's mandatory and\n  // should be placed on proper position for tests.\n  //\n  // Replace content with actual value\n\n  token.attrs[token.attrIndex('alt')][1] =\n    slf.renderInlineAsText(token.children, options, env)\n\n  return slf.renderToken(tokens, idx, options)\n}\n\ndefault_rules.hardbreak = function (tokens, idx, options /*, env */) {\n  return options.xhtmlOut ? '<br />\\n' : '<br>\\n'\n}\ndefault_rules.softbreak = function (tokens, idx, options /*, env */) {\n  return options.breaks ? (options.xhtmlOut ? '<br />\\n' : '<br>\\n') : '\\n'\n}\n\ndefault_rules.text = function (tokens, idx /*, options, env */) {\n  return escapeHtml(tokens[idx].content)\n}\n\ndefault_rules.html_block = function (tokens, idx /*, options, env */) {\n  return tokens[idx].content\n}\ndefault_rules.html_inline = function (tokens, idx /*, options, env */) {\n  return tokens[idx].content\n}\n\n/**\n * new Renderer()\n *\n * Creates new [[Renderer]] instance and fill [[Renderer#rules]] with defaults.\n **/\nfunction Renderer () {\n  /**\n   * Renderer#rules -> Object\n   *\n   * Contains render rules for tokens. Can be updated and extended.\n   *\n   * ##### Example\n   *\n   * ```javascript\n   * var md = require('markdown-it')();\n   *\n   * md.renderer.rules.strong_open  = function () { return '<b>'; };\n   * md.renderer.rules.strong_close = function () { return '</b>'; };\n   *\n   * var result = md.renderInline(...);\n   * ```\n   *\n   * Each rule is called as independent static function with fixed signature:\n   *\n   * ```javascript\n   * function my_token_render(tokens, idx, options, env, renderer) {\n   *   // ...\n   *   return renderedHTML;\n   * }\n   * ```\n   *\n   * See [source code](https://github.com/markdown-it/markdown-it/blob/master/lib/renderer.mjs)\n   * for more details and examples.\n   **/\n  this.rules = assign({}, default_rules)\n}\n\n/**\n * Renderer.renderAttrs(token) -> String\n *\n * Render token attributes to string.\n **/\nRenderer.prototype.renderAttrs = function renderAttrs (token) {\n  let i, l, result\n\n  if (!token.attrs) { return '' }\n\n  result = ''\n\n  for (i = 0, l = token.attrs.length; i < l; i++) {\n    result += ' ' + escapeHtml(token.attrs[i][0]) + '=\"' + escapeHtml(token.attrs[i][1]) + '\"'\n  }\n\n  return result\n}\n\n/**\n * Renderer.renderToken(tokens, idx, options) -> String\n * - tokens (Array): list of tokens\n * - idx (Numbed): token index to render\n * - options (Object): params of parser instance\n *\n * Default token renderer. Can be overriden by custom function\n * in [[Renderer#rules]].\n **/\nRenderer.prototype.renderToken = function renderToken (tokens, idx, options) {\n  const token = tokens[idx]\n  let result = ''\n\n  // Tight list paragraphs\n  if (token.hidden) {\n    return ''\n  }\n\n  // Insert a newline between hidden paragraph and subsequent opening\n  // block-level tag.\n  //\n  // For example, here we should insert a newline before blockquote:\n  //  - a\n  //    >\n  //\n  if (token.block && token.nesting !== -1 && idx && tokens[idx - 1].hidden) {\n    result += '\\n'\n  }\n\n  // Add token name, e.g. `<img`\n  result += (token.nesting === -1 ? '</' : '<') + token.tag\n\n  // Encode attributes, e.g. `<img src=\"foo\"`\n  result += this.renderAttrs(token)\n\n  // Add a slash for self-closing tags, e.g. `<img src=\"foo\" /`\n  if (token.nesting === 0 && options.xhtmlOut) {\n    result += ' /'\n  }\n\n  // Check if we need to add a newline after this tag\n  let needLf = false\n  if (token.block) {\n    needLf = true\n\n    if (token.nesting === 1) {\n      if (idx + 1 < tokens.length) {\n        const nextToken = tokens[idx + 1]\n\n        if (nextToken.type === 'inline' || nextToken.hidden) {\n          // Block-level tag containing an inline tag.\n          //\n          needLf = false\n        } else if (nextToken.nesting === -1 && nextToken.tag === token.tag) {\n          // Opening tag + closing tag of the same type. E.g. `<li></li>`.\n          //\n          needLf = false\n        }\n      }\n    }\n  }\n\n  result += needLf ? '>\\n' : '>'\n\n  return result\n}\n\n/**\n * Renderer.renderInline(tokens, options, env) -> String\n * - tokens (Array): list on block tokens to render\n * - options (Object): params of parser instance\n * - env (Object): additional data from parsed input (references, for example)\n *\n * The same as [[Renderer.render]], but for single token of `inline` type.\n **/\nRenderer.prototype.renderInline = function (tokens, options, env) {\n  let result = ''\n  const rules = this.rules\n\n  for (let i = 0, len = tokens.length; i < len; i++) {\n    const type = tokens[i].type\n\n    if (typeof rules[type] !== 'undefined') {\n      result += rules[type](tokens, i, options, env, this)\n    } else {\n      result += this.renderToken(tokens, i, options)\n    }\n  }\n\n  return result\n}\n\n/** internal\n * Renderer.renderInlineAsText(tokens, options, env) -> String\n * - tokens (Array): list on block tokens to render\n * - options (Object): params of parser instance\n * - env (Object): additional data from parsed input (references, for example)\n *\n * Special kludge for image `alt` attributes to conform CommonMark spec.\n * Don't try to use it! Spec requires to show `alt` content with stripped markup,\n * instead of simple escaping.\n **/\nRenderer.prototype.renderInlineAsText = function (tokens, options, env) {\n  let result = ''\n\n  for (let i = 0, len = tokens.length; i < len; i++) {\n    switch (tokens[i].type) {\n      case 'text':\n        result += tokens[i].content\n        break\n      case 'image':\n        result += this.renderInlineAsText(tokens[i].children, options, env)\n        break\n      case 'html_inline':\n      case 'html_block':\n        result += tokens[i].content\n        break\n      case 'softbreak':\n      case 'hardbreak':\n        result += '\\n'\n        break\n      default:\n        // all other tokens are skipped\n    }\n  }\n\n  return result\n}\n\n/**\n * Renderer.render(tokens, options, env) -> String\n * - tokens (Array): list on block tokens to render\n * - options (Object): params of parser instance\n * - env (Object): additional data from parsed input (references, for example)\n *\n * Takes token stream and generates HTML. Probably, you will never need to call\n * this method directly.\n **/\nRenderer.prototype.render = function (tokens, options, env) {\n  let result = ''\n  const rules = this.rules\n\n  for (let i = 0, len = tokens.length; i < len; i++) {\n    const type = tokens[i].type\n\n    if (type === 'inline') {\n      result += this.renderInline(tokens[i].children, options, env)\n    } else if (typeof rules[type] !== 'undefined') {\n      result += rules[type](tokens, i, options, env, this)\n    } else {\n      result += this.renderToken(tokens, i, options, env)\n    }\n  }\n\n  return result\n}\n\nexport default Renderer\n"], "names": [], "mappings": "AAAA;;;;;;EAME;;;AAEF;;AAEA,MAAM,gBAAgB,CAAC;AAEvB,cAAc,WAAW,GAAG,SAAU,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG;IAClE,MAAM,QAAQ,MAAM,CAAC,IAAI;IAEzB,OAAQ,UAAU,IAAI,WAAW,CAAC,SAAS,MACnC,CAAA,GAAA,wNAAA,CAAA,aAAU,AAAD,EAAE,MAAM,OAAO,IACxB;AACV;AAEA,cAAc,UAAU,GAAG,SAAU,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG;IACjE,MAAM,QAAQ,MAAM,CAAC,IAAI;IAEzB,OAAQ,SAAS,IAAI,WAAW,CAAC,SAAS,YAClC,CAAA,GAAA,wNAAA,CAAA,aAAU,AAAD,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,IAC9B;AACV;AAEA,cAAc,KAAK,GAAG,SAAU,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG;IAC5D,MAAM,QAAQ,MAAM,CAAC,IAAI;IACzB,MAAM,OAAO,MAAM,IAAI,GAAG,CAAA,GAAA,wNAAA,CAAA,cAAW,AAAD,EAAE,MAAM,IAAI,EAAE,IAAI,KAAK;IAC3D,IAAI,WAAW;IACf,IAAI,YAAY;IAEhB,IAAI,MAAM;QACR,MAAM,MAAM,KAAK,KAAK,CAAC;QACvB,WAAW,GAAG,CAAC,EAAE;QACjB,YAAY,IAAI,KAAK,CAAC,GAAG,IAAI,CAAC;IAChC;IAEA,IAAI;IACJ,IAAI,QAAQ,SAAS,EAAE;QACrB,cAAc,QAAQ,SAAS,CAAC,MAAM,OAAO,EAAE,UAAU,cAAc,CAAA,GAAA,wNAAA,CAAA,aAAU,AAAD,EAAE,MAAM,OAAO;IACjG,OAAO;QACL,cAAc,CAAA,GAAA,wNAAA,CAAA,aAAU,AAAD,EAAE,MAAM,OAAO;IACxC;IAEA,IAAI,YAAY,OAAO,CAAC,YAAY,GAAG;QACrC,OAAO,cAAc;IACvB;IAEA,6EAA6E;IAC7E,iFAAiF;IACjF,sCAAsC;IACtC,IAAI,MAAM;QACR,MAAM,IAAI,MAAM,SAAS,CAAC;QAC1B,MAAM,WAAW,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,KAAK,KAAK,EAAE;QAEvD,IAAI,IAAI,GAAG;YACT,SAAS,IAAI,CAAC;gBAAC;gBAAS,QAAQ,UAAU,GAAG;aAAS;QACxD,OAAO;YACL,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC,KAAK;YAC/B,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,MAAM,QAAQ,UAAU,GAAG;QAC/C;QAEA,uCAAuC;QACvC,MAAM,WAAW;YACf,OAAO;QACT;QAEA,OAAO,CAAC,UAAU,EAAE,IAAI,WAAW,CAAC,UAAU,CAAC,EAAE,YAAY,eAAe,CAAC;IAC/E;IAEA,OAAO,CAAC,UAAU,EAAE,IAAI,WAAW,CAAC,OAAO,CAAC,EAAE,YAAY,eAAe,CAAC;AAC5E;AAEA,cAAc,KAAK,GAAG,SAAU,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG;IAC5D,MAAM,QAAQ,MAAM,CAAC,IAAI;IAEzB,oEAAoE;IACpE,iDAAiD;IACjD,EAAE;IACF,oCAAoC;IAEpC,MAAM,KAAK,CAAC,MAAM,SAAS,CAAC,OAAO,CAAC,EAAE,GACpC,IAAI,kBAAkB,CAAC,MAAM,QAAQ,EAAE,SAAS;IAElD,OAAO,IAAI,WAAW,CAAC,QAAQ,KAAK;AACtC;AAEA,cAAc,SAAS,GAAG,SAAU,MAAM,EAAE,GAAG,EAAE,QAAQ,QAAQ,GAAT;IACtD,OAAO,QAAQ,QAAQ,GAAG,aAAa;AACzC;AACA,cAAc,SAAS,GAAG,SAAU,MAAM,EAAE,GAAG,EAAE,QAAQ,QAAQ,GAAT;IACtD,OAAO,QAAQ,MAAM,GAAI,QAAQ,QAAQ,GAAG,aAAa,WAAY;AACvE;AAEA,cAAc,IAAI,GAAG,SAAU,MAAM,EAAE,IAAI,iBAAiB,GAAlB;IACxC,OAAO,CAAA,GAAA,wNAAA,CAAA,aAAU,AAAD,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO;AACvC;AAEA,cAAc,UAAU,GAAG,SAAU,MAAM,EAAE,IAAI,iBAAiB,GAAlB;IAC9C,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO;AAC5B;AACA,cAAc,WAAW,GAAG,SAAU,MAAM,EAAE,IAAI,iBAAiB,GAAlB;IAC/C,OAAO,MAAM,CAAC,IAAI,CAAC,OAAO;AAC5B;AAEA;;;;EAIE,GACF,SAAS;IACP;;;;;;;;;;;;;;;;;;;;;;;;;;;IA2BE,GACF,IAAI,CAAC,KAAK,GAAG,CAAA,GAAA,wNAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG;AAC1B;AAEA;;;;EAIE,GACF,SAAS,SAAS,CAAC,WAAW,GAAG,SAAS,YAAa,KAAK;IAC1D,IAAI,GAAG,GAAG;IAEV,IAAI,CAAC,MAAM,KAAK,EAAE;QAAE,OAAO;IAAG;IAE9B,SAAS;IAET,IAAK,IAAI,GAAG,IAAI,MAAM,KAAK,CAAC,MAAM,EAAE,IAAI,GAAG,IAAK;QAC9C,UAAU,MAAM,CAAA,GAAA,wNAAA,CAAA,aAAU,AAAD,EAAE,MAAM,KAAK,CAAC,EAAE,CAAC,EAAE,IAAI,OAAO,CAAA,GAAA,wNAAA,CAAA,aAAU,AAAD,EAAE,MAAM,KAAK,CAAC,EAAE,CAAC,EAAE,IAAI;IACzF;IAEA,OAAO;AACT;AAEA;;;;;;;;EAQE,GACF,SAAS,SAAS,CAAC,WAAW,GAAG,SAAS,YAAa,MAAM,EAAE,GAAG,EAAE,OAAO;IACzE,MAAM,QAAQ,MAAM,CAAC,IAAI;IACzB,IAAI,SAAS;IAEb,wBAAwB;IACxB,IAAI,MAAM,MAAM,EAAE;QAChB,OAAO;IACT;IAEA,mEAAmE;IACnE,mBAAmB;IACnB,EAAE;IACF,kEAAkE;IAClE,OAAO;IACP,OAAO;IACP,EAAE;IACF,IAAI,MAAM,KAAK,IAAI,MAAM,OAAO,KAAK,CAAC,KAAK,OAAO,MAAM,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE;QACxE,UAAU;IACZ;IAEA,8BAA8B;IAC9B,UAAU,CAAC,MAAM,OAAO,KAAK,CAAC,IAAI,OAAO,GAAG,IAAI,MAAM,GAAG;IAEzD,2CAA2C;IAC3C,UAAU,IAAI,CAAC,WAAW,CAAC;IAE3B,6DAA6D;IAC7D,IAAI,MAAM,OAAO,KAAK,KAAK,QAAQ,QAAQ,EAAE;QAC3C,UAAU;IACZ;IAEA,mDAAmD;IACnD,IAAI,SAAS;IACb,IAAI,MAAM,KAAK,EAAE;QACf,SAAS;QAET,IAAI,MAAM,OAAO,KAAK,GAAG;YACvB,IAAI,MAAM,IAAI,OAAO,MAAM,EAAE;gBAC3B,MAAM,YAAY,MAAM,CAAC,MAAM,EAAE;gBAEjC,IAAI,UAAU,IAAI,KAAK,YAAY,UAAU,MAAM,EAAE;oBACnD,4CAA4C;oBAC5C,EAAE;oBACF,SAAS;gBACX,OAAO,IAAI,UAAU,OAAO,KAAK,CAAC,KAAK,UAAU,GAAG,KAAK,MAAM,GAAG,EAAE;oBAClE,gEAAgE;oBAChE,EAAE;oBACF,SAAS;gBACX;YACF;QACF;IACF;IAEA,UAAU,SAAS,QAAQ;IAE3B,OAAO;AACT;AAEA;;;;;;;EAOE,GACF,SAAS,SAAS,CAAC,YAAY,GAAG,SAAU,MAAM,EAAE,OAAO,EAAE,GAAG;IAC9D,IAAI,SAAS;IACb,MAAM,QAAQ,IAAI,CAAC,KAAK;IAExB,IAAK,IAAI,IAAI,GAAG,MAAM,OAAO,MAAM,EAAE,IAAI,KAAK,IAAK;QACjD,MAAM,OAAO,MAAM,CAAC,EAAE,CAAC,IAAI;QAE3B,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,aAAa;YACtC,UAAU,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,SAAS,KAAK,IAAI;QACrD,OAAO;YACL,UAAU,IAAI,CAAC,WAAW,CAAC,QAAQ,GAAG;QACxC;IACF;IAEA,OAAO;AACT;AAEA;;;;;;;;;EASE,GACF,SAAS,SAAS,CAAC,kBAAkB,GAAG,SAAU,MAAM,EAAE,OAAO,EAAE,GAAG;IACpE,IAAI,SAAS;IAEb,IAAK,IAAI,IAAI,GAAG,MAAM,OAAO,MAAM,EAAE,IAAI,KAAK,IAAK;QACjD,OAAQ,MAAM,CAAC,EAAE,CAAC,IAAI;YACpB,KAAK;gBACH,UAAU,MAAM,CAAC,EAAE,CAAC,OAAO;gBAC3B;YACF,KAAK;gBACH,UAAU,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS;gBAC/D;YACF,KAAK;YACL,KAAK;gBACH,UAAU,MAAM,CAAC,EAAE,CAAC,OAAO;gBAC3B;YACF,KAAK;YACL,KAAK;gBACH,UAAU;gBACV;YACF;QAEF;IACF;IAEA,OAAO;AACT;AAEA;;;;;;;;EAQE,GACF,SAAS,SAAS,CAAC,MAAM,GAAG,SAAU,MAAM,EAAE,OAAO,EAAE,GAAG;IACxD,IAAI,SAAS;IACb,MAAM,QAAQ,IAAI,CAAC,KAAK;IAExB,IAAK,IAAI,IAAI,GAAG,MAAM,OAAO,MAAM,EAAE,IAAI,KAAK,IAAK;QACjD,MAAM,OAAO,MAAM,CAAC,EAAE,CAAC,IAAI;QAE3B,IAAI,SAAS,UAAU;YACrB,UAAU,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS;QAC3D,OAAO,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,aAAa;YAC7C,UAAU,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,SAAS,KAAK,IAAI;QACrD,OAAO;YACL,UAAU,IAAI,CAAC,WAAW,CAAC,QAAQ,GAAG,SAAS;QACjD;IACF;IAEA,OAAO;AACT;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 845, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/ruler.mjs"], "sourcesContent": ["/**\n * class Ruler\n *\n * Helper class, used by [[MarkdownIt#core]], [[MarkdownIt#block]] and\n * [[MarkdownIt#inline]] to manage sequences of functions (rules):\n *\n * - keep rules in defined order\n * - assign the name to each rule\n * - enable/disable rules\n * - add/replace rules\n * - allow assign rules to additional named chains (in the same)\n * - cacheing lists of active rules\n *\n * You will not need use this class directly until write plugins. For simple\n * rules control use [[MarkdownIt.disable]], [[MarkdownIt.enable]] and\n * [[MarkdownIt.use]].\n **/\n\n/**\n * new Ruler()\n **/\nfunction Ruler () {\n  // List of added rules. Each element is:\n  //\n  // {\n  //   name: XXX,\n  //   enabled: Boolean,\n  //   fn: Function(),\n  //   alt: [ name2, name3 ]\n  // }\n  //\n  this.__rules__ = []\n\n  // Cached rule chains.\n  //\n  // First level - chain name, '' for default.\n  // Second level - diginal anchor for fast filtering by charcodes.\n  //\n  this.__cache__ = null\n}\n\n// Helper methods, should not be used directly\n\n// Find rule index by name\n//\nRuler.prototype.__find__ = function (name) {\n  for (let i = 0; i < this.__rules__.length; i++) {\n    if (this.__rules__[i].name === name) {\n      return i\n    }\n  }\n  return -1\n}\n\n// Build rules lookup cache\n//\nRuler.prototype.__compile__ = function () {\n  const self = this\n  const chains = ['']\n\n  // collect unique names\n  self.__rules__.forEach(function (rule) {\n    if (!rule.enabled) { return }\n\n    rule.alt.forEach(function (altName) {\n      if (chains.indexOf(altName) < 0) {\n        chains.push(altName)\n      }\n    })\n  })\n\n  self.__cache__ = {}\n\n  chains.forEach(function (chain) {\n    self.__cache__[chain] = []\n    self.__rules__.forEach(function (rule) {\n      if (!rule.enabled) { return }\n\n      if (chain && rule.alt.indexOf(chain) < 0) { return }\n\n      self.__cache__[chain].push(rule.fn)\n    })\n  })\n}\n\n/**\n * Ruler.at(name, fn [, options])\n * - name (String): rule name to replace.\n * - fn (Function): new rule function.\n * - options (Object): new rule options (not mandatory).\n *\n * Replace rule by name with new function & options. Throws error if name not\n * found.\n *\n * ##### Options:\n *\n * - __alt__ - array with names of \"alternate\" chains.\n *\n * ##### Example\n *\n * Replace existing typographer replacement rule with new one:\n *\n * ```javascript\n * var md = require('markdown-it')();\n *\n * md.core.ruler.at('replacements', function replace(state) {\n *   //...\n * });\n * ```\n **/\nRuler.prototype.at = function (name, fn, options) {\n  const index = this.__find__(name)\n  const opt = options || {}\n\n  if (index === -1) { throw new Error('Parser rule not found: ' + name) }\n\n  this.__rules__[index].fn = fn\n  this.__rules__[index].alt = opt.alt || []\n  this.__cache__ = null\n}\n\n/**\n * Ruler.before(beforeName, ruleName, fn [, options])\n * - beforeName (String): new rule will be added before this one.\n * - ruleName (String): name of added rule.\n * - fn (Function): rule function.\n * - options (Object): rule options (not mandatory).\n *\n * Add new rule to chain before one with given name. See also\n * [[Ruler.after]], [[Ruler.push]].\n *\n * ##### Options:\n *\n * - __alt__ - array with names of \"alternate\" chains.\n *\n * ##### Example\n *\n * ```javascript\n * var md = require('markdown-it')();\n *\n * md.block.ruler.before('paragraph', 'my_rule', function replace(state) {\n *   //...\n * });\n * ```\n **/\nRuler.prototype.before = function (beforeName, ruleName, fn, options) {\n  const index = this.__find__(beforeName)\n  const opt = options || {}\n\n  if (index === -1) { throw new Error('Parser rule not found: ' + beforeName) }\n\n  this.__rules__.splice(index, 0, {\n    name: ruleName,\n    enabled: true,\n    fn,\n    alt: opt.alt || []\n  })\n\n  this.__cache__ = null\n}\n\n/**\n * Ruler.after(afterName, ruleName, fn [, options])\n * - afterName (String): new rule will be added after this one.\n * - ruleName (String): name of added rule.\n * - fn (Function): rule function.\n * - options (Object): rule options (not mandatory).\n *\n * Add new rule to chain after one with given name. See also\n * [[Ruler.before]], [[Ruler.push]].\n *\n * ##### Options:\n *\n * - __alt__ - array with names of \"alternate\" chains.\n *\n * ##### Example\n *\n * ```javascript\n * var md = require('markdown-it')();\n *\n * md.inline.ruler.after('text', 'my_rule', function replace(state) {\n *   //...\n * });\n * ```\n **/\nRuler.prototype.after = function (afterName, ruleName, fn, options) {\n  const index = this.__find__(afterName)\n  const opt = options || {}\n\n  if (index === -1) { throw new Error('Parser rule not found: ' + afterName) }\n\n  this.__rules__.splice(index + 1, 0, {\n    name: ruleName,\n    enabled: true,\n    fn,\n    alt: opt.alt || []\n  })\n\n  this.__cache__ = null\n}\n\n/**\n * Ruler.push(ruleName, fn [, options])\n * - ruleName (String): name of added rule.\n * - fn (Function): rule function.\n * - options (Object): rule options (not mandatory).\n *\n * Push new rule to the end of chain. See also\n * [[Ruler.before]], [[Ruler.after]].\n *\n * ##### Options:\n *\n * - __alt__ - array with names of \"alternate\" chains.\n *\n * ##### Example\n *\n * ```javascript\n * var md = require('markdown-it')();\n *\n * md.core.ruler.push('my_rule', function replace(state) {\n *   //...\n * });\n * ```\n **/\nRuler.prototype.push = function (ruleName, fn, options) {\n  const opt = options || {}\n\n  this.__rules__.push({\n    name: ruleName,\n    enabled: true,\n    fn,\n    alt: opt.alt || []\n  })\n\n  this.__cache__ = null\n}\n\n/**\n * Ruler.enable(list [, ignoreInvalid]) -> Array\n * - list (String|Array): list of rule names to enable.\n * - ignoreInvalid (Boolean): set `true` to ignore errors when rule not found.\n *\n * Enable rules with given names. If any rule name not found - throw Error.\n * Errors can be disabled by second param.\n *\n * Returns list of found rule names (if no exception happened).\n *\n * See also [[Ruler.disable]], [[Ruler.enableOnly]].\n **/\nRuler.prototype.enable = function (list, ignoreInvalid) {\n  if (!Array.isArray(list)) { list = [list] }\n\n  const result = []\n\n  // Search by name and enable\n  list.forEach(function (name) {\n    const idx = this.__find__(name)\n\n    if (idx < 0) {\n      if (ignoreInvalid) { return }\n      throw new Error('Rules manager: invalid rule name ' + name)\n    }\n    this.__rules__[idx].enabled = true\n    result.push(name)\n  }, this)\n\n  this.__cache__ = null\n  return result\n}\n\n/**\n * Ruler.enableOnly(list [, ignoreInvalid])\n * - list (String|Array): list of rule names to enable (whitelist).\n * - ignoreInvalid (Boolean): set `true` to ignore errors when rule not found.\n *\n * Enable rules with given names, and disable everything else. If any rule name\n * not found - throw Error. Errors can be disabled by second param.\n *\n * See also [[Ruler.disable]], [[Ruler.enable]].\n **/\nRuler.prototype.enableOnly = function (list, ignoreInvalid) {\n  if (!Array.isArray(list)) { list = [list] }\n\n  this.__rules__.forEach(function (rule) { rule.enabled = false })\n\n  this.enable(list, ignoreInvalid)\n}\n\n/**\n * Ruler.disable(list [, ignoreInvalid]) -> Array\n * - list (String|Array): list of rule names to disable.\n * - ignoreInvalid (Boolean): set `true` to ignore errors when rule not found.\n *\n * Disable rules with given names. If any rule name not found - throw Error.\n * Errors can be disabled by second param.\n *\n * Returns list of found rule names (if no exception happened).\n *\n * See also [[Ruler.enable]], [[Ruler.enableOnly]].\n **/\nRuler.prototype.disable = function (list, ignoreInvalid) {\n  if (!Array.isArray(list)) { list = [list] }\n\n  const result = []\n\n  // Search by name and disable\n  list.forEach(function (name) {\n    const idx = this.__find__(name)\n\n    if (idx < 0) {\n      if (ignoreInvalid) { return }\n      throw new Error('Rules manager: invalid rule name ' + name)\n    }\n    this.__rules__[idx].enabled = false\n    result.push(name)\n  }, this)\n\n  this.__cache__ = null\n  return result\n}\n\n/**\n * Ruler.getRules(chainName) -> Array\n *\n * Return array of active functions (rules) for given chain name. It analyzes\n * rules configuration, compiles caches if not exists and returns result.\n *\n * Default chain name is `''` (empty string). It can't be skipped. That's\n * done intentionally, to keep signature monomorphic for high speed.\n **/\nRuler.prototype.getRules = function (chainName) {\n  if (this.__cache__ === null) {\n    this.__compile__()\n  }\n\n  // Chain can be empty, if rules disabled. But we still have to return Array.\n  return this.__cache__[chainName] || []\n}\n\nexport default Ruler\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;EAgBE,GAEF;;EAEE;;;AACF,SAAS;IACP,wCAAwC;IACxC,EAAE;IACF,IAAI;IACJ,eAAe;IACf,sBAAsB;IACtB,oBAAoB;IACpB,0BAA0B;IAC1B,IAAI;IACJ,EAAE;IACF,IAAI,CAAC,SAAS,GAAG,EAAE;IAEnB,sBAAsB;IACtB,EAAE;IACF,4CAA4C;IAC5C,iEAAiE;IACjE,EAAE;IACF,IAAI,CAAC,SAAS,GAAG;AACnB;AAEA,8CAA8C;AAE9C,0BAA0B;AAC1B,EAAE;AACF,MAAM,SAAS,CAAC,QAAQ,GAAG,SAAU,IAAI;IACvC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAK;QAC9C,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,KAAK,MAAM;YACnC,OAAO;QACT;IACF;IACA,OAAO,CAAC;AACV;AAEA,2BAA2B;AAC3B,EAAE;AACF,MAAM,SAAS,CAAC,WAAW,GAAG;IAC5B,MAAM,OAAO,IAAI;IACjB,MAAM,SAAS;QAAC;KAAG;IAEnB,uBAAuB;IACvB,KAAK,SAAS,CAAC,OAAO,CAAC,SAAU,IAAI;QACnC,IAAI,CAAC,KAAK,OAAO,EAAE;YAAE;QAAO;QAE5B,KAAK,GAAG,CAAC,OAAO,CAAC,SAAU,OAAO;YAChC,IAAI,OAAO,OAAO,CAAC,WAAW,GAAG;gBAC/B,OAAO,IAAI,CAAC;YACd;QACF;IACF;IAEA,KAAK,SAAS,GAAG,CAAC;IAElB,OAAO,OAAO,CAAC,SAAU,KAAK;QAC5B,KAAK,SAAS,CAAC,MAAM,GAAG,EAAE;QAC1B,KAAK,SAAS,CAAC,OAAO,CAAC,SAAU,IAAI;YACnC,IAAI,CAAC,KAAK,OAAO,EAAE;gBAAE;YAAO;YAE5B,IAAI,SAAS,KAAK,GAAG,CAAC,OAAO,CAAC,SAAS,GAAG;gBAAE;YAAO;YAEnD,KAAK,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE;QACpC;IACF;AACF;AAEA;;;;;;;;;;;;;;;;;;;;;;;;EAwBE,GACF,MAAM,SAAS,CAAC,EAAE,GAAG,SAAU,IAAI,EAAE,EAAE,EAAE,OAAO;IAC9C,MAAM,QAAQ,IAAI,CAAC,QAAQ,CAAC;IAC5B,MAAM,MAAM,WAAW,CAAC;IAExB,IAAI,UAAU,CAAC,GAAG;QAAE,MAAM,IAAI,MAAM,4BAA4B;IAAM;IAEtE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,GAAG;IAC3B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,GAAG,IAAI,GAAG,IAAI,EAAE;IACzC,IAAI,CAAC,SAAS,GAAG;AACnB;AAEA;;;;;;;;;;;;;;;;;;;;;;;EAuBE,GACF,MAAM,SAAS,CAAC,MAAM,GAAG,SAAU,UAAU,EAAE,QAAQ,EAAE,EAAE,EAAE,OAAO;IAClE,MAAM,QAAQ,IAAI,CAAC,QAAQ,CAAC;IAC5B,MAAM,MAAM,WAAW,CAAC;IAExB,IAAI,UAAU,CAAC,GAAG;QAAE,MAAM,IAAI,MAAM,4BAA4B;IAAY;IAE5E,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,GAAG;QAC9B,MAAM;QACN,SAAS;QACT;QACA,KAAK,IAAI,GAAG,IAAI,EAAE;IACpB;IAEA,IAAI,CAAC,SAAS,GAAG;AACnB;AAEA;;;;;;;;;;;;;;;;;;;;;;;EAuBE,GACF,MAAM,SAAS,CAAC,KAAK,GAAG,SAAU,SAAS,EAAE,QAAQ,EAAE,EAAE,EAAE,OAAO;IAChE,MAAM,QAAQ,IAAI,CAAC,QAAQ,CAAC;IAC5B,MAAM,MAAM,WAAW,CAAC;IAExB,IAAI,UAAU,CAAC,GAAG;QAAE,MAAM,IAAI,MAAM,4BAA4B;IAAW;IAE3E,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,GAAG,GAAG;QAClC,MAAM;QACN,SAAS;QACT;QACA,KAAK,IAAI,GAAG,IAAI,EAAE;IACpB;IAEA,IAAI,CAAC,SAAS,GAAG;AACnB;AAEA;;;;;;;;;;;;;;;;;;;;;;EAsBE,GACF,MAAM,SAAS,CAAC,IAAI,GAAG,SAAU,QAAQ,EAAE,EAAE,EAAE,OAAO;IACpD,MAAM,MAAM,WAAW,CAAC;IAExB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QAClB,MAAM;QACN,SAAS;QACT;QACA,KAAK,IAAI,GAAG,IAAI,EAAE;IACpB;IAEA,IAAI,CAAC,SAAS,GAAG;AACnB;AAEA;;;;;;;;;;;EAWE,GACF,MAAM,SAAS,CAAC,MAAM,GAAG,SAAU,IAAI,EAAE,aAAa;IACpD,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;QAAE,OAAO;YAAC;SAAK;IAAC;IAE1C,MAAM,SAAS,EAAE;IAEjB,4BAA4B;IAC5B,KAAK,OAAO,CAAC,SAAU,IAAI;QACzB,MAAM,MAAM,IAAI,CAAC,QAAQ,CAAC;QAE1B,IAAI,MAAM,GAAG;YACX,IAAI,eAAe;gBAAE;YAAO;YAC5B,MAAM,IAAI,MAAM,sCAAsC;QACxD;QACA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,GAAG;QAC9B,OAAO,IAAI,CAAC;IACd,GAAG,IAAI;IAEP,IAAI,CAAC,SAAS,GAAG;IACjB,OAAO;AACT;AAEA;;;;;;;;;EASE,GACF,MAAM,SAAS,CAAC,UAAU,GAAG,SAAU,IAAI,EAAE,aAAa;IACxD,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;QAAE,OAAO;YAAC;SAAK;IAAC;IAE1C,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,SAAU,IAAI;QAAI,KAAK,OAAO,GAAG;IAAM;IAE9D,IAAI,CAAC,MAAM,CAAC,MAAM;AACpB;AAEA;;;;;;;;;;;EAWE,GACF,MAAM,SAAS,CAAC,OAAO,GAAG,SAAU,IAAI,EAAE,aAAa;IACrD,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;QAAE,OAAO;YAAC;SAAK;IAAC;IAE1C,MAAM,SAAS,EAAE;IAEjB,6BAA6B;IAC7B,KAAK,OAAO,CAAC,SAAU,IAAI;QACzB,MAAM,MAAM,IAAI,CAAC,QAAQ,CAAC;QAE1B,IAAI,MAAM,GAAG;YACX,IAAI,eAAe;gBAAE;YAAO;YAC5B,MAAM,IAAI,MAAM,sCAAsC;QACxD;QACA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,GAAG;QAC9B,OAAO,IAAI,CAAC;IACd,GAAG,IAAI;IAEP,IAAI,CAAC,SAAS,GAAG;IACjB,OAAO;AACT;AAEA;;;;;;;;EAQE,GACF,MAAM,SAAS,CAAC,QAAQ,GAAG,SAAU,SAAS;IAC5C,IAAI,IAAI,CAAC,SAAS,KAAK,MAAM;QAC3B,IAAI,CAAC,WAAW;IAClB;IAEA,4EAA4E;IAC5E,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,IAAI,EAAE;AACxC;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1175, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/token.mjs"], "sourcesContent": ["// Token class\n\n/**\n * class Token\n **/\n\n/**\n * new Token(type, tag, nesting)\n *\n * Create new token and fill passed properties.\n **/\nfunction Token (type, tag, nesting) {\n  /**\n   * Token#type -> String\n   *\n   * Type of the token (string, e.g. \"paragraph_open\")\n   **/\n  this.type     = type\n\n  /**\n   * Token#tag -> String\n   *\n   * html tag name, e.g. \"p\"\n   **/\n  this.tag      = tag\n\n  /**\n   * Token#attrs -> Array\n   *\n   * Html attributes. Format: `[ [ name1, value1 ], [ name2, value2 ] ]`\n   **/\n  this.attrs    = null\n\n  /**\n   * Token#map -> Array\n   *\n   * Source map info. Format: `[ line_begin, line_end ]`\n   **/\n  this.map      = null\n\n  /**\n   * Token#nesting -> Number\n   *\n   * Level change (number in {-1, 0, 1} set), where:\n   *\n   * -  `1` means the tag is opening\n   * -  `0` means the tag is self-closing\n   * - `-1` means the tag is closing\n   **/\n  this.nesting  = nesting\n\n  /**\n   * Token#level -> Number\n   *\n   * nesting level, the same as `state.level`\n   **/\n  this.level    = 0\n\n  /**\n   * Token#children -> Array\n   *\n   * An array of child nodes (inline and img tokens)\n   **/\n  this.children = null\n\n  /**\n   * Token#content -> String\n   *\n   * In a case of self-closing tag (code, html, fence, etc.),\n   * it has contents of this tag.\n   **/\n  this.content  = ''\n\n  /**\n   * Token#markup -> String\n   *\n   * '*' or '_' for emphasis, fence string for fence, etc.\n   **/\n  this.markup   = ''\n\n  /**\n   * Token#info -> String\n   *\n   * Additional information:\n   *\n   * - Info string for \"fence\" tokens\n   * - The value \"auto\" for autolink \"link_open\" and \"link_close\" tokens\n   * - The string value of the item marker for ordered-list \"list_item_open\" tokens\n   **/\n  this.info     = ''\n\n  /**\n   * Token#meta -> Object\n   *\n   * A place for plugins to store an arbitrary data\n   **/\n  this.meta     = null\n\n  /**\n   * Token#block -> Boolean\n   *\n   * True for block-level tokens, false for inline tokens.\n   * Used in renderer to calculate line breaks\n   **/\n  this.block    = false\n\n  /**\n   * Token#hidden -> Boolean\n   *\n   * If it's true, ignore this element when rendering. Used for tight lists\n   * to hide paragraphs.\n   **/\n  this.hidden   = false\n}\n\n/**\n * Token.attrIndex(name) -> Number\n *\n * Search attribute index by name.\n **/\nToken.prototype.attrIndex = function attrIndex (name) {\n  if (!this.attrs) { return -1 }\n\n  const attrs = this.attrs\n\n  for (let i = 0, len = attrs.length; i < len; i++) {\n    if (attrs[i][0] === name) { return i }\n  }\n  return -1\n}\n\n/**\n * Token.attrPush(attrData)\n *\n * Add `[ name, value ]` attribute to list. Init attrs if necessary\n **/\nToken.prototype.attrPush = function attrPush (attrData) {\n  if (this.attrs) {\n    this.attrs.push(attrData)\n  } else {\n    this.attrs = [attrData]\n  }\n}\n\n/**\n * Token.attrSet(name, value)\n *\n * Set `name` attribute to `value`. Override old value if exists.\n **/\nToken.prototype.attrSet = function attrSet (name, value) {\n  const idx = this.attrIndex(name)\n  const attrData = [name, value]\n\n  if (idx < 0) {\n    this.attrPush(attrData)\n  } else {\n    this.attrs[idx] = attrData\n  }\n}\n\n/**\n * Token.attrGet(name)\n *\n * Get the value of attribute `name`, or null if it does not exist.\n **/\nToken.prototype.attrGet = function attrGet (name) {\n  const idx = this.attrIndex(name)\n  let value = null\n  if (idx >= 0) {\n    value = this.attrs[idx][1]\n  }\n  return value\n}\n\n/**\n * Token.attrJoin(name, value)\n *\n * Join value to existing attribute via space. Or create new attribute if not\n * exists. Useful to operate with token classes.\n **/\nToken.prototype.attrJoin = function attrJoin (name, value) {\n  const idx = this.attrIndex(name)\n\n  if (idx < 0) {\n    this.attrPush([name, value])\n  } else {\n    this.attrs[idx][1] = this.attrs[idx][1] + ' ' + value\n  }\n}\n\nexport default Token\n"], "names": [], "mappings": "AAAA,cAAc;AAEd;;EAEE,GAEF;;;;EAIE;;;AACF,SAAS,MAAO,IAAI,EAAE,GAAG,EAAE,OAAO;IAChC;;;;IAIE,GACF,IAAI,CAAC,IAAI,GAAO;IAEhB;;;;IAIE,GACF,IAAI,CAAC,GAAG,GAAQ;IAEhB;;;;IAIE,GACF,IAAI,CAAC,KAAK,GAAM;IAEhB;;;;IAIE,GACF,IAAI,CAAC,GAAG,GAAQ;IAEhB;;;;;;;;IAQE,GACF,IAAI,CAAC,OAAO,GAAI;IAEhB;;;;IAIE,GACF,IAAI,CAAC,KAAK,GAAM;IAEhB;;;;IAIE,GACF,IAAI,CAAC,QAAQ,GAAG;IAEhB;;;;;IAKE,GACF,IAAI,CAAC,OAAO,GAAI;IAEhB;;;;IAIE,GACF,IAAI,CAAC,MAAM,GAAK;IAEhB;;;;;;;;IAQE,GACF,IAAI,CAAC,IAAI,GAAO;IAEhB;;;;IAIE,GACF,IAAI,CAAC,IAAI,GAAO;IAEhB;;;;;IAKE,GACF,IAAI,CAAC,KAAK,GAAM;IAEhB;;;;;IAKE,GACF,IAAI,CAAC,MAAM,GAAK;AAClB;AAEA;;;;EAIE,GACF,MAAM,SAAS,CAAC,SAAS,GAAG,SAAS,UAAW,IAAI;IAClD,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;QAAE,OAAO,CAAC;IAAE;IAE7B,MAAM,QAAQ,IAAI,CAAC,KAAK;IAExB,IAAK,IAAI,IAAI,GAAG,MAAM,MAAM,MAAM,EAAE,IAAI,KAAK,IAAK;QAChD,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,MAAM;YAAE,OAAO;QAAE;IACvC;IACA,OAAO,CAAC;AACV;AAEA;;;;EAIE,GACF,MAAM,SAAS,CAAC,QAAQ,GAAG,SAAS,SAAU,QAAQ;IACpD,IAAI,IAAI,CAAC,KAAK,EAAE;QACd,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IAClB,OAAO;QACL,IAAI,CAAC,KAAK,GAAG;YAAC;SAAS;IACzB;AACF;AAEA;;;;EAIE,GACF,MAAM,SAAS,CAAC,OAAO,GAAG,SAAS,QAAS,IAAI,EAAE,KAAK;IACrD,MAAM,MAAM,IAAI,CAAC,SAAS,CAAC;IAC3B,MAAM,WAAW;QAAC;QAAM;KAAM;IAE9B,IAAI,MAAM,GAAG;QACX,IAAI,CAAC,QAAQ,CAAC;IAChB,OAAO;QACL,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG;IACpB;AACF;AAEA;;;;EAIE,GACF,MAAM,SAAS,CAAC,OAAO,GAAG,SAAS,QAAS,IAAI;IAC9C,MAAM,MAAM,IAAI,CAAC,SAAS,CAAC;IAC3B,IAAI,QAAQ;IACZ,IAAI,OAAO,GAAG;QACZ,QAAQ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;IAC5B;IACA,OAAO;AACT;AAEA;;;;;EAKE,GACF,MAAM,SAAS,CAAC,QAAQ,GAAG,SAAS,SAAU,IAAI,EAAE,KAAK;IACvD,MAAM,MAAM,IAAI,CAAC,SAAS,CAAC;IAE3B,IAAI,MAAM,GAAG;QACX,IAAI,CAAC,QAAQ,CAAC;YAAC;YAAM;SAAM;IAC7B,OAAO;QACL,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,MAAM;IAClD;AACF;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1343, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_core/state_core.mjs"], "sourcesContent": ["// Core state object\n//\n\nimport Token from '../token.mjs'\n\nfunction StateCore (src, md, env) {\n  this.src = src\n  this.env = env\n  this.tokens = []\n  this.inlineMode = false\n  this.md = md // link to parser instance\n}\n\n// re-export Token class to use in core rules\nStateCore.prototype.Token = Token\n\nexport default StateCore\n"], "names": [], "mappings": "AAAA,oBAAoB;AACpB,EAAE;;;;AAEF;;AAEA,SAAS,UAAW,GAAG,EAAE,EAAE,EAAE,GAAG;IAC9B,IAAI,CAAC,GAAG,GAAG;IACX,IAAI,CAAC,GAAG,GAAG;IACX,IAAI,CAAC,MAAM,GAAG,EAAE;IAChB,IAAI,CAAC,UAAU,GAAG;IAClB,IAAI,CAAC,EAAE,GAAG,GAAG,0BAA0B;;AACzC;AAEA,6CAA6C;AAC7C,UAAU,SAAS,CAAC,KAAK,GAAG,8MAAA,CAAA,UAAK;uCAElB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1367, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_core/normalize.mjs"], "sourcesContent": ["// Normalize input string\n\n// https://spec.commonmark.org/0.29/#line-ending\nconst NEWLINES_RE  = /\\r\\n?|\\n/g\nconst NULL_RE      = /\\0/g\n\nexport default function normalize (state) {\n  let str\n\n  // Normalize newlines\n  str = state.src.replace(NEWLINES_RE, '\\n')\n\n  // Replace NULL characters\n  str = str.replace(NULL_RE, '\\uFFFD')\n\n  state.src = str\n}\n"], "names": [], "mappings": "AAAA,yBAAyB;AAEzB,gDAAgD;;;;AAChD,MAAM,cAAe;AACrB,MAAM,UAAe;AAEN,SAAS,UAAW,KAAK;IACtC,IAAI;IAEJ,qBAAqB;IACrB,MAAM,MAAM,GAAG,CAAC,OAAO,CAAC,aAAa;IAErC,0BAA0B;IAC1B,MAAM,IAAI,OAAO,CAAC,SAAS;IAE3B,MAAM,GAAG,GAAG;AACd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1388, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_core/block.mjs"], "sourcesContent": ["export default function block (state) {\n  let token\n\n  if (state.inlineMode) {\n    token          = new state.Token('inline', '', 0)\n    token.content  = state.src\n    token.map      = [0, 1]\n    token.children = []\n    state.tokens.push(token)\n  } else {\n    state.md.block.parse(state.src, state.md, state.env, state.tokens)\n  }\n}\n"], "names": [], "mappings": ";;;AAAe,SAAS,MAAO,KAAK;IAClC,IAAI;IAEJ,IAAI,MAAM,UAAU,EAAE;QACpB,QAAiB,IAAI,MAAM,KAAK,CAAC,UAAU,IAAI;QAC/C,MAAM,OAAO,GAAI,MAAM,GAAG;QAC1B,MAAM,GAAG,GAAQ;YAAC;YAAG;SAAE;QACvB,MAAM,QAAQ,GAAG,EAAE;QACnB,MAAM,MAAM,CAAC,IAAI,CAAC;IACpB,OAAO;QACL,MAAM,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,MAAM,EAAE,EAAE,MAAM,GAAG,EAAE,MAAM,MAAM;IACnE;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1412, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_core/inline.mjs"], "sourcesContent": ["export default function inline (state) {\n  const tokens = state.tokens\n\n  // Parse inlines\n  for (let i = 0, l = tokens.length; i < l; i++) {\n    const tok = tokens[i]\n    if (tok.type === 'inline') {\n      state.md.inline.parse(tok.content, state.md, state.env, tok.children)\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAe,SAAS,OAAQ,KAAK;IACnC,MAAM,SAAS,MAAM,MAAM;IAE3B,gBAAgB;IAChB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAI,GAAG,IAAK;QAC7C,MAAM,MAAM,MAAM,CAAC,EAAE;QACrB,IAAI,IAAI,IAAI,KAAK,UAAU;YACzB,MAAM,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,OAAO,EAAE,MAAM,EAAE,EAAE,MAAM,GAAG,EAAE,IAAI,QAAQ;QACtE;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1431, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_core/linkify.mjs"], "sourcesContent": ["// Replace link-like texts with link nodes.\n//\n// Currently restricted by `md.validateLink()` to http/https/ftp\n//\n\nimport { arrayReplaceAt } from '../common/utils.mjs'\n\nfunction isLinkOpen (str) {\n  return /^<a[>\\s]/i.test(str)\n}\nfunction isLinkClose (str) {\n  return /^<\\/a\\s*>/i.test(str)\n}\n\nexport default function linkify (state) {\n  const blockTokens = state.tokens\n\n  if (!state.md.options.linkify) { return }\n\n  for (let j = 0, l = blockTokens.length; j < l; j++) {\n    if (blockTokens[j].type !== 'inline' ||\n        !state.md.linkify.pretest(blockTokens[j].content)) {\n      continue\n    }\n\n    let tokens = blockTokens[j].children\n\n    let htmlLinkLevel = 0\n\n    // We scan from the end, to keep position when new tags added.\n    // Use reversed logic in links start/end match\n    for (let i = tokens.length - 1; i >= 0; i--) {\n      const currentToken = tokens[i]\n\n      // Skip content of markdown links\n      if (currentToken.type === 'link_close') {\n        i--\n        while (tokens[i].level !== currentToken.level && tokens[i].type !== 'link_open') {\n          i--\n        }\n        continue\n      }\n\n      // Skip content of html tag links\n      if (currentToken.type === 'html_inline') {\n        if (isLinkOpen(currentToken.content) && htmlLinkLevel > 0) {\n          htmlLinkLevel--\n        }\n        if (isLinkClose(currentToken.content)) {\n          htmlLinkLevel++\n        }\n      }\n      if (htmlLinkLevel > 0) { continue }\n\n      if (currentToken.type === 'text' && state.md.linkify.test(currentToken.content)) {\n        const text = currentToken.content\n        let links = state.md.linkify.match(text)\n\n        // Now split string to nodes\n        const nodes = []\n        let level = currentToken.level\n        let lastPos = 0\n\n        // forbid escape sequence at the start of the string,\n        // this avoids http\\://example.com/ from being linkified as\n        // http:<a href=\"//example.com/\">//example.com/</a>\n        if (links.length > 0 &&\n            links[0].index === 0 &&\n            i > 0 &&\n            tokens[i - 1].type === 'text_special') {\n          links = links.slice(1)\n        }\n\n        for (let ln = 0; ln < links.length; ln++) {\n          const url = links[ln].url\n          const fullUrl = state.md.normalizeLink(url)\n          if (!state.md.validateLink(fullUrl)) { continue }\n\n          let urlText = links[ln].text\n\n          // Linkifier might send raw hostnames like \"example.com\", where url\n          // starts with domain name. So we prepend http:// in those cases,\n          // and remove it afterwards.\n          //\n          if (!links[ln].schema) {\n            urlText = state.md.normalizeLinkText('http://' + urlText).replace(/^http:\\/\\//, '')\n          } else if (links[ln].schema === 'mailto:' && !/^mailto:/i.test(urlText)) {\n            urlText = state.md.normalizeLinkText('mailto:' + urlText).replace(/^mailto:/, '')\n          } else {\n            urlText = state.md.normalizeLinkText(urlText)\n          }\n\n          const pos = links[ln].index\n\n          if (pos > lastPos) {\n            const token   = new state.Token('text', '', 0)\n            token.content = text.slice(lastPos, pos)\n            token.level   = level\n            nodes.push(token)\n          }\n\n          const token_o   = new state.Token('link_open', 'a', 1)\n          token_o.attrs   = [['href', fullUrl]]\n          token_o.level   = level++\n          token_o.markup  = 'linkify'\n          token_o.info    = 'auto'\n          nodes.push(token_o)\n\n          const token_t   = new state.Token('text', '', 0)\n          token_t.content = urlText\n          token_t.level   = level\n          nodes.push(token_t)\n\n          const token_c   = new state.Token('link_close', 'a', -1)\n          token_c.level   = --level\n          token_c.markup  = 'linkify'\n          token_c.info    = 'auto'\n          nodes.push(token_c)\n\n          lastPos = links[ln].lastIndex\n        }\n        if (lastPos < text.length) {\n          const token   = new state.Token('text', '', 0)\n          token.content = text.slice(lastPos)\n          token.level   = level\n          nodes.push(token)\n        }\n\n        // replace current node\n        blockTokens[j].children = tokens = arrayReplaceAt(tokens, i, nodes)\n      }\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA,2CAA2C;AAC3C,EAAE;AACF,gEAAgE;AAChE,EAAE;;;;AAEF;;AAEA,SAAS,WAAY,GAAG;IACtB,OAAO,YAAY,IAAI,CAAC;AAC1B;AACA,SAAS,YAAa,GAAG;IACvB,OAAO,aAAa,IAAI,CAAC;AAC3B;AAEe,SAAS,QAAS,KAAK;IACpC,MAAM,cAAc,MAAM,MAAM;IAEhC,IAAI,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE;QAAE;IAAO;IAExC,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAI,GAAG,IAAK;QAClD,IAAI,WAAW,CAAC,EAAE,CAAC,IAAI,KAAK,YACxB,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC,OAAO,GAAG;YACrD;QACF;QAEA,IAAI,SAAS,WAAW,CAAC,EAAE,CAAC,QAAQ;QAEpC,IAAI,gBAAgB;QAEpB,8DAA8D;QAC9D,8CAA8C;QAC9C,IAAK,IAAI,IAAI,OAAO,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;YAC3C,MAAM,eAAe,MAAM,CAAC,EAAE;YAE9B,iCAAiC;YACjC,IAAI,aAAa,IAAI,KAAK,cAAc;gBACtC;gBACA,MAAO,MAAM,CAAC,EAAE,CAAC,KAAK,KAAK,aAAa,KAAK,IAAI,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,YAAa;oBAC/E;gBACF;gBACA;YACF;YAEA,iCAAiC;YACjC,IAAI,aAAa,IAAI,KAAK,eAAe;gBACvC,IAAI,WAAW,aAAa,OAAO,KAAK,gBAAgB,GAAG;oBACzD;gBACF;gBACA,IAAI,YAAY,aAAa,OAAO,GAAG;oBACrC;gBACF;YACF;YACA,IAAI,gBAAgB,GAAG;gBAAE;YAAS;YAElC,IAAI,aAAa,IAAI,KAAK,UAAU,MAAM,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,OAAO,GAAG;gBAC/E,MAAM,OAAO,aAAa,OAAO;gBACjC,IAAI,QAAQ,MAAM,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;gBAEnC,4BAA4B;gBAC5B,MAAM,QAAQ,EAAE;gBAChB,IAAI,QAAQ,aAAa,KAAK;gBAC9B,IAAI,UAAU;gBAEd,qDAAqD;gBACrD,2DAA2D;gBAC3D,mDAAmD;gBACnD,IAAI,MAAM,MAAM,GAAG,KACf,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,KACnB,IAAI,KACJ,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,gBAAgB;oBACzC,QAAQ,MAAM,KAAK,CAAC;gBACtB;gBAEA,IAAK,IAAI,KAAK,GAAG,KAAK,MAAM,MAAM,EAAE,KAAM;oBACxC,MAAM,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG;oBACzB,MAAM,UAAU,MAAM,EAAE,CAAC,aAAa,CAAC;oBACvC,IAAI,CAAC,MAAM,EAAE,CAAC,YAAY,CAAC,UAAU;wBAAE;oBAAS;oBAEhD,IAAI,UAAU,KAAK,CAAC,GAAG,CAAC,IAAI;oBAE5B,mEAAmE;oBACnE,iEAAiE;oBACjE,4BAA4B;oBAC5B,EAAE;oBACF,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE;wBACrB,UAAU,MAAM,EAAE,CAAC,iBAAiB,CAAC,YAAY,SAAS,OAAO,CAAC,cAAc;oBAClF,OAAO,IAAI,KAAK,CAAC,GAAG,CAAC,MAAM,KAAK,aAAa,CAAC,YAAY,IAAI,CAAC,UAAU;wBACvE,UAAU,MAAM,EAAE,CAAC,iBAAiB,CAAC,YAAY,SAAS,OAAO,CAAC,YAAY;oBAChF,OAAO;wBACL,UAAU,MAAM,EAAE,CAAC,iBAAiB,CAAC;oBACvC;oBAEA,MAAM,MAAM,KAAK,CAAC,GAAG,CAAC,KAAK;oBAE3B,IAAI,MAAM,SAAS;wBACjB,MAAM,QAAU,IAAI,MAAM,KAAK,CAAC,QAAQ,IAAI;wBAC5C,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC,SAAS;wBACpC,MAAM,KAAK,GAAK;wBAChB,MAAM,IAAI,CAAC;oBACb;oBAEA,MAAM,UAAY,IAAI,MAAM,KAAK,CAAC,aAAa,KAAK;oBACpD,QAAQ,KAAK,GAAK;wBAAC;4BAAC;4BAAQ;yBAAQ;qBAAC;oBACrC,QAAQ,KAAK,GAAK;oBAClB,QAAQ,MAAM,GAAI;oBAClB,QAAQ,IAAI,GAAM;oBAClB,MAAM,IAAI,CAAC;oBAEX,MAAM,UAAY,IAAI,MAAM,KAAK,CAAC,QAAQ,IAAI;oBAC9C,QAAQ,OAAO,GAAG;oBAClB,QAAQ,KAAK,GAAK;oBAClB,MAAM,IAAI,CAAC;oBAEX,MAAM,UAAY,IAAI,MAAM,KAAK,CAAC,cAAc,KAAK,CAAC;oBACtD,QAAQ,KAAK,GAAK,EAAE;oBACpB,QAAQ,MAAM,GAAI;oBAClB,QAAQ,IAAI,GAAM;oBAClB,MAAM,IAAI,CAAC;oBAEX,UAAU,KAAK,CAAC,GAAG,CAAC,SAAS;gBAC/B;gBACA,IAAI,UAAU,KAAK,MAAM,EAAE;oBACzB,MAAM,QAAU,IAAI,MAAM,KAAK,CAAC,QAAQ,IAAI;oBAC5C,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC;oBAC3B,MAAM,KAAK,GAAK;oBAChB,MAAM,IAAI,CAAC;gBACb;gBAEA,uBAAuB;gBACvB,WAAW,CAAC,EAAE,CAAC,QAAQ,GAAG,SAAS,CAAA,GAAA,wNAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,GAAG;YAC/D;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1559, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_core/replacements.mjs"], "sourcesContent": ["// Simple typographic replacements\n//\n// (c) (C) → ©\n// (tm) (TM) → ™\n// (r) (R) → ®\n// +- → ±\n// ... → … (also ?.... → ?.., !.... → !..)\n// ???????? → ???, !!!!! → !!!, `,,` → `,`\n// -- → &ndash;, --- → &mdash;\n//\n\n// TODO:\n// - fractionals 1/2, 1/4, 3/4 -> ½, ¼, ¾\n// - multiplications 2 x 4 -> 2 × 4\n\nconst RARE_RE = /\\+-|\\.\\.|\\?\\?\\?\\?|!!!!|,,|--/\n\n// Workaround for phantomjs - need regex without /g flag,\n// or root check will fail every second time\nconst SCOPED_ABBR_TEST_RE = /\\((c|tm|r)\\)/i\n\nconst SCOPED_ABBR_RE = /\\((c|tm|r)\\)/ig\nconst SCOPED_ABBR = {\n  c: '©',\n  r: '®',\n  tm: '™'\n}\n\nfunction replaceFn (match, name) {\n  return SCOPED_ABBR[name.toLowerCase()]\n}\n\nfunction replace_scoped (inlineTokens) {\n  let inside_autolink = 0\n\n  for (let i = inlineTokens.length - 1; i >= 0; i--) {\n    const token = inlineTokens[i]\n\n    if (token.type === 'text' && !inside_autolink) {\n      token.content = token.content.replace(SCOPED_ABBR_RE, replaceFn)\n    }\n\n    if (token.type === 'link_open' && token.info === 'auto') {\n      inside_autolink--\n    }\n\n    if (token.type === 'link_close' && token.info === 'auto') {\n      inside_autolink++\n    }\n  }\n}\n\nfunction replace_rare (inlineTokens) {\n  let inside_autolink = 0\n\n  for (let i = inlineTokens.length - 1; i >= 0; i--) {\n    const token = inlineTokens[i]\n\n    if (token.type === 'text' && !inside_autolink) {\n      if (RARE_RE.test(token.content)) {\n        token.content = token.content\n          .replace(/\\+-/g, '±')\n          // .., ..., ....... -> …\n          // but ?..... & !..... -> ?.. & !..\n          .replace(/\\.{2,}/g, '…').replace(/([?!])…/g, '$1..')\n          .replace(/([?!]){4,}/g, '$1$1$1').replace(/,{2,}/g, ',')\n          // em-dash\n          .replace(/(^|[^-])---(?=[^-]|$)/mg, '$1\\u2014')\n          // en-dash\n          .replace(/(^|\\s)--(?=\\s|$)/mg, '$1\\u2013')\n          .replace(/(^|[^-\\s])--(?=[^-\\s]|$)/mg, '$1\\u2013')\n      }\n    }\n\n    if (token.type === 'link_open' && token.info === 'auto') {\n      inside_autolink--\n    }\n\n    if (token.type === 'link_close' && token.info === 'auto') {\n      inside_autolink++\n    }\n  }\n}\n\nexport default function replace (state) {\n  let blkIdx\n\n  if (!state.md.options.typographer) { return }\n\n  for (blkIdx = state.tokens.length - 1; blkIdx >= 0; blkIdx--) {\n    if (state.tokens[blkIdx].type !== 'inline') { continue }\n\n    if (SCOPED_ABBR_TEST_RE.test(state.tokens[blkIdx].content)) {\n      replace_scoped(state.tokens[blkIdx].children)\n    }\n\n    if (RARE_RE.test(state.tokens[blkIdx].content)) {\n      replace_rare(state.tokens[blkIdx].children)\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA,kCAAkC;AAClC,EAAE;AACF,cAAc;AACd,gBAAgB;AAChB,cAAc;AACd,SAAS;AACT,0CAA0C;AAC1C,0CAA0C;AAC1C,8BAA8B;AAC9B,EAAE;AAEF,QAAQ;AACR,yCAAyC;AACzC,mCAAmC;;;;AAEnC,MAAM,UAAU;AAEhB,yDAAyD;AACzD,4CAA4C;AAC5C,MAAM,sBAAsB;AAE5B,MAAM,iBAAiB;AACvB,MAAM,cAAc;IAClB,GAAG;IACH,GAAG;IACH,IAAI;AACN;AAEA,SAAS,UAAW,KAAK,EAAE,IAAI;IAC7B,OAAO,WAAW,CAAC,KAAK,WAAW,GAAG;AACxC;AAEA,SAAS,eAAgB,YAAY;IACnC,IAAI,kBAAkB;IAEtB,IAAK,IAAI,IAAI,aAAa,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QACjD,MAAM,QAAQ,YAAY,CAAC,EAAE;QAE7B,IAAI,MAAM,IAAI,KAAK,UAAU,CAAC,iBAAiB;YAC7C,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,gBAAgB;QACxD;QAEA,IAAI,MAAM,IAAI,KAAK,eAAe,MAAM,IAAI,KAAK,QAAQ;YACvD;QACF;QAEA,IAAI,MAAM,IAAI,KAAK,gBAAgB,MAAM,IAAI,KAAK,QAAQ;YACxD;QACF;IACF;AACF;AAEA,SAAS,aAAc,YAAY;IACjC,IAAI,kBAAkB;IAEtB,IAAK,IAAI,IAAI,aAAa,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QACjD,MAAM,QAAQ,YAAY,CAAC,EAAE;QAE7B,IAAI,MAAM,IAAI,KAAK,UAAU,CAAC,iBAAiB;YAC7C,IAAI,QAAQ,IAAI,CAAC,MAAM,OAAO,GAAG;gBAC/B,MAAM,OAAO,GAAG,MAAM,OAAO,CAC1B,OAAO,CAAC,QAAQ,IACjB,wBAAwB;gBACxB,mCAAmC;iBAClC,OAAO,CAAC,WAAW,KAAK,OAAO,CAAC,YAAY,QAC5C,OAAO,CAAC,eAAe,UAAU,OAAO,CAAC,UAAU,IACpD,UAAU;iBACT,OAAO,CAAC,2BAA2B,WACpC,UAAU;iBACT,OAAO,CAAC,sBAAsB,YAC9B,OAAO,CAAC,8BAA8B;YAC3C;QACF;QAEA,IAAI,MAAM,IAAI,KAAK,eAAe,MAAM,IAAI,KAAK,QAAQ;YACvD;QACF;QAEA,IAAI,MAAM,IAAI,KAAK,gBAAgB,MAAM,IAAI,KAAK,QAAQ;YACxD;QACF;IACF;AACF;AAEe,SAAS,QAAS,KAAK;IACpC,IAAI;IAEJ,IAAI,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE;QAAE;IAAO;IAE5C,IAAK,SAAS,MAAM,MAAM,CAAC,MAAM,GAAG,GAAG,UAAU,GAAG,SAAU;QAC5D,IAAI,MAAM,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,UAAU;YAAE;QAAS;QAEvD,IAAI,oBAAoB,IAAI,CAAC,MAAM,MAAM,CAAC,OAAO,CAAC,OAAO,GAAG;YAC1D,eAAe,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ;QAC9C;QAEA,IAAI,QAAQ,IAAI,CAAC,MAAM,MAAM,CAAC,OAAO,CAAC,OAAO,GAAG;YAC9C,aAAa,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ;QAC5C;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1647, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_core/smartquotes.mjs"], "sourcesContent": ["// Convert straight quotation marks to typographic ones\n//\n\nimport { isWhiteSpace, isPunctChar, isMdAsciiPunct } from '../common/utils.mjs'\n\nconst QUOTE_TEST_RE = /['\"]/\nconst QUOTE_RE = /['\"]/g\nconst APOSTROPHE = '\\u2019' /* ’ */\n\nfunction replaceAt (str, index, ch) {\n  return str.slice(0, index) + ch + str.slice(index + 1)\n}\n\nfunction process_inlines (tokens, state) {\n  let j\n\n  const stack = []\n\n  for (let i = 0; i < tokens.length; i++) {\n    const token = tokens[i]\n\n    const thisLevel = tokens[i].level\n\n    for (j = stack.length - 1; j >= 0; j--) {\n      if (stack[j].level <= thisLevel) { break }\n    }\n    stack.length = j + 1\n\n    if (token.type !== 'text') { continue }\n\n    let text = token.content\n    let pos = 0\n    let max = text.length\n\n    /* eslint no-labels:0,block-scoped-var:0 */\n    OUTER:\n    while (pos < max) {\n      QUOTE_RE.lastIndex = pos\n      const t = QUOTE_RE.exec(text)\n      if (!t) { break }\n\n      let canOpen = true\n      let canClose = true\n      pos = t.index + 1\n      const isSingle = (t[0] === \"'\")\n\n      // Find previous character,\n      // default to space if it's the beginning of the line\n      //\n      let lastChar = 0x20\n\n      if (t.index - 1 >= 0) {\n        lastChar = text.charCodeAt(t.index - 1)\n      } else {\n        for (j = i - 1; j >= 0; j--) {\n          if (tokens[j].type === 'softbreak' || tokens[j].type === 'hardbreak') break // lastChar defaults to 0x20\n          if (!tokens[j].content) continue // should skip all tokens except 'text', 'html_inline' or 'code_inline'\n\n          lastChar = tokens[j].content.charCodeAt(tokens[j].content.length - 1)\n          break\n        }\n      }\n\n      // Find next character,\n      // default to space if it's the end of the line\n      //\n      let nextChar = 0x20\n\n      if (pos < max) {\n        nextChar = text.charCodeAt(pos)\n      } else {\n        for (j = i + 1; j < tokens.length; j++) {\n          if (tokens[j].type === 'softbreak' || tokens[j].type === 'hardbreak') break // nextChar defaults to 0x20\n          if (!tokens[j].content) continue // should skip all tokens except 'text', 'html_inline' or 'code_inline'\n\n          nextChar = tokens[j].content.charCodeAt(0)\n          break\n        }\n      }\n\n      const isLastPunctChar = isMdAsciiPunct(lastChar) || isPunctChar(String.fromCharCode(lastChar))\n      const isNextPunctChar = isMdAsciiPunct(nextChar) || isPunctChar(String.fromCharCode(nextChar))\n\n      const isLastWhiteSpace = isWhiteSpace(lastChar)\n      const isNextWhiteSpace = isWhiteSpace(nextChar)\n\n      if (isNextWhiteSpace) {\n        canOpen = false\n      } else if (isNextPunctChar) {\n        if (!(isLastWhiteSpace || isLastPunctChar)) {\n          canOpen = false\n        }\n      }\n\n      if (isLastWhiteSpace) {\n        canClose = false\n      } else if (isLastPunctChar) {\n        if (!(isNextWhiteSpace || isNextPunctChar)) {\n          canClose = false\n        }\n      }\n\n      if (nextChar === 0x22 /* \" */ && t[0] === '\"') {\n        if (lastChar >= 0x30 /* 0 */ && lastChar <= 0x39 /* 9 */) {\n          // special case: 1\"\" - count first quote as an inch\n          canClose = canOpen = false\n        }\n      }\n\n      if (canOpen && canClose) {\n        // Replace quotes in the middle of punctuation sequence, but not\n        // in the middle of the words, i.e.:\n        //\n        // 1. foo \" bar \" baz - not replaced\n        // 2. foo-\"-bar-\"-baz - replaced\n        // 3. foo\"bar\"baz     - not replaced\n        //\n        canOpen = isLastPunctChar\n        canClose = isNextPunctChar\n      }\n\n      if (!canOpen && !canClose) {\n        // middle of word\n        if (isSingle) {\n          token.content = replaceAt(token.content, t.index, APOSTROPHE)\n        }\n        continue\n      }\n\n      if (canClose) {\n        // this could be a closing quote, rewind the stack to get a match\n        for (j = stack.length - 1; j >= 0; j--) {\n          let item = stack[j]\n          if (stack[j].level < thisLevel) { break }\n          if (item.single === isSingle && stack[j].level === thisLevel) {\n            item = stack[j]\n\n            let openQuote\n            let closeQuote\n            if (isSingle) {\n              openQuote = state.md.options.quotes[2]\n              closeQuote = state.md.options.quotes[3]\n            } else {\n              openQuote = state.md.options.quotes[0]\n              closeQuote = state.md.options.quotes[1]\n            }\n\n            // replace token.content *before* tokens[item.token].content,\n            // because, if they are pointing at the same token, replaceAt\n            // could mess up indices when quote length != 1\n            token.content = replaceAt(token.content, t.index, closeQuote)\n            tokens[item.token].content = replaceAt(\n              tokens[item.token].content, item.pos, openQuote)\n\n            pos += closeQuote.length - 1\n            if (item.token === i) { pos += openQuote.length - 1 }\n\n            text = token.content\n            max = text.length\n\n            stack.length = j\n            continue OUTER\n          }\n        }\n      }\n\n      if (canOpen) {\n        stack.push({\n          token: i,\n          pos: t.index,\n          single: isSingle,\n          level: thisLevel\n        })\n      } else if (canClose && isSingle) {\n        token.content = replaceAt(token.content, t.index, APOSTROPHE)\n      }\n    }\n  }\n}\n\nexport default function smartquotes (state) {\n  /* eslint max-depth:0 */\n  if (!state.md.options.typographer) { return }\n\n  for (let blkIdx = state.tokens.length - 1; blkIdx >= 0; blkIdx--) {\n    if (state.tokens[blkIdx].type !== 'inline' ||\n        !QUOTE_TEST_RE.test(state.tokens[blkIdx].content)) {\n      continue\n    }\n\n    process_inlines(state.tokens[blkIdx].children, state)\n  }\n}\n"], "names": [], "mappings": "AAAA,uDAAuD;AACvD,EAAE;;;;AAEF;;AAEA,MAAM,gBAAgB;AACtB,MAAM,WAAW;AACjB,MAAM,aAAa,SAAS,KAAK;AAEjC,SAAS,UAAW,GAAG,EAAE,KAAK,EAAE,EAAE;IAChC,OAAO,IAAI,KAAK,CAAC,GAAG,SAAS,KAAK,IAAI,KAAK,CAAC,QAAQ;AACtD;AAEA,SAAS,gBAAiB,MAAM,EAAE,KAAK;IACrC,IAAI;IAEJ,MAAM,QAAQ,EAAE;IAEhB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACtC,MAAM,QAAQ,MAAM,CAAC,EAAE;QAEvB,MAAM,YAAY,MAAM,CAAC,EAAE,CAAC,KAAK;QAEjC,IAAK,IAAI,MAAM,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;YACtC,IAAI,KAAK,CAAC,EAAE,CAAC,KAAK,IAAI,WAAW;gBAAE;YAAM;QAC3C;QACA,MAAM,MAAM,GAAG,IAAI;QAEnB,IAAI,MAAM,IAAI,KAAK,QAAQ;YAAE;QAAS;QAEtC,IAAI,OAAO,MAAM,OAAO;QACxB,IAAI,MAAM;QACV,IAAI,MAAM,KAAK,MAAM;QAErB,yCAAyC,GACzC,OACA,MAAO,MAAM,IAAK;YAChB,SAAS,SAAS,GAAG;YACrB,MAAM,IAAI,SAAS,IAAI,CAAC;YACxB,IAAI,CAAC,GAAG;gBAAE;YAAM;YAEhB,IAAI,UAAU;YACd,IAAI,WAAW;YACf,MAAM,EAAE,KAAK,GAAG;YAChB,MAAM,WAAY,CAAC,CAAC,EAAE,KAAK;YAE3B,2BAA2B;YAC3B,qDAAqD;YACrD,EAAE;YACF,IAAI,WAAW;YAEf,IAAI,EAAE,KAAK,GAAG,KAAK,GAAG;gBACpB,WAAW,KAAK,UAAU,CAAC,EAAE,KAAK,GAAG;YACvC,OAAO;gBACL,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAK;oBAC3B,IAAI,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,eAAe,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,aAAa,OAAM,4BAA4B;oBACxG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,UAAS,uEAAuE;oBAExG,WAAW,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,GAAG;oBACnE;gBACF;YACF;YAEA,uBAAuB;YACvB,+CAA+C;YAC/C,EAAE;YACF,IAAI,WAAW;YAEf,IAAI,MAAM,KAAK;gBACb,WAAW,KAAK,UAAU,CAAC;YAC7B,OAAO;gBACL,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;oBACtC,IAAI,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,eAAe,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,aAAa,OAAM,4BAA4B;oBACxG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,UAAS,uEAAuE;oBAExG,WAAW,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,UAAU,CAAC;oBACxC;gBACF;YACF;YAEA,MAAM,kBAAkB,CAAA,GAAA,wNAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,CAAA,GAAA,wNAAA,CAAA,cAAW,AAAD,EAAE,OAAO,YAAY,CAAC;YACpF,MAAM,kBAAkB,CAAA,GAAA,wNAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,CAAA,GAAA,wNAAA,CAAA,cAAW,AAAD,EAAE,OAAO,YAAY,CAAC;YAEpF,MAAM,mBAAmB,CAAA,GAAA,wNAAA,CAAA,eAAY,AAAD,EAAE;YACtC,MAAM,mBAAmB,CAAA,GAAA,wNAAA,CAAA,eAAY,AAAD,EAAE;YAEtC,IAAI,kBAAkB;gBACpB,UAAU;YACZ,OAAO,IAAI,iBAAiB;gBAC1B,IAAI,CAAC,CAAC,oBAAoB,eAAe,GAAG;oBAC1C,UAAU;gBACZ;YACF;YAEA,IAAI,kBAAkB;gBACpB,WAAW;YACb,OAAO,IAAI,iBAAiB;gBAC1B,IAAI,CAAC,CAAC,oBAAoB,eAAe,GAAG;oBAC1C,WAAW;gBACb;YACF;YAEA,IAAI,aAAa,KAAK,KAAK,OAAM,CAAC,CAAC,EAAE,KAAK,KAAK;gBAC7C,IAAI,YAAY,KAAK,KAAK,OAAM,YAAY,KAAK,KAAK,KAAI;oBACxD,mDAAmD;oBACnD,WAAW,UAAU;gBACvB;YACF;YAEA,IAAI,WAAW,UAAU;gBACvB,gEAAgE;gBAChE,oCAAoC;gBACpC,EAAE;gBACF,oCAAoC;gBACpC,gCAAgC;gBAChC,oCAAoC;gBACpC,EAAE;gBACF,UAAU;gBACV,WAAW;YACb;YAEA,IAAI,CAAC,WAAW,CAAC,UAAU;gBACzB,iBAAiB;gBACjB,IAAI,UAAU;oBACZ,MAAM,OAAO,GAAG,UAAU,MAAM,OAAO,EAAE,EAAE,KAAK,EAAE;gBACpD;gBACA;YACF;YAEA,IAAI,UAAU;gBACZ,iEAAiE;gBACjE,IAAK,IAAI,MAAM,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;oBACtC,IAAI,OAAO,KAAK,CAAC,EAAE;oBACnB,IAAI,KAAK,CAAC,EAAE,CAAC,KAAK,GAAG,WAAW;wBAAE;oBAAM;oBACxC,IAAI,KAAK,MAAM,KAAK,YAAY,KAAK,CAAC,EAAE,CAAC,KAAK,KAAK,WAAW;wBAC5D,OAAO,KAAK,CAAC,EAAE;wBAEf,IAAI;wBACJ,IAAI;wBACJ,IAAI,UAAU;4BACZ,YAAY,MAAM,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;4BACtC,aAAa,MAAM,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;wBACzC,OAAO;4BACL,YAAY,MAAM,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;4BACtC,aAAa,MAAM,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;wBACzC;wBAEA,6DAA6D;wBAC7D,6DAA6D;wBAC7D,+CAA+C;wBAC/C,MAAM,OAAO,GAAG,UAAU,MAAM,OAAO,EAAE,EAAE,KAAK,EAAE;wBAClD,MAAM,CAAC,KAAK,KAAK,CAAC,CAAC,OAAO,GAAG,UAC3B,MAAM,CAAC,KAAK,KAAK,CAAC,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE;wBAExC,OAAO,WAAW,MAAM,GAAG;wBAC3B,IAAI,KAAK,KAAK,KAAK,GAAG;4BAAE,OAAO,UAAU,MAAM,GAAG;wBAAE;wBAEpD,OAAO,MAAM,OAAO;wBACpB,MAAM,KAAK,MAAM;wBAEjB,MAAM,MAAM,GAAG;wBACf,SAAS;oBACX;gBACF;YACF;YAEA,IAAI,SAAS;gBACX,MAAM,IAAI,CAAC;oBACT,OAAO;oBACP,KAAK,EAAE,KAAK;oBACZ,QAAQ;oBACR,OAAO;gBACT;YACF,OAAO,IAAI,YAAY,UAAU;gBAC/B,MAAM,OAAO,GAAG,UAAU,MAAM,OAAO,EAAE,EAAE,KAAK,EAAE;YACpD;QACF;IACF;AACF;AAEe,SAAS,YAAa,KAAK;IACxC,sBAAsB,GACtB,IAAI,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE;QAAE;IAAO;IAE5C,IAAK,IAAI,SAAS,MAAM,MAAM,CAAC,MAAM,GAAG,GAAG,UAAU,GAAG,SAAU;QAChE,IAAI,MAAM,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,YAC9B,CAAC,cAAc,IAAI,CAAC,MAAM,MAAM,CAAC,OAAO,CAAC,OAAO,GAAG;YACrD;QACF;QAEA,gBAAgB,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE;IACjD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1822, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_core/text_join.mjs"], "sourcesContent": ["// Join raw text tokens with the rest of the text\n//\n// This is set as a separate rule to provide an opportunity for plugins\n// to run text replacements after text join, but before escape join.\n//\n// For example, `\\:)` shouldn't be replaced with an emoji.\n//\n\nexport default function text_join (state) {\n  let curr, last\n  const blockTokens = state.tokens\n  const l = blockTokens.length\n\n  for (let j = 0; j < l; j++) {\n    if (blockTokens[j].type !== 'inline') continue\n\n    const tokens = blockTokens[j].children\n    const max = tokens.length\n\n    for (curr = 0; curr < max; curr++) {\n      if (tokens[curr].type === 'text_special') {\n        tokens[curr].type = 'text'\n      }\n    }\n\n    for (curr = last = 0; curr < max; curr++) {\n      if (tokens[curr].type === 'text' &&\n          curr + 1 < max &&\n          tokens[curr + 1].type === 'text') {\n        // collapse two adjacent text nodes\n        tokens[curr + 1].content = tokens[curr].content + tokens[curr + 1].content\n      } else {\n        if (curr !== last) { tokens[last] = tokens[curr] }\n\n        last++\n      }\n    }\n\n    if (curr !== last) {\n      tokens.length = last\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA,iDAAiD;AACjD,EAAE;AACF,uEAAuE;AACvE,oEAAoE;AACpE,EAAE;AACF,0DAA0D;AAC1D,EAAE;;;;AAEa,SAAS,UAAW,KAAK;IACtC,IAAI,MAAM;IACV,MAAM,cAAc,MAAM,MAAM;IAChC,MAAM,IAAI,YAAY,MAAM;IAE5B,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC1B,IAAI,WAAW,CAAC,EAAE,CAAC,IAAI,KAAK,UAAU;QAEtC,MAAM,SAAS,WAAW,CAAC,EAAE,CAAC,QAAQ;QACtC,MAAM,MAAM,OAAO,MAAM;QAEzB,IAAK,OAAO,GAAG,OAAO,KAAK,OAAQ;YACjC,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,gBAAgB;gBACxC,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG;YACtB;QACF;QAEA,IAAK,OAAO,OAAO,GAAG,OAAO,KAAK,OAAQ;YACxC,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,UACtB,OAAO,IAAI,OACX,MAAM,CAAC,OAAO,EAAE,CAAC,IAAI,KAAK,QAAQ;gBACpC,mCAAmC;gBACnC,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO;YAC5E,OAAO;gBACL,IAAI,SAAS,MAAM;oBAAE,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK;gBAAC;gBAEjD;YACF;QACF;QAEA,IAAI,SAAS,MAAM;YACjB,OAAO,MAAM,GAAG;QAClB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1867, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/parser_core.mjs"], "sourcesContent": ["/** internal\n * class Core\n *\n * Top-level rules executor. Glues block/inline parsers and does intermediate\n * transformations.\n **/\n\nimport Ruler from './ruler.mjs'\nimport StateCore from './rules_core/state_core.mjs'\n\nimport r_normalize from './rules_core/normalize.mjs'\nimport r_block from './rules_core/block.mjs'\nimport r_inline from './rules_core/inline.mjs'\nimport r_linkify from './rules_core/linkify.mjs'\nimport r_replacements from './rules_core/replacements.mjs'\nimport r_smartquotes from './rules_core/smartquotes.mjs'\nimport r_text_join from './rules_core/text_join.mjs'\n\nconst _rules = [\n  ['normalize',      r_normalize],\n  ['block',          r_block],\n  ['inline',         r_inline],\n  ['linkify',        r_linkify],\n  ['replacements',   r_replacements],\n  ['smartquotes',    r_smartquotes],\n  // `text_join` finds `text_special` tokens (for escape sequences)\n  // and joins them with the rest of the text\n  ['text_join',      r_text_join]\n]\n\n/**\n * new Core()\n **/\nfunction Core () {\n  /**\n   * Core#ruler -> Ruler\n   *\n   * [[Ruler]] instance. Keep configuration of core rules.\n   **/\n  this.ruler = new Ruler()\n\n  for (let i = 0; i < _rules.length; i++) {\n    this.ruler.push(_rules[i][0], _rules[i][1])\n  }\n}\n\n/**\n * Core.process(state)\n *\n * Executes core chain rules.\n **/\nCore.prototype.process = function (state) {\n  const rules = this.ruler.getRules('')\n\n  for (let i = 0, l = rules.length; i < l; i++) {\n    rules[i](state)\n  }\n}\n\nCore.prototype.State = StateCore\n\nexport default Core\n"], "names": [], "mappings": "AAAA;;;;;EAKE;;;AAEF;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEA,MAAM,SAAS;IACb;QAAC;QAAkB,gOAAA,CAAA,UAAW;KAAC;IAC/B;QAAC;QAAkB,4NAAA,CAAA,UAAO;KAAC;IAC3B;QAAC;QAAkB,6NAAA,CAAA,UAAQ;KAAC;IAC5B;QAAC;QAAkB,8NAAA,CAAA,UAAS;KAAC;IAC7B;QAAC;QAAkB,mOAAA,CAAA,UAAc;KAAC;IAClC;QAAC;QAAkB,kOAAA,CAAA,UAAa;KAAC;IACjC,iEAAiE;IACjE,2CAA2C;IAC3C;QAAC;QAAkB,gOAAA,CAAA,UAAW;KAAC;CAChC;AAED;;EAEE,GACF,SAAS;IACP;;;;IAIE,GACF,IAAI,CAAC,KAAK,GAAG,IAAI,8MAAA,CAAA,UAAK;IAEtB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACtC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE;IAC5C;AACF;AAEA;;;;EAIE,GACF,KAAK,SAAS,CAAC,OAAO,GAAG,SAAU,KAAK;IACtC,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAElC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAI,GAAG,IAAK;QAC5C,KAAK,CAAC,EAAE,CAAC;IACX;AACF;AAEA,KAAK,SAAS,CAAC,KAAK,GAAG,iOAAA,CAAA,UAAS;uCAEjB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1955, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_block/state_block.mjs"], "sourcesContent": ["// Parser state class\n\nimport Token from '../token.mjs'\nimport { isSpace } from '../common/utils.mjs'\n\nfunction StateBlock (src, md, env, tokens) {\n  this.src = src\n\n  // link to parser instance\n  this.md     = md\n\n  this.env = env\n\n  //\n  // Internal state vartiables\n  //\n\n  this.tokens = tokens\n\n  this.bMarks = []  // line begin offsets for fast jumps\n  this.eMarks = []  // line end offsets for fast jumps\n  this.tShift = []  // offsets of the first non-space characters (tabs not expanded)\n  this.sCount = []  // indents for each line (tabs expanded)\n\n  // An amount of virtual spaces (tabs expanded) between beginning\n  // of each line (bMarks) and real beginning of that line.\n  //\n  // It exists only as a hack because blockquotes override bMarks\n  // losing information in the process.\n  //\n  // It's used only when expanding tabs, you can think about it as\n  // an initial tab length, e.g. bsCount=21 applied to string `\\t123`\n  // means first tab should be expanded to 4-21%4 === 3 spaces.\n  //\n  this.bsCount = []\n\n  // block parser variables\n\n  // required block content indent (for example, if we are\n  // inside a list, it would be positioned after list marker)\n  this.blkIndent  = 0\n  this.line       = 0 // line index in src\n  this.lineMax    = 0 // lines count\n  this.tight      = false  // loose/tight mode for lists\n  this.ddIndent   = -1 // indent of the current dd block (-1 if there isn't any)\n  this.listIndent = -1 // indent of the current list block (-1 if there isn't any)\n\n  // can be 'blockquote', 'list', 'root', 'paragraph' or 'reference'\n  // used in lists to determine if they interrupt a paragraph\n  this.parentType = 'root'\n\n  this.level = 0\n\n  // Create caches\n  // Generate markers.\n  const s = this.src\n\n  for (let start = 0, pos = 0, indent = 0, offset = 0, len = s.length, indent_found = false; pos < len; pos++) {\n    const ch = s.charCodeAt(pos)\n\n    if (!indent_found) {\n      if (isSpace(ch)) {\n        indent++\n\n        if (ch === 0x09) {\n          offset += 4 - offset % 4\n        } else {\n          offset++\n        }\n        continue\n      } else {\n        indent_found = true\n      }\n    }\n\n    if (ch === 0x0A || pos === len - 1) {\n      if (ch !== 0x0A) { pos++ }\n      this.bMarks.push(start)\n      this.eMarks.push(pos)\n      this.tShift.push(indent)\n      this.sCount.push(offset)\n      this.bsCount.push(0)\n\n      indent_found = false\n      indent = 0\n      offset = 0\n      start = pos + 1\n    }\n  }\n\n  // Push fake entry to simplify cache bounds checks\n  this.bMarks.push(s.length)\n  this.eMarks.push(s.length)\n  this.tShift.push(0)\n  this.sCount.push(0)\n  this.bsCount.push(0)\n\n  this.lineMax = this.bMarks.length - 1 // don't count last fake line\n}\n\n// Push new token to \"stream\".\n//\nStateBlock.prototype.push = function (type, tag, nesting) {\n  const token = new Token(type, tag, nesting)\n  token.block = true\n\n  if (nesting < 0) this.level-- // closing tag\n  token.level = this.level\n  if (nesting > 0) this.level++ // opening tag\n\n  this.tokens.push(token)\n  return token\n}\n\nStateBlock.prototype.isEmpty = function isEmpty (line) {\n  return this.bMarks[line] + this.tShift[line] >= this.eMarks[line]\n}\n\nStateBlock.prototype.skipEmptyLines = function skipEmptyLines (from) {\n  for (let max = this.lineMax; from < max; from++) {\n    if (this.bMarks[from] + this.tShift[from] < this.eMarks[from]) {\n      break\n    }\n  }\n  return from\n}\n\n// Skip spaces from given position.\nStateBlock.prototype.skipSpaces = function skipSpaces (pos) {\n  for (let max = this.src.length; pos < max; pos++) {\n    const ch = this.src.charCodeAt(pos)\n    if (!isSpace(ch)) { break }\n  }\n  return pos\n}\n\n// Skip spaces from given position in reverse.\nStateBlock.prototype.skipSpacesBack = function skipSpacesBack (pos, min) {\n  if (pos <= min) { return pos }\n\n  while (pos > min) {\n    if (!isSpace(this.src.charCodeAt(--pos))) { return pos + 1 }\n  }\n  return pos\n}\n\n// Skip char codes from given position\nStateBlock.prototype.skipChars = function skipChars (pos, code) {\n  for (let max = this.src.length; pos < max; pos++) {\n    if (this.src.charCodeAt(pos) !== code) { break }\n  }\n  return pos\n}\n\n// Skip char codes reverse from given position - 1\nStateBlock.prototype.skipCharsBack = function skipCharsBack (pos, code, min) {\n  if (pos <= min) { return pos }\n\n  while (pos > min) {\n    if (code !== this.src.charCodeAt(--pos)) { return pos + 1 }\n  }\n  return pos\n}\n\n// cut lines range from source.\nStateBlock.prototype.getLines = function getLines (begin, end, indent, keepLastLF) {\n  if (begin >= end) {\n    return ''\n  }\n\n  const queue = new Array(end - begin)\n\n  for (let i = 0, line = begin; line < end; line++, i++) {\n    let lineIndent = 0\n    const lineStart = this.bMarks[line]\n    let first = lineStart\n    let last\n\n    if (line + 1 < end || keepLastLF) {\n      // No need for bounds check because we have fake entry on tail.\n      last = this.eMarks[line] + 1\n    } else {\n      last = this.eMarks[line]\n    }\n\n    while (first < last && lineIndent < indent) {\n      const ch = this.src.charCodeAt(first)\n\n      if (isSpace(ch)) {\n        if (ch === 0x09) {\n          lineIndent += 4 - (lineIndent + this.bsCount[line]) % 4\n        } else {\n          lineIndent++\n        }\n      } else if (first - lineStart < this.tShift[line]) {\n        // patched tShift masked characters to look like spaces (blockquotes, list markers)\n        lineIndent++\n      } else {\n        break\n      }\n\n      first++\n    }\n\n    if (lineIndent > indent) {\n      // partially expanding tabs in code blocks, e.g '\\t\\tfoobar'\n      // with indent=2 becomes '  \\tfoobar'\n      queue[i] = new Array(lineIndent - indent + 1).join(' ') + this.src.slice(first, last)\n    } else {\n      queue[i] = this.src.slice(first, last)\n    }\n  }\n\n  return queue.join('')\n}\n\n// re-export Token class to use in block rules\nStateBlock.prototype.Token = Token\n\nexport default StateBlock\n"], "names": [], "mappings": "AAAA,qBAAqB;;;;AAErB;AACA;;;AAEA,SAAS,WAAY,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,MAAM;IACvC,IAAI,CAAC,GAAG,GAAG;IAEX,0BAA0B;IAC1B,IAAI,CAAC,EAAE,GAAO;IAEd,IAAI,CAAC,GAAG,GAAG;IAEX,EAAE;IACF,4BAA4B;IAC5B,EAAE;IAEF,IAAI,CAAC,MAAM,GAAG;IAEd,IAAI,CAAC,MAAM,GAAG,EAAE,CAAE,oCAAoC;;IACtD,IAAI,CAAC,MAAM,GAAG,EAAE,CAAE,kCAAkC;;IACpD,IAAI,CAAC,MAAM,GAAG,EAAE,CAAE,gEAAgE;;IAClF,IAAI,CAAC,MAAM,GAAG,EAAE,CAAE,wCAAwC;;IAE1D,gEAAgE;IAChE,yDAAyD;IACzD,EAAE;IACF,+DAA+D;IAC/D,qCAAqC;IACrC,EAAE;IACF,gEAAgE;IAChE,mEAAmE;IACnE,6DAA6D;IAC7D,EAAE;IACF,IAAI,CAAC,OAAO,GAAG,EAAE;IAEjB,yBAAyB;IAEzB,wDAAwD;IACxD,2DAA2D;IAC3D,IAAI,CAAC,SAAS,GAAI;IAClB,IAAI,CAAC,IAAI,GAAS,EAAE,oBAAoB;;IACxC,IAAI,CAAC,OAAO,GAAM,EAAE,cAAc;;IAClC,IAAI,CAAC,KAAK,GAAQ,MAAO,6BAA6B;;IACtD,IAAI,CAAC,QAAQ,GAAK,CAAC,EAAE,yDAAyD;;IAC9E,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE,2DAA2D;;IAEhF,kEAAkE;IAClE,2DAA2D;IAC3D,IAAI,CAAC,UAAU,GAAG;IAElB,IAAI,CAAC,KAAK,GAAG;IAEb,gBAAgB;IAChB,oBAAoB;IACpB,MAAM,IAAI,IAAI,CAAC,GAAG;IAElB,IAAK,IAAI,QAAQ,GAAG,MAAM,GAAG,SAAS,GAAG,SAAS,GAAG,MAAM,EAAE,MAAM,EAAE,eAAe,OAAO,MAAM,KAAK,MAAO;QAC3G,MAAM,KAAK,EAAE,UAAU,CAAC;QAExB,IAAI,CAAC,cAAc;YACjB,IAAI,CAAA,GAAA,wNAAA,CAAA,UAAO,AAAD,EAAE,KAAK;gBACf;gBAEA,IAAI,OAAO,MAAM;oBACf,UAAU,IAAI,SAAS;gBACzB,OAAO;oBACL;gBACF;gBACA;YACF,OAAO;gBACL,eAAe;YACjB;QACF;QAEA,IAAI,OAAO,QAAQ,QAAQ,MAAM,GAAG;YAClC,IAAI,OAAO,MAAM;gBAAE;YAAM;YACzB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACjB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACjB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACjB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACjB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;YAElB,eAAe;YACf,SAAS;YACT,SAAS;YACT,QAAQ,MAAM;QAChB;IACF;IAEA,kDAAkD;IAClD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,MAAM;IACzB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,MAAM;IACzB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IACjB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IACjB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;IAElB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,EAAE,6BAA6B;;AACrE;AAEA,8BAA8B;AAC9B,EAAE;AACF,WAAW,SAAS,CAAC,IAAI,GAAG,SAAU,IAAI,EAAE,GAAG,EAAE,OAAO;IACtD,MAAM,QAAQ,IAAI,8MAAA,CAAA,UAAK,CAAC,MAAM,KAAK;IACnC,MAAM,KAAK,GAAG;IAEd,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,GAAG,cAAc;;IAC5C,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK;IACxB,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,GAAG,cAAc;;IAE5C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IACjB,OAAO;AACT;AAEA,WAAW,SAAS,CAAC,OAAO,GAAG,SAAS,QAAS,IAAI;IACnD,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK;AACnE;AAEA,WAAW,SAAS,CAAC,cAAc,GAAG,SAAS,eAAgB,IAAI;IACjE,IAAK,IAAI,MAAM,IAAI,CAAC,OAAO,EAAE,OAAO,KAAK,OAAQ;QAC/C,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;YAC7D;QACF;IACF;IACA,OAAO;AACT;AAEA,mCAAmC;AACnC,WAAW,SAAS,CAAC,UAAU,GAAG,SAAS,WAAY,GAAG;IACxD,IAAK,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,KAAK,MAAO;QAChD,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;QAC/B,IAAI,CAAC,CAAA,GAAA,wNAAA,CAAA,UAAO,AAAD,EAAE,KAAK;YAAE;QAAM;IAC5B;IACA,OAAO;AACT;AAEA,8CAA8C;AAC9C,WAAW,SAAS,CAAC,cAAc,GAAG,SAAS,eAAgB,GAAG,EAAE,GAAG;IACrE,IAAI,OAAO,KAAK;QAAE,OAAO;IAAI;IAE7B,MAAO,MAAM,IAAK;QAChB,IAAI,CAAC,CAAA,GAAA,wNAAA,CAAA,UAAO,AAAD,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,OAAO;YAAE,OAAO,MAAM;QAAE;IAC7D;IACA,OAAO;AACT;AAEA,sCAAsC;AACtC,WAAW,SAAS,CAAC,SAAS,GAAG,SAAS,UAAW,GAAG,EAAE,IAAI;IAC5D,IAAK,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,KAAK,MAAO;QAChD,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,SAAS,MAAM;YAAE;QAAM;IACjD;IACA,OAAO;AACT;AAEA,kDAAkD;AAClD,WAAW,SAAS,CAAC,aAAa,GAAG,SAAS,cAAe,GAAG,EAAE,IAAI,EAAE,GAAG;IACzE,IAAI,OAAO,KAAK;QAAE,OAAO;IAAI;IAE7B,MAAO,MAAM,IAAK;QAChB,IAAI,SAAS,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,MAAM;YAAE,OAAO,MAAM;QAAE;IAC5D;IACA,OAAO;AACT;AAEA,+BAA+B;AAC/B,WAAW,SAAS,CAAC,QAAQ,GAAG,SAAS,SAAU,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,UAAU;IAC/E,IAAI,SAAS,KAAK;QAChB,OAAO;IACT;IAEA,MAAM,QAAQ,IAAI,MAAM,MAAM;IAE9B,IAAK,IAAI,IAAI,GAAG,OAAO,OAAO,OAAO,KAAK,QAAQ,IAAK;QACrD,IAAI,aAAa;QACjB,MAAM,YAAY,IAAI,CAAC,MAAM,CAAC,KAAK;QACnC,IAAI,QAAQ;QACZ,IAAI;QAEJ,IAAI,OAAO,IAAI,OAAO,YAAY;YAChC,+DAA+D;YAC/D,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG;QAC7B,OAAO;YACL,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK;QAC1B;QAEA,MAAO,QAAQ,QAAQ,aAAa,OAAQ;YAC1C,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;YAE/B,IAAI,CAAA,GAAA,wNAAA,CAAA,UAAO,AAAD,EAAE,KAAK;gBACf,IAAI,OAAO,MAAM;oBACf,cAAc,IAAI,CAAC,aAAa,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI;gBACxD,OAAO;oBACL;gBACF;YACF,OAAO,IAAI,QAAQ,YAAY,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;gBAChD,mFAAmF;gBACnF;YACF,OAAO;gBACL;YACF;YAEA;QACF;QAEA,IAAI,aAAa,QAAQ;YACvB,4DAA4D;YAC5D,qCAAqC;YACrC,KAAK,CAAC,EAAE,GAAG,IAAI,MAAM,aAAa,SAAS,GAAG,IAAI,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO;QAClF,OAAO;YACL,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO;QACnC;IACF;IAEA,OAAO,MAAM,IAAI,CAAC;AACpB;AAEA,8CAA8C;AAC9C,WAAW,SAAS,CAAC,KAAK,GAAG,8MAAA,CAAA,UAAK;uCAEnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2170, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_block/table.mjs"], "sourcesContent": ["// GFM table, https://github.github.com/gfm/#tables-extension-\n\nimport { isSpace } from '../common/utils.mjs'\n\n// Limit the amount of empty autocompleted cells in a table,\n// see https://github.com/markdown-it/markdown-it/issues/1000,\n//\n// Both pulldown-cmark and commonmark-hs limit the number of cells this way to ~200k.\n// We set it to 65k, which can expand user input by a factor of x370\n// (256x256 square is 1.8kB expanded into 650kB).\nconst MAX_AUTOCOMPLETED_CELLS = 0x10000\n\nfunction getLine (state, line) {\n  const pos = state.bMarks[line] + state.tShift[line]\n  const max = state.eMarks[line]\n\n  return state.src.slice(pos, max)\n}\n\nfunction escapedSplit (str) {\n  const result = []\n  const max = str.length\n\n  let pos = 0\n  let ch = str.charCodeAt(pos)\n  let isEscaped = false\n  let lastPos = 0\n  let current = ''\n\n  while (pos < max) {\n    if (ch === 0x7c/* | */) {\n      if (!isEscaped) {\n        // pipe separating cells, '|'\n        result.push(current + str.substring(lastPos, pos))\n        current = ''\n        lastPos = pos + 1\n      } else {\n        // escaped pipe, '\\|'\n        current += str.substring(lastPos, pos - 1)\n        lastPos = pos\n      }\n    }\n\n    isEscaped = (ch === 0x5c/* \\ */)\n    pos++\n\n    ch = str.charCodeAt(pos)\n  }\n\n  result.push(current + str.substring(lastPos))\n\n  return result\n}\n\nexport default function table (state, startLine, endLine, silent) {\n  // should have at least two lines\n  if (startLine + 2 > endLine) { return false }\n\n  let nextLine = startLine + 1\n\n  if (state.sCount[nextLine] < state.blkIndent) { return false }\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[nextLine] - state.blkIndent >= 4) { return false }\n\n  // first character of the second line should be '|', '-', ':',\n  // and no other characters are allowed but spaces;\n  // basically, this is the equivalent of /^[-:|][-:|\\s]*$/ regexp\n\n  let pos = state.bMarks[nextLine] + state.tShift[nextLine]\n  if (pos >= state.eMarks[nextLine]) { return false }\n\n  const firstCh = state.src.charCodeAt(pos++)\n  if (firstCh !== 0x7C/* | */ && firstCh !== 0x2D/* - */ && firstCh !== 0x3A/* : */) { return false }\n\n  if (pos >= state.eMarks[nextLine]) { return false }\n\n  const secondCh = state.src.charCodeAt(pos++)\n  if (secondCh !== 0x7C/* | */ && secondCh !== 0x2D/* - */ && secondCh !== 0x3A/* : */ && !isSpace(secondCh)) {\n    return false\n  }\n\n  // if first character is '-', then second character must not be a space\n  // (due to parsing ambiguity with list)\n  if (firstCh === 0x2D/* - */ && isSpace(secondCh)) { return false }\n\n  while (pos < state.eMarks[nextLine]) {\n    const ch = state.src.charCodeAt(pos)\n\n    if (ch !== 0x7C/* | */ && ch !== 0x2D/* - */ && ch !== 0x3A/* : */ && !isSpace(ch)) { return false }\n\n    pos++\n  }\n\n  let lineText = getLine(state, startLine + 1)\n  let columns = lineText.split('|')\n  const aligns = []\n  for (let i = 0; i < columns.length; i++) {\n    const t = columns[i].trim()\n    if (!t) {\n      // allow empty columns before and after table, but not in between columns;\n      // e.g. allow ` |---| `, disallow ` ---||--- `\n      if (i === 0 || i === columns.length - 1) {\n        continue\n      } else {\n        return false\n      }\n    }\n\n    if (!/^:?-+:?$/.test(t)) { return false }\n    if (t.charCodeAt(t.length - 1) === 0x3A/* : */) {\n      aligns.push(t.charCodeAt(0) === 0x3A/* : */ ? 'center' : 'right')\n    } else if (t.charCodeAt(0) === 0x3A/* : */) {\n      aligns.push('left')\n    } else {\n      aligns.push('')\n    }\n  }\n\n  lineText = getLine(state, startLine).trim()\n  if (lineText.indexOf('|') === -1) { return false }\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false }\n  columns = escapedSplit(lineText)\n  if (columns.length && columns[0] === '') columns.shift()\n  if (columns.length && columns[columns.length - 1] === '') columns.pop()\n\n  // header row will define an amount of columns in the entire table,\n  // and align row should be exactly the same (the rest of the rows can differ)\n  const columnCount = columns.length\n  if (columnCount === 0 || columnCount !== aligns.length) { return false }\n\n  if (silent) { return true }\n\n  const oldParentType = state.parentType\n  state.parentType = 'table'\n\n  // use 'blockquote' lists for termination because it's\n  // the most similar to tables\n  const terminatorRules = state.md.block.ruler.getRules('blockquote')\n\n  const token_to = state.push('table_open', 'table', 1)\n  const tableLines = [startLine, 0]\n  token_to.map = tableLines\n\n  const token_tho = state.push('thead_open', 'thead', 1)\n  token_tho.map = [startLine, startLine + 1]\n\n  const token_htro = state.push('tr_open', 'tr', 1)\n  token_htro.map = [startLine, startLine + 1]\n\n  for (let i = 0; i < columns.length; i++) {\n    const token_ho = state.push('th_open', 'th', 1)\n    if (aligns[i]) {\n      token_ho.attrs  = [['style', 'text-align:' + aligns[i]]]\n    }\n\n    const token_il = state.push('inline', '', 0)\n    token_il.content  = columns[i].trim()\n    token_il.children = []\n\n    state.push('th_close', 'th', -1)\n  }\n\n  state.push('tr_close', 'tr', -1)\n  state.push('thead_close', 'thead', -1)\n\n  let tbodyLines\n  let autocompletedCells = 0\n\n  for (nextLine = startLine + 2; nextLine < endLine; nextLine++) {\n    if (state.sCount[nextLine] < state.blkIndent) { break }\n\n    let terminate = false\n    for (let i = 0, l = terminatorRules.length; i < l; i++) {\n      if (terminatorRules[i](state, nextLine, endLine, true)) {\n        terminate = true\n        break\n      }\n    }\n\n    if (terminate) { break }\n    lineText = getLine(state, nextLine).trim()\n    if (!lineText) { break }\n    if (state.sCount[nextLine] - state.blkIndent >= 4) { break }\n    columns = escapedSplit(lineText)\n    if (columns.length && columns[0] === '') columns.shift()\n    if (columns.length && columns[columns.length - 1] === '') columns.pop()\n\n    // note: autocomplete count can be negative if user specifies more columns than header,\n    // but that does not affect intended use (which is limiting expansion)\n    autocompletedCells += columnCount - columns.length\n    if (autocompletedCells > MAX_AUTOCOMPLETED_CELLS) { break }\n\n    if (nextLine === startLine + 2) {\n      const token_tbo = state.push('tbody_open', 'tbody', 1)\n      token_tbo.map = tbodyLines = [startLine + 2, 0]\n    }\n\n    const token_tro = state.push('tr_open', 'tr', 1)\n    token_tro.map = [nextLine, nextLine + 1]\n\n    for (let i = 0; i < columnCount; i++) {\n      const token_tdo = state.push('td_open', 'td', 1)\n      if (aligns[i]) {\n        token_tdo.attrs  = [['style', 'text-align:' + aligns[i]]]\n      }\n\n      const token_il = state.push('inline', '', 0)\n      token_il.content  = columns[i] ? columns[i].trim() : ''\n      token_il.children = []\n\n      state.push('td_close', 'td', -1)\n    }\n    state.push('tr_close', 'tr', -1)\n  }\n\n  if (tbodyLines) {\n    state.push('tbody_close', 'tbody', -1)\n    tbodyLines[1] = nextLine\n  }\n\n  state.push('table_close', 'table', -1)\n  tableLines[1] = nextLine\n\n  state.parentType = oldParentType\n  state.line = nextLine\n  return true\n}\n"], "names": [], "mappings": "AAAA,8DAA8D;;;;AAE9D;;AAEA,4DAA4D;AAC5D,8DAA8D;AAC9D,EAAE;AACF,qFAAqF;AACrF,oEAAoE;AACpE,iDAAiD;AACjD,MAAM,0BAA0B;AAEhC,SAAS,QAAS,KAAK,EAAE,IAAI;IAC3B,MAAM,MAAM,MAAM,MAAM,CAAC,KAAK,GAAG,MAAM,MAAM,CAAC,KAAK;IACnD,MAAM,MAAM,MAAM,MAAM,CAAC,KAAK;IAE9B,OAAO,MAAM,GAAG,CAAC,KAAK,CAAC,KAAK;AAC9B;AAEA,SAAS,aAAc,GAAG;IACxB,MAAM,SAAS,EAAE;IACjB,MAAM,MAAM,IAAI,MAAM;IAEtB,IAAI,MAAM;IACV,IAAI,KAAK,IAAI,UAAU,CAAC;IACxB,IAAI,YAAY;IAChB,IAAI,UAAU;IACd,IAAI,UAAU;IAEd,MAAO,MAAM,IAAK;QAChB,IAAI,OAAO,KAAI,KAAK,KAAI;YACtB,IAAI,CAAC,WAAW;gBACd,6BAA6B;gBAC7B,OAAO,IAAI,CAAC,UAAU,IAAI,SAAS,CAAC,SAAS;gBAC7C,UAAU;gBACV,UAAU,MAAM;YAClB,OAAO;gBACL,qBAAqB;gBACrB,WAAW,IAAI,SAAS,CAAC,SAAS,MAAM;gBACxC,UAAU;YACZ;QACF;QAEA,YAAa,OAAO,KAAI,KAAK;QAC7B;QAEA,KAAK,IAAI,UAAU,CAAC;IACtB;IAEA,OAAO,IAAI,CAAC,UAAU,IAAI,SAAS,CAAC;IAEpC,OAAO;AACT;AAEe,SAAS,MAAO,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM;IAC9D,iCAAiC;IACjC,IAAI,YAAY,IAAI,SAAS;QAAE,OAAO;IAAM;IAE5C,IAAI,WAAW,YAAY;IAE3B,IAAI,MAAM,MAAM,CAAC,SAAS,GAAG,MAAM,SAAS,EAAE;QAAE,OAAO;IAAM;IAE7D,iEAAiE;IACjE,IAAI,MAAM,MAAM,CAAC,SAAS,GAAG,MAAM,SAAS,IAAI,GAAG;QAAE,OAAO;IAAM;IAElE,8DAA8D;IAC9D,kDAAkD;IAClD,gEAAgE;IAEhE,IAAI,MAAM,MAAM,MAAM,CAAC,SAAS,GAAG,MAAM,MAAM,CAAC,SAAS;IACzD,IAAI,OAAO,MAAM,MAAM,CAAC,SAAS,EAAE;QAAE,OAAO;IAAM;IAElD,MAAM,UAAU,MAAM,GAAG,CAAC,UAAU,CAAC;IACrC,IAAI,YAAY,KAAI,KAAK,OAAM,YAAY,KAAI,KAAK,OAAM,YAAY,KAAI,KAAK,KAAI;QAAE,OAAO;IAAM;IAElG,IAAI,OAAO,MAAM,MAAM,CAAC,SAAS,EAAE;QAAE,OAAO;IAAM;IAElD,MAAM,WAAW,MAAM,GAAG,CAAC,UAAU,CAAC;IACtC,IAAI,aAAa,KAAI,KAAK,OAAM,aAAa,KAAI,KAAK,OAAM,aAAa,KAAI,KAAK,OAAM,CAAC,CAAA,GAAA,wNAAA,CAAA,UAAO,AAAD,EAAE,WAAW;QAC1G,OAAO;IACT;IAEA,uEAAuE;IACvE,uCAAuC;IACvC,IAAI,YAAY,KAAI,KAAK,OAAM,CAAA,GAAA,wNAAA,CAAA,UAAO,AAAD,EAAE,WAAW;QAAE,OAAO;IAAM;IAEjE,MAAO,MAAM,MAAM,MAAM,CAAC,SAAS,CAAE;QACnC,MAAM,KAAK,MAAM,GAAG,CAAC,UAAU,CAAC;QAEhC,IAAI,OAAO,KAAI,KAAK,OAAM,OAAO,KAAI,KAAK,OAAM,OAAO,KAAI,KAAK,OAAM,CAAC,CAAA,GAAA,wNAAA,CAAA,UAAO,AAAD,EAAE,KAAK;YAAE,OAAO;QAAM;QAEnG;IACF;IAEA,IAAI,WAAW,QAAQ,OAAO,YAAY;IAC1C,IAAI,UAAU,SAAS,KAAK,CAAC;IAC7B,MAAM,SAAS,EAAE;IACjB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QACvC,MAAM,IAAI,OAAO,CAAC,EAAE,CAAC,IAAI;QACzB,IAAI,CAAC,GAAG;YACN,0EAA0E;YAC1E,8CAA8C;YAC9C,IAAI,MAAM,KAAK,MAAM,QAAQ,MAAM,GAAG,GAAG;gBACvC;YACF,OAAO;gBACL,OAAO;YACT;QACF;QAEA,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI;YAAE,OAAO;QAAM;QACxC,IAAI,EAAE,UAAU,CAAC,EAAE,MAAM,GAAG,OAAO,KAAI,KAAK,KAAI;YAC9C,OAAO,IAAI,CAAC,EAAE,UAAU,CAAC,OAAO,KAAI,KAAK,MAAK,WAAW;QAC3D,OAAO,IAAI,EAAE,UAAU,CAAC,OAAO,KAAI,KAAK,KAAI;YAC1C,OAAO,IAAI,CAAC;QACd,OAAO;YACL,OAAO,IAAI,CAAC;QACd;IACF;IAEA,WAAW,QAAQ,OAAO,WAAW,IAAI;IACzC,IAAI,SAAS,OAAO,CAAC,SAAS,CAAC,GAAG;QAAE,OAAO;IAAM;IACjD,IAAI,MAAM,MAAM,CAAC,UAAU,GAAG,MAAM,SAAS,IAAI,GAAG;QAAE,OAAO;IAAM;IACnE,UAAU,aAAa;IACvB,IAAI,QAAQ,MAAM,IAAI,OAAO,CAAC,EAAE,KAAK,IAAI,QAAQ,KAAK;IACtD,IAAI,QAAQ,MAAM,IAAI,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE,KAAK,IAAI,QAAQ,GAAG;IAErE,mEAAmE;IACnE,6EAA6E;IAC7E,MAAM,cAAc,QAAQ,MAAM;IAClC,IAAI,gBAAgB,KAAK,gBAAgB,OAAO,MAAM,EAAE;QAAE,OAAO;IAAM;IAEvE,IAAI,QAAQ;QAAE,OAAO;IAAK;IAE1B,MAAM,gBAAgB,MAAM,UAAU;IACtC,MAAM,UAAU,GAAG;IAEnB,sDAAsD;IACtD,6BAA6B;IAC7B,MAAM,kBAAkB,MAAM,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC;IAEtD,MAAM,WAAW,MAAM,IAAI,CAAC,cAAc,SAAS;IACnD,MAAM,aAAa;QAAC;QAAW;KAAE;IACjC,SAAS,GAAG,GAAG;IAEf,MAAM,YAAY,MAAM,IAAI,CAAC,cAAc,SAAS;IACpD,UAAU,GAAG,GAAG;QAAC;QAAW,YAAY;KAAE;IAE1C,MAAM,aAAa,MAAM,IAAI,CAAC,WAAW,MAAM;IAC/C,WAAW,GAAG,GAAG;QAAC;QAAW,YAAY;KAAE;IAE3C,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QACvC,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,MAAM;QAC7C,IAAI,MAAM,CAAC,EAAE,EAAE;YACb,SAAS,KAAK,GAAI;gBAAC;oBAAC;oBAAS,gBAAgB,MAAM,CAAC,EAAE;iBAAC;aAAC;QAC1D;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,UAAU,IAAI;QAC1C,SAAS,OAAO,GAAI,OAAO,CAAC,EAAE,CAAC,IAAI;QACnC,SAAS,QAAQ,GAAG,EAAE;QAEtB,MAAM,IAAI,CAAC,YAAY,MAAM,CAAC;IAChC;IAEA,MAAM,IAAI,CAAC,YAAY,MAAM,CAAC;IAC9B,MAAM,IAAI,CAAC,eAAe,SAAS,CAAC;IAEpC,IAAI;IACJ,IAAI,qBAAqB;IAEzB,IAAK,WAAW,YAAY,GAAG,WAAW,SAAS,WAAY;QAC7D,IAAI,MAAM,MAAM,CAAC,SAAS,GAAG,MAAM,SAAS,EAAE;YAAE;QAAM;QAEtD,IAAI,YAAY;QAChB,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,MAAM,EAAE,IAAI,GAAG,IAAK;YACtD,IAAI,eAAe,CAAC,EAAE,CAAC,OAAO,UAAU,SAAS,OAAO;gBACtD,YAAY;gBACZ;YACF;QACF;QAEA,IAAI,WAAW;YAAE;QAAM;QACvB,WAAW,QAAQ,OAAO,UAAU,IAAI;QACxC,IAAI,CAAC,UAAU;YAAE;QAAM;QACvB,IAAI,MAAM,MAAM,CAAC,SAAS,GAAG,MAAM,SAAS,IAAI,GAAG;YAAE;QAAM;QAC3D,UAAU,aAAa;QACvB,IAAI,QAAQ,MAAM,IAAI,OAAO,CAAC,EAAE,KAAK,IAAI,QAAQ,KAAK;QACtD,IAAI,QAAQ,MAAM,IAAI,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE,KAAK,IAAI,QAAQ,GAAG;QAErE,uFAAuF;QACvF,sEAAsE;QACtE,sBAAsB,cAAc,QAAQ,MAAM;QAClD,IAAI,qBAAqB,yBAAyB;YAAE;QAAM;QAE1D,IAAI,aAAa,YAAY,GAAG;YAC9B,MAAM,YAAY,MAAM,IAAI,CAAC,cAAc,SAAS;YACpD,UAAU,GAAG,GAAG,aAAa;gBAAC,YAAY;gBAAG;aAAE;QACjD;QAEA,MAAM,YAAY,MAAM,IAAI,CAAC,WAAW,MAAM;QAC9C,UAAU,GAAG,GAAG;YAAC;YAAU,WAAW;SAAE;QAExC,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;YACpC,MAAM,YAAY,MAAM,IAAI,CAAC,WAAW,MAAM;YAC9C,IAAI,MAAM,CAAC,EAAE,EAAE;gBACb,UAAU,KAAK,GAAI;oBAAC;wBAAC;wBAAS,gBAAgB,MAAM,CAAC,EAAE;qBAAC;iBAAC;YAC3D;YAEA,MAAM,WAAW,MAAM,IAAI,CAAC,UAAU,IAAI;YAC1C,SAAS,OAAO,GAAI,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC,IAAI,KAAK;YACrD,SAAS,QAAQ,GAAG,EAAE;YAEtB,MAAM,IAAI,CAAC,YAAY,MAAM,CAAC;QAChC;QACA,MAAM,IAAI,CAAC,YAAY,MAAM,CAAC;IAChC;IAEA,IAAI,YAAY;QACd,MAAM,IAAI,CAAC,eAAe,SAAS,CAAC;QACpC,UAAU,CAAC,EAAE,GAAG;IAClB;IAEA,MAAM,IAAI,CAAC,eAAe,SAAS,CAAC;IACpC,UAAU,CAAC,EAAE,GAAG;IAEhB,MAAM,UAAU,GAAG;IACnB,MAAM,IAAI,GAAG;IACb,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2418, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_block/code.mjs"], "sourcesContent": ["// Code block (4 spaces padded)\n\nexport default function code (state, startLine, endLine/*, silent */) {\n  if (state.sCount[startLine] - state.blkIndent < 4) { return false }\n\n  let nextLine = startLine + 1\n  let last = nextLine\n\n  while (nextLine < endLine) {\n    if (state.isEmpty(nextLine)) {\n      nextLine++\n      continue\n    }\n\n    if (state.sCount[nextLine] - state.blkIndent >= 4) {\n      nextLine++\n      last = nextLine\n      continue\n    }\n    break\n  }\n\n  state.line = last\n\n  const token   = state.push('code_block', 'code', 0)\n  token.content = state.getLines(startLine, last, 4 + state.blkIndent, false) + '\\n'\n  token.map     = [startLine, state.line]\n\n  return true\n}\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;AAEhB,SAAS,KAAM,KAAK,EAAE,SAAS,EAAE,QAAO,WAAW,GAAX;IACrD,IAAI,MAAM,MAAM,CAAC,UAAU,GAAG,MAAM,SAAS,GAAG,GAAG;QAAE,OAAO;IAAM;IAElE,IAAI,WAAW,YAAY;IAC3B,IAAI,OAAO;IAEX,MAAO,WAAW,QAAS;QACzB,IAAI,MAAM,OAAO,CAAC,WAAW;YAC3B;YACA;QACF;QAEA,IAAI,MAAM,MAAM,CAAC,SAAS,GAAG,MAAM,SAAS,IAAI,GAAG;YACjD;YACA,OAAO;YACP;QACF;QACA;IACF;IAEA,MAAM,IAAI,GAAG;IAEb,MAAM,QAAU,MAAM,IAAI,CAAC,cAAc,QAAQ;IACjD,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,WAAW,MAAM,IAAI,MAAM,SAAS,EAAE,SAAS;IAC9E,MAAM,GAAG,GAAO;QAAC;QAAW,MAAM,IAAI;KAAC;IAEvC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2455, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_block/fence.mjs"], "sourcesContent": ["// fences (``` lang, ~~~ lang)\n\nexport default function fence (state, startLine, endLine, silent) {\n  let pos = state.bMarks[startLine] + state.tShift[startLine]\n  let max = state.eMarks[startLine]\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false }\n\n  if (pos + 3 > max) { return false }\n\n  const marker = state.src.charCodeAt(pos)\n\n  if (marker !== 0x7E/* ~ */ && marker !== 0x60 /* ` */) {\n    return false\n  }\n\n  // scan marker length\n  let mem = pos\n  pos = state.skipChars(pos, marker)\n\n  let len = pos - mem\n\n  if (len < 3) { return false }\n\n  const markup = state.src.slice(mem, pos)\n  const params = state.src.slice(pos, max)\n\n  if (marker === 0x60 /* ` */) {\n    if (params.indexOf(String.fromCharCode(marker)) >= 0) {\n      return false\n    }\n  }\n\n  // Since start is found, we can report success here in validation mode\n  if (silent) { return true }\n\n  // search end of block\n  let nextLine = startLine\n  let haveEndMarker = false\n\n  for (;;) {\n    nextLine++\n    if (nextLine >= endLine) {\n      // unclosed block should be autoclosed by end of document.\n      // also block seems to be autoclosed by end of parent\n      break\n    }\n\n    pos = mem = state.bMarks[nextLine] + state.tShift[nextLine]\n    max = state.eMarks[nextLine]\n\n    if (pos < max && state.sCount[nextLine] < state.blkIndent) {\n      // non-empty line with negative indent should stop the list:\n      // - ```\n      //  test\n      break\n    }\n\n    if (state.src.charCodeAt(pos) !== marker) { continue }\n\n    if (state.sCount[nextLine] - state.blkIndent >= 4) {\n      // closing fence should be indented less than 4 spaces\n      continue\n    }\n\n    pos = state.skipChars(pos, marker)\n\n    // closing code fence must be at least as long as the opening one\n    if (pos - mem < len) { continue }\n\n    // make sure tail has spaces only\n    pos = state.skipSpaces(pos)\n\n    if (pos < max) { continue }\n\n    haveEndMarker = true\n    // found!\n    break\n  }\n\n  // If a fence has heading spaces, they should be removed from its inner block\n  len = state.sCount[startLine]\n\n  state.line = nextLine + (haveEndMarker ? 1 : 0)\n\n  const token   = state.push('fence', 'code', 0)\n  token.info    = params\n  token.content = state.getLines(startLine + 1, nextLine, len, true)\n  token.markup  = markup\n  token.map     = [startLine, state.line]\n\n  return true\n}\n"], "names": [], "mappings": "AAAA,8BAA8B;;;;AAEf,SAAS,MAAO,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM;IAC9D,IAAI,MAAM,MAAM,MAAM,CAAC,UAAU,GAAG,MAAM,MAAM,CAAC,UAAU;IAC3D,IAAI,MAAM,MAAM,MAAM,CAAC,UAAU;IAEjC,iEAAiE;IACjE,IAAI,MAAM,MAAM,CAAC,UAAU,GAAG,MAAM,SAAS,IAAI,GAAG;QAAE,OAAO;IAAM;IAEnE,IAAI,MAAM,IAAI,KAAK;QAAE,OAAO;IAAM;IAElC,MAAM,SAAS,MAAM,GAAG,CAAC,UAAU,CAAC;IAEpC,IAAI,WAAW,KAAI,KAAK,OAAM,WAAW,KAAK,KAAK,KAAI;QACrD,OAAO;IACT;IAEA,qBAAqB;IACrB,IAAI,MAAM;IACV,MAAM,MAAM,SAAS,CAAC,KAAK;IAE3B,IAAI,MAAM,MAAM;IAEhB,IAAI,MAAM,GAAG;QAAE,OAAO;IAAM;IAE5B,MAAM,SAAS,MAAM,GAAG,CAAC,KAAK,CAAC,KAAK;IACpC,MAAM,SAAS,MAAM,GAAG,CAAC,KAAK,CAAC,KAAK;IAEpC,IAAI,WAAW,KAAK,KAAK,KAAI;QAC3B,IAAI,OAAO,OAAO,CAAC,OAAO,YAAY,CAAC,YAAY,GAAG;YACpD,OAAO;QACT;IACF;IAEA,sEAAsE;IACtE,IAAI,QAAQ;QAAE,OAAO;IAAK;IAE1B,sBAAsB;IACtB,IAAI,WAAW;IACf,IAAI,gBAAgB;IAEpB,OAAS;QACP;QACA,IAAI,YAAY,SAAS;YAGvB;QACF;QAEA,MAAM,MAAM,MAAM,MAAM,CAAC,SAAS,GAAG,MAAM,MAAM,CAAC,SAAS;QAC3D,MAAM,MAAM,MAAM,CAAC,SAAS;QAE5B,IAAI,MAAM,OAAO,MAAM,MAAM,CAAC,SAAS,GAAG,MAAM,SAAS,EAAE;YAIzD;QACF;QAEA,IAAI,MAAM,GAAG,CAAC,UAAU,CAAC,SAAS,QAAQ;YAAE;QAAS;QAErD,IAAI,MAAM,MAAM,CAAC,SAAS,GAAG,MAAM,SAAS,IAAI,GAAG;YAEjD;QACF;QAEA,MAAM,MAAM,SAAS,CAAC,KAAK;QAE3B,iEAAiE;QACjE,IAAI,MAAM,MAAM,KAAK;YAAE;QAAS;QAEhC,iCAAiC;QACjC,MAAM,MAAM,UAAU,CAAC;QAEvB,IAAI,MAAM,KAAK;YAAE;QAAS;QAE1B,gBAAgB;QAEhB;IACF;IAEA,6EAA6E;IAC7E,MAAM,MAAM,MAAM,CAAC,UAAU;IAE7B,MAAM,IAAI,GAAG,WAAW,CAAC,gBAAgB,IAAI,CAAC;IAE9C,MAAM,QAAU,MAAM,IAAI,CAAC,SAAS,QAAQ;IAC5C,MAAM,IAAI,GAAM;IAChB,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,YAAY,GAAG,UAAU,KAAK;IAC7D,MAAM,MAAM,GAAI;IAChB,MAAM,GAAG,GAAO;QAAC;QAAW,MAAM,IAAI;KAAC;IAEvC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2542, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_block/blockquote.mjs"], "sourcesContent": ["// Block quotes\n\nimport { isSpace } from '../common/utils.mjs'\n\nexport default function blockquote (state, startLine, endLine, silent) {\n  let pos = state.bMarks[startLine] + state.tShift[startLine]\n  let max = state.eMarks[startLine]\n\n  const oldLineMax = state.lineMax\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false }\n\n  // check the block quote marker\n  if (state.src.charCodeAt(pos) !== 0x3E/* > */) { return false }\n\n  // we know that it's going to be a valid blockquote,\n  // so no point trying to find the end of it in silent mode\n  if (silent) { return true }\n\n  const oldBMarks  = []\n  const oldBSCount = []\n  const oldSCount  = []\n  const oldTShift  = []\n\n  const terminatorRules = state.md.block.ruler.getRules('blockquote')\n\n  const oldParentType = state.parentType\n  state.parentType = 'blockquote'\n  let lastLineEmpty = false\n  let nextLine\n\n  // Search the end of the block\n  //\n  // Block ends with either:\n  //  1. an empty line outside:\n  //     ```\n  //     > test\n  //\n  //     ```\n  //  2. an empty line inside:\n  //     ```\n  //     >\n  //     test\n  //     ```\n  //  3. another tag:\n  //     ```\n  //     > test\n  //      - - -\n  //     ```\n  for (nextLine = startLine; nextLine < endLine; nextLine++) {\n    // check if it's outdented, i.e. it's inside list item and indented\n    // less than said list item:\n    //\n    // ```\n    // 1. anything\n    //    > current blockquote\n    // 2. checking this line\n    // ```\n    const isOutdented = state.sCount[nextLine] < state.blkIndent\n\n    pos = state.bMarks[nextLine] + state.tShift[nextLine]\n    max = state.eMarks[nextLine]\n\n    if (pos >= max) {\n      // Case 1: line is not inside the blockquote, and this line is empty.\n      break\n    }\n\n    if (state.src.charCodeAt(pos++) === 0x3E/* > */ && !isOutdented) {\n      // This line is inside the blockquote.\n\n      // set offset past spaces and \">\"\n      let initial = state.sCount[nextLine] + 1\n      let spaceAfterMarker\n      let adjustTab\n\n      // skip one optional space after '>'\n      if (state.src.charCodeAt(pos) === 0x20 /* space */) {\n        // ' >   test '\n        //     ^ -- position start of line here:\n        pos++\n        initial++\n        adjustTab = false\n        spaceAfterMarker = true\n      } else if (state.src.charCodeAt(pos) === 0x09 /* tab */) {\n        spaceAfterMarker = true\n\n        if ((state.bsCount[nextLine] + initial) % 4 === 3) {\n          // '  >\\t  test '\n          //       ^ -- position start of line here (tab has width===1)\n          pos++\n          initial++\n          adjustTab = false\n        } else {\n          // ' >\\t  test '\n          //    ^ -- position start of line here + shift bsCount slightly\n          //         to make extra space appear\n          adjustTab = true\n        }\n      } else {\n        spaceAfterMarker = false\n      }\n\n      let offset = initial\n      oldBMarks.push(state.bMarks[nextLine])\n      state.bMarks[nextLine] = pos\n\n      while (pos < max) {\n        const ch = state.src.charCodeAt(pos)\n\n        if (isSpace(ch)) {\n          if (ch === 0x09) {\n            offset += 4 - (offset + state.bsCount[nextLine] + (adjustTab ? 1 : 0)) % 4\n          } else {\n            offset++\n          }\n        } else {\n          break\n        }\n\n        pos++\n      }\n\n      lastLineEmpty = pos >= max\n\n      oldBSCount.push(state.bsCount[nextLine])\n      state.bsCount[nextLine] = state.sCount[nextLine] + 1 + (spaceAfterMarker ? 1 : 0)\n\n      oldSCount.push(state.sCount[nextLine])\n      state.sCount[nextLine] = offset - initial\n\n      oldTShift.push(state.tShift[nextLine])\n      state.tShift[nextLine] = pos - state.bMarks[nextLine]\n      continue\n    }\n\n    // Case 2: line is not inside the blockquote, and the last line was empty.\n    if (lastLineEmpty) { break }\n\n    // Case 3: another tag found.\n    let terminate = false\n    for (let i = 0, l = terminatorRules.length; i < l; i++) {\n      if (terminatorRules[i](state, nextLine, endLine, true)) {\n        terminate = true\n        break\n      }\n    }\n\n    if (terminate) {\n      // Quirk to enforce \"hard termination mode\" for paragraphs;\n      // normally if you call `tokenize(state, startLine, nextLine)`,\n      // paragraphs will look below nextLine for paragraph continuation,\n      // but if blockquote is terminated by another tag, they shouldn't\n      state.lineMax = nextLine\n\n      if (state.blkIndent !== 0) {\n        // state.blkIndent was non-zero, we now set it to zero,\n        // so we need to re-calculate all offsets to appear as\n        // if indent wasn't changed\n        oldBMarks.push(state.bMarks[nextLine])\n        oldBSCount.push(state.bsCount[nextLine])\n        oldTShift.push(state.tShift[nextLine])\n        oldSCount.push(state.sCount[nextLine])\n        state.sCount[nextLine] -= state.blkIndent\n      }\n\n      break\n    }\n\n    oldBMarks.push(state.bMarks[nextLine])\n    oldBSCount.push(state.bsCount[nextLine])\n    oldTShift.push(state.tShift[nextLine])\n    oldSCount.push(state.sCount[nextLine])\n\n    // A negative indentation means that this is a paragraph continuation\n    //\n    state.sCount[nextLine] = -1\n  }\n\n  const oldIndent = state.blkIndent\n  state.blkIndent = 0\n\n  const token_o  = state.push('blockquote_open', 'blockquote', 1)\n  token_o.markup = '>'\n  const lines = [startLine, 0]\n  token_o.map    = lines\n\n  state.md.block.tokenize(state, startLine, nextLine)\n\n  const token_c  = state.push('blockquote_close', 'blockquote', -1)\n  token_c.markup = '>'\n\n  state.lineMax = oldLineMax\n  state.parentType = oldParentType\n  lines[1] = state.line\n\n  // Restore original tShift; this might not be necessary since the parser\n  // has already been here, but just to make sure we can do that.\n  for (let i = 0; i < oldTShift.length; i++) {\n    state.bMarks[i + startLine] = oldBMarks[i]\n    state.tShift[i + startLine] = oldTShift[i]\n    state.sCount[i + startLine] = oldSCount[i]\n    state.bsCount[i + startLine] = oldBSCount[i]\n  }\n  state.blkIndent = oldIndent\n\n  return true\n}\n"], "names": [], "mappings": "AAAA,eAAe;;;;AAEf;;AAEe,SAAS,WAAY,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM;IACnE,IAAI,MAAM,MAAM,MAAM,CAAC,UAAU,GAAG,MAAM,MAAM,CAAC,UAAU;IAC3D,IAAI,MAAM,MAAM,MAAM,CAAC,UAAU;IAEjC,MAAM,aAAa,MAAM,OAAO;IAEhC,iEAAiE;IACjE,IAAI,MAAM,MAAM,CAAC,UAAU,GAAG,MAAM,SAAS,IAAI,GAAG;QAAE,OAAO;IAAM;IAEnE,+BAA+B;IAC/B,IAAI,MAAM,GAAG,CAAC,UAAU,CAAC,SAAS,KAAI,KAAK,KAAI;QAAE,OAAO;IAAM;IAE9D,oDAAoD;IACpD,0DAA0D;IAC1D,IAAI,QAAQ;QAAE,OAAO;IAAK;IAE1B,MAAM,YAAa,EAAE;IACrB,MAAM,aAAa,EAAE;IACrB,MAAM,YAAa,EAAE;IACrB,MAAM,YAAa,EAAE;IAErB,MAAM,kBAAkB,MAAM,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC;IAEtD,MAAM,gBAAgB,MAAM,UAAU;IACtC,MAAM,UAAU,GAAG;IACnB,IAAI,gBAAgB;IACpB,IAAI;IAEJ,8BAA8B;IAC9B,EAAE;IACF,0BAA0B;IAC1B,6BAA6B;IAC7B,UAAU;IACV,aAAa;IACb,EAAE;IACF,UAAU;IACV,4BAA4B;IAC5B,UAAU;IACV,QAAQ;IACR,WAAW;IACX,UAAU;IACV,mBAAmB;IACnB,UAAU;IACV,aAAa;IACb,aAAa;IACb,UAAU;IACV,IAAK,WAAW,WAAW,WAAW,SAAS,WAAY;QACzD,mEAAmE;QACnE,4BAA4B;QAC5B,EAAE;QACF,MAAM;QACN,cAAc;QACd,0BAA0B;QAC1B,wBAAwB;QACxB,MAAM;QACN,MAAM,cAAc,MAAM,MAAM,CAAC,SAAS,GAAG,MAAM,SAAS;QAE5D,MAAM,MAAM,MAAM,CAAC,SAAS,GAAG,MAAM,MAAM,CAAC,SAAS;QACrD,MAAM,MAAM,MAAM,CAAC,SAAS;QAE5B,IAAI,OAAO,KAAK;YAEd;QACF;QAEA,IAAI,MAAM,GAAG,CAAC,UAAU,CAAC,WAAW,KAAI,KAAK,OAAM,CAAC,aAAa;YAC/D,sCAAsC;YAEtC,iCAAiC;YACjC,IAAI,UAAU,MAAM,MAAM,CAAC,SAAS,GAAG;YACvC,IAAI;YACJ,IAAI;YAEJ,oCAAoC;YACpC,IAAI,MAAM,GAAG,CAAC,UAAU,CAAC,SAAS,KAAK,SAAS,KAAI;gBAClD,eAAe;gBACf,wCAAwC;gBACxC;gBACA;gBACA,YAAY;gBACZ,mBAAmB;YACrB,OAAO,IAAI,MAAM,GAAG,CAAC,UAAU,CAAC,SAAS,KAAK,OAAO,KAAI;gBACvD,mBAAmB;gBAEnB,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS,GAAG,OAAO,IAAI,MAAM,GAAG;oBACjD,iBAAiB;oBACjB,6DAA6D;oBAC7D;oBACA;oBACA,YAAY;gBACd,OAAO;oBACL,gBAAgB;oBAChB,+DAA+D;oBAC/D,qCAAqC;oBACrC,YAAY;gBACd;YACF,OAAO;gBACL,mBAAmB;YACrB;YAEA,IAAI,SAAS;YACb,UAAU,IAAI,CAAC,MAAM,MAAM,CAAC,SAAS;YACrC,MAAM,MAAM,CAAC,SAAS,GAAG;YAEzB,MAAO,MAAM,IAAK;gBAChB,MAAM,KAAK,MAAM,GAAG,CAAC,UAAU,CAAC;gBAEhC,IAAI,CAAA,GAAA,wNAAA,CAAA,UAAO,AAAD,EAAE,KAAK;oBACf,IAAI,OAAO,MAAM;wBACf,UAAU,IAAI,CAAC,SAAS,MAAM,OAAO,CAAC,SAAS,GAAG,CAAC,YAAY,IAAI,CAAC,CAAC,IAAI;oBAC3E,OAAO;wBACL;oBACF;gBACF,OAAO;oBACL;gBACF;gBAEA;YACF;YAEA,gBAAgB,OAAO;YAEvB,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS;YACvC,MAAM,OAAO,CAAC,SAAS,GAAG,MAAM,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,mBAAmB,IAAI,CAAC;YAEhF,UAAU,IAAI,CAAC,MAAM,MAAM,CAAC,SAAS;YACrC,MAAM,MAAM,CAAC,SAAS,GAAG,SAAS;YAElC,UAAU,IAAI,CAAC,MAAM,MAAM,CAAC,SAAS;YACrC,MAAM,MAAM,CAAC,SAAS,GAAG,MAAM,MAAM,MAAM,CAAC,SAAS;YACrD;QACF;QAEA,0EAA0E;QAC1E,IAAI,eAAe;YAAE;QAAM;QAE3B,6BAA6B;QAC7B,IAAI,YAAY;QAChB,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,MAAM,EAAE,IAAI,GAAG,IAAK;YACtD,IAAI,eAAe,CAAC,EAAE,CAAC,OAAO,UAAU,SAAS,OAAO;gBACtD,YAAY;gBACZ;YACF;QACF;QAEA,IAAI,WAAW;YACb,2DAA2D;YAC3D,+DAA+D;YAC/D,kEAAkE;YAClE,iEAAiE;YACjE,MAAM,OAAO,GAAG;YAEhB,IAAI,MAAM,SAAS,KAAK,GAAG;gBACzB,uDAAuD;gBACvD,sDAAsD;gBACtD,2BAA2B;gBAC3B,UAAU,IAAI,CAAC,MAAM,MAAM,CAAC,SAAS;gBACrC,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS;gBACvC,UAAU,IAAI,CAAC,MAAM,MAAM,CAAC,SAAS;gBACrC,UAAU,IAAI,CAAC,MAAM,MAAM,CAAC,SAAS;gBACrC,MAAM,MAAM,CAAC,SAAS,IAAI,MAAM,SAAS;YAC3C;YAEA;QACF;QAEA,UAAU,IAAI,CAAC,MAAM,MAAM,CAAC,SAAS;QACrC,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS;QACvC,UAAU,IAAI,CAAC,MAAM,MAAM,CAAC,SAAS;QACrC,UAAU,IAAI,CAAC,MAAM,MAAM,CAAC,SAAS;QAErC,qEAAqE;QACrE,EAAE;QACF,MAAM,MAAM,CAAC,SAAS,GAAG,CAAC;IAC5B;IAEA,MAAM,YAAY,MAAM,SAAS;IACjC,MAAM,SAAS,GAAG;IAElB,MAAM,UAAW,MAAM,IAAI,CAAC,mBAAmB,cAAc;IAC7D,QAAQ,MAAM,GAAG;IACjB,MAAM,QAAQ;QAAC;QAAW;KAAE;IAC5B,QAAQ,GAAG,GAAM;IAEjB,MAAM,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,WAAW;IAE1C,MAAM,UAAW,MAAM,IAAI,CAAC,oBAAoB,cAAc,CAAC;IAC/D,QAAQ,MAAM,GAAG;IAEjB,MAAM,OAAO,GAAG;IAChB,MAAM,UAAU,GAAG;IACnB,KAAK,CAAC,EAAE,GAAG,MAAM,IAAI;IAErB,wEAAwE;IACxE,+DAA+D;IAC/D,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QACzC,MAAM,MAAM,CAAC,IAAI,UAAU,GAAG,SAAS,CAAC,EAAE;QAC1C,MAAM,MAAM,CAAC,IAAI,UAAU,GAAG,SAAS,CAAC,EAAE;QAC1C,MAAM,MAAM,CAAC,IAAI,UAAU,GAAG,SAAS,CAAC,EAAE;QAC1C,MAAM,OAAO,CAAC,IAAI,UAAU,GAAG,UAAU,CAAC,EAAE;IAC9C;IACA,MAAM,SAAS,GAAG;IAElB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2733, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_block/hr.mjs"], "sourcesContent": ["// Horizontal rule\n\nimport { isSpace } from '../common/utils.mjs'\n\nexport default function hr (state, startLine, endLine, silent) {\n  const max = state.eMarks[startLine]\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false }\n\n  let pos = state.bMarks[startLine] + state.tShift[startLine]\n  const marker = state.src.charCodeAt(pos++)\n\n  // Check hr marker\n  if (marker !== 0x2A/* * */ &&\n      marker !== 0x2D/* - */ &&\n      marker !== 0x5F/* _ */) {\n    return false\n  }\n\n  // markers can be mixed with spaces, but there should be at least 3 of them\n\n  let cnt = 1\n  while (pos < max) {\n    const ch = state.src.charCodeAt(pos++)\n    if (ch !== marker && !isSpace(ch)) { return false }\n    if (ch === marker) { cnt++ }\n  }\n\n  if (cnt < 3) { return false }\n\n  if (silent) { return true }\n\n  state.line = startLine + 1\n\n  const token  = state.push('hr', 'hr', 0)\n  token.map    = [startLine, state.line]\n  token.markup = Array(cnt + 1).join(String.fromCharCode(marker))\n\n  return true\n}\n"], "names": [], "mappings": "AAAA,kBAAkB;;;;AAElB;;AAEe,SAAS,GAAI,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM;IAC3D,MAAM,MAAM,MAAM,MAAM,CAAC,UAAU;IACnC,iEAAiE;IACjE,IAAI,MAAM,MAAM,CAAC,UAAU,GAAG,MAAM,SAAS,IAAI,GAAG;QAAE,OAAO;IAAM;IAEnE,IAAI,MAAM,MAAM,MAAM,CAAC,UAAU,GAAG,MAAM,MAAM,CAAC,UAAU;IAC3D,MAAM,SAAS,MAAM,GAAG,CAAC,UAAU,CAAC;IAEpC,kBAAkB;IAClB,IAAI,WAAW,KAAI,KAAK,OACpB,WAAW,KAAI,KAAK,OACpB,WAAW,KAAI,KAAK,KAAI;QAC1B,OAAO;IACT;IAEA,2EAA2E;IAE3E,IAAI,MAAM;IACV,MAAO,MAAM,IAAK;QAChB,MAAM,KAAK,MAAM,GAAG,CAAC,UAAU,CAAC;QAChC,IAAI,OAAO,UAAU,CAAC,CAAA,GAAA,wNAAA,CAAA,UAAO,AAAD,EAAE,KAAK;YAAE,OAAO;QAAM;QAClD,IAAI,OAAO,QAAQ;YAAE;QAAM;IAC7B;IAEA,IAAI,MAAM,GAAG;QAAE,OAAO;IAAM;IAE5B,IAAI,QAAQ;QAAE,OAAO;IAAK;IAE1B,MAAM,IAAI,GAAG,YAAY;IAEzB,MAAM,QAAS,MAAM,IAAI,CAAC,MAAM,MAAM;IACtC,MAAM,GAAG,GAAM;QAAC;QAAW,MAAM,IAAI;KAAC;IACtC,MAAM,MAAM,GAAG,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,YAAY,CAAC;IAEvD,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2783, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_block/list.mjs"], "sourcesContent": ["// Lists\n\nimport { isSpace } from '../common/utils.mjs'\n\n// Search `[-+*][\\n ]`, returns next pos after marker on success\n// or -1 on fail.\nfunction skipBulletListMarker (state, startLine) {\n  const max = state.eMarks[startLine]\n  let pos = state.bMarks[startLine] + state.tShift[startLine]\n\n  const marker = state.src.charCodeAt(pos++)\n  // Check bullet\n  if (marker !== 0x2A/* * */ &&\n      marker !== 0x2D/* - */ &&\n      marker !== 0x2B/* + */) {\n    return -1\n  }\n\n  if (pos < max) {\n    const ch = state.src.charCodeAt(pos)\n\n    if (!isSpace(ch)) {\n      // \" -test \" - is not a list item\n      return -1\n    }\n  }\n\n  return pos\n}\n\n// Search `\\d+[.)][\\n ]`, returns next pos after marker on success\n// or -1 on fail.\nfunction skipOrderedListMarker (state, startLine) {\n  const start = state.bMarks[startLine] + state.tShift[startLine]\n  const max = state.eMarks[startLine]\n  let pos = start\n\n  // List marker should have at least 2 chars (digit + dot)\n  if (pos + 1 >= max) { return -1 }\n\n  let ch = state.src.charCodeAt(pos++)\n\n  if (ch < 0x30/* 0 */ || ch > 0x39/* 9 */) { return -1 }\n\n  for (;;) {\n    // EOL -> fail\n    if (pos >= max) { return -1 }\n\n    ch = state.src.charCodeAt(pos++)\n\n    if (ch >= 0x30/* 0 */ && ch <= 0x39/* 9 */) {\n      // List marker should have no more than 9 digits\n      // (prevents integer overflow in browsers)\n      if (pos - start >= 10) { return -1 }\n\n      continue\n    }\n\n    // found valid marker\n    if (ch === 0x29/* ) */ || ch === 0x2e/* . */) {\n      break\n    }\n\n    return -1\n  }\n\n  if (pos < max) {\n    ch = state.src.charCodeAt(pos)\n\n    if (!isSpace(ch)) {\n      // \" 1.test \" - is not a list item\n      return -1\n    }\n  }\n  return pos\n}\n\nfunction markTightParagraphs (state, idx) {\n  const level = state.level + 2\n\n  for (let i = idx + 2, l = state.tokens.length - 2; i < l; i++) {\n    if (state.tokens[i].level === level && state.tokens[i].type === 'paragraph_open') {\n      state.tokens[i + 2].hidden = true\n      state.tokens[i].hidden = true\n      i += 2\n    }\n  }\n}\n\nexport default function list (state, startLine, endLine, silent) {\n  let max, pos, start, token\n  let nextLine = startLine\n  let tight = true\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[nextLine] - state.blkIndent >= 4) { return false }\n\n  // Special case:\n  //  - item 1\n  //   - item 2\n  //    - item 3\n  //     - item 4\n  //      - this one is a paragraph continuation\n  if (state.listIndent >= 0 &&\n      state.sCount[nextLine] - state.listIndent >= 4 &&\n      state.sCount[nextLine] < state.blkIndent) {\n    return false\n  }\n\n  let isTerminatingParagraph = false\n\n  // limit conditions when list can interrupt\n  // a paragraph (validation mode only)\n  if (silent && state.parentType === 'paragraph') {\n    // Next list item should still terminate previous list item;\n    //\n    // This code can fail if plugins use blkIndent as well as lists,\n    // but I hope the spec gets fixed long before that happens.\n    //\n    if (state.sCount[nextLine] >= state.blkIndent) {\n      isTerminatingParagraph = true\n    }\n  }\n\n  // Detect list type and position after marker\n  let isOrdered\n  let markerValue\n  let posAfterMarker\n  if ((posAfterMarker = skipOrderedListMarker(state, nextLine)) >= 0) {\n    isOrdered = true\n    start = state.bMarks[nextLine] + state.tShift[nextLine]\n    markerValue = Number(state.src.slice(start, posAfterMarker - 1))\n\n    // If we're starting a new ordered list right after\n    // a paragraph, it should start with 1.\n    if (isTerminatingParagraph && markerValue !== 1) return false\n  } else if ((posAfterMarker = skipBulletListMarker(state, nextLine)) >= 0) {\n    isOrdered = false\n  } else {\n    return false\n  }\n\n  // If we're starting a new unordered list right after\n  // a paragraph, first line should not be empty.\n  if (isTerminatingParagraph) {\n    if (state.skipSpaces(posAfterMarker) >= state.eMarks[nextLine]) return false\n  }\n\n  // For validation mode we can terminate immediately\n  if (silent) { return true }\n\n  // We should terminate list on style change. Remember first one to compare.\n  const markerCharCode = state.src.charCodeAt(posAfterMarker - 1)\n\n  // Start list\n  const listTokIdx = state.tokens.length\n\n  if (isOrdered) {\n    token       = state.push('ordered_list_open', 'ol', 1)\n    if (markerValue !== 1) {\n      token.attrs = [['start', markerValue]]\n    }\n  } else {\n    token       = state.push('bullet_list_open', 'ul', 1)\n  }\n\n  const listLines = [nextLine, 0]\n  token.map    = listLines\n  token.markup = String.fromCharCode(markerCharCode)\n\n  //\n  // Iterate list items\n  //\n\n  let prevEmptyEnd = false\n  const terminatorRules = state.md.block.ruler.getRules('list')\n\n  const oldParentType = state.parentType\n  state.parentType = 'list'\n\n  while (nextLine < endLine) {\n    pos = posAfterMarker\n    max = state.eMarks[nextLine]\n\n    const initial = state.sCount[nextLine] + posAfterMarker - (state.bMarks[nextLine] + state.tShift[nextLine])\n    let offset = initial\n\n    while (pos < max) {\n      const ch = state.src.charCodeAt(pos)\n\n      if (ch === 0x09) {\n        offset += 4 - (offset + state.bsCount[nextLine]) % 4\n      } else if (ch === 0x20) {\n        offset++\n      } else {\n        break\n      }\n\n      pos++\n    }\n\n    const contentStart = pos\n    let indentAfterMarker\n\n    if (contentStart >= max) {\n      // trimming space in \"-    \\n  3\" case, indent is 1 here\n      indentAfterMarker = 1\n    } else {\n      indentAfterMarker = offset - initial\n    }\n\n    // If we have more than 4 spaces, the indent is 1\n    // (the rest is just indented code block)\n    if (indentAfterMarker > 4) { indentAfterMarker = 1 }\n\n    // \"  -  test\"\n    //  ^^^^^ - calculating total length of this thing\n    const indent = initial + indentAfterMarker\n\n    // Run subparser & write tokens\n    token        = state.push('list_item_open', 'li', 1)\n    token.markup = String.fromCharCode(markerCharCode)\n    const itemLines = [nextLine, 0]\n    token.map    = itemLines\n    if (isOrdered) {\n      token.info = state.src.slice(start, posAfterMarker - 1)\n    }\n\n    // change current state, then restore it after parser subcall\n    const oldTight = state.tight\n    const oldTShift = state.tShift[nextLine]\n    const oldSCount = state.sCount[nextLine]\n\n    //  - example list\n    // ^ listIndent position will be here\n    //   ^ blkIndent position will be here\n    //\n    const oldListIndent = state.listIndent\n    state.listIndent = state.blkIndent\n    state.blkIndent = indent\n\n    state.tight = true\n    state.tShift[nextLine] = contentStart - state.bMarks[nextLine]\n    state.sCount[nextLine] = offset\n\n    if (contentStart >= max && state.isEmpty(nextLine + 1)) {\n      // workaround for this case\n      // (list item is empty, list terminates before \"foo\"):\n      // ~~~~~~~~\n      //   -\n      //\n      //     foo\n      // ~~~~~~~~\n      state.line = Math.min(state.line + 2, endLine)\n    } else {\n      state.md.block.tokenize(state, nextLine, endLine, true)\n    }\n\n    // If any of list item is tight, mark list as tight\n    if (!state.tight || prevEmptyEnd) {\n      tight = false\n    }\n    // Item become loose if finish with empty line,\n    // but we should filter last element, because it means list finish\n    prevEmptyEnd = (state.line - nextLine) > 1 && state.isEmpty(state.line - 1)\n\n    state.blkIndent = state.listIndent\n    state.listIndent = oldListIndent\n    state.tShift[nextLine] = oldTShift\n    state.sCount[nextLine] = oldSCount\n    state.tight = oldTight\n\n    token        = state.push('list_item_close', 'li', -1)\n    token.markup = String.fromCharCode(markerCharCode)\n\n    nextLine = state.line\n    itemLines[1] = nextLine\n\n    if (nextLine >= endLine) { break }\n\n    //\n    // Try to check if list is terminated or continued.\n    //\n    if (state.sCount[nextLine] < state.blkIndent) { break }\n\n    // if it's indented more than 3 spaces, it should be a code block\n    if (state.sCount[nextLine] - state.blkIndent >= 4) { break }\n\n    // fail if terminating block found\n    let terminate = false\n    for (let i = 0, l = terminatorRules.length; i < l; i++) {\n      if (terminatorRules[i](state, nextLine, endLine, true)) {\n        terminate = true\n        break\n      }\n    }\n    if (terminate) { break }\n\n    // fail if list has another type\n    if (isOrdered) {\n      posAfterMarker = skipOrderedListMarker(state, nextLine)\n      if (posAfterMarker < 0) { break }\n      start = state.bMarks[nextLine] + state.tShift[nextLine]\n    } else {\n      posAfterMarker = skipBulletListMarker(state, nextLine)\n      if (posAfterMarker < 0) { break }\n    }\n\n    if (markerCharCode !== state.src.charCodeAt(posAfterMarker - 1)) { break }\n  }\n\n  // Finalize list\n  if (isOrdered) {\n    token = state.push('ordered_list_close', 'ol', -1)\n  } else {\n    token = state.push('bullet_list_close', 'ul', -1)\n  }\n  token.markup = String.fromCharCode(markerCharCode)\n\n  listLines[1] = nextLine\n  state.line = nextLine\n\n  state.parentType = oldParentType\n\n  // mark paragraphs tight if needed\n  if (tight) {\n    markTightParagraphs(state, listTokIdx)\n  }\n\n  return true\n}\n"], "names": [], "mappings": "AAAA,QAAQ;;;;AAER;;AAEA,gEAAgE;AAChE,iBAAiB;AACjB,SAAS,qBAAsB,KAAK,EAAE,SAAS;IAC7C,MAAM,MAAM,MAAM,MAAM,CAAC,UAAU;IACnC,IAAI,MAAM,MAAM,MAAM,CAAC,UAAU,GAAG,MAAM,MAAM,CAAC,UAAU;IAE3D,MAAM,SAAS,MAAM,GAAG,CAAC,UAAU,CAAC;IACpC,eAAe;IACf,IAAI,WAAW,KAAI,KAAK,OACpB,WAAW,KAAI,KAAK,OACpB,WAAW,KAAI,KAAK,KAAI;QAC1B,OAAO,CAAC;IACV;IAEA,IAAI,MAAM,KAAK;QACb,MAAM,KAAK,MAAM,GAAG,CAAC,UAAU,CAAC;QAEhC,IAAI,CAAC,CAAA,GAAA,wNAAA,CAAA,UAAO,AAAD,EAAE,KAAK;YAChB,iCAAiC;YACjC,OAAO,CAAC;QACV;IACF;IAEA,OAAO;AACT;AAEA,kEAAkE;AAClE,iBAAiB;AACjB,SAAS,sBAAuB,KAAK,EAAE,SAAS;IAC9C,MAAM,QAAQ,MAAM,MAAM,CAAC,UAAU,GAAG,MAAM,MAAM,CAAC,UAAU;IAC/D,MAAM,MAAM,MAAM,MAAM,CAAC,UAAU;IACnC,IAAI,MAAM;IAEV,yDAAyD;IACzD,IAAI,MAAM,KAAK,KAAK;QAAE,OAAO,CAAC;IAAE;IAEhC,IAAI,KAAK,MAAM,GAAG,CAAC,UAAU,CAAC;IAE9B,IAAI,KAAK,KAAI,KAAK,OAAM,KAAK,KAAI,KAAK,KAAI;QAAE,OAAO,CAAC;IAAE;IAEtD,OAAS;QACP,cAAc;QACd,IAAI,OAAO,KAAK;YAAE,OAAO,CAAC;QAAE;QAE5B,KAAK,MAAM,GAAG,CAAC,UAAU,CAAC;QAE1B,IAAI,MAAM,KAAI,KAAK,OAAM,MAAM,KAAI,KAAK,KAAI;YAC1C,gDAAgD;YAChD,0CAA0C;YAC1C,IAAI,MAAM,SAAS,IAAI;gBAAE,OAAO,CAAC;YAAE;YAEnC;QACF;QAEA,qBAAqB;QACrB,IAAI,OAAO,KAAI,KAAK,OAAM,OAAO,KAAI,KAAK,KAAI;YAC5C;QACF;QAEA,OAAO,CAAC;IACV;IAEA,IAAI,MAAM,KAAK;QACb,KAAK,MAAM,GAAG,CAAC,UAAU,CAAC;QAE1B,IAAI,CAAC,QAAQ,KAAK;YAChB,kCAAkC;YAClC,OAAO,CAAC;QACV;IACF;IACA,OAAO;AACT;AAEA,SAAS,oBAAqB,KAAK,EAAE,GAAG;IACtC,MAAM,QAAQ,MAAM,KAAK,GAAG;IAE5B,IAAK,IAAI,IAAI,MAAM,GAAG,IAAI,MAAM,MAAM,CAAC,MAAM,GAAG,GAAG,IAAI,GAAG,IAAK;QAC7D,IAAI,MAAM,MAAM,CAAC,EAAE,CAAC,KAAK,KAAK,SAAS,MAAM,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,kBAAkB;YAChF,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG;YAC7B,MAAM,MAAM,CAAC,EAAE,CAAC,MAAM,GAAG;YACzB,KAAK;QACP;IACF;AACF;AAEe,SAAS,KAAM,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM;IAC7D,IAAI,KAAK,KAAK,OAAO;IACrB,IAAI,WAAW;IACf,IAAI,QAAQ;IAEZ,iEAAiE;IACjE,IAAI,MAAM,MAAM,CAAC,SAAS,GAAG,MAAM,SAAS,IAAI,GAAG;QAAE,OAAO;IAAM;IAElE,gBAAgB;IAChB,YAAY;IACZ,aAAa;IACb,cAAc;IACd,eAAe;IACf,8CAA8C;IAC9C,IAAI,MAAM,UAAU,IAAI,KACpB,MAAM,MAAM,CAAC,SAAS,GAAG,MAAM,UAAU,IAAI,KAC7C,MAAM,MAAM,CAAC,SAAS,GAAG,MAAM,SAAS,EAAE;QAC5C,OAAO;IACT;IAEA,IAAI,yBAAyB;IAE7B,2CAA2C;IAC3C,qCAAqC;IACrC,IAAI,UAAU,MAAM,UAAU,KAAK,aAAa;QAC9C,4DAA4D;QAC5D,EAAE;QACF,gEAAgE;QAChE,2DAA2D;QAC3D,EAAE;QACF,IAAI,MAAM,MAAM,CAAC,SAAS,IAAI,MAAM,SAAS,EAAE;YAC7C,yBAAyB;QAC3B;IACF;IAEA,6CAA6C;IAC7C,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI,CAAC,iBAAiB,sBAAsB,OAAO,SAAS,KAAK,GAAG;QAClE,YAAY;QACZ,QAAQ,MAAM,MAAM,CAAC,SAAS,GAAG,MAAM,MAAM,CAAC,SAAS;QACvD,cAAc,OAAO,MAAM,GAAG,CAAC,KAAK,CAAC,OAAO,iBAAiB;QAE7D,mDAAmD;QACnD,uCAAuC;QACvC,IAAI,0BAA0B,gBAAgB,GAAG,OAAO;IAC1D,OAAO,IAAI,CAAC,iBAAiB,qBAAqB,OAAO,SAAS,KAAK,GAAG;QACxE,YAAY;IACd,OAAO;QACL,OAAO;IACT;IAEA,qDAAqD;IACrD,+CAA+C;IAC/C,IAAI,wBAAwB;QAC1B,IAAI,MAAM,UAAU,CAAC,mBAAmB,MAAM,MAAM,CAAC,SAAS,EAAE,OAAO;IACzE;IAEA,mDAAmD;IACnD,IAAI,QAAQ;QAAE,OAAO;IAAK;IAE1B,2EAA2E;IAC3E,MAAM,iBAAiB,MAAM,GAAG,CAAC,UAAU,CAAC,iBAAiB;IAE7D,aAAa;IACb,MAAM,aAAa,MAAM,MAAM,CAAC,MAAM;IAEtC,IAAI,WAAW;QACb,QAAc,MAAM,IAAI,CAAC,qBAAqB,MAAM;QACpD,IAAI,gBAAgB,GAAG;YACrB,MAAM,KAAK,GAAG;gBAAC;oBAAC;oBAAS;iBAAY;aAAC;QACxC;IACF,OAAO;QACL,QAAc,MAAM,IAAI,CAAC,oBAAoB,MAAM;IACrD;IAEA,MAAM,YAAY;QAAC;QAAU;KAAE;IAC/B,MAAM,GAAG,GAAM;IACf,MAAM,MAAM,GAAG,OAAO,YAAY,CAAC;IAEnC,EAAE;IACF,qBAAqB;IACrB,EAAE;IAEF,IAAI,eAAe;IACnB,MAAM,kBAAkB,MAAM,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC;IAEtD,MAAM,gBAAgB,MAAM,UAAU;IACtC,MAAM,UAAU,GAAG;IAEnB,MAAO,WAAW,QAAS;QACzB,MAAM;QACN,MAAM,MAAM,MAAM,CAAC,SAAS;QAE5B,MAAM,UAAU,MAAM,MAAM,CAAC,SAAS,GAAG,iBAAiB,CAAC,MAAM,MAAM,CAAC,SAAS,GAAG,MAAM,MAAM,CAAC,SAAS;QAC1G,IAAI,SAAS;QAEb,MAAO,MAAM,IAAK;YAChB,MAAM,KAAK,MAAM,GAAG,CAAC,UAAU,CAAC;YAEhC,IAAI,OAAO,MAAM;gBACf,UAAU,IAAI,CAAC,SAAS,MAAM,OAAO,CAAC,SAAS,IAAI;YACrD,OAAO,IAAI,OAAO,MAAM;gBACtB;YACF,OAAO;gBACL;YACF;YAEA;QACF;QAEA,MAAM,eAAe;QACrB,IAAI;QAEJ,IAAI,gBAAgB,KAAK;YACvB,wDAAwD;YACxD,oBAAoB;QACtB,OAAO;YACL,oBAAoB,SAAS;QAC/B;QAEA,iDAAiD;QACjD,yCAAyC;QACzC,IAAI,oBAAoB,GAAG;YAAE,oBAAoB;QAAE;QAEnD,cAAc;QACd,kDAAkD;QAClD,MAAM,SAAS,UAAU;QAEzB,+BAA+B;QAC/B,QAAe,MAAM,IAAI,CAAC,kBAAkB,MAAM;QAClD,MAAM,MAAM,GAAG,OAAO,YAAY,CAAC;QACnC,MAAM,YAAY;YAAC;YAAU;SAAE;QAC/B,MAAM,GAAG,GAAM;QACf,IAAI,WAAW;YACb,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,KAAK,CAAC,OAAO,iBAAiB;QACvD;QAEA,6DAA6D;QAC7D,MAAM,WAAW,MAAM,KAAK;QAC5B,MAAM,YAAY,MAAM,MAAM,CAAC,SAAS;QACxC,MAAM,YAAY,MAAM,MAAM,CAAC,SAAS;QAExC,kBAAkB;QAClB,qCAAqC;QACrC,sCAAsC;QACtC,EAAE;QACF,MAAM,gBAAgB,MAAM,UAAU;QACtC,MAAM,UAAU,GAAG,MAAM,SAAS;QAClC,MAAM,SAAS,GAAG;QAElB,MAAM,KAAK,GAAG;QACd,MAAM,MAAM,CAAC,SAAS,GAAG,eAAe,MAAM,MAAM,CAAC,SAAS;QAC9D,MAAM,MAAM,CAAC,SAAS,GAAG;QAEzB,IAAI,gBAAgB,OAAO,MAAM,OAAO,CAAC,WAAW,IAAI;YACtD,2BAA2B;YAC3B,sDAAsD;YACtD,WAAW;YACX,MAAM;YACN,EAAE;YACF,UAAU;YACV,WAAW;YACX,MAAM,IAAI,GAAG,KAAK,GAAG,CAAC,MAAM,IAAI,GAAG,GAAG;QACxC,OAAO;YACL,MAAM,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,UAAU,SAAS;QACpD;QAEA,mDAAmD;QACnD,IAAI,CAAC,MAAM,KAAK,IAAI,cAAc;YAChC,QAAQ;QACV;QACA,+CAA+C;QAC/C,kEAAkE;QAClE,eAAe,AAAC,MAAM,IAAI,GAAG,WAAY,KAAK,MAAM,OAAO,CAAC,MAAM,IAAI,GAAG;QAEzE,MAAM,SAAS,GAAG,MAAM,UAAU;QAClC,MAAM,UAAU,GAAG;QACnB,MAAM,MAAM,CAAC,SAAS,GAAG;QACzB,MAAM,MAAM,CAAC,SAAS,GAAG;QACzB,MAAM,KAAK,GAAG;QAEd,QAAe,MAAM,IAAI,CAAC,mBAAmB,MAAM,CAAC;QACpD,MAAM,MAAM,GAAG,OAAO,YAAY,CAAC;QAEnC,WAAW,MAAM,IAAI;QACrB,SAAS,CAAC,EAAE,GAAG;QAEf,IAAI,YAAY,SAAS;YAAE;QAAM;QAEjC,EAAE;QACF,mDAAmD;QACnD,EAAE;QACF,IAAI,MAAM,MAAM,CAAC,SAAS,GAAG,MAAM,SAAS,EAAE;YAAE;QAAM;QAEtD,iEAAiE;QACjE,IAAI,MAAM,MAAM,CAAC,SAAS,GAAG,MAAM,SAAS,IAAI,GAAG;YAAE;QAAM;QAE3D,kCAAkC;QAClC,IAAI,YAAY;QAChB,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,MAAM,EAAE,IAAI,GAAG,IAAK;YACtD,IAAI,eAAe,CAAC,EAAE,CAAC,OAAO,UAAU,SAAS,OAAO;gBACtD,YAAY;gBACZ;YACF;QACF;QACA,IAAI,WAAW;YAAE;QAAM;QAEvB,gCAAgC;QAChC,IAAI,WAAW;YACb,iBAAiB,sBAAsB,OAAO;YAC9C,IAAI,iBAAiB,GAAG;gBAAE;YAAM;YAChC,QAAQ,MAAM,MAAM,CAAC,SAAS,GAAG,MAAM,MAAM,CAAC,SAAS;QACzD,OAAO;YACL,iBAAiB,qBAAqB,OAAO;YAC7C,IAAI,iBAAiB,GAAG;gBAAE;YAAM;QAClC;QAEA,IAAI,mBAAmB,MAAM,GAAG,CAAC,UAAU,CAAC,iBAAiB,IAAI;YAAE;QAAM;IAC3E;IAEA,gBAAgB;IAChB,IAAI,WAAW;QACb,QAAQ,MAAM,IAAI,CAAC,sBAAsB,MAAM,CAAC;IAClD,OAAO;QACL,QAAQ,MAAM,IAAI,CAAC,qBAAqB,MAAM,CAAC;IACjD;IACA,MAAM,MAAM,GAAG,OAAO,YAAY,CAAC;IAEnC,SAAS,CAAC,EAAE,GAAG;IACf,MAAM,IAAI,GAAG;IAEb,MAAM,UAAU,GAAG;IAEnB,kCAAkC;IAClC,IAAI,OAAO;QACT,oBAAoB,OAAO;IAC7B;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3094, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_block/reference.mjs"], "sourcesContent": ["import { isSpace, normalizeReference } from '../common/utils.mjs'\n\nexport default function reference (state, startLine, _endLine, silent) {\n  let pos = state.bMarks[startLine] + state.tShift[startLine]\n  let max = state.eMarks[startLine]\n  let nextLine = startLine + 1\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false }\n\n  if (state.src.charCodeAt(pos) !== 0x5B/* [ */) { return false }\n\n  function getNextLine (nextLine) {\n    const endLine = state.lineMax\n\n    if (nextLine >= endLine || state.isEmpty(nextLine)) {\n      // empty line or end of input\n      return null\n    }\n\n    let isContinuation = false\n\n    // this would be a code block normally, but after paragraph\n    // it's considered a lazy continuation regardless of what's there\n    if (state.sCount[nextLine] - state.blkIndent > 3) { isContinuation = true }\n\n    // quirk for blockquotes, this line should already be checked by that rule\n    if (state.sCount[nextLine] < 0) { isContinuation = true }\n\n    if (!isContinuation) {\n      const terminatorRules = state.md.block.ruler.getRules('reference')\n      const oldParentType = state.parentType\n      state.parentType = 'reference'\n\n      // Some tags can terminate paragraph without empty line.\n      let terminate = false\n      for (let i = 0, l = terminatorRules.length; i < l; i++) {\n        if (terminatorRules[i](state, nextLine, endLine, true)) {\n          terminate = true\n          break\n        }\n      }\n\n      state.parentType = oldParentType\n      if (terminate) {\n        // terminated by another block\n        return null\n      }\n    }\n\n    const pos = state.bMarks[nextLine] + state.tShift[nextLine]\n    const max = state.eMarks[nextLine]\n\n    // max + 1 explicitly includes the newline\n    return state.src.slice(pos, max + 1)\n  }\n\n  let str = state.src.slice(pos, max + 1)\n\n  max = str.length\n  let labelEnd = -1\n\n  for (pos = 1; pos < max; pos++) {\n    const ch = str.charCodeAt(pos)\n    if (ch === 0x5B /* [ */) {\n      return false\n    } else if (ch === 0x5D /* ] */) {\n      labelEnd = pos\n      break\n    } else if (ch === 0x0A /* \\n */) {\n      const lineContent = getNextLine(nextLine)\n      if (lineContent !== null) {\n        str += lineContent\n        max = str.length\n        nextLine++\n      }\n    } else if (ch === 0x5C /* \\ */) {\n      pos++\n      if (pos < max && str.charCodeAt(pos) === 0x0A) {\n        const lineContent = getNextLine(nextLine)\n        if (lineContent !== null) {\n          str += lineContent\n          max = str.length\n          nextLine++\n        }\n      }\n    }\n  }\n\n  if (labelEnd < 0 || str.charCodeAt(labelEnd + 1) !== 0x3A/* : */) { return false }\n\n  // [label]:   destination   'title'\n  //         ^^^ skip optional whitespace here\n  for (pos = labelEnd + 2; pos < max; pos++) {\n    const ch = str.charCodeAt(pos)\n    if (ch === 0x0A) {\n      const lineContent = getNextLine(nextLine)\n      if (lineContent !== null) {\n        str += lineContent\n        max = str.length\n        nextLine++\n      }\n    } else if (isSpace(ch)) {\n      /* eslint no-empty:0 */\n    } else {\n      break\n    }\n  }\n\n  // [label]:   destination   'title'\n  //            ^^^^^^^^^^^ parse this\n  const destRes = state.md.helpers.parseLinkDestination(str, pos, max)\n  if (!destRes.ok) { return false }\n\n  const href = state.md.normalizeLink(destRes.str)\n  if (!state.md.validateLink(href)) { return false }\n\n  pos = destRes.pos\n\n  // save cursor state, we could require to rollback later\n  const destEndPos = pos\n  const destEndLineNo = nextLine\n\n  // [label]:   destination   'title'\n  //                       ^^^ skipping those spaces\n  const start = pos\n  for (; pos < max; pos++) {\n    const ch = str.charCodeAt(pos)\n    if (ch === 0x0A) {\n      const lineContent = getNextLine(nextLine)\n      if (lineContent !== null) {\n        str += lineContent\n        max = str.length\n        nextLine++\n      }\n    } else if (isSpace(ch)) {\n      /* eslint no-empty:0 */\n    } else {\n      break\n    }\n  }\n\n  // [label]:   destination   'title'\n  //                          ^^^^^^^ parse this\n  let titleRes = state.md.helpers.parseLinkTitle(str, pos, max)\n  while (titleRes.can_continue) {\n    const lineContent = getNextLine(nextLine)\n    if (lineContent === null) break\n    str += lineContent\n    pos = max\n    max = str.length\n    nextLine++\n    titleRes = state.md.helpers.parseLinkTitle(str, pos, max, titleRes)\n  }\n  let title\n\n  if (pos < max && start !== pos && titleRes.ok) {\n    title = titleRes.str\n    pos = titleRes.pos\n  } else {\n    title = ''\n    pos = destEndPos\n    nextLine = destEndLineNo\n  }\n\n  // skip trailing spaces until the rest of the line\n  while (pos < max) {\n    const ch = str.charCodeAt(pos)\n    if (!isSpace(ch)) { break }\n    pos++\n  }\n\n  if (pos < max && str.charCodeAt(pos) !== 0x0A) {\n    if (title) {\n      // garbage at the end of the line after title,\n      // but it could still be a valid reference if we roll back\n      title = ''\n      pos = destEndPos\n      nextLine = destEndLineNo\n      while (pos < max) {\n        const ch = str.charCodeAt(pos)\n        if (!isSpace(ch)) { break }\n        pos++\n      }\n    }\n  }\n\n  if (pos < max && str.charCodeAt(pos) !== 0x0A) {\n    // garbage at the end of the line\n    return false\n  }\n\n  const label = normalizeReference(str.slice(1, labelEnd))\n  if (!label) {\n    // CommonMark 0.20 disallows empty labels\n    return false\n  }\n\n  // Reference can not terminate anything. This check is for safety only.\n  /* istanbul ignore if */\n  if (silent) { return true }\n\n  if (typeof state.env.references === 'undefined') {\n    state.env.references = {}\n  }\n  if (typeof state.env.references[label] === 'undefined') {\n    state.env.references[label] = { title, href }\n  }\n\n  state.line = nextLine\n  return true\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEe,SAAS,UAAW,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM;IACnE,IAAI,MAAM,MAAM,MAAM,CAAC,UAAU,GAAG,MAAM,MAAM,CAAC,UAAU;IAC3D,IAAI,MAAM,MAAM,MAAM,CAAC,UAAU;IACjC,IAAI,WAAW,YAAY;IAE3B,iEAAiE;IACjE,IAAI,MAAM,MAAM,CAAC,UAAU,GAAG,MAAM,SAAS,IAAI,GAAG;QAAE,OAAO;IAAM;IAEnE,IAAI,MAAM,GAAG,CAAC,UAAU,CAAC,SAAS,KAAI,KAAK,KAAI;QAAE,OAAO;IAAM;IAE9D,SAAS,YAAa,QAAQ;QAC5B,MAAM,UAAU,MAAM,OAAO;QAE7B,IAAI,YAAY,WAAW,MAAM,OAAO,CAAC,WAAW;YAClD,6BAA6B;YAC7B,OAAO;QACT;QAEA,IAAI,iBAAiB;QAErB,2DAA2D;QAC3D,iEAAiE;QACjE,IAAI,MAAM,MAAM,CAAC,SAAS,GAAG,MAAM,SAAS,GAAG,GAAG;YAAE,iBAAiB;QAAK;QAE1E,0EAA0E;QAC1E,IAAI,MAAM,MAAM,CAAC,SAAS,GAAG,GAAG;YAAE,iBAAiB;QAAK;QAExD,IAAI,CAAC,gBAAgB;YACnB,MAAM,kBAAkB,MAAM,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC;YACtD,MAAM,gBAAgB,MAAM,UAAU;YACtC,MAAM,UAAU,GAAG;YAEnB,wDAAwD;YACxD,IAAI,YAAY;YAChB,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,MAAM,EAAE,IAAI,GAAG,IAAK;gBACtD,IAAI,eAAe,CAAC,EAAE,CAAC,OAAO,UAAU,SAAS,OAAO;oBACtD,YAAY;oBACZ;gBACF;YACF;YAEA,MAAM,UAAU,GAAG;YACnB,IAAI,WAAW;gBACb,8BAA8B;gBAC9B,OAAO;YACT;QACF;QAEA,MAAM,MAAM,MAAM,MAAM,CAAC,SAAS,GAAG,MAAM,MAAM,CAAC,SAAS;QAC3D,MAAM,MAAM,MAAM,MAAM,CAAC,SAAS;QAElC,0CAA0C;QAC1C,OAAO,MAAM,GAAG,CAAC,KAAK,CAAC,KAAK,MAAM;IACpC;IAEA,IAAI,MAAM,MAAM,GAAG,CAAC,KAAK,CAAC,KAAK,MAAM;IAErC,MAAM,IAAI,MAAM;IAChB,IAAI,WAAW,CAAC;IAEhB,IAAK,MAAM,GAAG,MAAM,KAAK,MAAO;QAC9B,MAAM,KAAK,IAAI,UAAU,CAAC;QAC1B,IAAI,OAAO,KAAK,KAAK,KAAI;YACvB,OAAO;QACT,OAAO,IAAI,OAAO,KAAK,KAAK,KAAI;YAC9B,WAAW;YACX;QACF,OAAO,IAAI,OAAO,KAAK,MAAM,KAAI;YAC/B,MAAM,cAAc,YAAY;YAChC,IAAI,gBAAgB,MAAM;gBACxB,OAAO;gBACP,MAAM,IAAI,MAAM;gBAChB;YACF;QACF,OAAO,IAAI,OAAO,KAAK,KAAK,KAAI;YAC9B;YACA,IAAI,MAAM,OAAO,IAAI,UAAU,CAAC,SAAS,MAAM;gBAC7C,MAAM,cAAc,YAAY;gBAChC,IAAI,gBAAgB,MAAM;oBACxB,OAAO;oBACP,MAAM,IAAI,MAAM;oBAChB;gBACF;YACF;QACF;IACF;IAEA,IAAI,WAAW,KAAK,IAAI,UAAU,CAAC,WAAW,OAAO,KAAI,KAAK,KAAI;QAAE,OAAO;IAAM;IAEjF,mCAAmC;IACnC,4CAA4C;IAC5C,IAAK,MAAM,WAAW,GAAG,MAAM,KAAK,MAAO;QACzC,MAAM,KAAK,IAAI,UAAU,CAAC;QAC1B,IAAI,OAAO,MAAM;YACf,MAAM,cAAc,YAAY;YAChC,IAAI,gBAAgB,MAAM;gBACxB,OAAO;gBACP,MAAM,IAAI,MAAM;gBAChB;YACF;QACF,OAAO,IAAI,CAAA,GAAA,wNAAA,CAAA,UAAO,AAAD,EAAE,KAAK;QACtB,qBAAqB,GACvB,OAAO;YACL;QACF;IACF;IAEA,mCAAmC;IACnC,oCAAoC;IACpC,MAAM,UAAU,MAAM,EAAE,CAAC,OAAO,CAAC,oBAAoB,CAAC,KAAK,KAAK;IAChE,IAAI,CAAC,QAAQ,EAAE,EAAE;QAAE,OAAO;IAAM;IAEhC,MAAM,OAAO,MAAM,EAAE,CAAC,aAAa,CAAC,QAAQ,GAAG;IAC/C,IAAI,CAAC,MAAM,EAAE,CAAC,YAAY,CAAC,OAAO;QAAE,OAAO;IAAM;IAEjD,MAAM,QAAQ,GAAG;IAEjB,wDAAwD;IACxD,MAAM,aAAa;IACnB,MAAM,gBAAgB;IAEtB,mCAAmC;IACnC,kDAAkD;IAClD,MAAM,QAAQ;IACd,MAAO,MAAM,KAAK,MAAO;QACvB,MAAM,KAAK,IAAI,UAAU,CAAC;QAC1B,IAAI,OAAO,MAAM;YACf,MAAM,cAAc,YAAY;YAChC,IAAI,gBAAgB,MAAM;gBACxB,OAAO;gBACP,MAAM,IAAI,MAAM;gBAChB;YACF;QACF,OAAO,IAAI,CAAA,GAAA,wNAAA,CAAA,UAAO,AAAD,EAAE,KAAK;QACtB,qBAAqB,GACvB,OAAO;YACL;QACF;IACF;IAEA,mCAAmC;IACnC,8CAA8C;IAC9C,IAAI,WAAW,MAAM,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,KAAK;IACzD,MAAO,SAAS,YAAY,CAAE;QAC5B,MAAM,cAAc,YAAY;QAChC,IAAI,gBAAgB,MAAM;QAC1B,OAAO;QACP,MAAM;QACN,MAAM,IAAI,MAAM;QAChB;QACA,WAAW,MAAM,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,KAAK,KAAK;IAC5D;IACA,IAAI;IAEJ,IAAI,MAAM,OAAO,UAAU,OAAO,SAAS,EAAE,EAAE;QAC7C,QAAQ,SAAS,GAAG;QACpB,MAAM,SAAS,GAAG;IACpB,OAAO;QACL,QAAQ;QACR,MAAM;QACN,WAAW;IACb;IAEA,kDAAkD;IAClD,MAAO,MAAM,IAAK;QAChB,MAAM,KAAK,IAAI,UAAU,CAAC;QAC1B,IAAI,CAAC,CAAA,GAAA,wNAAA,CAAA,UAAO,AAAD,EAAE,KAAK;YAAE;QAAM;QAC1B;IACF;IAEA,IAAI,MAAM,OAAO,IAAI,UAAU,CAAC,SAAS,MAAM;QAC7C,IAAI,OAAO;YACT,8CAA8C;YAC9C,0DAA0D;YAC1D,QAAQ;YACR,MAAM;YACN,WAAW;YACX,MAAO,MAAM,IAAK;gBAChB,MAAM,KAAK,IAAI,UAAU,CAAC;gBAC1B,IAAI,CAAC,CAAA,GAAA,wNAAA,CAAA,UAAO,AAAD,EAAE,KAAK;oBAAE;gBAAM;gBAC1B;YACF;QACF;IACF;IAEA,IAAI,MAAM,OAAO,IAAI,UAAU,CAAC,SAAS,MAAM;QAC7C,iCAAiC;QACjC,OAAO;IACT;IAEA,MAAM,QAAQ,CAAA,GAAA,wNAAA,CAAA,qBAAkB,AAAD,EAAE,IAAI,KAAK,CAAC,GAAG;IAC9C,IAAI,CAAC,OAAO;QACV,yCAAyC;QACzC,OAAO;IACT;IAEA,uEAAuE;IACvE,sBAAsB,GACtB,IAAI,QAAQ;QAAE,OAAO;IAAK;IAE1B,IAAI,OAAO,MAAM,GAAG,CAAC,UAAU,KAAK,aAAa;QAC/C,MAAM,GAAG,CAAC,UAAU,GAAG,CAAC;IAC1B;IACA,IAAI,OAAO,MAAM,GAAG,CAAC,UAAU,CAAC,MAAM,KAAK,aAAa;QACtD,MAAM,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG;YAAE;YAAO;QAAK;IAC9C;IAEA,MAAM,IAAI,GAAG;IACb,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3304, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/common/html_blocks.mjs"], "sourcesContent": ["// List of valid html blocks names, according to commonmark spec\n// https://spec.commonmark.org/0.30/#html-blocks\n\nexport default [\n  'address',\n  'article',\n  'aside',\n  'base',\n  'basefont',\n  'blockquote',\n  'body',\n  'caption',\n  'center',\n  'col',\n  'colgroup',\n  'dd',\n  'details',\n  'dialog',\n  'dir',\n  'div',\n  'dl',\n  'dt',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'footer',\n  'form',\n  'frame',\n  'frameset',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'head',\n  'header',\n  'hr',\n  'html',\n  'iframe',\n  'legend',\n  'li',\n  'link',\n  'main',\n  'menu',\n  'menuitem',\n  'nav',\n  'noframes',\n  'ol',\n  'optgroup',\n  'option',\n  'p',\n  'param',\n  'search',\n  'section',\n  'summary',\n  'table',\n  'tbody',\n  'td',\n  'tfoot',\n  'th',\n  'thead',\n  'title',\n  'tr',\n  'track',\n  'ul'\n]\n"], "names": [], "mappings": "AAAA,gEAAgE;AAChE,gDAAgD;;;;uCAEjC;IACb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3379, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/common/html_re.mjs"], "sourcesContent": ["// Regexps to match html elements\n\nconst attr_name     = '[a-zA-Z_:][a-zA-Z0-9:._-]*'\n\nconst unquoted      = '[^\"\\'=<>`\\\\x00-\\\\x20]+'\nconst single_quoted = \"'[^']*'\"\nconst double_quoted = '\"[^\"]*\"'\n\nconst attr_value  = '(?:' + unquoted + '|' + single_quoted + '|' + double_quoted + ')'\n\nconst attribute   = '(?:\\\\s+' + attr_name + '(?:\\\\s*=\\\\s*' + attr_value + ')?)'\n\nconst open_tag    = '<[A-Za-z][A-Za-z0-9\\\\-]*' + attribute + '*\\\\s*\\\\/?>'\n\nconst close_tag   = '<\\\\/[A-Za-z][A-Za-z0-9\\\\-]*\\\\s*>'\nconst comment     = '<!---?>|<!--(?:[^-]|-[^-]|--[^>])*-->'\nconst processing  = '<[?][\\\\s\\\\S]*?[?]>'\nconst declaration = '<![A-Za-z][^>]*>'\nconst cdata       = '<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>'\n\nconst HTML_TAG_RE = new RegExp('^(?:' + open_tag + '|' + close_tag + '|' + comment +\n                        '|' + processing + '|' + declaration + '|' + cdata + ')')\nconst HTML_OPEN_CLOSE_TAG_RE = new RegExp('^(?:' + open_tag + '|' + close_tag + ')')\n\nexport { HTML_TAG_RE, HTML_OPEN_CLOSE_TAG_RE }\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;AAEjC,MAAM,YAAgB;AAEtB,MAAM,WAAgB;AACtB,MAAM,gBAAgB;AACtB,MAAM,gBAAgB;AAEtB,MAAM,aAAc,QAAQ,WAAW,MAAM,gBAAgB,MAAM,gBAAgB;AAEnF,MAAM,YAAc,YAAY,YAAY,iBAAiB,aAAa;AAE1E,MAAM,WAAc,6BAA6B,YAAY;AAE7D,MAAM,YAAc;AACpB,MAAM,UAAc;AACpB,MAAM,aAAc;AACpB,MAAM,cAAc;AACpB,MAAM,QAAc;AAEpB,MAAM,cAAc,IAAI,OAAO,SAAS,WAAW,MAAM,YAAY,MAAM,UACnD,MAAM,aAAa,MAAM,cAAc,MAAM,QAAQ;AAC7E,MAAM,yBAAyB,IAAI,OAAO,SAAS,WAAW,MAAM,YAAY", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3405, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_block/html_block.mjs"], "sourcesContent": ["// HTML block\n\nimport block_names from '../common/html_blocks.mjs'\nimport { HTML_OPEN_CLOSE_TAG_RE } from '../common/html_re.mjs'\n\n// An array of opening and corresponding closing sequences for html tags,\n// last argument defines whether it can terminate a paragraph or not\n//\nconst HTML_SEQUENCES = [\n  [/^<(script|pre|style|textarea)(?=(\\s|>|$))/i, /<\\/(script|pre|style|textarea)>/i, true],\n  [/^<!--/,        /-->/,   true],\n  [/^<\\?/,         /\\?>/,   true],\n  [/^<![A-Z]/,     />/,     true],\n  [/^<!\\[CDATA\\[/, /\\]\\]>/, true],\n  [new RegExp('^</?(' + block_names.join('|') + ')(?=(\\\\s|/?>|$))', 'i'), /^$/, true],\n  [new RegExp(HTML_OPEN_CLOSE_TAG_RE.source + '\\\\s*$'),  /^$/, false]\n]\n\nexport default function html_block (state, startLine, endLine, silent) {\n  let pos = state.bMarks[startLine] + state.tShift[startLine]\n  let max = state.eMarks[startLine]\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false }\n\n  if (!state.md.options.html) { return false }\n\n  if (state.src.charCodeAt(pos) !== 0x3C/* < */) { return false }\n\n  let lineText = state.src.slice(pos, max)\n\n  let i = 0\n  for (; i < HTML_SEQUENCES.length; i++) {\n    if (HTML_SEQUENCES[i][0].test(lineText)) { break }\n  }\n  if (i === HTML_SEQUENCES.length) { return false }\n\n  if (silent) {\n    // true if this sequence can be a terminator, false otherwise\n    return HTML_SEQUENCES[i][2]\n  }\n\n  let nextLine = startLine + 1\n\n  // If we are here - we detected HTML block.\n  // Let's roll down till block end.\n  if (!HTML_SEQUENCES[i][1].test(lineText)) {\n    for (; nextLine < endLine; nextLine++) {\n      if (state.sCount[nextLine] < state.blkIndent) { break }\n\n      pos = state.bMarks[nextLine] + state.tShift[nextLine]\n      max = state.eMarks[nextLine]\n      lineText = state.src.slice(pos, max)\n\n      if (HTML_SEQUENCES[i][1].test(lineText)) {\n        if (lineText.length !== 0) { nextLine++ }\n        break\n      }\n    }\n  }\n\n  state.line = nextLine\n\n  const token   = state.push('html_block', '', 0)\n  token.map     = [startLine, nextLine]\n  token.content = state.getLines(startLine, nextLine, state.blkIndent, true)\n\n  return true\n}\n"], "names": [], "mappings": "AAAA,aAAa;;;;AAEb;AACA;;;AAEA,yEAAyE;AACzE,oEAAoE;AACpE,EAAE;AACF,MAAM,iBAAiB;IACrB;QAAC;QAA8C;QAAoC;KAAK;IACxF;QAAC;QAAgB;QAAS;KAAK;IAC/B;QAAC;QAAgB;QAAS;KAAK;IAC/B;QAAC;QAAgB;QAAS;KAAK;IAC/B;QAAC;QAAgB;QAAS;KAAK;IAC/B;QAAC,IAAI,OAAO,UAAU,8NAAA,CAAA,UAAW,CAAC,IAAI,CAAC,OAAO,oBAAoB;QAAM;QAAM;KAAK;IACnF;QAAC,IAAI,OAAO,0NAAA,CAAA,yBAAsB,CAAC,MAAM,GAAG;QAAW;QAAM;KAAM;CACpE;AAEc,SAAS,WAAY,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM;IACnE,IAAI,MAAM,MAAM,MAAM,CAAC,UAAU,GAAG,MAAM,MAAM,CAAC,UAAU;IAC3D,IAAI,MAAM,MAAM,MAAM,CAAC,UAAU;IAEjC,iEAAiE;IACjE,IAAI,MAAM,MAAM,CAAC,UAAU,GAAG,MAAM,SAAS,IAAI,GAAG;QAAE,OAAO;IAAM;IAEnE,IAAI,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE;QAAE,OAAO;IAAM;IAE3C,IAAI,MAAM,GAAG,CAAC,UAAU,CAAC,SAAS,KAAI,KAAK,KAAI;QAAE,OAAO;IAAM;IAE9D,IAAI,WAAW,MAAM,GAAG,CAAC,KAAK,CAAC,KAAK;IAEpC,IAAI,IAAI;IACR,MAAO,IAAI,eAAe,MAAM,EAAE,IAAK;QACrC,IAAI,cAAc,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW;YAAE;QAAM;IACnD;IACA,IAAI,MAAM,eAAe,MAAM,EAAE;QAAE,OAAO;IAAM;IAEhD,IAAI,QAAQ;QACV,6DAA6D;QAC7D,OAAO,cAAc,CAAC,EAAE,CAAC,EAAE;IAC7B;IAEA,IAAI,WAAW,YAAY;IAE3B,2CAA2C;IAC3C,kCAAkC;IAClC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW;QACxC,MAAO,WAAW,SAAS,WAAY;YACrC,IAAI,MAAM,MAAM,CAAC,SAAS,GAAG,MAAM,SAAS,EAAE;gBAAE;YAAM;YAEtD,MAAM,MAAM,MAAM,CAAC,SAAS,GAAG,MAAM,MAAM,CAAC,SAAS;YACrD,MAAM,MAAM,MAAM,CAAC,SAAS;YAC5B,WAAW,MAAM,GAAG,CAAC,KAAK,CAAC,KAAK;YAEhC,IAAI,cAAc,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW;gBACvC,IAAI,SAAS,MAAM,KAAK,GAAG;oBAAE;gBAAW;gBACxC;YACF;QACF;IACF;IAEA,MAAM,IAAI,GAAG;IAEb,MAAM,QAAU,MAAM,IAAI,CAAC,cAAc,IAAI;IAC7C,MAAM,GAAG,GAAO;QAAC;QAAW;KAAS;IACrC,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,WAAW,UAAU,MAAM,SAAS,EAAE;IAErE,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3514, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_block/heading.mjs"], "sourcesContent": ["// heading (#, ##, ...)\n\nimport { isSpace } from '../common/utils.mjs'\n\nexport default function heading (state, startLine, endLine, silent) {\n  let pos = state.bMarks[startLine] + state.tShift[startLine]\n  let max = state.eMarks[startLine]\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false }\n\n  let ch  = state.src.charCodeAt(pos)\n\n  if (ch !== 0x23/* # */ || pos >= max) { return false }\n\n  // count heading level\n  let level = 1\n  ch = state.src.charCodeAt(++pos)\n  while (ch === 0x23/* # */ && pos < max && level <= 6) {\n    level++\n    ch = state.src.charCodeAt(++pos)\n  }\n\n  if (level > 6 || (pos < max && !isSpace(ch))) { return false }\n\n  if (silent) { return true }\n\n  // Let's cut tails like '    ###  ' from the end of string\n\n  max = state.skipSpacesBack(max, pos)\n  const tmp = state.skipCharsBack(max, 0x23, pos) // #\n  if (tmp > pos && isSpace(state.src.charCodeAt(tmp - 1))) {\n    max = tmp\n  }\n\n  state.line = startLine + 1\n\n  const token_o  = state.push('heading_open', 'h' + String(level), 1)\n  token_o.markup = '########'.slice(0, level)\n  token_o.map    = [startLine, state.line]\n\n  const token_i    = state.push('inline', '', 0)\n  token_i.content  = state.src.slice(pos, max).trim()\n  token_i.map      = [startLine, state.line]\n  token_i.children = []\n\n  const token_c  = state.push('heading_close', 'h' + String(level), -1)\n  token_c.markup = '########'.slice(0, level)\n\n  return true\n}\n"], "names": [], "mappings": "AAAA,uBAAuB;;;;AAEvB;;AAEe,SAAS,QAAS,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM;IAChE,IAAI,MAAM,MAAM,MAAM,CAAC,UAAU,GAAG,MAAM,MAAM,CAAC,UAAU;IAC3D,IAAI,MAAM,MAAM,MAAM,CAAC,UAAU;IAEjC,iEAAiE;IACjE,IAAI,MAAM,MAAM,CAAC,UAAU,GAAG,MAAM,SAAS,IAAI,GAAG;QAAE,OAAO;IAAM;IAEnE,IAAI,KAAM,MAAM,GAAG,CAAC,UAAU,CAAC;IAE/B,IAAI,OAAO,KAAI,KAAK,OAAM,OAAO,KAAK;QAAE,OAAO;IAAM;IAErD,sBAAsB;IACtB,IAAI,QAAQ;IACZ,KAAK,MAAM,GAAG,CAAC,UAAU,CAAC,EAAE;IAC5B,MAAO,OAAO,KAAI,KAAK,OAAM,MAAM,OAAO,SAAS,EAAG;QACpD;QACA,KAAK,MAAM,GAAG,CAAC,UAAU,CAAC,EAAE;IAC9B;IAEA,IAAI,QAAQ,KAAM,MAAM,OAAO,CAAC,CAAA,GAAA,wNAAA,CAAA,UAAO,AAAD,EAAE,KAAM;QAAE,OAAO;IAAM;IAE7D,IAAI,QAAQ;QAAE,OAAO;IAAK;IAE1B,0DAA0D;IAE1D,MAAM,MAAM,cAAc,CAAC,KAAK;IAChC,MAAM,MAAM,MAAM,aAAa,CAAC,KAAK,MAAM,KAAK,IAAI;;IACpD,IAAI,MAAM,OAAO,CAAA,GAAA,wNAAA,CAAA,UAAO,AAAD,EAAE,MAAM,GAAG,CAAC,UAAU,CAAC,MAAM,KAAK;QACvD,MAAM;IACR;IAEA,MAAM,IAAI,GAAG,YAAY;IAEzB,MAAM,UAAW,MAAM,IAAI,CAAC,gBAAgB,MAAM,OAAO,QAAQ;IACjE,QAAQ,MAAM,GAAG,WAAW,KAAK,CAAC,GAAG;IACrC,QAAQ,GAAG,GAAM;QAAC;QAAW,MAAM,IAAI;KAAC;IAExC,MAAM,UAAa,MAAM,IAAI,CAAC,UAAU,IAAI;IAC5C,QAAQ,OAAO,GAAI,MAAM,GAAG,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI;IACjD,QAAQ,GAAG,GAAQ;QAAC;QAAW,MAAM,IAAI;KAAC;IAC1C,QAAQ,QAAQ,GAAG,EAAE;IAErB,MAAM,UAAW,MAAM,IAAI,CAAC,iBAAiB,MAAM,OAAO,QAAQ,CAAC;IACnE,QAAQ,MAAM,GAAG,WAAW,KAAK,CAAC,GAAG;IAErC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3575, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_block/lheading.mjs"], "sourcesContent": ["// lheading (---, ===)\n\nexport default function lheading (state, startLine, endLine/*, silent */) {\n  const terminatorRules = state.md.block.ruler.getRules('paragraph')\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false }\n\n  const oldParentType = state.parentType\n  state.parentType = 'paragraph' // use paragraph to match terminatorRules\n\n  // jump line-by-line until empty one or EOF\n  let level = 0\n  let marker\n  let nextLine = startLine + 1\n\n  for (; nextLine < endLine && !state.isEmpty(nextLine); nextLine++) {\n    // this would be a code block normally, but after paragraph\n    // it's considered a lazy continuation regardless of what's there\n    if (state.sCount[nextLine] - state.blkIndent > 3) { continue }\n\n    //\n    // Check for underline in setext header\n    //\n    if (state.sCount[nextLine] >= state.blkIndent) {\n      let pos = state.bMarks[nextLine] + state.tShift[nextLine]\n      const max = state.eMarks[nextLine]\n\n      if (pos < max) {\n        marker = state.src.charCodeAt(pos)\n\n        if (marker === 0x2D/* - */ || marker === 0x3D/* = */) {\n          pos = state.skipChars(pos, marker)\n          pos = state.skipSpaces(pos)\n\n          if (pos >= max) {\n            level = (marker === 0x3D/* = */ ? 1 : 2)\n            break\n          }\n        }\n      }\n    }\n\n    // quirk for blockquotes, this line should already be checked by that rule\n    if (state.sCount[nextLine] < 0) { continue }\n\n    // Some tags can terminate paragraph without empty line.\n    let terminate = false\n    for (let i = 0, l = terminatorRules.length; i < l; i++) {\n      if (terminatorRules[i](state, nextLine, endLine, true)) {\n        terminate = true\n        break\n      }\n    }\n    if (terminate) { break }\n  }\n\n  if (!level) {\n    // Didn't find valid underline\n    return false\n  }\n\n  const content = state.getLines(startLine, nextLine, state.blkIndent, false).trim()\n\n  state.line = nextLine + 1\n\n  const token_o    = state.push('heading_open', 'h' + String(level), 1)\n  token_o.markup   = String.fromCharCode(marker)\n  token_o.map      = [startLine, state.line]\n\n  const token_i    = state.push('inline', '', 0)\n  token_i.content  = content\n  token_i.map      = [startLine, state.line - 1]\n  token_i.children = []\n\n  const token_c    = state.push('heading_close', 'h' + String(level), -1)\n  token_c.markup   = String.fromCharCode(marker)\n\n  state.parentType = oldParentType\n\n  return true\n}\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;AAEP,SAAS,SAAU,KAAK,EAAE,SAAS,EAAE,QAAO,WAAW,GAAX;IACzD,MAAM,kBAAkB,MAAM,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC;IAEtD,iEAAiE;IACjE,IAAI,MAAM,MAAM,CAAC,UAAU,GAAG,MAAM,SAAS,IAAI,GAAG;QAAE,OAAO;IAAM;IAEnE,MAAM,gBAAgB,MAAM,UAAU;IACtC,MAAM,UAAU,GAAG,YAAY,yCAAyC;;IAExE,2CAA2C;IAC3C,IAAI,QAAQ;IACZ,IAAI;IACJ,IAAI,WAAW,YAAY;IAE3B,MAAO,WAAW,WAAW,CAAC,MAAM,OAAO,CAAC,WAAW,WAAY;QACjE,2DAA2D;QAC3D,iEAAiE;QACjE,IAAI,MAAM,MAAM,CAAC,SAAS,GAAG,MAAM,SAAS,GAAG,GAAG;YAAE;QAAS;QAE7D,EAAE;QACF,uCAAuC;QACvC,EAAE;QACF,IAAI,MAAM,MAAM,CAAC,SAAS,IAAI,MAAM,SAAS,EAAE;YAC7C,IAAI,MAAM,MAAM,MAAM,CAAC,SAAS,GAAG,MAAM,MAAM,CAAC,SAAS;YACzD,MAAM,MAAM,MAAM,MAAM,CAAC,SAAS;YAElC,IAAI,MAAM,KAAK;gBACb,SAAS,MAAM,GAAG,CAAC,UAAU,CAAC;gBAE9B,IAAI,WAAW,KAAI,KAAK,OAAM,WAAW,KAAI,KAAK,KAAI;oBACpD,MAAM,MAAM,SAAS,CAAC,KAAK;oBAC3B,MAAM,MAAM,UAAU,CAAC;oBAEvB,IAAI,OAAO,KAAK;wBACd,QAAS,WAAW,KAAI,KAAK,MAAK,IAAI;wBACtC;oBACF;gBACF;YACF;QACF;QAEA,0EAA0E;QAC1E,IAAI,MAAM,MAAM,CAAC,SAAS,GAAG,GAAG;YAAE;QAAS;QAE3C,wDAAwD;QACxD,IAAI,YAAY;QAChB,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,MAAM,EAAE,IAAI,GAAG,IAAK;YACtD,IAAI,eAAe,CAAC,EAAE,CAAC,OAAO,UAAU,SAAS,OAAO;gBACtD,YAAY;gBACZ;YACF;QACF;QACA,IAAI,WAAW;YAAE;QAAM;IACzB;IAEA,IAAI,CAAC,OAAO;QACV,8BAA8B;QAC9B,OAAO;IACT;IAEA,MAAM,UAAU,MAAM,QAAQ,CAAC,WAAW,UAAU,MAAM,SAAS,EAAE,OAAO,IAAI;IAEhF,MAAM,IAAI,GAAG,WAAW;IAExB,MAAM,UAAa,MAAM,IAAI,CAAC,gBAAgB,MAAM,OAAO,QAAQ;IACnE,QAAQ,MAAM,GAAK,OAAO,YAAY,CAAC;IACvC,QAAQ,GAAG,GAAQ;QAAC;QAAW,MAAM,IAAI;KAAC;IAE1C,MAAM,UAAa,MAAM,IAAI,CAAC,UAAU,IAAI;IAC5C,QAAQ,OAAO,GAAI;IACnB,QAAQ,GAAG,GAAQ;QAAC;QAAW,MAAM,IAAI,GAAG;KAAE;IAC9C,QAAQ,QAAQ,GAAG,EAAE;IAErB,MAAM,UAAa,MAAM,IAAI,CAAC,iBAAiB,MAAM,OAAO,QAAQ,CAAC;IACrE,QAAQ,MAAM,GAAK,OAAO,YAAY,CAAC;IAEvC,MAAM,UAAU,GAAG;IAEnB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3662, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_block/paragraph.mjs"], "sourcesContent": ["// Paragraph\n\nexport default function paragraph (state, startLine, endLine) {\n  const terminatorRules = state.md.block.ruler.getRules('paragraph')\n  const oldParentType = state.parentType\n  let nextLine = startLine + 1\n  state.parentType = 'paragraph'\n\n  // jump line-by-line until empty one or EOF\n  for (; nextLine < endLine && !state.isEmpty(nextLine); nextLine++) {\n    // this would be a code block normally, but after paragraph\n    // it's considered a lazy continuation regardless of what's there\n    if (state.sCount[nextLine] - state.blkIndent > 3) { continue }\n\n    // quirk for blockquotes, this line should already be checked by that rule\n    if (state.sCount[nextLine] < 0) { continue }\n\n    // Some tags can terminate paragraph without empty line.\n    let terminate = false\n    for (let i = 0, l = terminatorRules.length; i < l; i++) {\n      if (terminatorRules[i](state, nextLine, endLine, true)) {\n        terminate = true\n        break\n      }\n    }\n    if (terminate) { break }\n  }\n\n  const content = state.getLines(startLine, nextLine, state.blkIndent, false).trim()\n\n  state.line = nextLine\n\n  const token_o    = state.push('paragraph_open', 'p', 1)\n  token_o.map      = [startLine, state.line]\n\n  const token_i    = state.push('inline', '', 0)\n  token_i.content  = content\n  token_i.map      = [startLine, state.line]\n  token_i.children = []\n\n  state.push('paragraph_close', 'p', -1)\n\n  state.parentType = oldParentType\n\n  return true\n}\n"], "names": [], "mappings": "AAAA,YAAY;;;;AAEG,SAAS,UAAW,KAAK,EAAE,SAAS,EAAE,OAAO;IAC1D,MAAM,kBAAkB,MAAM,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC;IACtD,MAAM,gBAAgB,MAAM,UAAU;IACtC,IAAI,WAAW,YAAY;IAC3B,MAAM,UAAU,GAAG;IAEnB,2CAA2C;IAC3C,MAAO,WAAW,WAAW,CAAC,MAAM,OAAO,CAAC,WAAW,WAAY;QACjE,2DAA2D;QAC3D,iEAAiE;QACjE,IAAI,MAAM,MAAM,CAAC,SAAS,GAAG,MAAM,SAAS,GAAG,GAAG;YAAE;QAAS;QAE7D,0EAA0E;QAC1E,IAAI,MAAM,MAAM,CAAC,SAAS,GAAG,GAAG;YAAE;QAAS;QAE3C,wDAAwD;QACxD,IAAI,YAAY;QAChB,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,MAAM,EAAE,IAAI,GAAG,IAAK;YACtD,IAAI,eAAe,CAAC,EAAE,CAAC,OAAO,UAAU,SAAS,OAAO;gBACtD,YAAY;gBACZ;YACF;QACF;QACA,IAAI,WAAW;YAAE;QAAM;IACzB;IAEA,MAAM,UAAU,MAAM,QAAQ,CAAC,WAAW,UAAU,MAAM,SAAS,EAAE,OAAO,IAAI;IAEhF,MAAM,IAAI,GAAG;IAEb,MAAM,UAAa,MAAM,IAAI,CAAC,kBAAkB,KAAK;IACrD,QAAQ,GAAG,GAAQ;QAAC;QAAW,MAAM,IAAI;KAAC;IAE1C,MAAM,UAAa,MAAM,IAAI,CAAC,UAAU,IAAI;IAC5C,QAAQ,OAAO,GAAI;IACnB,QAAQ,GAAG,GAAQ;QAAC;QAAW,MAAM,IAAI;KAAC;IAC1C,QAAQ,QAAQ,GAAG,EAAE;IAErB,MAAM,IAAI,CAAC,mBAAmB,KAAK,CAAC;IAEpC,MAAM,UAAU,GAAG;IAEnB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3718, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/parser_block.mjs"], "sourcesContent": ["/** internal\n * class ParserBlock\n *\n * Block-level tokenizer.\n **/\n\nimport Ruler from './ruler.mjs'\nimport StateBlock from './rules_block/state_block.mjs'\n\nimport r_table from './rules_block/table.mjs'\nimport r_code from './rules_block/code.mjs'\nimport r_fence from './rules_block/fence.mjs'\nimport r_blockquote from './rules_block/blockquote.mjs'\nimport r_hr from './rules_block/hr.mjs'\nimport r_list from './rules_block/list.mjs'\nimport r_reference from './rules_block/reference.mjs'\nimport r_html_block from './rules_block/html_block.mjs'\nimport r_heading from './rules_block/heading.mjs'\nimport r_lheading from './rules_block/lheading.mjs'\nimport r_paragraph from './rules_block/paragraph.mjs'\n\nconst _rules = [\n  // First 2 params - rule name & source. Secondary array - list of rules,\n  // which can be terminated by this one.\n  ['table',      r_table,      ['paragraph', 'reference']],\n  ['code',       r_code],\n  ['fence',      r_fence,      ['paragraph', 'reference', 'blockquote', 'list']],\n  ['blockquote', r_blockquote, ['paragraph', 'reference', 'blockquote', 'list']],\n  ['hr',         r_hr,         ['paragraph', 'reference', 'blockquote', 'list']],\n  ['list',       r_list,       ['paragraph', 'reference', 'blockquote']],\n  ['reference',  r_reference],\n  ['html_block', r_html_block, ['paragraph', 'reference', 'blockquote']],\n  ['heading',    r_heading,    ['paragraph', 'reference', 'blockquote']],\n  ['lheading',   r_lheading],\n  ['paragraph',  r_paragraph]\n]\n\n/**\n * new ParserBlock()\n **/\nfunction ParserBlock () {\n  /**\n   * ParserBlock#ruler -> Ruler\n   *\n   * [[Ruler]] instance. Keep configuration of block rules.\n   **/\n  this.ruler = new Ruler()\n\n  for (let i = 0; i < _rules.length; i++) {\n    this.ruler.push(_rules[i][0], _rules[i][1], { alt: (_rules[i][2] || []).slice() })\n  }\n}\n\n// Generate tokens for input range\n//\nParserBlock.prototype.tokenize = function (state, startLine, endLine) {\n  const rules = this.ruler.getRules('')\n  const len = rules.length\n  const maxNesting = state.md.options.maxNesting\n  let line = startLine\n  let hasEmptyLines = false\n\n  while (line < endLine) {\n    state.line = line = state.skipEmptyLines(line)\n    if (line >= endLine) { break }\n\n    // Termination condition for nested calls.\n    // Nested calls currently used for blockquotes & lists\n    if (state.sCount[line] < state.blkIndent) { break }\n\n    // If nesting level exceeded - skip tail to the end. That's not ordinary\n    // situation and we should not care about content.\n    if (state.level >= maxNesting) {\n      state.line = endLine\n      break\n    }\n\n    // Try all possible rules.\n    // On success, rule should:\n    //\n    // - update `state.line`\n    // - update `state.tokens`\n    // - return true\n    const prevLine = state.line\n    let ok = false\n\n    for (let i = 0; i < len; i++) {\n      ok = rules[i](state, line, endLine, false)\n      if (ok) {\n        if (prevLine >= state.line) {\n          throw new Error(\"block rule didn't increment state.line\")\n        }\n        break\n      }\n    }\n\n    // this can only happen if user disables paragraph rule\n    if (!ok) throw new Error('none of the block rules matched')\n\n    // set state.tight if we had an empty line before current tag\n    // i.e. latest empty line should not count\n    state.tight = !hasEmptyLines\n\n    // paragraph might \"eat\" one newline after it in nested lists\n    if (state.isEmpty(state.line - 1)) {\n      hasEmptyLines = true\n    }\n\n    line = state.line\n\n    if (line < endLine && state.isEmpty(line)) {\n      hasEmptyLines = true\n      line++\n      state.line = line\n    }\n  }\n}\n\n/**\n * ParserBlock.parse(str, md, env, outTokens)\n *\n * Process input string and push block tokens into `outTokens`\n **/\nParserBlock.prototype.parse = function (src, md, env, outTokens) {\n  if (!src) { return }\n\n  const state = new this.State(src, md, env, outTokens)\n\n  this.tokenize(state, state.line, state.lineMax)\n}\n\nParserBlock.prototype.State = StateBlock\n\nexport default ParserBlock\n"], "names": [], "mappings": "AAAA;;;;EAIE;;;AAEF;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AAEA,MAAM,SAAS;IACb,wEAAwE;IACxE,uCAAuC;IACvC;QAAC;QAAc,6NAAA,CAAA,UAAO;QAAO;YAAC;YAAa;SAAY;KAAC;IACxD;QAAC;QAAc,4NAAA,CAAA,UAAM;KAAC;IACtB;QAAC;QAAc,6NAAA,CAAA,UAAO;QAAO;YAAC;YAAa;YAAa;YAAc;SAAO;KAAC;IAC9E;QAAC;QAAc,kOAAA,CAAA,UAAY;QAAE;YAAC;YAAa;YAAa;YAAc;SAAO;KAAC;IAC9E;QAAC;QAAc,0NAAA,CAAA,UAAI;QAAU;YAAC;YAAa;YAAa;YAAc;SAAO;KAAC;IAC9E;QAAC;QAAc,4NAAA,CAAA,UAAM;QAAQ;YAAC;YAAa;YAAa;SAAa;KAAC;IACtE;QAAC;QAAc,iOAAA,CAAA,UAAW;KAAC;IAC3B;QAAC;QAAc,kOAAA,CAAA,UAAY;QAAE;YAAC;YAAa;YAAa;SAAa;KAAC;IACtE;QAAC;QAAc,+NAAA,CAAA,UAAS;QAAK;YAAC;YAAa;YAAa;SAAa;KAAC;IACtE;QAAC;QAAc,gOAAA,CAAA,UAAU;KAAC;IAC1B;QAAC;QAAc,iOAAA,CAAA,UAAW;KAAC;CAC5B;AAED;;EAEE,GACF,SAAS;IACP;;;;IAIE,GACF,IAAI,CAAC,KAAK,GAAG,IAAI,8MAAA,CAAA,UAAK;IAEtB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACtC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE;YAAE,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,KAAK;QAAG;IAClF;AACF;AAEA,kCAAkC;AAClC,EAAE;AACF,YAAY,SAAS,CAAC,QAAQ,GAAG,SAAU,KAAK,EAAE,SAAS,EAAE,OAAO;IAClE,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAClC,MAAM,MAAM,MAAM,MAAM;IACxB,MAAM,aAAa,MAAM,EAAE,CAAC,OAAO,CAAC,UAAU;IAC9C,IAAI,OAAO;IACX,IAAI,gBAAgB;IAEpB,MAAO,OAAO,QAAS;QACrB,MAAM,IAAI,GAAG,OAAO,MAAM,cAAc,CAAC;QACzC,IAAI,QAAQ,SAAS;YAAE;QAAM;QAE7B,0CAA0C;QAC1C,sDAAsD;QACtD,IAAI,MAAM,MAAM,CAAC,KAAK,GAAG,MAAM,SAAS,EAAE;YAAE;QAAM;QAElD,wEAAwE;QACxE,kDAAkD;QAClD,IAAI,MAAM,KAAK,IAAI,YAAY;YAC7B,MAAM,IAAI,GAAG;YACb;QACF;QAEA,0BAA0B;QAC1B,2BAA2B;QAC3B,EAAE;QACF,wBAAwB;QACxB,0BAA0B;QAC1B,gBAAgB;QAChB,MAAM,WAAW,MAAM,IAAI;QAC3B,IAAI,KAAK;QAET,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC5B,KAAK,KAAK,CAAC,EAAE,CAAC,OAAO,MAAM,SAAS;YACpC,IAAI,IAAI;gBACN,IAAI,YAAY,MAAM,IAAI,EAAE;oBAC1B,MAAM,IAAI,MAAM;gBAClB;gBACA;YACF;QACF;QAEA,uDAAuD;QACvD,IAAI,CAAC,IAAI,MAAM,IAAI,MAAM;QAEzB,6DAA6D;QAC7D,0CAA0C;QAC1C,MAAM,KAAK,GAAG,CAAC;QAEf,6DAA6D;QAC7D,IAAI,MAAM,OAAO,CAAC,MAAM,IAAI,GAAG,IAAI;YACjC,gBAAgB;QAClB;QAEA,OAAO,MAAM,IAAI;QAEjB,IAAI,OAAO,WAAW,MAAM,OAAO,CAAC,OAAO;YACzC,gBAAgB;YAChB;YACA,MAAM,IAAI,GAAG;QACf;IACF;AACF;AAEA;;;;EAIE,GACF,YAAY,SAAS,CAAC,KAAK,GAAG,SAAU,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,SAAS;IAC7D,IAAI,CAAC,KAAK;QAAE;IAAO;IAEnB,MAAM,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,KAAK;IAE3C,IAAI,CAAC,QAAQ,CAAC,OAAO,MAAM,IAAI,EAAE,MAAM,OAAO;AAChD;AAEA,YAAY,SAAS,CAAC,KAAK,GAAG,mOAAA,CAAA,UAAU;uCAEzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3927, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_inline/state_inline.mjs"], "sourcesContent": ["// Inline parser state\n\nimport Token from '../token.mjs'\nimport { isWhiteSpace, isPunctChar, isMdAsciiPunct } from '../common/utils.mjs'\n\nfunction StateInline (src, md, env, outTokens) {\n  this.src = src\n  this.env = env\n  this.md = md\n  this.tokens = outTokens\n  this.tokens_meta = Array(outTokens.length)\n\n  this.pos = 0\n  this.posMax = this.src.length\n  this.level = 0\n  this.pending = ''\n  this.pendingLevel = 0\n\n  // Stores { start: end } pairs. Useful for backtrack\n  // optimization of pairs parse (emphasis, strikes).\n  this.cache = {}\n\n  // List of emphasis-like delimiters for current tag\n  this.delimiters = []\n\n  // Stack of delimiter lists for upper level tags\n  this._prev_delimiters = []\n\n  // backtick length => last seen position\n  this.backticks = {}\n  this.backticksScanned = false\n\n  // Counter used to disable inline linkify-it execution\n  // inside <a> and markdown links\n  this.linkLevel = 0\n}\n\n// Flush pending text\n//\nStateInline.prototype.pushPending = function () {\n  const token = new Token('text', '', 0)\n  token.content = this.pending\n  token.level = this.pendingLevel\n  this.tokens.push(token)\n  this.pending = ''\n  return token\n}\n\n// Push new token to \"stream\".\n// If pending text exists - flush it as text token\n//\nStateInline.prototype.push = function (type, tag, nesting) {\n  if (this.pending) {\n    this.pushPending()\n  }\n\n  const token = new Token(type, tag, nesting)\n  let token_meta = null\n\n  if (nesting < 0) {\n    // closing tag\n    this.level--\n    this.delimiters = this._prev_delimiters.pop()\n  }\n\n  token.level = this.level\n\n  if (nesting > 0) {\n    // opening tag\n    this.level++\n    this._prev_delimiters.push(this.delimiters)\n    this.delimiters = []\n    token_meta = { delimiters: this.delimiters }\n  }\n\n  this.pendingLevel = this.level\n  this.tokens.push(token)\n  this.tokens_meta.push(token_meta)\n  return token\n}\n\n// Scan a sequence of emphasis-like markers, and determine whether\n// it can start an emphasis sequence or end an emphasis sequence.\n//\n//  - start - position to scan from (it should point at a valid marker);\n//  - canSplitWord - determine if these markers can be found inside a word\n//\nStateInline.prototype.scanDelims = function (start, canSplitWord) {\n  const max = this.posMax\n  const marker = this.src.charCodeAt(start)\n\n  // treat beginning of the line as a whitespace\n  const lastChar = start > 0 ? this.src.charCodeAt(start - 1) : 0x20\n\n  let pos = start\n  while (pos < max && this.src.charCodeAt(pos) === marker) { pos++ }\n\n  const count = pos - start\n\n  // treat end of the line as a whitespace\n  const nextChar = pos < max ? this.src.charCodeAt(pos) : 0x20\n\n  const isLastPunctChar = isMdAsciiPunct(lastChar) || isPunctChar(String.fromCharCode(lastChar))\n  const isNextPunctChar = isMdAsciiPunct(nextChar) || isPunctChar(String.fromCharCode(nextChar))\n\n  const isLastWhiteSpace = isWhiteSpace(lastChar)\n  const isNextWhiteSpace = isWhiteSpace(nextChar)\n\n  const left_flanking =\n    !isNextWhiteSpace && (!isNextPunctChar || isLastWhiteSpace || isLastPunctChar)\n  const right_flanking =\n    !isLastWhiteSpace && (!isLastPunctChar || isNextWhiteSpace || isNextPunctChar)\n\n  const can_open  = left_flanking  && (canSplitWord || !right_flanking || isLastPunctChar)\n  const can_close = right_flanking && (canSplitWord || !left_flanking  || isNextPunctChar)\n\n  return { can_open, can_close, length: count }\n}\n\n// re-export Token class to use in block rules\nStateInline.prototype.Token = Token\n\nexport default StateInline\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;AAEtB;AACA;;;AAEA,SAAS,YAAa,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,SAAS;IAC3C,IAAI,CAAC,GAAG,GAAG;IACX,IAAI,CAAC,GAAG,GAAG;IACX,IAAI,CAAC,EAAE,GAAG;IACV,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,WAAW,GAAG,MAAM,UAAU,MAAM;IAEzC,IAAI,CAAC,GAAG,GAAG;IACX,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM;IAC7B,IAAI,CAAC,KAAK,GAAG;IACb,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,YAAY,GAAG;IAEpB,oDAAoD;IACpD,mDAAmD;IACnD,IAAI,CAAC,KAAK,GAAG,CAAC;IAEd,mDAAmD;IACnD,IAAI,CAAC,UAAU,GAAG,EAAE;IAEpB,gDAAgD;IAChD,IAAI,CAAC,gBAAgB,GAAG,EAAE;IAE1B,wCAAwC;IACxC,IAAI,CAAC,SAAS,GAAG,CAAC;IAClB,IAAI,CAAC,gBAAgB,GAAG;IAExB,sDAAsD;IACtD,gCAAgC;IAChC,IAAI,CAAC,SAAS,GAAG;AACnB;AAEA,qBAAqB;AACrB,EAAE;AACF,YAAY,SAAS,CAAC,WAAW,GAAG;IAClC,MAAM,QAAQ,IAAI,8MAAA,CAAA,UAAK,CAAC,QAAQ,IAAI;IACpC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO;IAC5B,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY;IAC/B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IACjB,IAAI,CAAC,OAAO,GAAG;IACf,OAAO;AACT;AAEA,8BAA8B;AAC9B,kDAAkD;AAClD,EAAE;AACF,YAAY,SAAS,CAAC,IAAI,GAAG,SAAU,IAAI,EAAE,GAAG,EAAE,OAAO;IACvD,IAAI,IAAI,CAAC,OAAO,EAAE;QAChB,IAAI,CAAC,WAAW;IAClB;IAEA,MAAM,QAAQ,IAAI,8MAAA,CAAA,UAAK,CAAC,MAAM,KAAK;IACnC,IAAI,aAAa;IAEjB,IAAI,UAAU,GAAG;QACf,cAAc;QACd,IAAI,CAAC,KAAK;QACV,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG;IAC7C;IAEA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK;IAExB,IAAI,UAAU,GAAG;QACf,cAAc;QACd,IAAI,CAAC,KAAK;QACV,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU;QAC1C,IAAI,CAAC,UAAU,GAAG,EAAE;QACpB,aAAa;YAAE,YAAY,IAAI,CAAC,UAAU;QAAC;IAC7C;IAEA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK;IAC9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;IACjB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;IACtB,OAAO;AACT;AAEA,kEAAkE;AAClE,iEAAiE;AACjE,EAAE;AACF,wEAAwE;AACxE,0EAA0E;AAC1E,EAAE;AACF,YAAY,SAAS,CAAC,UAAU,GAAG,SAAU,KAAK,EAAE,YAAY;IAC9D,MAAM,MAAM,IAAI,CAAC,MAAM;IACvB,MAAM,SAAS,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;IAEnC,8CAA8C;IAC9C,MAAM,WAAW,QAAQ,IAAI,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,KAAK;IAE9D,IAAI,MAAM;IACV,MAAO,MAAM,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,SAAS,OAAQ;QAAE;IAAM;IAEjE,MAAM,QAAQ,MAAM;IAEpB,wCAAwC;IACxC,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO;IAExD,MAAM,kBAAkB,CAAA,GAAA,wNAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,CAAA,GAAA,wNAAA,CAAA,cAAW,AAAD,EAAE,OAAO,YAAY,CAAC;IACpF,MAAM,kBAAkB,CAAA,GAAA,wNAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,CAAA,GAAA,wNAAA,CAAA,cAAW,AAAD,EAAE,OAAO,YAAY,CAAC;IAEpF,MAAM,mBAAmB,CAAA,GAAA,wNAAA,CAAA,eAAY,AAAD,EAAE;IACtC,MAAM,mBAAmB,CAAA,GAAA,wNAAA,CAAA,eAAY,AAAD,EAAE;IAEtC,MAAM,gBACJ,CAAC,oBAAoB,CAAC,CAAC,mBAAmB,oBAAoB,eAAe;IAC/E,MAAM,iBACJ,CAAC,oBAAoB,CAAC,CAAC,mBAAmB,oBAAoB,eAAe;IAE/E,MAAM,WAAY,iBAAkB,CAAC,gBAAgB,CAAC,kBAAkB,eAAe;IACvF,MAAM,YAAY,kBAAkB,CAAC,gBAAgB,CAAC,iBAAkB,eAAe;IAEvF,OAAO;QAAE;QAAU;QAAW,QAAQ;IAAM;AAC9C;AAEA,8CAA8C;AAC9C,YAAY,SAAS,CAAC,KAAK,GAAG,8MAAA,CAAA,UAAK;uCAEpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4040, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_inline/text.mjs"], "sourcesContent": ["// Skip text characters for text token, place those to pending buffer\n// and increment current pos\n\n// Rule to skip pure text\n// '{}$%@~+=:' reserved for extentions\n\n// !, \", #, $, %, &, ', (, ), *, +, ,, -, ., /, :, ;, <, =, >, ?, @, [, \\, ], ^, _, `, {, |, }, or ~\n\n// !!!! Don't confuse with \"Markdown ASCII Punctuation\" chars\n// http://spec.commonmark.org/0.15/#ascii-punctuation-character\nfunction isTerminatorChar (ch) {\n  switch (ch) {\n    case 0x0A/* \\n */:\n    case 0x21/* ! */:\n    case 0x23/* # */:\n    case 0x24/* $ */:\n    case 0x25/* % */:\n    case 0x26/* & */:\n    case 0x2A/* * */:\n    case 0x2B/* + */:\n    case 0x2D/* - */:\n    case 0x3A/* : */:\n    case 0x3C/* < */:\n    case 0x3D/* = */:\n    case 0x3E/* > */:\n    case 0x40/* @ */:\n    case 0x5B/* [ */:\n    case 0x5C/* \\ */:\n    case 0x5D/* ] */:\n    case 0x5E/* ^ */:\n    case 0x5F/* _ */:\n    case 0x60/* ` */:\n    case 0x7B/* { */:\n    case 0x7D/* } */:\n    case 0x7E/* ~ */:\n      return true\n    default:\n      return false\n  }\n}\n\nexport default function text (state, silent) {\n  let pos = state.pos\n\n  while (pos < state.posMax && !isTerminatorChar(state.src.charCodeAt(pos))) {\n    pos++\n  }\n\n  if (pos === state.pos) { return false }\n\n  if (!silent) { state.pending += state.src.slice(state.pos, pos) }\n\n  state.pos = pos\n\n  return true\n}\n\n// Alternative implementation, for memory.\n//\n// It costs 10% of performance, but allows extend terminators list, if place it\n// to `ParserInline` property. Probably, will switch to it sometime, such\n// flexibility required.\n\n/*\nvar TERMINATOR_RE = /[\\n!#$%&*+\\-:<=>@[\\\\\\]^_`{}~]/;\n\nmodule.exports = function text(state, silent) {\n  var pos = state.pos,\n      idx = state.src.slice(pos).search(TERMINATOR_RE);\n\n  // first char is terminator -> empty text\n  if (idx === 0) { return false; }\n\n  // no terminator -> text till end of string\n  if (idx < 0) {\n    if (!silent) { state.pending += state.src.slice(pos); }\n    state.pos = state.src.length;\n    return true;\n  }\n\n  if (!silent) { state.pending += state.src.slice(pos, pos + idx); }\n\n  state.pos += idx;\n\n  return true;\n}; */\n"], "names": [], "mappings": "AAAA,qEAAqE;AACrE,4BAA4B;AAE5B,yBAAyB;AACzB,sCAAsC;AAEtC,oGAAoG;AAEpG,6DAA6D;AAC7D,+DAA+D;;;;AAC/D,SAAS,iBAAkB,EAAE;IAC3B,OAAQ;QACN,KAAK,KAAI,MAAM;QACf,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;QACd,KAAK,KAAI,KAAK;YACZ,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEe,SAAS,KAAM,KAAK,EAAE,MAAM;IACzC,IAAI,MAAM,MAAM,GAAG;IAEnB,MAAO,MAAM,MAAM,MAAM,IAAI,CAAC,iBAAiB,MAAM,GAAG,CAAC,UAAU,CAAC,MAAO;QACzE;IACF;IAEA,IAAI,QAAQ,MAAM,GAAG,EAAE;QAAE,OAAO;IAAM;IAEtC,IAAI,CAAC,QAAQ;QAAE,MAAM,OAAO,IAAI,MAAM,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;IAAK;IAEhE,MAAM,GAAG,GAAG;IAEZ,OAAO;AACT,EAEA,0CAA0C;CAC1C,EAAE;CACF,+EAA+E;CAC/E,yEAAyE;CACzE,wBAAwB;CAExB;;;;;;;;;;;;;;;;;;;;;;GAsBG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4127, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_inline/linkify.mjs"], "sourcesContent": ["// Process links like https://example.org/\n\n// RFC3986: scheme = ALPHA *( ALPHA / DIGIT / \"+\" / \"-\" / \".\" )\nconst SCHEME_RE = /(?:^|[^a-z0-9.+-])([a-z][a-z0-9.+-]*)$/i\n\nexport default function linkify (state, silent) {\n  if (!state.md.options.linkify) return false\n  if (state.linkLevel > 0) return false\n\n  const pos = state.pos\n  const max = state.posMax\n\n  if (pos + 3 > max) return false\n  if (state.src.charCodeAt(pos) !== 0x3A/* : */) return false\n  if (state.src.charCodeAt(pos + 1) !== 0x2F/* / */) return false\n  if (state.src.charCodeAt(pos + 2) !== 0x2F/* / */) return false\n\n  const match = state.pending.match(SCHEME_RE)\n  if (!match) return false\n\n  const proto = match[1]\n\n  const link = state.md.linkify.matchAtStart(state.src.slice(pos - proto.length))\n  if (!link) return false\n\n  let url = link.url\n\n  // invalid link, but still detected by linkify somehow;\n  // need to check to prevent infinite loop below\n  if (url.length <= proto.length) return false\n\n  // disallow '*' at the end of the link (conflicts with emphasis)\n  url = url.replace(/\\*+$/, '')\n\n  const fullUrl = state.md.normalizeLink(url)\n  if (!state.md.validateLink(fullUrl)) return false\n\n  if (!silent) {\n    state.pending = state.pending.slice(0, -proto.length)\n\n    const token_o = state.push('link_open', 'a', 1)\n    token_o.attrs = [['href', fullUrl]]\n    token_o.markup = 'linkify'\n    token_o.info = 'auto'\n\n    const token_t = state.push('text', '', 0)\n    token_t.content = state.md.normalizeLinkText(url)\n\n    const token_c = state.push('link_close', 'a', -1)\n    token_c.markup = 'linkify'\n    token_c.info = 'auto'\n  }\n\n  state.pos += url.length - proto.length\n  return true\n}\n"], "names": [], "mappings": "AAAA,0CAA0C;AAE1C,+DAA+D;;;;AAC/D,MAAM,YAAY;AAEH,SAAS,QAAS,KAAK,EAAE,MAAM;IAC5C,IAAI,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO;IACtC,IAAI,MAAM,SAAS,GAAG,GAAG,OAAO;IAEhC,MAAM,MAAM,MAAM,GAAG;IACrB,MAAM,MAAM,MAAM,MAAM;IAExB,IAAI,MAAM,IAAI,KAAK,OAAO;IAC1B,IAAI,MAAM,GAAG,CAAC,UAAU,CAAC,SAAS,KAAI,KAAK,KAAI,OAAO;IACtD,IAAI,MAAM,GAAG,CAAC,UAAU,CAAC,MAAM,OAAO,KAAI,KAAK,KAAI,OAAO;IAC1D,IAAI,MAAM,GAAG,CAAC,UAAU,CAAC,MAAM,OAAO,KAAI,KAAK,KAAI,OAAO;IAE1D,MAAM,QAAQ,MAAM,OAAO,CAAC,KAAK,CAAC;IAClC,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,QAAQ,KAAK,CAAC,EAAE;IAEtB,MAAM,OAAO,MAAM,EAAE,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,MAAM,MAAM,MAAM;IAC7E,IAAI,CAAC,MAAM,OAAO;IAElB,IAAI,MAAM,KAAK,GAAG;IAElB,uDAAuD;IACvD,+CAA+C;IAC/C,IAAI,IAAI,MAAM,IAAI,MAAM,MAAM,EAAE,OAAO;IAEvC,gEAAgE;IAChE,MAAM,IAAI,OAAO,CAAC,QAAQ;IAE1B,MAAM,UAAU,MAAM,EAAE,CAAC,aAAa,CAAC;IACvC,IAAI,CAAC,MAAM,EAAE,CAAC,YAAY,CAAC,UAAU,OAAO;IAE5C,IAAI,CAAC,QAAQ;QACX,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,MAAM;QAEpD,MAAM,UAAU,MAAM,IAAI,CAAC,aAAa,KAAK;QAC7C,QAAQ,KAAK,GAAG;YAAC;gBAAC;gBAAQ;aAAQ;SAAC;QACnC,QAAQ,MAAM,GAAG;QACjB,QAAQ,IAAI,GAAG;QAEf,MAAM,UAAU,MAAM,IAAI,CAAC,QAAQ,IAAI;QACvC,QAAQ,OAAO,GAAG,MAAM,EAAE,CAAC,iBAAiB,CAAC;QAE7C,MAAM,UAAU,MAAM,IAAI,CAAC,cAAc,KAAK,CAAC;QAC/C,QAAQ,MAAM,GAAG;QACjB,QAAQ,IAAI,GAAG;IACjB;IAEA,MAAM,GAAG,IAAI,IAAI,MAAM,GAAG,MAAM,MAAM;IACtC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4181, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_inline/newline.mjs"], "sourcesContent": ["// Proceess '\\n'\n\nimport { isSpace } from '../common/utils.mjs'\n\nexport default function newline (state, silent) {\n  let pos = state.pos\n\n  if (state.src.charCodeAt(pos) !== 0x0A/* \\n */) { return false }\n\n  const pmax = state.pending.length - 1\n  const max = state.posMax\n\n  // '  \\n' -> hardbreak\n  // Lookup in pending chars is bad practice! Don't copy to other rules!\n  // Pending string is stored in concat mode, indexed lookups will cause\n  // convertion to flat mode.\n  if (!silent) {\n    if (pmax >= 0 && state.pending.charCodeAt(pmax) === 0x20) {\n      if (pmax >= 1 && state.pending.charCodeAt(pmax - 1) === 0x20) {\n        // Find whitespaces tail of pending chars.\n        let ws = pmax - 1\n        while (ws >= 1 && state.pending.charCodeAt(ws - 1) === 0x20) ws--\n\n        state.pending = state.pending.slice(0, ws)\n        state.push('hardbreak', 'br', 0)\n      } else {\n        state.pending = state.pending.slice(0, -1)\n        state.push('softbreak', 'br', 0)\n      }\n    } else {\n      state.push('softbreak', 'br', 0)\n    }\n  }\n\n  pos++\n\n  // skip heading spaces for next line\n  while (pos < max && isSpace(state.src.charCodeAt(pos))) { pos++ }\n\n  state.pos = pos\n  return true\n}\n"], "names": [], "mappings": "AAAA,gBAAgB;;;;AAEhB;;AAEe,SAAS,QAAS,KAAK,EAAE,MAAM;IAC5C,IAAI,MAAM,MAAM,GAAG;IAEnB,IAAI,MAAM,GAAG,CAAC,UAAU,CAAC,SAAS,KAAI,MAAM,KAAI;QAAE,OAAO;IAAM;IAE/D,MAAM,OAAO,MAAM,OAAO,CAAC,MAAM,GAAG;IACpC,MAAM,MAAM,MAAM,MAAM;IAExB,sBAAsB;IACtB,sEAAsE;IACtE,sEAAsE;IACtE,2BAA2B;IAC3B,IAAI,CAAC,QAAQ;QACX,IAAI,QAAQ,KAAK,MAAM,OAAO,CAAC,UAAU,CAAC,UAAU,MAAM;YACxD,IAAI,QAAQ,KAAK,MAAM,OAAO,CAAC,UAAU,CAAC,OAAO,OAAO,MAAM;gBAC5D,0CAA0C;gBAC1C,IAAI,KAAK,OAAO;gBAChB,MAAO,MAAM,KAAK,MAAM,OAAO,CAAC,UAAU,CAAC,KAAK,OAAO,KAAM;gBAE7D,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG;gBACvC,MAAM,IAAI,CAAC,aAAa,MAAM;YAChC,OAAO;gBACL,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC;gBACxC,MAAM,IAAI,CAAC,aAAa,MAAM;YAChC;QACF,OAAO;YACL,MAAM,IAAI,CAAC,aAAa,MAAM;QAChC;IACF;IAEA;IAEA,oCAAoC;IACpC,MAAO,MAAM,OAAO,CAAA,GAAA,wNAAA,CAAA,UAAO,AAAD,EAAE,MAAM,GAAG,CAAC,UAAU,CAAC,MAAO;QAAE;IAAM;IAEhE,MAAM,GAAG,GAAG;IACZ,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4228, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_inline/escape.mjs"], "sourcesContent": ["// Process escaped chars and hardbreaks\n\nimport { isSpace } from '../common/utils.mjs'\n\nconst ESCAPED = []\n\nfor (let i = 0; i < 256; i++) { ESCAPED.push(0) }\n\n'\\\\!\"#$%&\\'()*+,./:;<=>?@[]^_`{|}~-'\n  .split('').forEach(function (ch) { ESCAPED[ch.charCodeAt(0)] = 1 })\n\nexport default function escape (state, silent) {\n  let pos = state.pos\n  const max = state.posMax\n\n  if (state.src.charCodeAt(pos) !== 0x5C/* \\ */) return false\n  pos++\n\n  // '\\' at the end of the inline block\n  if (pos >= max) return false\n\n  let ch1 = state.src.charCodeAt(pos)\n\n  if (ch1 === 0x0A) {\n    if (!silent) {\n      state.push('hardbreak', 'br', 0)\n    }\n\n    pos++\n    // skip leading whitespaces from next line\n    while (pos < max) {\n      ch1 = state.src.charCodeAt(pos)\n      if (!isSpace(ch1)) break\n      pos++\n    }\n\n    state.pos = pos\n    return true\n  }\n\n  let escapedStr = state.src[pos]\n\n  if (ch1 >= 0xD800 && ch1 <= 0xDBFF && pos + 1 < max) {\n    const ch2 = state.src.charCodeAt(pos + 1)\n\n    if (ch2 >= 0xDC00 && ch2 <= 0xDFFF) {\n      escapedStr += state.src[pos + 1]\n      pos++\n    }\n  }\n\n  const origStr = '\\\\' + escapedStr\n\n  if (!silent) {\n    const token = state.push('text_special', '', 0)\n\n    if (ch1 < 256 && ESCAPED[ch1] !== 0) {\n      token.content = escapedStr\n    } else {\n      token.content = origStr\n    }\n\n    token.markup = origStr\n    token.info   = 'escape'\n  }\n\n  state.pos = pos + 1\n  return true\n}\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AAEvC;;AAEA,MAAM,UAAU,EAAE;AAElB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;IAAE,QAAQ,IAAI,CAAC;AAAG;AAEhD,qCACG,KAAK,CAAC,IAAI,OAAO,CAAC,SAAU,EAAE;IAAI,OAAO,CAAC,GAAG,UAAU,CAAC,GAAG,GAAG;AAAE;AAEpD,SAAS,OAAQ,KAAK,EAAE,MAAM;IAC3C,IAAI,MAAM,MAAM,GAAG;IACnB,MAAM,MAAM,MAAM,MAAM;IAExB,IAAI,MAAM,GAAG,CAAC,UAAU,CAAC,SAAS,KAAI,KAAK,KAAI,OAAO;IACtD;IAEA,qCAAqC;IACrC,IAAI,OAAO,KAAK,OAAO;IAEvB,IAAI,MAAM,MAAM,GAAG,CAAC,UAAU,CAAC;IAE/B,IAAI,QAAQ,MAAM;QAChB,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,CAAC,aAAa,MAAM;QAChC;QAEA;QACA,0CAA0C;QAC1C,MAAO,MAAM,IAAK;YAChB,MAAM,MAAM,GAAG,CAAC,UAAU,CAAC;YAC3B,IAAI,CAAC,CAAA,GAAA,wNAAA,CAAA,UAAO,AAAD,EAAE,MAAM;YACnB;QACF;QAEA,MAAM,GAAG,GAAG;QACZ,OAAO;IACT;IAEA,IAAI,aAAa,MAAM,GAAG,CAAC,IAAI;IAE/B,IAAI,OAAO,UAAU,OAAO,UAAU,MAAM,IAAI,KAAK;QACnD,MAAM,MAAM,MAAM,GAAG,CAAC,UAAU,CAAC,MAAM;QAEvC,IAAI,OAAO,UAAU,OAAO,QAAQ;YAClC,cAAc,MAAM,GAAG,CAAC,MAAM,EAAE;YAChC;QACF;IACF;IAEA,MAAM,UAAU,OAAO;IAEvB,IAAI,CAAC,QAAQ;QACX,MAAM,QAAQ,MAAM,IAAI,CAAC,gBAAgB,IAAI;QAE7C,IAAI,MAAM,OAAO,OAAO,CAAC,IAAI,KAAK,GAAG;YACnC,MAAM,OAAO,GAAG;QAClB,OAAO;YACL,MAAM,OAAO,GAAG;QAClB;QAEA,MAAM,MAAM,GAAG;QACf,MAAM,IAAI,GAAK;IACjB;IAEA,MAAM,GAAG,GAAG,MAAM;IAClB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4291, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_inline/backticks.mjs"], "sourcesContent": ["// Parse backticks\n\nexport default function backtick (state, silent) {\n  let pos = state.pos\n  const ch = state.src.charCodeAt(pos)\n\n  if (ch !== 0x60/* ` */) { return false }\n\n  const start = pos\n  pos++\n  const max = state.posMax\n\n  // scan marker length\n  while (pos < max && state.src.charCodeAt(pos) === 0x60/* ` */) { pos++ }\n\n  const marker = state.src.slice(start, pos)\n  const openerLength = marker.length\n\n  if (state.backticksScanned && (state.backticks[openerLength] || 0) <= start) {\n    if (!silent) state.pending += marker\n    state.pos += openerLength\n    return true\n  }\n\n  let matchEnd = pos\n  let matchStart\n\n  // Nothing found in the cache, scan until the end of the line (or until marker is found)\n  while ((matchStart = state.src.indexOf('`', matchEnd)) !== -1) {\n    matchEnd = matchStart + 1\n\n    // scan marker length\n    while (matchEnd < max && state.src.charCodeAt(matchEnd) === 0x60/* ` */) { matchEnd++ }\n\n    const closerLength = matchEnd - matchStart\n\n    if (closerLength === openerLength) {\n      // Found matching closer length.\n      if (!silent) {\n        const token = state.push('code_inline', 'code', 0)\n        token.markup = marker\n        token.content = state.src.slice(pos, matchStart)\n          .replace(/\\n/g, ' ')\n          .replace(/^ (.+) $/, '$1')\n      }\n      state.pos = matchEnd\n      return true\n    }\n\n    // Some different length found, put it in cache as upper limit of where closer can be found\n    state.backticks[closerLength] = matchStart\n  }\n\n  // Scanned through the end, didn't find anything\n  state.backticksScanned = true\n\n  if (!silent) state.pending += marker\n  state.pos += openerLength\n  return true\n}\n"], "names": [], "mappings": "AAAA,kBAAkB;;;;AAEH,SAAS,SAAU,KAAK,EAAE,MAAM;IAC7C,IAAI,MAAM,MAAM,GAAG;IACnB,MAAM,KAAK,MAAM,GAAG,CAAC,UAAU,CAAC;IAEhC,IAAI,OAAO,KAAI,KAAK,KAAI;QAAE,OAAO;IAAM;IAEvC,MAAM,QAAQ;IACd;IACA,MAAM,MAAM,MAAM,MAAM;IAExB,qBAAqB;IACrB,MAAO,MAAM,OAAO,MAAM,GAAG,CAAC,UAAU,CAAC,SAAS,KAAI,KAAK,IAAI;QAAE;IAAM;IAEvE,MAAM,SAAS,MAAM,GAAG,CAAC,KAAK,CAAC,OAAO;IACtC,MAAM,eAAe,OAAO,MAAM;IAElC,IAAI,MAAM,gBAAgB,IAAI,CAAC,MAAM,SAAS,CAAC,aAAa,IAAI,CAAC,KAAK,OAAO;QAC3E,IAAI,CAAC,QAAQ,MAAM,OAAO,IAAI;QAC9B,MAAM,GAAG,IAAI;QACb,OAAO;IACT;IAEA,IAAI,WAAW;IACf,IAAI;IAEJ,wFAAwF;IACxF,MAAO,CAAC,aAAa,MAAM,GAAG,CAAC,OAAO,CAAC,KAAK,SAAS,MAAM,CAAC,EAAG;QAC7D,WAAW,aAAa;QAExB,qBAAqB;QACrB,MAAO,WAAW,OAAO,MAAM,GAAG,CAAC,UAAU,CAAC,cAAc,KAAI,KAAK,IAAI;YAAE;QAAW;QAEtF,MAAM,eAAe,WAAW;QAEhC,IAAI,iBAAiB,cAAc;YACjC,gCAAgC;YAChC,IAAI,CAAC,QAAQ;gBACX,MAAM,QAAQ,MAAM,IAAI,CAAC,eAAe,QAAQ;gBAChD,MAAM,MAAM,GAAG;gBACf,MAAM,OAAO,GAAG,MAAM,GAAG,CAAC,KAAK,CAAC,KAAK,YAClC,OAAO,CAAC,OAAO,KACf,OAAO,CAAC,YAAY;YACzB;YACA,MAAM,GAAG,GAAG;YACZ,OAAO;QACT;QAEA,2FAA2F;QAC3F,MAAM,SAAS,CAAC,aAAa,GAAG;IAClC;IAEA,gDAAgD;IAChD,MAAM,gBAAgB,GAAG;IAEzB,IAAI,CAAC,QAAQ,MAAM,OAAO,IAAI;IAC9B,MAAM,GAAG,IAAI;IACb,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4350, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_inline/strikethrough.mjs"], "sourcesContent": ["// ~~strike through~~\n//\n\n// Insert each marker as a separate text token, and add it to delimiter list\n//\nfunction strikethrough_tokenize (state, silent) {\n  const start = state.pos\n  const marker = state.src.charCodeAt(start)\n\n  if (silent) { return false }\n\n  if (marker !== 0x7E/* ~ */) { return false }\n\n  const scanned = state.scanDelims(state.pos, true)\n  let len = scanned.length\n  const ch = String.fromCharCode(marker)\n\n  if (len < 2) { return false }\n\n  let token\n\n  if (len % 2) {\n    token         = state.push('text', '', 0)\n    token.content = ch\n    len--\n  }\n\n  for (let i = 0; i < len; i += 2) {\n    token         = state.push('text', '', 0)\n    token.content = ch + ch\n\n    state.delimiters.push({\n      marker,\n      length: 0,     // disable \"rule of 3\" length checks meant for emphasis\n      token: state.tokens.length - 1,\n      end: -1,\n      open: scanned.can_open,\n      close: scanned.can_close\n    })\n  }\n\n  state.pos += scanned.length\n\n  return true\n}\n\nfunction postProcess (state, delimiters) {\n  let token\n  const loneMarkers = []\n  const max = delimiters.length\n\n  for (let i = 0; i < max; i++) {\n    const startDelim = delimiters[i]\n\n    if (startDelim.marker !== 0x7E/* ~ */) {\n      continue\n    }\n\n    if (startDelim.end === -1) {\n      continue\n    }\n\n    const endDelim = delimiters[startDelim.end]\n\n    token         = state.tokens[startDelim.token]\n    token.type    = 's_open'\n    token.tag     = 's'\n    token.nesting = 1\n    token.markup  = '~~'\n    token.content = ''\n\n    token         = state.tokens[endDelim.token]\n    token.type    = 's_close'\n    token.tag     = 's'\n    token.nesting = -1\n    token.markup  = '~~'\n    token.content = ''\n\n    if (state.tokens[endDelim.token - 1].type === 'text' &&\n        state.tokens[endDelim.token - 1].content === '~') {\n      loneMarkers.push(endDelim.token - 1)\n    }\n  }\n\n  // If a marker sequence has an odd number of characters, it's splitted\n  // like this: `~~~~~` -> `~` + `~~` + `~~`, leaving one marker at the\n  // start of the sequence.\n  //\n  // So, we have to move all those markers after subsequent s_close tags.\n  //\n  while (loneMarkers.length) {\n    const i = loneMarkers.pop()\n    let j = i + 1\n\n    while (j < state.tokens.length && state.tokens[j].type === 's_close') {\n      j++\n    }\n\n    j--\n\n    if (i !== j) {\n      token = state.tokens[j]\n      state.tokens[j] = state.tokens[i]\n      state.tokens[i] = token\n    }\n  }\n}\n\n// Walk through delimiter list and replace text tokens with tags\n//\nfunction strikethrough_postProcess (state) {\n  const tokens_meta = state.tokens_meta\n  const max = state.tokens_meta.length\n\n  postProcess(state, state.delimiters)\n\n  for (let curr = 0; curr < max; curr++) {\n    if (tokens_meta[curr] && tokens_meta[curr].delimiters) {\n      postProcess(state, tokens_meta[curr].delimiters)\n    }\n  }\n}\n\nexport default {\n  tokenize: strikethrough_tokenize,\n  postProcess: strikethrough_postProcess\n}\n"], "names": [], "mappings": "AAAA,qBAAqB;AACrB,EAAE;AAEF,4EAA4E;AAC5E,EAAE;;;;AACF,SAAS,uBAAwB,KAAK,EAAE,MAAM;IAC5C,MAAM,QAAQ,MAAM,GAAG;IACvB,MAAM,SAAS,MAAM,GAAG,CAAC,UAAU,CAAC;IAEpC,IAAI,QAAQ;QAAE,OAAO;IAAM;IAE3B,IAAI,WAAW,KAAI,KAAK,KAAI;QAAE,OAAO;IAAM;IAE3C,MAAM,UAAU,MAAM,UAAU,CAAC,MAAM,GAAG,EAAE;IAC5C,IAAI,MAAM,QAAQ,MAAM;IACxB,MAAM,KAAK,OAAO,YAAY,CAAC;IAE/B,IAAI,MAAM,GAAG;QAAE,OAAO;IAAM;IAE5B,IAAI;IAEJ,IAAI,MAAM,GAAG;QACX,QAAgB,MAAM,IAAI,CAAC,QAAQ,IAAI;QACvC,MAAM,OAAO,GAAG;QAChB;IACF;IAEA,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,EAAG;QAC/B,QAAgB,MAAM,IAAI,CAAC,QAAQ,IAAI;QACvC,MAAM,OAAO,GAAG,KAAK;QAErB,MAAM,UAAU,CAAC,IAAI,CAAC;YACpB;YACA,QAAQ;YACR,OAAO,MAAM,MAAM,CAAC,MAAM,GAAG;YAC7B,KAAK,CAAC;YACN,MAAM,QAAQ,QAAQ;YACtB,OAAO,QAAQ,SAAS;QAC1B;IACF;IAEA,MAAM,GAAG,IAAI,QAAQ,MAAM;IAE3B,OAAO;AACT;AAEA,SAAS,YAAa,KAAK,EAAE,UAAU;IACrC,IAAI;IACJ,MAAM,cAAc,EAAE;IACtB,MAAM,MAAM,WAAW,MAAM;IAE7B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;QAC5B,MAAM,aAAa,UAAU,CAAC,EAAE;QAEhC,IAAI,WAAW,MAAM,KAAK,KAAI,KAAK,KAAI;YACrC;QACF;QAEA,IAAI,WAAW,GAAG,KAAK,CAAC,GAAG;YACzB;QACF;QAEA,MAAM,WAAW,UAAU,CAAC,WAAW,GAAG,CAAC;QAE3C,QAAgB,MAAM,MAAM,CAAC,WAAW,KAAK,CAAC;QAC9C,MAAM,IAAI,GAAM;QAChB,MAAM,GAAG,GAAO;QAChB,MAAM,OAAO,GAAG;QAChB,MAAM,MAAM,GAAI;QAChB,MAAM,OAAO,GAAG;QAEhB,QAAgB,MAAM,MAAM,CAAC,SAAS,KAAK,CAAC;QAC5C,MAAM,IAAI,GAAM;QAChB,MAAM,GAAG,GAAO;QAChB,MAAM,OAAO,GAAG,CAAC;QACjB,MAAM,MAAM,GAAI;QAChB,MAAM,OAAO,GAAG;QAEhB,IAAI,MAAM,MAAM,CAAC,SAAS,KAAK,GAAG,EAAE,CAAC,IAAI,KAAK,UAC1C,MAAM,MAAM,CAAC,SAAS,KAAK,GAAG,EAAE,CAAC,OAAO,KAAK,KAAK;YACpD,YAAY,IAAI,CAAC,SAAS,KAAK,GAAG;QACpC;IACF;IAEA,sEAAsE;IACtE,qEAAqE;IACrE,yBAAyB;IACzB,EAAE;IACF,uEAAuE;IACvE,EAAE;IACF,MAAO,YAAY,MAAM,CAAE;QACzB,MAAM,IAAI,YAAY,GAAG;QACzB,IAAI,IAAI,IAAI;QAEZ,MAAO,IAAI,MAAM,MAAM,CAAC,MAAM,IAAI,MAAM,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,UAAW;YACpE;QACF;QAEA;QAEA,IAAI,MAAM,GAAG;YACX,QAAQ,MAAM,MAAM,CAAC,EAAE;YACvB,MAAM,MAAM,CAAC,EAAE,GAAG,MAAM,MAAM,CAAC,EAAE;YACjC,MAAM,MAAM,CAAC,EAAE,GAAG;QACpB;IACF;AACF;AAEA,gEAAgE;AAChE,EAAE;AACF,SAAS,0BAA2B,KAAK;IACvC,MAAM,cAAc,MAAM,WAAW;IACrC,MAAM,MAAM,MAAM,WAAW,CAAC,MAAM;IAEpC,YAAY,OAAO,MAAM,UAAU;IAEnC,IAAK,IAAI,OAAO,GAAG,OAAO,KAAK,OAAQ;QACrC,IAAI,WAAW,CAAC,KAAK,IAAI,WAAW,CAAC,KAAK,CAAC,UAAU,EAAE;YACrD,YAAY,OAAO,WAAW,CAAC,KAAK,CAAC,UAAU;QACjD;IACF;AACF;uCAEe;IACb,UAAU;IACV,aAAa;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4464, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_inline/emphasis.mjs"], "sourcesContent": ["// Process *this* and _that_\n//\n\n// Insert each marker as a separate text token, and add it to delimiter list\n//\nfunction emphasis_tokenize (state, silent) {\n  const start = state.pos\n  const marker = state.src.charCodeAt(start)\n\n  if (silent) { return false }\n\n  if (marker !== 0x5F /* _ */ && marker !== 0x2A /* * */) { return false }\n\n  const scanned = state.scanDelims(state.pos, marker === 0x2A)\n\n  for (let i = 0; i < scanned.length; i++) {\n    const token = state.push('text', '', 0)\n    token.content = String.fromCharCode(marker)\n\n    state.delimiters.push({\n      // Char code of the starting marker (number).\n      //\n      marker,\n\n      // Total length of these series of delimiters.\n      //\n      length: scanned.length,\n\n      // A position of the token this delimiter corresponds to.\n      //\n      token: state.tokens.length - 1,\n\n      // If this delimiter is matched as a valid opener, `end` will be\n      // equal to its position, otherwise it's `-1`.\n      //\n      end: -1,\n\n      // Boolean flags that determine if this delimiter could open or close\n      // an emphasis.\n      //\n      open: scanned.can_open,\n      close: scanned.can_close\n    })\n  }\n\n  state.pos += scanned.length\n\n  return true\n}\n\nfunction postProcess (state, delimiters) {\n  const max = delimiters.length\n\n  for (let i = max - 1; i >= 0; i--) {\n    const startDelim = delimiters[i]\n\n    if (startDelim.marker !== 0x5F/* _ */ && startDelim.marker !== 0x2A/* * */) {\n      continue\n    }\n\n    // Process only opening markers\n    if (startDelim.end === -1) {\n      continue\n    }\n\n    const endDelim = delimiters[startDelim.end]\n\n    // If the previous delimiter has the same marker and is adjacent to this one,\n    // merge those into one strong delimiter.\n    //\n    // `<em><em>whatever</em></em>` -> `<strong>whatever</strong>`\n    //\n    const isStrong = i > 0 &&\n               delimiters[i - 1].end === startDelim.end + 1 &&\n               // check that first two markers match and adjacent\n               delimiters[i - 1].marker === startDelim.marker &&\n               delimiters[i - 1].token === startDelim.token - 1 &&\n               // check that last two markers are adjacent (we can safely assume they match)\n               delimiters[startDelim.end + 1].token === endDelim.token + 1\n\n    const ch = String.fromCharCode(startDelim.marker)\n\n    const token_o   = state.tokens[startDelim.token]\n    token_o.type    = isStrong ? 'strong_open' : 'em_open'\n    token_o.tag     = isStrong ? 'strong' : 'em'\n    token_o.nesting = 1\n    token_o.markup  = isStrong ? ch + ch : ch\n    token_o.content = ''\n\n    const token_c   = state.tokens[endDelim.token]\n    token_c.type    = isStrong ? 'strong_close' : 'em_close'\n    token_c.tag     = isStrong ? 'strong' : 'em'\n    token_c.nesting = -1\n    token_c.markup  = isStrong ? ch + ch : ch\n    token_c.content = ''\n\n    if (isStrong) {\n      state.tokens[delimiters[i - 1].token].content = ''\n      state.tokens[delimiters[startDelim.end + 1].token].content = ''\n      i--\n    }\n  }\n}\n\n// Walk through delimiter list and replace text tokens with tags\n//\nfunction emphasis_post_process (state) {\n  const tokens_meta = state.tokens_meta\n  const max = state.tokens_meta.length\n\n  postProcess(state, state.delimiters)\n\n  for (let curr = 0; curr < max; curr++) {\n    if (tokens_meta[curr] && tokens_meta[curr].delimiters) {\n      postProcess(state, tokens_meta[curr].delimiters)\n    }\n  }\n}\n\nexport default {\n  tokenize: emphasis_tokenize,\n  postProcess: emphasis_post_process\n}\n"], "names": [], "mappings": "AAAA,4BAA4B;AAC5B,EAAE;AAEF,4EAA4E;AAC5E,EAAE;;;;AACF,SAAS,kBAAmB,KAAK,EAAE,MAAM;IACvC,MAAM,QAAQ,MAAM,GAAG;IACvB,MAAM,SAAS,MAAM,GAAG,CAAC,UAAU,CAAC;IAEpC,IAAI,QAAQ;QAAE,OAAO;IAAM;IAE3B,IAAI,WAAW,KAAK,KAAK,OAAM,WAAW,KAAK,KAAK,KAAI;QAAE,OAAO;IAAM;IAEvE,MAAM,UAAU,MAAM,UAAU,CAAC,MAAM,GAAG,EAAE,WAAW;IAEvD,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QACvC,MAAM,QAAQ,MAAM,IAAI,CAAC,QAAQ,IAAI;QACrC,MAAM,OAAO,GAAG,OAAO,YAAY,CAAC;QAEpC,MAAM,UAAU,CAAC,IAAI,CAAC;YACpB,6CAA6C;YAC7C,EAAE;YACF;YAEA,8CAA8C;YAC9C,EAAE;YACF,QAAQ,QAAQ,MAAM;YAEtB,yDAAyD;YACzD,EAAE;YACF,OAAO,MAAM,MAAM,CAAC,MAAM,GAAG;YAE7B,gEAAgE;YAChE,8CAA8C;YAC9C,EAAE;YACF,KAAK,CAAC;YAEN,qEAAqE;YACrE,eAAe;YACf,EAAE;YACF,MAAM,QAAQ,QAAQ;YACtB,OAAO,QAAQ,SAAS;QAC1B;IACF;IAEA,MAAM,GAAG,IAAI,QAAQ,MAAM;IAE3B,OAAO;AACT;AAEA,SAAS,YAAa,KAAK,EAAE,UAAU;IACrC,MAAM,MAAM,WAAW,MAAM;IAE7B,IAAK,IAAI,IAAI,MAAM,GAAG,KAAK,GAAG,IAAK;QACjC,MAAM,aAAa,UAAU,CAAC,EAAE;QAEhC,IAAI,WAAW,MAAM,KAAK,KAAI,KAAK,OAAM,WAAW,MAAM,KAAK,KAAI,KAAK,KAAI;YAC1E;QACF;QAEA,+BAA+B;QAC/B,IAAI,WAAW,GAAG,KAAK,CAAC,GAAG;YACzB;QACF;QAEA,MAAM,WAAW,UAAU,CAAC,WAAW,GAAG,CAAC;QAE3C,6EAA6E;QAC7E,yCAAyC;QACzC,EAAE;QACF,8DAA8D;QAC9D,EAAE;QACF,MAAM,WAAW,IAAI,KACV,UAAU,CAAC,IAAI,EAAE,CAAC,GAAG,KAAK,WAAW,GAAG,GAAG,KAC3C,kDAAkD;QAClD,UAAU,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,WAAW,MAAM,IAC9C,UAAU,CAAC,IAAI,EAAE,CAAC,KAAK,KAAK,WAAW,KAAK,GAAG,KAC/C,6EAA6E;QAC7E,UAAU,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC,KAAK,KAAK,SAAS,KAAK,GAAG;QAErE,MAAM,KAAK,OAAO,YAAY,CAAC,WAAW,MAAM;QAEhD,MAAM,UAAY,MAAM,MAAM,CAAC,WAAW,KAAK,CAAC;QAChD,QAAQ,IAAI,GAAM,WAAW,gBAAgB;QAC7C,QAAQ,GAAG,GAAO,WAAW,WAAW;QACxC,QAAQ,OAAO,GAAG;QAClB,QAAQ,MAAM,GAAI,WAAW,KAAK,KAAK;QACvC,QAAQ,OAAO,GAAG;QAElB,MAAM,UAAY,MAAM,MAAM,CAAC,SAAS,KAAK,CAAC;QAC9C,QAAQ,IAAI,GAAM,WAAW,iBAAiB;QAC9C,QAAQ,GAAG,GAAO,WAAW,WAAW;QACxC,QAAQ,OAAO,GAAG,CAAC;QACnB,QAAQ,MAAM,GAAI,WAAW,KAAK,KAAK;QACvC,QAAQ,OAAO,GAAG;QAElB,IAAI,UAAU;YACZ,MAAM,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,OAAO,GAAG;YAChD,MAAM,MAAM,CAAC,UAAU,CAAC,WAAW,GAAG,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,OAAO,GAAG;YAC7D;QACF;IACF;AACF;AAEA,gEAAgE;AAChE,EAAE;AACF,SAAS,sBAAuB,KAAK;IACnC,MAAM,cAAc,MAAM,WAAW;IACrC,MAAM,MAAM,MAAM,WAAW,CAAC,MAAM;IAEpC,YAAY,OAAO,MAAM,UAAU;IAEnC,IAAK,IAAI,OAAO,GAAG,OAAO,KAAK,OAAQ;QACrC,IAAI,WAAW,CAAC,KAAK,IAAI,WAAW,CAAC,KAAK,CAAC,UAAU,EAAE;YACrD,YAAY,OAAO,WAAW,CAAC,KAAK,CAAC,UAAU;QACjD;IACF;AACF;uCAEe;IACb,UAAU;IACV,aAAa;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4570, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_inline/link.mjs"], "sourcesContent": ["// Process [link](<to> \"stuff\")\n\nimport { normalizeReference, isSpace } from '../common/utils.mjs'\n\nexport default function link (state, silent) {\n  let code, label, res, ref\n  let href = ''\n  let title = ''\n  let start = state.pos\n  let parseReference = true\n\n  if (state.src.charCodeAt(state.pos) !== 0x5B/* [ */) { return false }\n\n  const oldPos = state.pos\n  const max = state.posMax\n  const labelStart = state.pos + 1\n  const labelEnd = state.md.helpers.parseLinkLabel(state, state.pos, true)\n\n  // parser failed to find ']', so it's not a valid link\n  if (labelEnd < 0) { return false }\n\n  let pos = labelEnd + 1\n  if (pos < max && state.src.charCodeAt(pos) === 0x28/* ( */) {\n    //\n    // Inline link\n    //\n\n    // might have found a valid shortcut link, disable reference parsing\n    parseReference = false\n\n    // [link](  <href>  \"title\"  )\n    //        ^^ skipping these spaces\n    pos++\n    for (; pos < max; pos++) {\n      code = state.src.charCodeAt(pos)\n      if (!isSpace(code) && code !== 0x0A) { break }\n    }\n    if (pos >= max) { return false }\n\n    // [link](  <href>  \"title\"  )\n    //          ^^^^^^ parsing link destination\n    start = pos\n    res = state.md.helpers.parseLinkDestination(state.src, pos, state.posMax)\n    if (res.ok) {\n      href = state.md.normalizeLink(res.str)\n      if (state.md.validateLink(href)) {\n        pos = res.pos\n      } else {\n        href = ''\n      }\n\n      // [link](  <href>  \"title\"  )\n      //                ^^ skipping these spaces\n      start = pos\n      for (; pos < max; pos++) {\n        code = state.src.charCodeAt(pos)\n        if (!isSpace(code) && code !== 0x0A) { break }\n      }\n\n      // [link](  <href>  \"title\"  )\n      //                  ^^^^^^^ parsing link title\n      res = state.md.helpers.parseLinkTitle(state.src, pos, state.posMax)\n      if (pos < max && start !== pos && res.ok) {\n        title = res.str\n        pos = res.pos\n\n        // [link](  <href>  \"title\"  )\n        //                         ^^ skipping these spaces\n        for (; pos < max; pos++) {\n          code = state.src.charCodeAt(pos)\n          if (!isSpace(code) && code !== 0x0A) { break }\n        }\n      }\n    }\n\n    if (pos >= max || state.src.charCodeAt(pos) !== 0x29/* ) */) {\n      // parsing a valid shortcut link failed, fallback to reference\n      parseReference = true\n    }\n    pos++\n  }\n\n  if (parseReference) {\n    //\n    // Link reference\n    //\n    if (typeof state.env.references === 'undefined') { return false }\n\n    if (pos < max && state.src.charCodeAt(pos) === 0x5B/* [ */) {\n      start = pos + 1\n      pos = state.md.helpers.parseLinkLabel(state, pos)\n      if (pos >= 0) {\n        label = state.src.slice(start, pos++)\n      } else {\n        pos = labelEnd + 1\n      }\n    } else {\n      pos = labelEnd + 1\n    }\n\n    // covers label === '' and label === undefined\n    // (collapsed reference link and shortcut reference link respectively)\n    if (!label) { label = state.src.slice(labelStart, labelEnd) }\n\n    ref = state.env.references[normalizeReference(label)]\n    if (!ref) {\n      state.pos = oldPos\n      return false\n    }\n    href = ref.href\n    title = ref.title\n  }\n\n  //\n  // We found the end of the link, and know for a fact it's a valid link;\n  // so all that's left to do is to call tokenizer.\n  //\n  if (!silent) {\n    state.pos = labelStart\n    state.posMax = labelEnd\n\n    const token_o = state.push('link_open', 'a', 1)\n    const attrs = [['href', href]]\n    token_o.attrs  = attrs\n    if (title) {\n      attrs.push(['title', title])\n    }\n\n    state.linkLevel++\n    state.md.inline.tokenize(state)\n    state.linkLevel--\n\n    state.push('link_close', 'a', -1)\n  }\n\n  state.pos = pos\n  state.posMax = max\n  return true\n}\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;AAE/B;;AAEe,SAAS,KAAM,KAAK,EAAE,MAAM;IACzC,IAAI,MAAM,OAAO,KAAK;IACtB,IAAI,OAAO;IACX,IAAI,QAAQ;IACZ,IAAI,QAAQ,MAAM,GAAG;IACrB,IAAI,iBAAiB;IAErB,IAAI,MAAM,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,MAAM,KAAI,KAAK,KAAI;QAAE,OAAO;IAAM;IAEpE,MAAM,SAAS,MAAM,GAAG;IACxB,MAAM,MAAM,MAAM,MAAM;IACxB,MAAM,aAAa,MAAM,GAAG,GAAG;IAC/B,MAAM,WAAW,MAAM,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,MAAM,GAAG,EAAE;IAEnE,sDAAsD;IACtD,IAAI,WAAW,GAAG;QAAE,OAAO;IAAM;IAEjC,IAAI,MAAM,WAAW;IACrB,IAAI,MAAM,OAAO,MAAM,GAAG,CAAC,UAAU,CAAC,SAAS,KAAI,KAAK,KAAI;QAC1D,EAAE;QACF,cAAc;QACd,EAAE;QAEF,oEAAoE;QACpE,iBAAiB;QAEjB,8BAA8B;QAC9B,kCAAkC;QAClC;QACA,MAAO,MAAM,KAAK,MAAO;YACvB,OAAO,MAAM,GAAG,CAAC,UAAU,CAAC;YAC5B,IAAI,CAAC,CAAA,GAAA,wNAAA,CAAA,UAAO,AAAD,EAAE,SAAS,SAAS,MAAM;gBAAE;YAAM;QAC/C;QACA,IAAI,OAAO,KAAK;YAAE,OAAO;QAAM;QAE/B,8BAA8B;QAC9B,2CAA2C;QAC3C,QAAQ;QACR,MAAM,MAAM,EAAE,CAAC,OAAO,CAAC,oBAAoB,CAAC,MAAM,GAAG,EAAE,KAAK,MAAM,MAAM;QACxE,IAAI,IAAI,EAAE,EAAE;YACV,OAAO,MAAM,EAAE,CAAC,aAAa,CAAC,IAAI,GAAG;YACrC,IAAI,MAAM,EAAE,CAAC,YAAY,CAAC,OAAO;gBAC/B,MAAM,IAAI,GAAG;YACf,OAAO;gBACL,OAAO;YACT;YAEA,8BAA8B;YAC9B,0CAA0C;YAC1C,QAAQ;YACR,MAAO,MAAM,KAAK,MAAO;gBACvB,OAAO,MAAM,GAAG,CAAC,UAAU,CAAC;gBAC5B,IAAI,CAAC,CAAA,GAAA,wNAAA,CAAA,UAAO,AAAD,EAAE,SAAS,SAAS,MAAM;oBAAE;gBAAM;YAC/C;YAEA,8BAA8B;YAC9B,8CAA8C;YAC9C,MAAM,MAAM,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,GAAG,EAAE,KAAK,MAAM,MAAM;YAClE,IAAI,MAAM,OAAO,UAAU,OAAO,IAAI,EAAE,EAAE;gBACxC,QAAQ,IAAI,GAAG;gBACf,MAAM,IAAI,GAAG;gBAEb,8BAA8B;gBAC9B,mDAAmD;gBACnD,MAAO,MAAM,KAAK,MAAO;oBACvB,OAAO,MAAM,GAAG,CAAC,UAAU,CAAC;oBAC5B,IAAI,CAAC,CAAA,GAAA,wNAAA,CAAA,UAAO,AAAD,EAAE,SAAS,SAAS,MAAM;wBAAE;oBAAM;gBAC/C;YACF;QACF;QAEA,IAAI,OAAO,OAAO,MAAM,GAAG,CAAC,UAAU,CAAC,SAAS,KAAI,KAAK,KAAI;YAC3D,8DAA8D;YAC9D,iBAAiB;QACnB;QACA;IACF;IAEA,IAAI,gBAAgB;QAClB,EAAE;QACF,iBAAiB;QACjB,EAAE;QACF,IAAI,OAAO,MAAM,GAAG,CAAC,UAAU,KAAK,aAAa;YAAE,OAAO;QAAM;QAEhE,IAAI,MAAM,OAAO,MAAM,GAAG,CAAC,UAAU,CAAC,SAAS,KAAI,KAAK,KAAI;YAC1D,QAAQ,MAAM;YACd,MAAM,MAAM,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;YAC7C,IAAI,OAAO,GAAG;gBACZ,QAAQ,MAAM,GAAG,CAAC,KAAK,CAAC,OAAO;YACjC,OAAO;gBACL,MAAM,WAAW;YACnB;QACF,OAAO;YACL,MAAM,WAAW;QACnB;QAEA,8CAA8C;QAC9C,sEAAsE;QACtE,IAAI,CAAC,OAAO;YAAE,QAAQ,MAAM,GAAG,CAAC,KAAK,CAAC,YAAY;QAAU;QAE5D,MAAM,MAAM,GAAG,CAAC,UAAU,CAAC,CAAA,GAAA,wNAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;QACrD,IAAI,CAAC,KAAK;YACR,MAAM,GAAG,GAAG;YACZ,OAAO;QACT;QACA,OAAO,IAAI,IAAI;QACf,QAAQ,IAAI,KAAK;IACnB;IAEA,EAAE;IACF,uEAAuE;IACvE,iDAAiD;IACjD,EAAE;IACF,IAAI,CAAC,QAAQ;QACX,MAAM,GAAG,GAAG;QACZ,MAAM,MAAM,GAAG;QAEf,MAAM,UAAU,MAAM,IAAI,CAAC,aAAa,KAAK;QAC7C,MAAM,QAAQ;YAAC;gBAAC;gBAAQ;aAAK;SAAC;QAC9B,QAAQ,KAAK,GAAI;QACjB,IAAI,OAAO;YACT,MAAM,IAAI,CAAC;gBAAC;gBAAS;aAAM;QAC7B;QAEA,MAAM,SAAS;QACf,MAAM,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC;QACzB,MAAM,SAAS;QAEf,MAAM,IAAI,CAAC,cAAc,KAAK,CAAC;IACjC;IAEA,MAAM,GAAG,GAAG;IACZ,MAAM,MAAM,GAAG;IACf,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4721, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_inline/image.mjs"], "sourcesContent": ["// Process ![image](<src> \"title\")\n\nimport { normalizeReference, isSpace } from '../common/utils.mjs'\n\nexport default function image (state, silent) {\n  let code, content, label, pos, ref, res, title, start\n  let href = ''\n  const oldPos = state.pos\n  const max = state.posMax\n\n  if (state.src.charCodeAt(state.pos) !== 0x21/* ! */) { return false }\n  if (state.src.charCodeAt(state.pos + 1) !== 0x5B/* [ */) { return false }\n\n  const labelStart = state.pos + 2\n  const labelEnd = state.md.helpers.parseLinkLabel(state, state.pos + 1, false)\n\n  // parser failed to find ']', so it's not a valid link\n  if (labelEnd < 0) { return false }\n\n  pos = labelEnd + 1\n  if (pos < max && state.src.charCodeAt(pos) === 0x28/* ( */) {\n    //\n    // Inline link\n    //\n\n    // [link](  <href>  \"title\"  )\n    //        ^^ skipping these spaces\n    pos++\n    for (; pos < max; pos++) {\n      code = state.src.charCodeAt(pos)\n      if (!isSpace(code) && code !== 0x0A) { break }\n    }\n    if (pos >= max) { return false }\n\n    // [link](  <href>  \"title\"  )\n    //          ^^^^^^ parsing link destination\n    start = pos\n    res = state.md.helpers.parseLinkDestination(state.src, pos, state.posMax)\n    if (res.ok) {\n      href = state.md.normalizeLink(res.str)\n      if (state.md.validateLink(href)) {\n        pos = res.pos\n      } else {\n        href = ''\n      }\n    }\n\n    // [link](  <href>  \"title\"  )\n    //                ^^ skipping these spaces\n    start = pos\n    for (; pos < max; pos++) {\n      code = state.src.charCodeAt(pos)\n      if (!isSpace(code) && code !== 0x0A) { break }\n    }\n\n    // [link](  <href>  \"title\"  )\n    //                  ^^^^^^^ parsing link title\n    res = state.md.helpers.parseLinkTitle(state.src, pos, state.posMax)\n    if (pos < max && start !== pos && res.ok) {\n      title = res.str\n      pos = res.pos\n\n      // [link](  <href>  \"title\"  )\n      //                         ^^ skipping these spaces\n      for (; pos < max; pos++) {\n        code = state.src.charCodeAt(pos)\n        if (!isSpace(code) && code !== 0x0A) { break }\n      }\n    } else {\n      title = ''\n    }\n\n    if (pos >= max || state.src.charCodeAt(pos) !== 0x29/* ) */) {\n      state.pos = oldPos\n      return false\n    }\n    pos++\n  } else {\n    //\n    // Link reference\n    //\n    if (typeof state.env.references === 'undefined') { return false }\n\n    if (pos < max && state.src.charCodeAt(pos) === 0x5B/* [ */) {\n      start = pos + 1\n      pos = state.md.helpers.parseLinkLabel(state, pos)\n      if (pos >= 0) {\n        label = state.src.slice(start, pos++)\n      } else {\n        pos = labelEnd + 1\n      }\n    } else {\n      pos = labelEnd + 1\n    }\n\n    // covers label === '' and label === undefined\n    // (collapsed reference link and shortcut reference link respectively)\n    if (!label) { label = state.src.slice(labelStart, labelEnd) }\n\n    ref = state.env.references[normalizeReference(label)]\n    if (!ref) {\n      state.pos = oldPos\n      return false\n    }\n    href = ref.href\n    title = ref.title\n  }\n\n  //\n  // We found the end of the link, and know for a fact it's a valid link;\n  // so all that's left to do is to call tokenizer.\n  //\n  if (!silent) {\n    content = state.src.slice(labelStart, labelEnd)\n\n    const tokens = []\n    state.md.inline.parse(\n      content,\n      state.md,\n      state.env,\n      tokens\n    )\n\n    const token = state.push('image', 'img', 0)\n    const attrs = [['src', href], ['alt', '']]\n    token.attrs = attrs\n    token.children = tokens\n    token.content = content\n\n    if (title) {\n      attrs.push(['title', title])\n    }\n  }\n\n  state.pos = pos\n  state.posMax = max\n  return true\n}\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;AAElC;;AAEe,SAAS,MAAO,KAAK,EAAE,MAAM;IAC1C,IAAI,MAAM,SAAS,OAAO,KAAK,KAAK,KAAK,OAAO;IAChD,IAAI,OAAO;IACX,MAAM,SAAS,MAAM,GAAG;IACxB,MAAM,MAAM,MAAM,MAAM;IAExB,IAAI,MAAM,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,MAAM,KAAI,KAAK,KAAI;QAAE,OAAO;IAAM;IACpE,IAAI,MAAM,GAAG,CAAC,UAAU,CAAC,MAAM,GAAG,GAAG,OAAO,KAAI,KAAK,KAAI;QAAE,OAAO;IAAM;IAExE,MAAM,aAAa,MAAM,GAAG,GAAG;IAC/B,MAAM,WAAW,MAAM,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,MAAM,GAAG,GAAG,GAAG;IAEvE,sDAAsD;IACtD,IAAI,WAAW,GAAG;QAAE,OAAO;IAAM;IAEjC,MAAM,WAAW;IACjB,IAAI,MAAM,OAAO,MAAM,GAAG,CAAC,UAAU,CAAC,SAAS,KAAI,KAAK,KAAI;QAC1D,EAAE;QACF,cAAc;QACd,EAAE;QAEF,8BAA8B;QAC9B,kCAAkC;QAClC;QACA,MAAO,MAAM,KAAK,MAAO;YACvB,OAAO,MAAM,GAAG,CAAC,UAAU,CAAC;YAC5B,IAAI,CAAC,CAAA,GAAA,wNAAA,CAAA,UAAO,AAAD,EAAE,SAAS,SAAS,MAAM;gBAAE;YAAM;QAC/C;QACA,IAAI,OAAO,KAAK;YAAE,OAAO;QAAM;QAE/B,8BAA8B;QAC9B,2CAA2C;QAC3C,QAAQ;QACR,MAAM,MAAM,EAAE,CAAC,OAAO,CAAC,oBAAoB,CAAC,MAAM,GAAG,EAAE,KAAK,MAAM,MAAM;QACxE,IAAI,IAAI,EAAE,EAAE;YACV,OAAO,MAAM,EAAE,CAAC,aAAa,CAAC,IAAI,GAAG;YACrC,IAAI,MAAM,EAAE,CAAC,YAAY,CAAC,OAAO;gBAC/B,MAAM,IAAI,GAAG;YACf,OAAO;gBACL,OAAO;YACT;QACF;QAEA,8BAA8B;QAC9B,0CAA0C;QAC1C,QAAQ;QACR,MAAO,MAAM,KAAK,MAAO;YACvB,OAAO,MAAM,GAAG,CAAC,UAAU,CAAC;YAC5B,IAAI,CAAC,CAAA,GAAA,wNAAA,CAAA,UAAO,AAAD,EAAE,SAAS,SAAS,MAAM;gBAAE;YAAM;QAC/C;QAEA,8BAA8B;QAC9B,8CAA8C;QAC9C,MAAM,MAAM,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,GAAG,EAAE,KAAK,MAAM,MAAM;QAClE,IAAI,MAAM,OAAO,UAAU,OAAO,IAAI,EAAE,EAAE;YACxC,QAAQ,IAAI,GAAG;YACf,MAAM,IAAI,GAAG;YAEb,8BAA8B;YAC9B,mDAAmD;YACnD,MAAO,MAAM,KAAK,MAAO;gBACvB,OAAO,MAAM,GAAG,CAAC,UAAU,CAAC;gBAC5B,IAAI,CAAC,CAAA,GAAA,wNAAA,CAAA,UAAO,AAAD,EAAE,SAAS,SAAS,MAAM;oBAAE;gBAAM;YAC/C;QACF,OAAO;YACL,QAAQ;QACV;QAEA,IAAI,OAAO,OAAO,MAAM,GAAG,CAAC,UAAU,CAAC,SAAS,KAAI,KAAK,KAAI;YAC3D,MAAM,GAAG,GAAG;YACZ,OAAO;QACT;QACA;IACF,OAAO;QACL,EAAE;QACF,iBAAiB;QACjB,EAAE;QACF,IAAI,OAAO,MAAM,GAAG,CAAC,UAAU,KAAK,aAAa;YAAE,OAAO;QAAM;QAEhE,IAAI,MAAM,OAAO,MAAM,GAAG,CAAC,UAAU,CAAC,SAAS,KAAI,KAAK,KAAI;YAC1D,QAAQ,MAAM;YACd,MAAM,MAAM,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO;YAC7C,IAAI,OAAO,GAAG;gBACZ,QAAQ,MAAM,GAAG,CAAC,KAAK,CAAC,OAAO;YACjC,OAAO;gBACL,MAAM,WAAW;YACnB;QACF,OAAO;YACL,MAAM,WAAW;QACnB;QAEA,8CAA8C;QAC9C,sEAAsE;QACtE,IAAI,CAAC,OAAO;YAAE,QAAQ,MAAM,GAAG,CAAC,KAAK,CAAC,YAAY;QAAU;QAE5D,MAAM,MAAM,GAAG,CAAC,UAAU,CAAC,CAAA,GAAA,wNAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;QACrD,IAAI,CAAC,KAAK;YACR,MAAM,GAAG,GAAG;YACZ,OAAO;QACT;QACA,OAAO,IAAI,IAAI;QACf,QAAQ,IAAI,KAAK;IACnB;IAEA,EAAE;IACF,uEAAuE;IACvE,iDAAiD;IACjD,EAAE;IACF,IAAI,CAAC,QAAQ;QACX,UAAU,MAAM,GAAG,CAAC,KAAK,CAAC,YAAY;QAEtC,MAAM,SAAS,EAAE;QACjB,MAAM,EAAE,CAAC,MAAM,CAAC,KAAK,CACnB,SACA,MAAM,EAAE,EACR,MAAM,GAAG,EACT;QAGF,MAAM,QAAQ,MAAM,IAAI,CAAC,SAAS,OAAO;QACzC,MAAM,QAAQ;YAAC;gBAAC;gBAAO;aAAK;YAAE;gBAAC;gBAAO;aAAG;SAAC;QAC1C,MAAM,KAAK,GAAG;QACd,MAAM,QAAQ,GAAG;QACjB,MAAM,OAAO,GAAG;QAEhB,IAAI,OAAO;YACT,MAAM,IAAI,CAAC;gBAAC;gBAAS;aAAM;QAC7B;IACF;IAEA,MAAM,GAAG,GAAG;IACZ,MAAM,MAAM,GAAG;IACf,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4874, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_inline/autolink.mjs"], "sourcesContent": ["// Process autolinks '<protocol:...>'\n\n/* eslint max-len:0 */\nconst EMAIL_RE    = /^([a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)$/\n/* eslint-disable-next-line no-control-regex */\nconst AUTOLINK_RE = /^([a-zA-Z][a-zA-Z0-9+.-]{1,31}):([^<>\\x00-\\x20]*)$/\n\nexport default function autolink (state, silent) {\n  let pos = state.pos\n\n  if (state.src.charCodeAt(pos) !== 0x3C/* < */) { return false }\n\n  const start = state.pos\n  const max = state.posMax\n\n  for (;;) {\n    if (++pos >= max) return false\n\n    const ch = state.src.charCodeAt(pos)\n\n    if (ch === 0x3C /* < */) return false\n    if (ch === 0x3E /* > */) break\n  }\n\n  const url = state.src.slice(start + 1, pos)\n\n  if (AUTOLINK_RE.test(url)) {\n    const fullUrl = state.md.normalizeLink(url)\n    if (!state.md.validateLink(fullUrl)) { return false }\n\n    if (!silent) {\n      const token_o   = state.push('link_open', 'a', 1)\n      token_o.attrs   = [['href', fullUrl]]\n      token_o.markup  = 'autolink'\n      token_o.info    = 'auto'\n\n      const token_t   = state.push('text', '', 0)\n      token_t.content = state.md.normalizeLinkText(url)\n\n      const token_c   = state.push('link_close', 'a', -1)\n      token_c.markup  = 'autolink'\n      token_c.info    = 'auto'\n    }\n\n    state.pos += url.length + 2\n    return true\n  }\n\n  if (EMAIL_RE.test(url)) {\n    const fullUrl = state.md.normalizeLink('mailto:' + url)\n    if (!state.md.validateLink(fullUrl)) { return false }\n\n    if (!silent) {\n      const token_o   = state.push('link_open', 'a', 1)\n      token_o.attrs   = [['href', fullUrl]]\n      token_o.markup  = 'autolink'\n      token_o.info    = 'auto'\n\n      const token_t   = state.push('text', '', 0)\n      token_t.content = state.md.normalizeLinkText(url)\n\n      const token_c   = state.push('link_close', 'a', -1)\n      token_c.markup  = 'autolink'\n      token_c.info    = 'auto'\n    }\n\n    state.pos += url.length + 2\n    return true\n  }\n\n  return false\n}\n"], "names": [], "mappings": "AAAA,qCAAqC;AAErC,oBAAoB;;;AACpB,MAAM,WAAc;AACpB,6CAA6C,GAC7C,MAAM,cAAc;AAEL,SAAS,SAAU,KAAK,EAAE,MAAM;IAC7C,IAAI,MAAM,MAAM,GAAG;IAEnB,IAAI,MAAM,GAAG,CAAC,UAAU,CAAC,SAAS,KAAI,KAAK,KAAI;QAAE,OAAO;IAAM;IAE9D,MAAM,QAAQ,MAAM,GAAG;IACvB,MAAM,MAAM,MAAM,MAAM;IAExB,OAAS;QACP,IAAI,EAAE,OAAO,KAAK,OAAO;QAEzB,MAAM,KAAK,MAAM,GAAG,CAAC,UAAU,CAAC;QAEhC,IAAI,OAAO,KAAK,KAAK,KAAI,OAAO;QAChC,IAAI,OAAO,KAAK,KAAK,KAAI;IAC3B;IAEA,MAAM,MAAM,MAAM,GAAG,CAAC,KAAK,CAAC,QAAQ,GAAG;IAEvC,IAAI,YAAY,IAAI,CAAC,MAAM;QACzB,MAAM,UAAU,MAAM,EAAE,CAAC,aAAa,CAAC;QACvC,IAAI,CAAC,MAAM,EAAE,CAAC,YAAY,CAAC,UAAU;YAAE,OAAO;QAAM;QAEpD,IAAI,CAAC,QAAQ;YACX,MAAM,UAAY,MAAM,IAAI,CAAC,aAAa,KAAK;YAC/C,QAAQ,KAAK,GAAK;gBAAC;oBAAC;oBAAQ;iBAAQ;aAAC;YACrC,QAAQ,MAAM,GAAI;YAClB,QAAQ,IAAI,GAAM;YAElB,MAAM,UAAY,MAAM,IAAI,CAAC,QAAQ,IAAI;YACzC,QAAQ,OAAO,GAAG,MAAM,EAAE,CAAC,iBAAiB,CAAC;YAE7C,MAAM,UAAY,MAAM,IAAI,CAAC,cAAc,KAAK,CAAC;YACjD,QAAQ,MAAM,GAAI;YAClB,QAAQ,IAAI,GAAM;QACpB;QAEA,MAAM,GAAG,IAAI,IAAI,MAAM,GAAG;QAC1B,OAAO;IACT;IAEA,IAAI,SAAS,IAAI,CAAC,MAAM;QACtB,MAAM,UAAU,MAAM,EAAE,CAAC,aAAa,CAAC,YAAY;QACnD,IAAI,CAAC,MAAM,EAAE,CAAC,YAAY,CAAC,UAAU;YAAE,OAAO;QAAM;QAEpD,IAAI,CAAC,QAAQ;YACX,MAAM,UAAY,MAAM,IAAI,CAAC,aAAa,KAAK;YAC/C,QAAQ,KAAK,GAAK;gBAAC;oBAAC;oBAAQ;iBAAQ;aAAC;YACrC,QAAQ,MAAM,GAAI;YAClB,QAAQ,IAAI,GAAM;YAElB,MAAM,UAAY,MAAM,IAAI,CAAC,QAAQ,IAAI;YACzC,QAAQ,OAAO,GAAG,MAAM,EAAE,CAAC,iBAAiB,CAAC;YAE7C,MAAM,UAAY,MAAM,IAAI,CAAC,cAAc,KAAK,CAAC;YACjD,QAAQ,MAAM,GAAI;YAClB,QAAQ,IAAI,GAAM;QACpB;QAEA,MAAM,GAAG,IAAI,IAAI,MAAM,GAAG;QAC1B,OAAO;IACT;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4950, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_inline/html_inline.mjs"], "sourcesContent": ["// Process html tags\n\nimport { HTML_TAG_RE } from '../common/html_re.mjs'\n\nfunction isLinkOpen (str) {\n  return /^<a[>\\s]/i.test(str)\n}\nfunction isLinkClose (str) {\n  return /^<\\/a\\s*>/i.test(str)\n}\n\nfunction isLetter (ch) {\n  /* eslint no-bitwise:0 */\n  const lc = ch | 0x20 // to lower case\n  return (lc >= 0x61/* a */) && (lc <= 0x7a/* z */)\n}\n\nexport default function html_inline (state, silent) {\n  if (!state.md.options.html) { return false }\n\n  // Check start\n  const max = state.posMax\n  const pos = state.pos\n  if (state.src.charCodeAt(pos) !== 0x3C/* < */ ||\n      pos + 2 >= max) {\n    return false\n  }\n\n  // Quick fail on second char\n  const ch = state.src.charCodeAt(pos + 1)\n  if (ch !== 0x21/* ! */ &&\n      ch !== 0x3F/* ? */ &&\n      ch !== 0x2F/* / */ &&\n      !isLetter(ch)) {\n    return false\n  }\n\n  const match = state.src.slice(pos).match(HTML_TAG_RE)\n  if (!match) { return false }\n\n  if (!silent) {\n    const token = state.push('html_inline', '', 0)\n    token.content = match[0]\n\n    if (isLinkOpen(token.content))  state.linkLevel++\n    if (isLinkClose(token.content)) state.linkLevel--\n  }\n  state.pos += match[0].length\n  return true\n}\n"], "names": [], "mappings": "AAAA,oBAAoB;;;;AAEpB;;AAEA,SAAS,WAAY,GAAG;IACtB,OAAO,YAAY,IAAI,CAAC;AAC1B;AACA,SAAS,YAAa,GAAG;IACvB,OAAO,aAAa,IAAI,CAAC;AAC3B;AAEA,SAAS,SAAU,EAAE;IACnB,uBAAuB,GACvB,MAAM,KAAK,KAAK,KAAK,gBAAgB;;IACrC,OAAO,AAAC,MAAM,KAAI,KAAK,OAAQ,MAAM,KAAI,KAAK;AAChD;AAEe,SAAS,YAAa,KAAK,EAAE,MAAM;IAChD,IAAI,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE;QAAE,OAAO;IAAM;IAE3C,cAAc;IACd,MAAM,MAAM,MAAM,MAAM;IACxB,MAAM,MAAM,MAAM,GAAG;IACrB,IAAI,MAAM,GAAG,CAAC,UAAU,CAAC,SAAS,KAAI,KAAK,OACvC,MAAM,KAAK,KAAK;QAClB,OAAO;IACT;IAEA,4BAA4B;IAC5B,MAAM,KAAK,MAAM,GAAG,CAAC,UAAU,CAAC,MAAM;IACtC,IAAI,OAAO,KAAI,KAAK,OAChB,OAAO,KAAI,KAAK,OAChB,OAAO,KAAI,KAAK,OAChB,CAAC,SAAS,KAAK;QACjB,OAAO;IACT;IAEA,MAAM,QAAQ,MAAM,GAAG,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,0NAAA,CAAA,cAAW;IACpD,IAAI,CAAC,OAAO;QAAE,OAAO;IAAM;IAE3B,IAAI,CAAC,QAAQ;QACX,MAAM,QAAQ,MAAM,IAAI,CAAC,eAAe,IAAI;QAC5C,MAAM,OAAO,GAAG,KAAK,CAAC,EAAE;QAExB,IAAI,WAAW,MAAM,OAAO,GAAI,MAAM,SAAS;QAC/C,IAAI,YAAY,MAAM,OAAO,GAAG,MAAM,SAAS;IACjD;IACA,MAAM,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC,MAAM;IAC5B,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5001, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_inline/entity.mjs"], "sourcesContent": ["// Process html entity - &#123;, &#xAF;, &quot;, ...\n\nimport { decodeHTML } from 'entities'\nimport { isValidEntityCode, fromCodePoint } from '../common/utils.mjs'\n\nconst DIGITAL_RE = /^&#((?:x[a-f0-9]{1,6}|[0-9]{1,7}));/i\nconst NAMED_RE   = /^&([a-z][a-z0-9]{1,31});/i\n\nexport default function entity (state, silent) {\n  const pos = state.pos\n  const max = state.posMax\n\n  if (state.src.charCodeAt(pos) !== 0x26/* & */) return false\n\n  if (pos + 1 >= max) return false\n\n  const ch = state.src.charCodeAt(pos + 1)\n\n  if (ch === 0x23 /* # */) {\n    const match = state.src.slice(pos).match(DIGITAL_RE)\n    if (match) {\n      if (!silent) {\n        const code = match[1][0].toLowerCase() === 'x' ? parseInt(match[1].slice(1), 16) : parseInt(match[1], 10)\n\n        const token   = state.push('text_special', '', 0)\n        token.content = isValidEntityCode(code) ? fromCodePoint(code) : fromCodePoint(0xFFFD)\n        token.markup  = match[0]\n        token.info    = 'entity'\n      }\n      state.pos += match[0].length\n      return true\n    }\n  } else {\n    const match = state.src.slice(pos).match(NAMED_RE)\n    if (match) {\n      const decoded = decodeHTML(match[0])\n      if (decoded !== match[0]) {\n        if (!silent) {\n          const token   = state.push('text_special', '', 0)\n          token.content = decoded\n          token.markup  = match[0]\n          token.info    = 'entity'\n        }\n        state.pos += match[0].length\n        return true\n      }\n    }\n  }\n\n  return false\n}\n"], "names": [], "mappings": "AAAA,oDAAoD;;;;AAEpD;AAAA;AACA;;;AAEA,MAAM,aAAa;AACnB,MAAM,WAAa;AAEJ,SAAS,OAAQ,KAAK,EAAE,MAAM;IAC3C,MAAM,MAAM,MAAM,GAAG;IACrB,MAAM,MAAM,MAAM,MAAM;IAExB,IAAI,MAAM,GAAG,CAAC,UAAU,CAAC,SAAS,KAAI,KAAK,KAAI,OAAO;IAEtD,IAAI,MAAM,KAAK,KAAK,OAAO;IAE3B,MAAM,KAAK,MAAM,GAAG,CAAC,UAAU,CAAC,MAAM;IAEtC,IAAI,OAAO,KAAK,KAAK,KAAI;QACvB,MAAM,QAAQ,MAAM,GAAG,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC;QACzC,IAAI,OAAO;YACT,IAAI,CAAC,QAAQ;gBACX,MAAM,OAAO,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,WAAW,OAAO,MAAM,SAAS,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,MAAM,SAAS,KAAK,CAAC,EAAE,EAAE;gBAEtG,MAAM,QAAU,MAAM,IAAI,CAAC,gBAAgB,IAAI;gBAC/C,MAAM,OAAO,GAAG,CAAA,GAAA,wNAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,CAAA,GAAA,wNAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,CAAA,GAAA,wNAAA,CAAA,gBAAa,AAAD,EAAE;gBAC9E,MAAM,MAAM,GAAI,KAAK,CAAC,EAAE;gBACxB,MAAM,IAAI,GAAM;YAClB;YACA,MAAM,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC,MAAM;YAC5B,OAAO;QACT;IACF,OAAO;QACL,MAAM,QAAQ,MAAM,GAAG,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC;QACzC,IAAI,OAAO;YACT,MAAM,UAAU,CAAA,GAAA,wNAAA,CAAA,aAAU,AAAD,EAAE,KAAK,CAAC,EAAE;YACnC,IAAI,YAAY,KAAK,CAAC,EAAE,EAAE;gBACxB,IAAI,CAAC,QAAQ;oBACX,MAAM,QAAU,MAAM,IAAI,CAAC,gBAAgB,IAAI;oBAC/C,MAAM,OAAO,GAAG;oBAChB,MAAM,MAAM,GAAI,KAAK,CAAC,EAAE;oBACxB,MAAM,IAAI,GAAM;gBAClB;gBACA,MAAM,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC,MAAM;gBAC5B,OAAO;YACT;QACF;IACF;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5055, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_inline/balance_pairs.mjs"], "sourcesContent": ["// For each opening emphasis-like marker find a matching closing one\n//\n\nfunction processDelimiters (delimiters) {\n  const openersBottom = {}\n  const max = delimiters.length\n\n  if (!max) return\n\n  // headerIdx is the first delimiter of the current (where closer is) delimiter run\n  let headerIdx = 0\n  let lastTokenIdx = -2 // needs any value lower than -1\n  const jumps = []\n\n  for (let closerIdx = 0; closerIdx < max; closerIdx++) {\n    const closer = delimiters[closerIdx]\n\n    jumps.push(0)\n\n    // markers belong to same delimiter run if:\n    //  - they have adjacent tokens\n    //  - AND markers are the same\n    //\n    if (delimiters[headerIdx].marker !== closer.marker || lastTokenIdx !== closer.token - 1) {\n      headerIdx = closerIdx\n    }\n\n    lastTokenIdx = closer.token\n\n    // Length is only used for emphasis-specific \"rule of 3\",\n    // if it's not defined (in strikethrough or 3rd party plugins),\n    // we can default it to 0 to disable those checks.\n    //\n    closer.length = closer.length || 0\n\n    if (!closer.close) continue\n\n    // Previously calculated lower bounds (previous fails)\n    // for each marker, each delimiter length modulo 3,\n    // and for whether this closer can be an opener;\n    // https://github.com/commonmark/cmark/commit/34250e12ccebdc6372b8b49c44fab57c72443460\n    /* eslint-disable-next-line no-prototype-builtins */\n    if (!openersBottom.hasOwnProperty(closer.marker)) {\n      openersBottom[closer.marker] = [-1, -1, -1, -1, -1, -1]\n    }\n\n    const minOpenerIdx = openersBottom[closer.marker][(closer.open ? 3 : 0) + (closer.length % 3)]\n\n    let openerIdx = headerIdx - jumps[headerIdx] - 1\n\n    let newMinOpenerIdx = openerIdx\n\n    for (; openerIdx > minOpenerIdx; openerIdx -= jumps[openerIdx] + 1) {\n      const opener = delimiters[openerIdx]\n\n      if (opener.marker !== closer.marker) continue\n\n      if (opener.open && opener.end < 0) {\n        let isOddMatch = false\n\n        // from spec:\n        //\n        // If one of the delimiters can both open and close emphasis, then the\n        // sum of the lengths of the delimiter runs containing the opening and\n        // closing delimiters must not be a multiple of 3 unless both lengths\n        // are multiples of 3.\n        //\n        if (opener.close || closer.open) {\n          if ((opener.length + closer.length) % 3 === 0) {\n            if (opener.length % 3 !== 0 || closer.length % 3 !== 0) {\n              isOddMatch = true\n            }\n          }\n        }\n\n        if (!isOddMatch) {\n          // If previous delimiter cannot be an opener, we can safely skip\n          // the entire sequence in future checks. This is required to make\n          // sure algorithm has linear complexity (see *_*_*_*_*_... case).\n          //\n          const lastJump = openerIdx > 0 && !delimiters[openerIdx - 1].open\n            ? jumps[openerIdx - 1] + 1\n            : 0\n\n          jumps[closerIdx] = closerIdx - openerIdx + lastJump\n          jumps[openerIdx] = lastJump\n\n          closer.open  = false\n          opener.end   = closerIdx\n          opener.close = false\n          newMinOpenerIdx = -1\n          // treat next token as start of run,\n          // it optimizes skips in **<...>**a**<...>** pathological case\n          lastTokenIdx = -2\n          break\n        }\n      }\n    }\n\n    if (newMinOpenerIdx !== -1) {\n      // If match for this delimiter run failed, we want to set lower bound for\n      // future lookups. This is required to make sure algorithm has linear\n      // complexity.\n      //\n      // See details here:\n      // https://github.com/commonmark/cmark/issues/178#issuecomment-270417442\n      //\n      openersBottom[closer.marker][(closer.open ? 3 : 0) + ((closer.length || 0) % 3)] = newMinOpenerIdx\n    }\n  }\n}\n\nexport default function link_pairs (state) {\n  const tokens_meta = state.tokens_meta\n  const max = state.tokens_meta.length\n\n  processDelimiters(state.delimiters)\n\n  for (let curr = 0; curr < max; curr++) {\n    if (tokens_meta[curr] && tokens_meta[curr].delimiters) {\n      processDelimiters(tokens_meta[curr].delimiters)\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA,oEAAoE;AACpE,EAAE;;;;AAEF,SAAS,kBAAmB,UAAU;IACpC,MAAM,gBAAgB,CAAC;IACvB,MAAM,MAAM,WAAW,MAAM;IAE7B,IAAI,CAAC,KAAK;IAEV,kFAAkF;IAClF,IAAI,YAAY;IAChB,IAAI,eAAe,CAAC,EAAE,gCAAgC;;IACtD,MAAM,QAAQ,EAAE;IAEhB,IAAK,IAAI,YAAY,GAAG,YAAY,KAAK,YAAa;QACpD,MAAM,SAAS,UAAU,CAAC,UAAU;QAEpC,MAAM,IAAI,CAAC;QAEX,2CAA2C;QAC3C,+BAA+B;QAC/B,8BAA8B;QAC9B,EAAE;QACF,IAAI,UAAU,CAAC,UAAU,CAAC,MAAM,KAAK,OAAO,MAAM,IAAI,iBAAiB,OAAO,KAAK,GAAG,GAAG;YACvF,YAAY;QACd;QAEA,eAAe,OAAO,KAAK;QAE3B,yDAAyD;QACzD,+DAA+D;QAC/D,kDAAkD;QAClD,EAAE;QACF,OAAO,MAAM,GAAG,OAAO,MAAM,IAAI;QAEjC,IAAI,CAAC,OAAO,KAAK,EAAE;QAEnB,sDAAsD;QACtD,mDAAmD;QACnD,gDAAgD;QAChD,sFAAsF;QACtF,kDAAkD,GAClD,IAAI,CAAC,cAAc,cAAc,CAAC,OAAO,MAAM,GAAG;YAChD,aAAa,CAAC,OAAO,MAAM,CAAC,GAAG;gBAAC,CAAC;gBAAG,CAAC;gBAAG,CAAC;gBAAG,CAAC;gBAAG,CAAC;gBAAG,CAAC;aAAE;QACzD;QAEA,MAAM,eAAe,aAAa,CAAC,OAAO,MAAM,CAAC,CAAC,CAAC,OAAO,IAAI,GAAG,IAAI,CAAC,IAAK,OAAO,MAAM,GAAG,EAAG;QAE9F,IAAI,YAAY,YAAY,KAAK,CAAC,UAAU,GAAG;QAE/C,IAAI,kBAAkB;QAEtB,MAAO,YAAY,cAAc,aAAa,KAAK,CAAC,UAAU,GAAG,EAAG;YAClE,MAAM,SAAS,UAAU,CAAC,UAAU;YAEpC,IAAI,OAAO,MAAM,KAAK,OAAO,MAAM,EAAE;YAErC,IAAI,OAAO,IAAI,IAAI,OAAO,GAAG,GAAG,GAAG;gBACjC,IAAI,aAAa;gBAEjB,aAAa;gBACb,EAAE;gBACF,sEAAsE;gBACtE,sEAAsE;gBACtE,qEAAqE;gBACrE,sBAAsB;gBACtB,EAAE;gBACF,IAAI,OAAO,KAAK,IAAI,OAAO,IAAI,EAAE;oBAC/B,IAAI,CAAC,OAAO,MAAM,GAAG,OAAO,MAAM,IAAI,MAAM,GAAG;wBAC7C,IAAI,OAAO,MAAM,GAAG,MAAM,KAAK,OAAO,MAAM,GAAG,MAAM,GAAG;4BACtD,aAAa;wBACf;oBACF;gBACF;gBAEA,IAAI,CAAC,YAAY;oBACf,gEAAgE;oBAChE,iEAAiE;oBACjE,iEAAiE;oBACjE,EAAE;oBACF,MAAM,WAAW,YAAY,KAAK,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC,IAAI,GAC7D,KAAK,CAAC,YAAY,EAAE,GAAG,IACvB;oBAEJ,KAAK,CAAC,UAAU,GAAG,YAAY,YAAY;oBAC3C,KAAK,CAAC,UAAU,GAAG;oBAEnB,OAAO,IAAI,GAAI;oBACf,OAAO,GAAG,GAAK;oBACf,OAAO,KAAK,GAAG;oBACf,kBAAkB,CAAC;oBACnB,oCAAoC;oBACpC,8DAA8D;oBAC9D,eAAe,CAAC;oBAChB;gBACF;YACF;QACF;QAEA,IAAI,oBAAoB,CAAC,GAAG;YAC1B,yEAAyE;YACzE,qEAAqE;YACrE,cAAc;YACd,EAAE;YACF,oBAAoB;YACpB,wEAAwE;YACxE,EAAE;YACF,aAAa,CAAC,OAAO,MAAM,CAAC,CAAC,CAAC,OAAO,IAAI,GAAG,IAAI,CAAC,IAAK,CAAC,OAAO,MAAM,IAAI,CAAC,IAAI,EAAG,GAAG;QACrF;IACF;AACF;AAEe,SAAS,WAAY,KAAK;IACvC,MAAM,cAAc,MAAM,WAAW;IACrC,MAAM,MAAM,MAAM,WAAW,CAAC,MAAM;IAEpC,kBAAkB,MAAM,UAAU;IAElC,IAAK,IAAI,OAAO,GAAG,OAAO,KAAK,OAAQ;QACrC,IAAI,WAAW,CAAC,KAAK,IAAI,WAAW,CAAC,KAAK,CAAC,UAAU,EAAE;YACrD,kBAAkB,WAAW,CAAC,KAAK,CAAC,UAAU;QAChD;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5169, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/rules_inline/fragments_join.mjs"], "sourcesContent": ["// Clean up tokens after emphasis and strikethrough postprocessing:\n// merge adjacent text nodes into one and re-calculate all token levels\n//\n// This is necessary because initially emphasis delimiter markers (*, _, ~)\n// are treated as their own separate text tokens. Then emphasis rule either\n// leaves them as text (needed to merge with adjacent text) or turns them\n// into opening/closing tags (which messes up levels inside).\n//\n\nexport default function fragments_join (state) {\n  let curr, last\n  let level = 0\n  const tokens = state.tokens\n  const max = state.tokens.length\n\n  for (curr = last = 0; curr < max; curr++) {\n    // re-calculate levels after emphasis/strikethrough turns some text nodes\n    // into opening/closing tags\n    if (tokens[curr].nesting < 0) level-- // closing tag\n    tokens[curr].level = level\n    if (tokens[curr].nesting > 0) level++ // opening tag\n\n    if (tokens[curr].type === 'text' &&\n        curr + 1 < max &&\n        tokens[curr + 1].type === 'text') {\n      // collapse two adjacent text nodes\n      tokens[curr + 1].content = tokens[curr].content + tokens[curr + 1].content\n    } else {\n      if (curr !== last) { tokens[last] = tokens[curr] }\n\n      last++\n    }\n  }\n\n  if (curr !== last) {\n    tokens.length = last\n  }\n}\n"], "names": [], "mappings": "AAAA,mEAAmE;AACnE,uEAAuE;AACvE,EAAE;AACF,2EAA2E;AAC3E,2EAA2E;AAC3E,yEAAyE;AACzE,6DAA6D;AAC7D,EAAE;;;;AAEa,SAAS,eAAgB,KAAK;IAC3C,IAAI,MAAM;IACV,IAAI,QAAQ;IACZ,MAAM,SAAS,MAAM,MAAM;IAC3B,MAAM,MAAM,MAAM,MAAM,CAAC,MAAM;IAE/B,IAAK,OAAO,OAAO,GAAG,OAAO,KAAK,OAAQ;QACxC,yEAAyE;QACzE,4BAA4B;QAC5B,IAAI,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,QAAQ,cAAc;;QACpD,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG;QACrB,IAAI,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,QAAQ,cAAc;;QAEpD,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,UACtB,OAAO,IAAI,OACX,MAAM,CAAC,OAAO,EAAE,CAAC,IAAI,KAAK,QAAQ;YACpC,mCAAmC;YACnC,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO;QAC5E,OAAO;YACL,IAAI,SAAS,MAAM;gBAAE,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK;YAAC;YAEjD;QACF;IACF;IAEA,IAAI,SAAS,MAAM;QACjB,OAAO,MAAM,GAAG;IAClB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5213, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/parser_inline.mjs"], "sourcesContent": ["/** internal\n * class ParserInline\n *\n * Tokenizes paragraph content.\n **/\n\nimport Ruler from './ruler.mjs'\nimport StateInline from './rules_inline/state_inline.mjs'\n\nimport r_text from './rules_inline/text.mjs'\nimport r_linkify from './rules_inline/linkify.mjs'\nimport r_newline from './rules_inline/newline.mjs'\nimport r_escape from './rules_inline/escape.mjs'\nimport r_backticks from './rules_inline/backticks.mjs'\nimport r_strikethrough from './rules_inline/strikethrough.mjs'\nimport r_emphasis from './rules_inline/emphasis.mjs'\nimport r_link from './rules_inline/link.mjs'\nimport r_image from './rules_inline/image.mjs'\nimport r_autolink from './rules_inline/autolink.mjs'\nimport r_html_inline from './rules_inline/html_inline.mjs'\nimport r_entity from './rules_inline/entity.mjs'\n\nimport r_balance_pairs from './rules_inline/balance_pairs.mjs'\nimport r_fragments_join from './rules_inline/fragments_join.mjs'\n\n// Parser rules\n\nconst _rules = [\n  ['text',            r_text],\n  ['linkify',         r_linkify],\n  ['newline',         r_newline],\n  ['escape',          r_escape],\n  ['backticks',       r_backticks],\n  ['strikethrough',   r_strikethrough.tokenize],\n  ['emphasis',        r_emphasis.tokenize],\n  ['link',            r_link],\n  ['image',           r_image],\n  ['autolink',        r_autolink],\n  ['html_inline',     r_html_inline],\n  ['entity',          r_entity]\n]\n\n// `rule2` ruleset was created specifically for emphasis/strikethrough\n// post-processing and may be changed in the future.\n//\n// Don't use this for anything except pairs (plugins working with `balance_pairs`).\n//\nconst _rules2 = [\n  ['balance_pairs',   r_balance_pairs],\n  ['strikethrough',   r_strikethrough.postProcess],\n  ['emphasis',        r_emphasis.postProcess],\n  // rules for pairs separate '**' into its own text tokens, which may be left unused,\n  // rule below merges unused segments back with the rest of the text\n  ['fragments_join',  r_fragments_join]\n]\n\n/**\n * new ParserInline()\n **/\nfunction ParserInline () {\n  /**\n   * ParserInline#ruler -> Ruler\n   *\n   * [[Ruler]] instance. Keep configuration of inline rules.\n   **/\n  this.ruler = new Ruler()\n\n  for (let i = 0; i < _rules.length; i++) {\n    this.ruler.push(_rules[i][0], _rules[i][1])\n  }\n\n  /**\n   * ParserInline#ruler2 -> Ruler\n   *\n   * [[Ruler]] instance. Second ruler used for post-processing\n   * (e.g. in emphasis-like rules).\n   **/\n  this.ruler2 = new Ruler()\n\n  for (let i = 0; i < _rules2.length; i++) {\n    this.ruler2.push(_rules2[i][0], _rules2[i][1])\n  }\n}\n\n// Skip single token by running all rules in validation mode;\n// returns `true` if any rule reported success\n//\nParserInline.prototype.skipToken = function (state) {\n  const pos = state.pos\n  const rules = this.ruler.getRules('')\n  const len = rules.length\n  const maxNesting = state.md.options.maxNesting\n  const cache = state.cache\n\n  if (typeof cache[pos] !== 'undefined') {\n    state.pos = cache[pos]\n    return\n  }\n\n  let ok = false\n\n  if (state.level < maxNesting) {\n    for (let i = 0; i < len; i++) {\n      // Increment state.level and decrement it later to limit recursion.\n      // It's harmless to do here, because no tokens are created. But ideally,\n      // we'd need a separate private state variable for this purpose.\n      //\n      state.level++\n      ok = rules[i](state, true)\n      state.level--\n\n      if (ok) {\n        if (pos >= state.pos) { throw new Error(\"inline rule didn't increment state.pos\") }\n        break\n      }\n    }\n  } else {\n    // Too much nesting, just skip until the end of the paragraph.\n    //\n    // NOTE: this will cause links to behave incorrectly in the following case,\n    //       when an amount of `[` is exactly equal to `maxNesting + 1`:\n    //\n    //       [[[[[[[[[[[[[[[[[[[[[foo]()\n    //\n    // TODO: remove this workaround when CM standard will allow nested links\n    //       (we can replace it by preventing links from being parsed in\n    //       validation mode)\n    //\n    state.pos = state.posMax\n  }\n\n  if (!ok) { state.pos++ }\n  cache[pos] = state.pos\n}\n\n// Generate tokens for input range\n//\nParserInline.prototype.tokenize = function (state) {\n  const rules = this.ruler.getRules('')\n  const len = rules.length\n  const end = state.posMax\n  const maxNesting = state.md.options.maxNesting\n\n  while (state.pos < end) {\n    // Try all possible rules.\n    // On success, rule should:\n    //\n    // - update `state.pos`\n    // - update `state.tokens`\n    // - return true\n    const prevPos = state.pos\n    let ok = false\n\n    if (state.level < maxNesting) {\n      for (let i = 0; i < len; i++) {\n        ok = rules[i](state, false)\n        if (ok) {\n          if (prevPos >= state.pos) { throw new Error(\"inline rule didn't increment state.pos\") }\n          break\n        }\n      }\n    }\n\n    if (ok) {\n      if (state.pos >= end) { break }\n      continue\n    }\n\n    state.pending += state.src[state.pos++]\n  }\n\n  if (state.pending) {\n    state.pushPending()\n  }\n}\n\n/**\n * ParserInline.parse(str, md, env, outTokens)\n *\n * Process input string and push inline tokens into `outTokens`\n **/\nParserInline.prototype.parse = function (str, md, env, outTokens) {\n  const state = new this.State(str, md, env, outTokens)\n\n  this.tokenize(state)\n\n  const rules = this.ruler2.getRules('')\n  const len = rules.length\n\n  for (let i = 0; i < len; i++) {\n    rules[i](state)\n  }\n}\n\nParserInline.prototype.State = StateInline\n\nexport default ParserInline\n"], "names": [], "mappings": "AAAA;;;;EAIE;;;AAEF;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;;;;;;;;;;;;;;;;;AAEA,eAAe;AAEf,MAAM,SAAS;IACb;QAAC;QAAmB,6NAAA,CAAA,UAAM;KAAC;IAC3B;QAAC;QAAmB,gOAAA,CAAA,UAAS;KAAC;IAC9B;QAAC;QAAmB,gOAAA,CAAA,UAAS;KAAC;IAC9B;QAAC;QAAmB,+NAAA,CAAA,UAAQ;KAAC;IAC7B;QAAC;QAAmB,kOAAA,CAAA,UAAW;KAAC;IAChC;QAAC;QAAmB,sOAAA,CAAA,UAAe,CAAC,QAAQ;KAAC;IAC7C;QAAC;QAAmB,iOAAA,CAAA,UAAU,CAAC,QAAQ;KAAC;IACxC;QAAC;QAAmB,6NAAA,CAAA,UAAM;KAAC;IAC3B;QAAC;QAAmB,8NAAA,CAAA,UAAO;KAAC;IAC5B;QAAC;QAAmB,iOAAA,CAAA,UAAU;KAAC;IAC/B;QAAC;QAAmB,oOAAA,CAAA,UAAa;KAAC;IAClC;QAAC;QAAmB,+NAAA,CAAA,UAAQ;KAAC;CAC9B;AAED,sEAAsE;AACtE,oDAAoD;AACpD,EAAE;AACF,mFAAmF;AACnF,EAAE;AACF,MAAM,UAAU;IACd;QAAC;QAAmB,sOAAA,CAAA,UAAe;KAAC;IACpC;QAAC;QAAmB,sOAAA,CAAA,UAAe,CAAC,WAAW;KAAC;IAChD;QAAC;QAAmB,iOAAA,CAAA,UAAU,CAAC,WAAW;KAAC;IAC3C,oFAAoF;IACpF,mEAAmE;IACnE;QAAC;QAAmB,uOAAA,CAAA,UAAgB;KAAC;CACtC;AAED;;EAEE,GACF,SAAS;IACP;;;;IAIE,GACF,IAAI,CAAC,KAAK,GAAG,IAAI,8MAAA,CAAA,UAAK;IAEtB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACtC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE;IAC5C;IAEA;;;;;IAKE,GACF,IAAI,CAAC,MAAM,GAAG,IAAI,8MAAA,CAAA,UAAK;IAEvB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QACvC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,CAAC,EAAE;IAC/C;AACF;AAEA,6DAA6D;AAC7D,8CAA8C;AAC9C,EAAE;AACF,aAAa,SAAS,CAAC,SAAS,GAAG,SAAU,KAAK;IAChD,MAAM,MAAM,MAAM,GAAG;IACrB,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAClC,MAAM,MAAM,MAAM,MAAM;IACxB,MAAM,aAAa,MAAM,EAAE,CAAC,OAAO,CAAC,UAAU;IAC9C,MAAM,QAAQ,MAAM,KAAK;IAEzB,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,aAAa;QACrC,MAAM,GAAG,GAAG,KAAK,CAAC,IAAI;QACtB;IACF;IAEA,IAAI,KAAK;IAET,IAAI,MAAM,KAAK,GAAG,YAAY;QAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC5B,mEAAmE;YACnE,wEAAwE;YACxE,gEAAgE;YAChE,EAAE;YACF,MAAM,KAAK;YACX,KAAK,KAAK,CAAC,EAAE,CAAC,OAAO;YACrB,MAAM,KAAK;YAEX,IAAI,IAAI;gBACN,IAAI,OAAO,MAAM,GAAG,EAAE;oBAAE,MAAM,IAAI,MAAM;gBAA0C;gBAClF;YACF;QACF;IACF,OAAO;QACL,8DAA8D;QAC9D,EAAE;QACF,2EAA2E;QAC3E,oEAAoE;QACpE,EAAE;QACF,oCAAoC;QACpC,EAAE;QACF,wEAAwE;QACxE,oEAAoE;QACpE,yBAAyB;QACzB,EAAE;QACF,MAAM,GAAG,GAAG,MAAM,MAAM;IAC1B;IAEA,IAAI,CAAC,IAAI;QAAE,MAAM,GAAG;IAAG;IACvB,KAAK,CAAC,IAAI,GAAG,MAAM,GAAG;AACxB;AAEA,kCAAkC;AAClC,EAAE;AACF,aAAa,SAAS,CAAC,QAAQ,GAAG,SAAU,KAAK;IAC/C,MAAM,QAAQ,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;IAClC,MAAM,MAAM,MAAM,MAAM;IACxB,MAAM,MAAM,MAAM,MAAM;IACxB,MAAM,aAAa,MAAM,EAAE,CAAC,OAAO,CAAC,UAAU;IAE9C,MAAO,MAAM,GAAG,GAAG,IAAK;QACtB,0BAA0B;QAC1B,2BAA2B;QAC3B,EAAE;QACF,uBAAuB;QACvB,0BAA0B;QAC1B,gBAAgB;QAChB,MAAM,UAAU,MAAM,GAAG;QACzB,IAAI,KAAK;QAET,IAAI,MAAM,KAAK,GAAG,YAAY;YAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;gBAC5B,KAAK,KAAK,CAAC,EAAE,CAAC,OAAO;gBACrB,IAAI,IAAI;oBACN,IAAI,WAAW,MAAM,GAAG,EAAE;wBAAE,MAAM,IAAI,MAAM;oBAA0C;oBACtF;gBACF;YACF;QACF;QAEA,IAAI,IAAI;YACN,IAAI,MAAM,GAAG,IAAI,KAAK;gBAAE;YAAM;YAC9B;QACF;QAEA,MAAM,OAAO,IAAI,MAAM,GAAG,CAAC,MAAM,GAAG,GAAG;IACzC;IAEA,IAAI,MAAM,OAAO,EAAE;QACjB,MAAM,WAAW;IACnB;AACF;AAEA;;;;EAIE,GACF,aAAa,SAAS,CAAC,KAAK,GAAG,SAAU,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,SAAS;IAC9D,MAAM,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,KAAK;IAE3C,IAAI,CAAC,QAAQ,CAAC;IAEd,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;IACnC,MAAM,MAAM,MAAM,MAAM;IAExB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;QAC5B,KAAK,CAAC,EAAE,CAAC;IACX;AACF;AAEA,aAAa,SAAS,CAAC,KAAK,GAAG,qOAAA,CAAA,UAAW;uCAE3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5458, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/presets/default.mjs"], "sourcesContent": ["// markdown-it default options\n\nexport default {\n  options: {\n    // Enable HTML tags in source\n    html: false,\n\n    // Use '/' to close single tags (<br />)\n    xhtmlOut: false,\n\n    // Convert '\\n' in paragraphs into <br>\n    breaks: false,\n\n    // CSS language prefix for fenced blocks\n    langPrefix: 'language-',\n\n    // autoconvert URL-like texts to links\n    linkify: false,\n\n    // Enable some language-neutral replacements + quotes beautification\n    typographer: false,\n\n    // Double + single quotes replacement pairs, when typographer enabled,\n    // and smartquotes on. Could be either a String or an Array.\n    //\n    // For example, you can use '«»„“' for Russian, '„“‚‘' for German,\n    // and ['«\\xA0', '\\xA0»', '‹\\xA0', '\\xA0›'] for French (including nbsp).\n    quotes: '\\u201c\\u201d\\u2018\\u2019', /* “”‘’ */\n\n    // Highlighter function. Should return escaped HTML,\n    // or '' if the source string is not changed and should be escaped externaly.\n    // If result starts with <pre... internal wrapper is skipped.\n    //\n    // function (/*str, lang*/) { return ''; }\n    //\n    highlight: null,\n\n    // Internal protection, recursion limit\n    maxNesting: 100\n  },\n\n  components: {\n    core: {},\n    block: {},\n    inline: {}\n  }\n}\n"], "names": [], "mappings": "AAAA,8BAA8B;;;;uCAEf;IACb,SAAS;QACP,6BAA6B;QAC7B,MAAM;QAEN,wCAAwC;QACxC,UAAU;QAEV,uCAAuC;QACvC,QAAQ;QAER,wCAAwC;QACxC,YAAY;QAEZ,sCAAsC;QACtC,SAAS;QAET,oEAAoE;QACpE,aAAa;QAEb,sEAAsE;QACtE,4DAA4D;QAC5D,EAAE;QACF,kEAAkE;QAClE,wEAAwE;QACxE,QAAQ;QAA4B,QAAQ,GAE5C,oDAAoD;QACpD,6EAA6E;QAC7E,6DAA6D;QAC7D,EAAE;QACF,0CAA0C;QAC1C,EAAE;QACF,WAAW;QAEX,uCAAuC;QACvC,YAAY;IACd;IAEA,YAAY;QACV,MAAM,CAAC;QACP,OAAO,CAAC;QACR,QAAQ,CAAC;IACX;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5504, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/presets/zero.mjs"], "sourcesContent": ["// \"Zero\" preset, with nothing enabled. Useful for manual configuring of simple\n// modes. For example, to parse bold/italic only.\n\nexport default {\n  options: {\n    // Enable HTML tags in source\n    html: false,\n\n    // Use '/' to close single tags (<br />)\n    xhtmlOut: false,\n\n    // Convert '\\n' in paragraphs into <br>\n    breaks: false,\n\n    // CSS language prefix for fenced blocks\n    langPrefix: 'language-',\n\n    // autoconvert URL-like texts to links\n    linkify: false,\n\n    // Enable some language-neutral replacements + quotes beautification\n    typographer: false,\n\n    // Double + single quotes replacement pairs, when typographer enabled,\n    // and smartquotes on. Could be either a String or an Array.\n    //\n    // For example, you can use '«»„“' for Russian, '„“‚‘' for German,\n    // and ['«\\xA0', '\\xA0»', '‹\\xA0', '\\xA0›'] for French (including nbsp).\n    quotes: '\\u201c\\u201d\\u2018\\u2019', /* “”‘’ */\n\n    // Highlighter function. Should return escaped HTML,\n    // or '' if the source string is not changed and should be escaped externaly.\n    // If result starts with <pre... internal wrapper is skipped.\n    //\n    // function (/*str, lang*/) { return ''; }\n    //\n    highlight: null,\n\n    // Internal protection, recursion limit\n    maxNesting: 20\n  },\n\n  components: {\n\n    core: {\n      rules: [\n        'normalize',\n        'block',\n        'inline',\n        'text_join'\n      ]\n    },\n\n    block: {\n      rules: [\n        'paragraph'\n      ]\n    },\n\n    inline: {\n      rules: [\n        'text'\n      ],\n      rules2: [\n        'balance_pairs',\n        'fragments_join'\n      ]\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA,+EAA+E;AAC/E,iDAAiD;;;;uCAElC;IACb,SAAS;QACP,6BAA6B;QAC7B,MAAM;QAEN,wCAAwC;QACxC,UAAU;QAEV,uCAAuC;QACvC,QAAQ;QAER,wCAAwC;QACxC,YAAY;QAEZ,sCAAsC;QACtC,SAAS;QAET,oEAAoE;QACpE,aAAa;QAEb,sEAAsE;QACtE,4DAA4D;QAC5D,EAAE;QACF,kEAAkE;QAClE,wEAAwE;QACxE,QAAQ;QAA4B,QAAQ,GAE5C,oDAAoD;QACpD,6EAA6E;QAC7E,6DAA6D;QAC7D,EAAE;QACF,0CAA0C;QAC1C,EAAE;QACF,WAAW;QAEX,uCAAuC;QACvC,YAAY;IACd;IAEA,YAAY;QAEV,MAAM;YACJ,OAAO;gBACL;gBACA;gBACA;gBACA;aACD;QACH;QAEA,OAAO;YACL,OAAO;gBACL;aACD;QACH;QAEA,QAAQ;YACN,OAAO;gBACL;aACD;YACD,QAAQ;gBACN;gBACA;aACD;QACH;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5570, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/presets/commonmark.mjs"], "sourcesContent": ["// Commonmark default options\n\nexport default {\n  options: {\n    // Enable HTML tags in source\n    html: true,\n\n    // Use '/' to close single tags (<br />)\n    xhtmlOut: true,\n\n    // Convert '\\n' in paragraphs into <br>\n    breaks: false,\n\n    // CSS language prefix for fenced blocks\n    langPrefix: 'language-',\n\n    // autoconvert URL-like texts to links\n    linkify: false,\n\n    // Enable some language-neutral replacements + quotes beautification\n    typographer: false,\n\n    // Double + single quotes replacement pairs, when typographer enabled,\n    // and smartquotes on. Could be either a String or an Array.\n    //\n    // For example, you can use '«»„“' for Russian, '„“‚‘' for German,\n    // and ['«\\xA0', '\\xA0»', '‹\\xA0', '\\xA0›'] for French (including nbsp).\n    quotes: '\\u201c\\u201d\\u2018\\u2019', /* “”‘’ */\n\n    // Highlighter function. Should return escaped HTML,\n    // or '' if the source string is not changed and should be escaped externaly.\n    // If result starts with <pre... internal wrapper is skipped.\n    //\n    // function (/*str, lang*/) { return ''; }\n    //\n    highlight: null,\n\n    // Internal protection, recursion limit\n    maxNesting: 20\n  },\n\n  components: {\n\n    core: {\n      rules: [\n        'normalize',\n        'block',\n        'inline',\n        'text_join'\n      ]\n    },\n\n    block: {\n      rules: [\n        'blockquote',\n        'code',\n        'fence',\n        'heading',\n        'hr',\n        'html_block',\n        'lheading',\n        'list',\n        'reference',\n        'paragraph'\n      ]\n    },\n\n    inline: {\n      rules: [\n        'autolink',\n        'backticks',\n        'emphasis',\n        'entity',\n        'escape',\n        'html_inline',\n        'image',\n        'link',\n        'newline',\n        'text'\n      ],\n      rules2: [\n        'balance_pairs',\n        'emphasis',\n        'fragments_join'\n      ]\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA,6BAA6B;;;;uCAEd;IACb,SAAS;QACP,6BAA6B;QAC7B,MAAM;QAEN,wCAAwC;QACxC,UAAU;QAEV,uCAAuC;QACvC,QAAQ;QAER,wCAAwC;QACxC,YAAY;QAEZ,sCAAsC;QACtC,SAAS;QAET,oEAAoE;QACpE,aAAa;QAEb,sEAAsE;QACtE,4DAA4D;QAC5D,EAAE;QACF,kEAAkE;QAClE,wEAAwE;QACxE,QAAQ;QAA4B,QAAQ,GAE5C,oDAAoD;QACpD,6EAA6E;QAC7E,6DAA6D;QAC7D,EAAE;QACF,0CAA0C;QAC1C,EAAE;QACF,WAAW;QAEX,uCAAuC;QACvC,YAAY;IACd;IAEA,YAAY;QAEV,MAAM;YACJ,OAAO;gBACL;gBACA;gBACA;gBACA;aACD;QACH;QAEA,OAAO;YACL,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QAEA,QAAQ;YACN,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,QAAQ;gBACN;gBACA;gBACA;aACD;QACH;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5654, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/lib/index.mjs"], "sourcesContent": ["// Main parser class\n\nimport * as utils from './common/utils.mjs'\nimport * as helpers from './helpers/index.mjs'\nimport Renderer from './renderer.mjs'\nimport ParserCore from './parser_core.mjs'\nimport ParserBlock from './parser_block.mjs'\nimport ParserInline from './parser_inline.mjs'\nimport LinkifyIt from 'linkify-it'\nimport * as mdurl from 'mdurl'\nimport punycode from 'punycode.js'\n\nimport cfg_default from './presets/default.mjs'\nimport cfg_zero from './presets/zero.mjs'\nimport cfg_commonmark from './presets/commonmark.mjs'\n\nconst config = {\n  default: cfg_default,\n  zero: cfg_zero,\n  commonmark: cfg_commonmark\n}\n\n//\n// This validator can prohibit more than really needed to prevent XSS. It's a\n// tradeoff to keep code simple and to be secure by default.\n//\n// If you need different setup - override validator method as you wish. Or\n// replace it with dummy function and use external sanitizer.\n//\n\nconst BAD_PROTO_RE = /^(vbscript|javascript|file|data):/\nconst GOOD_DATA_RE = /^data:image\\/(gif|png|jpeg|webp);/\n\nfunction validateLink (url) {\n  // url should be normalized at this point, and existing entities are decoded\n  const str = url.trim().toLowerCase()\n\n  return BAD_PROTO_RE.test(str) ? GOOD_DATA_RE.test(str) : true\n}\n\nconst RECODE_HOSTNAME_FOR = ['http:', 'https:', 'mailto:']\n\nfunction normalizeLink (url) {\n  const parsed = mdurl.parse(url, true)\n\n  if (parsed.hostname) {\n    // Encode hostnames in urls like:\n    // `http://host/`, `https://host/`, `mailto:user@host`, `//host/`\n    //\n    // We don't encode unknown schemas, because it's likely that we encode\n    // something we shouldn't (e.g. `skype:name` treated as `skype:host`)\n    //\n    if (!parsed.protocol || RECODE_HOSTNAME_FOR.indexOf(parsed.protocol) >= 0) {\n      try {\n        parsed.hostname = punycode.toASCII(parsed.hostname)\n      } catch (er) { /**/ }\n    }\n  }\n\n  return mdurl.encode(mdurl.format(parsed))\n}\n\nfunction normalizeLinkText (url) {\n  const parsed = mdurl.parse(url, true)\n\n  if (parsed.hostname) {\n    // Encode hostnames in urls like:\n    // `http://host/`, `https://host/`, `mailto:user@host`, `//host/`\n    //\n    // We don't encode unknown schemas, because it's likely that we encode\n    // something we shouldn't (e.g. `skype:name` treated as `skype:host`)\n    //\n    if (!parsed.protocol || RECODE_HOSTNAME_FOR.indexOf(parsed.protocol) >= 0) {\n      try {\n        parsed.hostname = punycode.toUnicode(parsed.hostname)\n      } catch (er) { /**/ }\n    }\n  }\n\n  // add '%' to exclude list because of https://github.com/markdown-it/markdown-it/issues/720\n  return mdurl.decode(mdurl.format(parsed), mdurl.decode.defaultChars + '%')\n}\n\n/**\n * class MarkdownIt\n *\n * Main parser/renderer class.\n *\n * ##### Usage\n *\n * ```javascript\n * // node.js, \"classic\" way:\n * var MarkdownIt = require('markdown-it'),\n *     md = new MarkdownIt();\n * var result = md.render('# markdown-it rulezz!');\n *\n * // node.js, the same, but with sugar:\n * var md = require('markdown-it')();\n * var result = md.render('# markdown-it rulezz!');\n *\n * // browser without AMD, added to \"window\" on script load\n * // Note, there are no dash.\n * var md = window.markdownit();\n * var result = md.render('# markdown-it rulezz!');\n * ```\n *\n * Single line rendering, without paragraph wrap:\n *\n * ```javascript\n * var md = require('markdown-it')();\n * var result = md.renderInline('__markdown-it__ rulezz!');\n * ```\n **/\n\n/**\n * new MarkdownIt([presetName, options])\n * - presetName (String): optional, `commonmark` / `zero`\n * - options (Object)\n *\n * Creates parser instanse with given config. Can be called without `new`.\n *\n * ##### presetName\n *\n * MarkdownIt provides named presets as a convenience to quickly\n * enable/disable active syntax rules and options for common use cases.\n *\n * - [\"commonmark\"](https://github.com/markdown-it/markdown-it/blob/master/lib/presets/commonmark.mjs) -\n *   configures parser to strict [CommonMark](http://commonmark.org/) mode.\n * - [default](https://github.com/markdown-it/markdown-it/blob/master/lib/presets/default.mjs) -\n *   similar to GFM, used when no preset name given. Enables all available rules,\n *   but still without html, typographer & autolinker.\n * - [\"zero\"](https://github.com/markdown-it/markdown-it/blob/master/lib/presets/zero.mjs) -\n *   all rules disabled. Useful to quickly setup your config via `.enable()`.\n *   For example, when you need only `bold` and `italic` markup and nothing else.\n *\n * ##### options:\n *\n * - __html__ - `false`. Set `true` to enable HTML tags in source. Be careful!\n *   That's not safe! You may need external sanitizer to protect output from XSS.\n *   It's better to extend features via plugins, instead of enabling HTML.\n * - __xhtmlOut__ - `false`. Set `true` to add '/' when closing single tags\n *   (`<br />`). This is needed only for full CommonMark compatibility. In real\n *   world you will need HTML output.\n * - __breaks__ - `false`. Set `true` to convert `\\n` in paragraphs into `<br>`.\n * - __langPrefix__ - `language-`. CSS language class prefix for fenced blocks.\n *   Can be useful for external highlighters.\n * - __linkify__ - `false`. Set `true` to autoconvert URL-like text to links.\n * - __typographer__  - `false`. Set `true` to enable [some language-neutral\n *   replacement](https://github.com/markdown-it/markdown-it/blob/master/lib/rules_core/replacements.mjs) +\n *   quotes beautification (smartquotes).\n * - __quotes__ - `“”‘’`, String or Array. Double + single quotes replacement\n *   pairs, when typographer enabled and smartquotes on. For example, you can\n *   use `'«»„“'` for Russian, `'„“‚‘'` for German, and\n *   `['«\\xA0', '\\xA0»', '‹\\xA0', '\\xA0›']` for French (including nbsp).\n * - __highlight__ - `null`. Highlighter function for fenced code blocks.\n *   Highlighter `function (str, lang)` should return escaped HTML. It can also\n *   return empty string if the source was not changed and should be escaped\n *   externaly. If result starts with <pre... internal wrapper is skipped.\n *\n * ##### Example\n *\n * ```javascript\n * // commonmark mode\n * var md = require('markdown-it')('commonmark');\n *\n * // default mode\n * var md = require('markdown-it')();\n *\n * // enable everything\n * var md = require('markdown-it')({\n *   html: true,\n *   linkify: true,\n *   typographer: true\n * });\n * ```\n *\n * ##### Syntax highlighting\n *\n * ```js\n * var hljs = require('highlight.js') // https://highlightjs.org/\n *\n * var md = require('markdown-it')({\n *   highlight: function (str, lang) {\n *     if (lang && hljs.getLanguage(lang)) {\n *       try {\n *         return hljs.highlight(str, { language: lang, ignoreIllegals: true }).value;\n *       } catch (__) {}\n *     }\n *\n *     return ''; // use external default escaping\n *   }\n * });\n * ```\n *\n * Or with full wrapper override (if you need assign class to `<pre>` or `<code>`):\n *\n * ```javascript\n * var hljs = require('highlight.js') // https://highlightjs.org/\n *\n * // Actual default values\n * var md = require('markdown-it')({\n *   highlight: function (str, lang) {\n *     if (lang && hljs.getLanguage(lang)) {\n *       try {\n *         return '<pre><code class=\"hljs\">' +\n *                hljs.highlight(str, { language: lang, ignoreIllegals: true }).value +\n *                '</code></pre>';\n *       } catch (__) {}\n *     }\n *\n *     return '<pre><code class=\"hljs\">' + md.utils.escapeHtml(str) + '</code></pre>';\n *   }\n * });\n * ```\n *\n **/\nfunction MarkdownIt (presetName, options) {\n  if (!(this instanceof MarkdownIt)) {\n    return new MarkdownIt(presetName, options)\n  }\n\n  if (!options) {\n    if (!utils.isString(presetName)) {\n      options = presetName || {}\n      presetName = 'default'\n    }\n  }\n\n  /**\n   * MarkdownIt#inline -> ParserInline\n   *\n   * Instance of [[ParserInline]]. You may need it to add new rules when\n   * writing plugins. For simple rules control use [[MarkdownIt.disable]] and\n   * [[MarkdownIt.enable]].\n   **/\n  this.inline = new ParserInline()\n\n  /**\n   * MarkdownIt#block -> ParserBlock\n   *\n   * Instance of [[ParserBlock]]. You may need it to add new rules when\n   * writing plugins. For simple rules control use [[MarkdownIt.disable]] and\n   * [[MarkdownIt.enable]].\n   **/\n  this.block = new ParserBlock()\n\n  /**\n   * MarkdownIt#core -> Core\n   *\n   * Instance of [[Core]] chain executor. You may need it to add new rules when\n   * writing plugins. For simple rules control use [[MarkdownIt.disable]] and\n   * [[MarkdownIt.enable]].\n   **/\n  this.core = new ParserCore()\n\n  /**\n   * MarkdownIt#renderer -> Renderer\n   *\n   * Instance of [[Renderer]]. Use it to modify output look. Or to add rendering\n   * rules for new token types, generated by plugins.\n   *\n   * ##### Example\n   *\n   * ```javascript\n   * var md = require('markdown-it')();\n   *\n   * function myToken(tokens, idx, options, env, self) {\n   *   //...\n   *   return result;\n   * };\n   *\n   * md.renderer.rules['my_token'] = myToken\n   * ```\n   *\n   * See [[Renderer]] docs and [source code](https://github.com/markdown-it/markdown-it/blob/master/lib/renderer.mjs).\n   **/\n  this.renderer = new Renderer()\n\n  /**\n   * MarkdownIt#linkify -> LinkifyIt\n   *\n   * [linkify-it](https://github.com/markdown-it/linkify-it) instance.\n   * Used by [linkify](https://github.com/markdown-it/markdown-it/blob/master/lib/rules_core/linkify.mjs)\n   * rule.\n   **/\n  this.linkify = new LinkifyIt()\n\n  /**\n   * MarkdownIt#validateLink(url) -> Boolean\n   *\n   * Link validation function. CommonMark allows too much in links. By default\n   * we disable `javascript:`, `vbscript:`, `file:` schemas, and almost all `data:...` schemas\n   * except some embedded image types.\n   *\n   * You can change this behaviour:\n   *\n   * ```javascript\n   * var md = require('markdown-it')();\n   * // enable everything\n   * md.validateLink = function () { return true; }\n   * ```\n   **/\n  this.validateLink = validateLink\n\n  /**\n   * MarkdownIt#normalizeLink(url) -> String\n   *\n   * Function used to encode link url to a machine-readable format,\n   * which includes url-encoding, punycode, etc.\n   **/\n  this.normalizeLink = normalizeLink\n\n  /**\n   * MarkdownIt#normalizeLinkText(url) -> String\n   *\n   * Function used to decode link url to a human-readable format`\n   **/\n  this.normalizeLinkText = normalizeLinkText\n\n  // Expose utils & helpers for easy acces from plugins\n\n  /**\n   * MarkdownIt#utils -> utils\n   *\n   * Assorted utility functions, useful to write plugins. See details\n   * [here](https://github.com/markdown-it/markdown-it/blob/master/lib/common/utils.mjs).\n   **/\n  this.utils = utils\n\n  /**\n   * MarkdownIt#helpers -> helpers\n   *\n   * Link components parser functions, useful to write plugins. See details\n   * [here](https://github.com/markdown-it/markdown-it/blob/master/lib/helpers).\n   **/\n  this.helpers = utils.assign({}, helpers)\n\n  this.options = {}\n  this.configure(presetName)\n\n  if (options) { this.set(options) }\n}\n\n/** chainable\n * MarkdownIt.set(options)\n *\n * Set parser options (in the same format as in constructor). Probably, you\n * will never need it, but you can change options after constructor call.\n *\n * ##### Example\n *\n * ```javascript\n * var md = require('markdown-it')()\n *             .set({ html: true, breaks: true })\n *             .set({ typographer, true });\n * ```\n *\n * __Note:__ To achieve the best possible performance, don't modify a\n * `markdown-it` instance options on the fly. If you need multiple configurations\n * it's best to create multiple instances and initialize each with separate\n * config.\n **/\nMarkdownIt.prototype.set = function (options) {\n  utils.assign(this.options, options)\n  return this\n}\n\n/** chainable, internal\n * MarkdownIt.configure(presets)\n *\n * Batch load of all options and compenent settings. This is internal method,\n * and you probably will not need it. But if you will - see available presets\n * and data structure [here](https://github.com/markdown-it/markdown-it/tree/master/lib/presets)\n *\n * We strongly recommend to use presets instead of direct config loads. That\n * will give better compatibility with next versions.\n **/\nMarkdownIt.prototype.configure = function (presets) {\n  const self = this\n\n  if (utils.isString(presets)) {\n    const presetName = presets\n    presets = config[presetName]\n    if (!presets) { throw new Error('Wrong `markdown-it` preset \"' + presetName + '\", check name') }\n  }\n\n  if (!presets) { throw new Error('Wrong `markdown-it` preset, can\\'t be empty') }\n\n  if (presets.options) { self.set(presets.options) }\n\n  if (presets.components) {\n    Object.keys(presets.components).forEach(function (name) {\n      if (presets.components[name].rules) {\n        self[name].ruler.enableOnly(presets.components[name].rules)\n      }\n      if (presets.components[name].rules2) {\n        self[name].ruler2.enableOnly(presets.components[name].rules2)\n      }\n    })\n  }\n  return this\n}\n\n/** chainable\n * MarkdownIt.enable(list, ignoreInvalid)\n * - list (String|Array): rule name or list of rule names to enable\n * - ignoreInvalid (Boolean): set `true` to ignore errors when rule not found.\n *\n * Enable list or rules. It will automatically find appropriate components,\n * containing rules with given names. If rule not found, and `ignoreInvalid`\n * not set - throws exception.\n *\n * ##### Example\n *\n * ```javascript\n * var md = require('markdown-it')()\n *             .enable(['sub', 'sup'])\n *             .disable('smartquotes');\n * ```\n **/\nMarkdownIt.prototype.enable = function (list, ignoreInvalid) {\n  let result = []\n\n  if (!Array.isArray(list)) { list = [list] }\n\n  ['core', 'block', 'inline'].forEach(function (chain) {\n    result = result.concat(this[chain].ruler.enable(list, true))\n  }, this)\n\n  result = result.concat(this.inline.ruler2.enable(list, true))\n\n  const missed = list.filter(function (name) { return result.indexOf(name) < 0 })\n\n  if (missed.length && !ignoreInvalid) {\n    throw new Error('MarkdownIt. Failed to enable unknown rule(s): ' + missed)\n  }\n\n  return this\n}\n\n/** chainable\n * MarkdownIt.disable(list, ignoreInvalid)\n * - list (String|Array): rule name or list of rule names to disable.\n * - ignoreInvalid (Boolean): set `true` to ignore errors when rule not found.\n *\n * The same as [[MarkdownIt.enable]], but turn specified rules off.\n **/\nMarkdownIt.prototype.disable = function (list, ignoreInvalid) {\n  let result = []\n\n  if (!Array.isArray(list)) { list = [list] }\n\n  ['core', 'block', 'inline'].forEach(function (chain) {\n    result = result.concat(this[chain].ruler.disable(list, true))\n  }, this)\n\n  result = result.concat(this.inline.ruler2.disable(list, true))\n\n  const missed = list.filter(function (name) { return result.indexOf(name) < 0 })\n\n  if (missed.length && !ignoreInvalid) {\n    throw new Error('MarkdownIt. Failed to disable unknown rule(s): ' + missed)\n  }\n  return this\n}\n\n/** chainable\n * MarkdownIt.use(plugin, params)\n *\n * Load specified plugin with given params into current parser instance.\n * It's just a sugar to call `plugin(md, params)` with curring.\n *\n * ##### Example\n *\n * ```javascript\n * var iterator = require('markdown-it-for-inline');\n * var md = require('markdown-it')()\n *             .use(iterator, 'foo_replace', 'text', function (tokens, idx) {\n *               tokens[idx].content = tokens[idx].content.replace(/foo/g, 'bar');\n *             });\n * ```\n **/\nMarkdownIt.prototype.use = function (plugin /*, params, ... */) {\n  const args = [this].concat(Array.prototype.slice.call(arguments, 1))\n  plugin.apply(plugin, args)\n  return this\n}\n\n/** internal\n * MarkdownIt.parse(src, env) -> Array\n * - src (String): source string\n * - env (Object): environment sandbox\n *\n * Parse input string and return list of block tokens (special token type\n * \"inline\" will contain list of inline tokens). You should not call this\n * method directly, until you write custom renderer (for example, to produce\n * AST).\n *\n * `env` is used to pass data between \"distributed\" rules and return additional\n * metadata like reference info, needed for the renderer. It also can be used to\n * inject data in specific cases. Usually, you will be ok to pass `{}`,\n * and then pass updated object to renderer.\n **/\nMarkdownIt.prototype.parse = function (src, env) {\n  if (typeof src !== 'string') {\n    throw new Error('Input data should be a String')\n  }\n\n  const state = new this.core.State(src, this, env)\n\n  this.core.process(state)\n\n  return state.tokens\n}\n\n/**\n * MarkdownIt.render(src [, env]) -> String\n * - src (String): source string\n * - env (Object): environment sandbox\n *\n * Render markdown string into html. It does all magic for you :).\n *\n * `env` can be used to inject additional metadata (`{}` by default).\n * But you will not need it with high probability. See also comment\n * in [[MarkdownIt.parse]].\n **/\nMarkdownIt.prototype.render = function (src, env) {\n  env = env || {}\n\n  return this.renderer.render(this.parse(src, env), this.options, env)\n}\n\n/** internal\n * MarkdownIt.parseInline(src, env) -> Array\n * - src (String): source string\n * - env (Object): environment sandbox\n *\n * The same as [[MarkdownIt.parse]] but skip all block rules. It returns the\n * block tokens list with the single `inline` element, containing parsed inline\n * tokens in `children` property. Also updates `env` object.\n **/\nMarkdownIt.prototype.parseInline = function (src, env) {\n  const state = new this.core.State(src, this, env)\n\n  state.inlineMode = true\n  this.core.process(state)\n\n  return state.tokens\n}\n\n/**\n * MarkdownIt.renderInline(src [, env]) -> String\n * - src (String): source string\n * - env (Object): environment sandbox\n *\n * Similar to [[MarkdownIt.render]] but for single paragraph content. Result\n * will NOT be wrapped into `<p>` tags.\n **/\nMarkdownIt.prototype.renderInline = function (src, env) {\n  env = env || {}\n\n  return this.renderer.render(this.parseInline(src, env), this.options, env)\n}\n\nexport default MarkdownIt\n"], "names": [], "mappings": "AAAA,oBAAoB;;;;AAEpB;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;;;;;;;;;;;;;AAEA,MAAM,SAAS;IACb,SAAS,2NAAA,CAAA,UAAW;IACpB,MAAM,wNAAA,CAAA,UAAQ;IACd,YAAY,8NAAA,CAAA,UAAc;AAC5B;AAEA,EAAE;AACF,6EAA6E;AAC7E,4DAA4D;AAC5D,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,EAAE;AAEF,MAAM,eAAe;AACrB,MAAM,eAAe;AAErB,SAAS,aAAc,GAAG;IACxB,4EAA4E;IAC5E,MAAM,MAAM,IAAI,IAAI,GAAG,WAAW;IAElC,OAAO,aAAa,IAAI,CAAC,OAAO,aAAa,IAAI,CAAC,OAAO;AAC3D;AAEA,MAAM,sBAAsB;IAAC;IAAS;IAAU;CAAU;AAE1D,SAAS,cAAe,GAAG;IACzB,MAAM,SAAS,CAAA,GAAA,+NAAA,CAAA,QAAW,AAAD,EAAE,KAAK;IAEhC,IAAI,OAAO,QAAQ,EAAE;QACnB,iCAAiC;QACjC,iEAAiE;QACjE,EAAE;QACF,sEAAsE;QACtE,qEAAqE;QACrE,EAAE;QACF,IAAI,CAAC,OAAO,QAAQ,IAAI,oBAAoB,OAAO,CAAC,OAAO,QAAQ,KAAK,GAAG;YACzE,IAAI;gBACF,OAAO,QAAQ,GAAG,+MAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,OAAO,QAAQ;YACpD,EAAE,OAAO,IAAI,CAAO;QACtB;IACF;IAEA,OAAO,CAAA,GAAA,iOAAA,CAAA,SAAY,AAAD,EAAE,CAAA,GAAA,iOAAA,CAAA,SAAY,AAAD,EAAE;AACnC;AAEA,SAAS,kBAAmB,GAAG;IAC7B,MAAM,SAAS,CAAA,GAAA,+NAAA,CAAA,QAAW,AAAD,EAAE,KAAK;IAEhC,IAAI,OAAO,QAAQ,EAAE;QACnB,iCAAiC;QACjC,iEAAiE;QACjE,EAAE;QACF,sEAAsE;QACtE,qEAAqE;QACrE,EAAE;QACF,IAAI,CAAC,OAAO,QAAQ,IAAI,oBAAoB,OAAO,CAAC,OAAO,QAAQ,KAAK,GAAG;YACzE,IAAI;gBACF,OAAO,QAAQ,GAAG,+MAAA,CAAA,UAAQ,CAAC,SAAS,CAAC,OAAO,QAAQ;YACtD,EAAE,OAAO,IAAI,CAAO;QACtB;IACF;IAEA,2FAA2F;IAC3F,OAAO,CAAA,GAAA,iOAAA,CAAA,SAAY,AAAD,EAAE,CAAA,GAAA,iOAAA,CAAA,SAAY,AAAD,EAAE,SAAS,iOAAA,CAAA,SAAY,CAAC,YAAY,GAAG;AACxE;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA6BE,GAEF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAqGE,GACF,SAAS,WAAY,UAAU,EAAE,OAAO;IACtC,IAAI,CAAC,CAAC,IAAI,YAAY,UAAU,GAAG;QACjC,OAAO,IAAI,WAAW,YAAY;IACpC;IAEA,IAAI,CAAC,SAAS;QACZ,IAAI,CAAC,yNAAM,QAAQ,CAAC,aAAa;YAC/B,UAAU,cAAc,CAAC;YACzB,aAAa;QACf;IACF;IAEA;;;;;;IAME,GACF,IAAI,CAAC,MAAM,GAAG,IAAI,sNAAA,CAAA,UAAY;IAE9B;;;;;;IAME,GACF,IAAI,CAAC,KAAK,GAAG,IAAI,qNAAA,CAAA,UAAW;IAE5B;;;;;;IAME,GACF,IAAI,CAAC,IAAI,GAAG,IAAI,oNAAA,CAAA,UAAU;IAE1B;;;;;;;;;;;;;;;;;;;;IAoBE,GACF,IAAI,CAAC,QAAQ,GAAG,IAAI,iNAAA,CAAA,UAAQ;IAE5B;;;;;;IAME,GACF,IAAI,CAAC,OAAO,GAAG,IAAI,oMAAA,CAAA,UAAS;IAE5B;;;;;;;;;;;;;;IAcE,GACF,IAAI,CAAC,YAAY,GAAG;IAEpB;;;;;IAKE,GACF,IAAI,CAAC,aAAa,GAAG;IAErB;;;;IAIE,GACF,IAAI,CAAC,iBAAiB,GAAG;IAEzB,qDAAqD;IAErD;;;;;IAKE,GACF,IAAI,CAAC,KAAK,GAAG;IAEb;;;;;IAKE,GACF,IAAI,CAAC,OAAO,GAAG,yNAAM,MAAM,CAAC,CAAC,GAAG;IAEhC,IAAI,CAAC,OAAO,GAAG,CAAC;IAChB,IAAI,CAAC,SAAS,CAAC;IAEf,IAAI,SAAS;QAAE,IAAI,CAAC,GAAG,CAAC;IAAS;AACnC;AAEA;;;;;;;;;;;;;;;;;;EAkBE,GACF,WAAW,SAAS,CAAC,GAAG,GAAG,SAAU,OAAO;IAC1C,yNAAM,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE;IAC3B,OAAO,IAAI;AACb;AAEA;;;;;;;;;EASE,GACF,WAAW,SAAS,CAAC,SAAS,GAAG,SAAU,OAAO;IAChD,MAAM,OAAO,IAAI;IAEjB,IAAI,yNAAM,QAAQ,CAAC,UAAU;QAC3B,MAAM,aAAa;QACnB,UAAU,MAAM,CAAC,WAAW;QAC5B,IAAI,CAAC,SAAS;YAAE,MAAM,IAAI,MAAM,iCAAiC,aAAa;QAAiB;IACjG;IAEA,IAAI,CAAC,SAAS;QAAE,MAAM,IAAI,MAAM;IAA+C;IAE/E,IAAI,QAAQ,OAAO,EAAE;QAAE,KAAK,GAAG,CAAC,QAAQ,OAAO;IAAE;IAEjD,IAAI,QAAQ,UAAU,EAAE;QACtB,OAAO,IAAI,CAAC,QAAQ,UAAU,EAAE,OAAO,CAAC,SAAU,IAAI;YACpD,IAAI,QAAQ,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE;gBAClC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,UAAU,CAAC,KAAK,CAAC,KAAK;YAC5D;YACA,IAAI,QAAQ,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE;gBACnC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,UAAU,CAAC,KAAK,CAAC,MAAM;YAC9D;QACF;IACF;IACA,OAAO,IAAI;AACb;AAEA;;;;;;;;;;;;;;;;EAgBE,GACF,WAAW,SAAS,CAAC,MAAM,GAAG,SAAU,IAAI,EAAE,aAAa;IACzD,IAAI,SAAS,EAAE;IAEf,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;QAAE,OAAO;YAAC;SAAK;IAAC;IAE1C;QAAC;QAAQ;QAAS;KAAS,CAAC,OAAO,CAAC,SAAU,KAAK;QACjD,SAAS,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM;IACxD,GAAG,IAAI;IAEP,SAAS,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM;IAEvD,MAAM,SAAS,KAAK,MAAM,CAAC,SAAU,IAAI;QAAI,OAAO,OAAO,OAAO,CAAC,QAAQ;IAAE;IAE7E,IAAI,OAAO,MAAM,IAAI,CAAC,eAAe;QACnC,MAAM,IAAI,MAAM,mDAAmD;IACrE;IAEA,OAAO,IAAI;AACb;AAEA;;;;;;EAME,GACF,WAAW,SAAS,CAAC,OAAO,GAAG,SAAU,IAAI,EAAE,aAAa;IAC1D,IAAI,SAAS,EAAE;IAEf,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;QAAE,OAAO;YAAC;SAAK;IAAC;IAE1C;QAAC;QAAQ;QAAS;KAAS,CAAC,OAAO,CAAC,SAAU,KAAK;QACjD,SAAS,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM;IACzD,GAAG,IAAI;IAEP,SAAS,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM;IAExD,MAAM,SAAS,KAAK,MAAM,CAAC,SAAU,IAAI;QAAI,OAAO,OAAO,OAAO,CAAC,QAAQ;IAAE;IAE7E,IAAI,OAAO,MAAM,IAAI,CAAC,eAAe;QACnC,MAAM,IAAI,MAAM,oDAAoD;IACtE;IACA,OAAO,IAAI;AACb;AAEA;;;;;;;;;;;;;;;EAeE,GACF,WAAW,SAAS,CAAC,GAAG,GAAG,SAAU,OAAO,gBAAgB,GAAjB;IACzC,MAAM,OAAO;QAAC,IAAI;KAAC,CAAC,MAAM,CAAC,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW;IACjE,OAAO,KAAK,CAAC,QAAQ;IACrB,OAAO,IAAI;AACb;AAEA;;;;;;;;;;;;;;EAcE,GACF,WAAW,SAAS,CAAC,KAAK,GAAG,SAAU,GAAG,EAAE,GAAG;IAC7C,IAAI,OAAO,QAAQ,UAAU;QAC3B,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE;IAE7C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;IAElB,OAAO,MAAM,MAAM;AACrB;AAEA;;;;;;;;;;EAUE,GACF,WAAW,SAAS,CAAC,MAAM,GAAG,SAAU,GAAG,EAAE,GAAG;IAC9C,MAAM,OAAO,CAAC;IAEd,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,MAAM,IAAI,CAAC,OAAO,EAAE;AAClE;AAEA;;;;;;;;EAQE,GACF,WAAW,SAAS,CAAC,WAAW,GAAG,SAAU,GAAG,EAAE,GAAG;IACnD,MAAM,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE;IAE7C,MAAM,UAAU,GAAG;IACnB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;IAElB,OAAO,MAAM,MAAM;AACrB;AAEA;;;;;;;EAOE,GACF,WAAW,SAAS,CAAC,YAAY,GAAG,SAAU,GAAG,EAAE,GAAG;IACpD,MAAM,OAAO,CAAC;IAEd,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,MAAM,IAAI,CAAC,OAAO,EAAE;AACxE;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6196, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/markdown-it%4014.1.0/node_modules/markdown-it/index.mjs"], "sourcesContent": ["export { default } from './lib/index.mjs'\n"], "names": [], "mappings": ";AAAA", "ignoreList": [0], "debugId": null}}]}