/* [project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-replies.module.css [app-client] (css) */
.tweet-replies-module__gLz8CW__replies {
  padding: .25rem 0;
}

.tweet-replies-module__gLz8CW__link {
  color: var(--tweet-color-blue-secondary);
  user-select: none;
  border: var(--tweet-border);
  border-radius: 9999px;
  outline-style: none;
  justify-content: center;
  align-items: center;
  min-width: 32px;
  min-height: 32px;
  padding: 0 1rem;
  text-decoration: none;
  transition-property: background-color;
  transition-duration: .2s;
  display: flex;
}

.tweet-replies-module__gLz8CW__link:hover {
  background-color: var(--tweet-color-blue-secondary-hover);
}

.tweet-replies-module__gLz8CW__text {
  font-weight: var(--tweet-replies-font-weight);
  font-size: var(--tweet-replies-font-size);
  line-height: var(--tweet-replies-line-height);
  overflow-wrap: break-word;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

/*# sourceMappingURL=5aae6_react-tweet_dist_twitter-theme_tweet-replies_module_css_f9ee138c._.single.css.map*/