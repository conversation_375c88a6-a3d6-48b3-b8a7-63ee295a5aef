/* [project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.module.css [app-client] (css) */
.tweet-in-reply-to-module__Rykmaq__root {
  color: var(--tweet-font-color-secondary);
  overflow-wrap: break-word;
  white-space: pre-wrap;
  margin-bottom: .25rem;
  font-size: .9375rem;
  line-height: 1.25rem;
  text-decoration: none;
}

.tweet-in-reply-to-module__Rykmaq__root:hover {
  text-decoration-line: underline;
  text-decoration-thickness: 1px;
}

/*# sourceMappingURL=5aae6_react-tweet_dist_twitter-theme_tweet-in-reply-to_module_css_f9ee138c._.single.css.map*/