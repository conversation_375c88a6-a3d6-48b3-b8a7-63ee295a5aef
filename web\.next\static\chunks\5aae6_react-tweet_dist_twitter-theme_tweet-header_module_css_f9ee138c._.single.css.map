{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-header.module.css"], "sourcesContent": [".header {\n  display: flex;\n  padding-bottom: 0.75rem;\n  line-height: var(--tweet-header-line-height);\n  font-size: var(--tweet-header-font-size);\n  white-space: nowrap;\n  overflow-wrap: break-word;\n  overflow: hidden;\n}\n\n.avatar {\n  position: relative;\n  height: 48px;\n  width: 48px;\n}\n.avatarOverflow {\n  height: 100%;\n  width: 100%;\n  position: absolute;\n  overflow: hidden;\n  border-radius: 9999px;\n}\n.avatarSquare {\n  border-radius: 4px;\n}\n.avatarShadow {\n  height: 100%;\n  width: 100%;\n  transition-property: background-color;\n  transition-duration: 0.2s;\n  box-shadow: rgb(0 0 0 / 3%) 0px 0px 2px inset;\n}\n.avatarShadow:hover {\n  background-color: rgba(26, 26, 26, 0.15);\n}\n\n.author {\n  max-width: calc(100% - 84px);\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  margin: 0 0.5rem;\n}\n.authorLink {\n  text-decoration: none;\n  color: inherit;\n  display: flex;\n  align-items: center;\n}\n.authorLink:hover {\n  text-decoration-line: underline;\n}\n.authorVerified {\n  display: inline-flex;\n}\n.authorLinkText {\n  font-weight: 700;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  white-space: nowrap;\n}\n\n.authorMeta {\n  display: flex;\n}\n.authorFollow {\n  display: flex;\n}\n.username {\n  color: var(--tweet-font-color-secondary);\n  text-decoration: none;\n  text-overflow: ellipsis;\n}\n.follow {\n  color: var(--tweet-color-blue-secondary);\n  text-decoration: none;\n  font-weight: 700;\n}\n.follow:hover {\n  text-decoration-line: underline;\n}\n.separator {\n  padding: 0 0.25rem;\n}\n\n.brand {\n  margin-inline-start: auto;\n}\n\n.twitterIcon {\n  width: 23.75px;\n  height: 23.75px;\n  color: var(--tweet-twitter-icon-color);\n  fill: currentColor;\n  user-select: none;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;AAUA;;;;;;AAKA;;;;;;;;AAOA;;;;AAGA;;;;;;;;AAOA;;;;AAIA;;;;;;;;AAOA;;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;;;;AAOA;;;;AAMA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAIA;;;;AAIA", "ignoreList": [0]}}]}