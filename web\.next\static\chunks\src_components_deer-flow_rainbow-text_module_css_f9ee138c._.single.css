/* [project]/src/components/deer-flow/rainbow-text.module.css [app-client] (css) */
.rainbow-text-module__D-e5SG__animated {
  background: linear-gradient(to right, rgb(from var(--card-foreground) r g b / .3) 15%, rgb(from var(--card-foreground) r g b / .75) 35%, rgb(from var(--card-foreground) r g b / .75) 65%, rgb(from var(--card-foreground) r g b / .3) 85%);
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
  background-size: 500%;
  -webkit-background-clip: text;
  background-clip: text;
  animation: 2s ease-in-out infinite alternate rainbow-text-module__D-e5SG__textShine;
}

@keyframes rainbow-text-module__D-e5SG__textShine {
  0% {
    background-position: 0%;
  }

  100% {
    background-position: 100%;
  }
}

/*# sourceMappingURL=src_components_deer-flow_rainbow-text_module_css_f9ee138c._.single.css.map*/