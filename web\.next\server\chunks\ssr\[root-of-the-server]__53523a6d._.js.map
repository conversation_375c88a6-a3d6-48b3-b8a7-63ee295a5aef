{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/components/theme-provider.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ThemeProvider as NextThemesProvider } from \"next-themes\";\r\nimport * as React from \"react\";\r\n\r\nexport function ThemeProvider({\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof NextThemesProvider>) {\r\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAKO,SAAS,cAAc,EAC5B,QAAQ,EACR,GAAG,OAC6C;IAChD,qBAAO,uVAAC,yPAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC", "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/components/deer-flow/theme-provider-wrapper.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\n\"use client\";\r\n\r\nimport { usePathname } from \"next/navigation\";\r\n\r\nimport { ThemeProvider } from \"~/components/theme-provider\";\r\n\r\nexport function ThemeProviderWrapper({\r\n  children,\r\n}: {\r\n  children: React.ReactNode;\r\n}) {\r\n  const pathname = usePathname();\r\n  const isChatPage = pathname?.startsWith(\"/chat\");\r\n\r\n  return (\r\n    <ThemeProvider\r\n      attribute=\"class\"\r\n      defaultTheme={\"dark\"}\r\n      enableSystem={isChatPage}\r\n      forcedTheme={isChatPage ? undefined : \"dark\"}\r\n      disableTransitionOnChange\r\n    >\r\n      {children}\r\n    </ThemeProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAI/B;AAEA;AAJA;;;;AAMO,SAAS,qBAAqB,EACnC,QAAQ,EAGT;IACC,MAAM,WAAW,CAAA,GAAA,2OAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,aAAa,UAAU,WAAW;IAExC,qBACE,uVAAC,uIAAA,CAAA,gBAAa;QACZ,WAAU;QACV,cAAc;QACd,cAAc;QACd,aAAa,aAAa,YAAY;QACtC,yBAAyB;kBAExB;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/components/deer-flow/toaster.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\n\"use client\";\r\n\r\nimport { useTheme } from \"next-themes\";\r\nimport { Toaster as Sonner } from \"sonner\";\r\n\r\ntype ToasterProps = React.ComponentProps<typeof Sonner>;\r\n\r\nconst Toaster = ({ ...props }: ToasterProps) => {\r\n  const { resolvedTheme = \"dark\" } = useTheme();\r\n  return (\r\n    <Sonner\r\n      theme={resolvedTheme as ToasterProps[\"theme\"]}\r\n      className=\"toaster group\"\r\n      toastOptions={{\r\n        classNames: {\r\n          toast:\r\n            \"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg\",\r\n          description: \"group-[.toast]:text-muted-foreground\",\r\n          actionButton:\r\n            \"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground\",\r\n          cancelButton:\r\n            \"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground\",\r\n        },\r\n      }}\r\n      {...props}\r\n    />\r\n  );\r\n};\r\n\r\nexport { Toaster };\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAI/B;AACA;AAHA;;;;AAOA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;IACzC,MAAM,EAAE,gBAAgB,MAAM,EAAE,GAAG,CAAA,GAAA,yPAAA,CAAA,WAAQ,AAAD;IAC1C,qBACE,uVAAC,wQAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,cAAc;YACZ,YAAY;gBACV,OACE;gBACF,aAAa;gBACb,cACE;gBACF,cACE;YACJ;QACF;QACC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}]}