{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/lib/core.js"], "sourcesContent": ["/* eslint-disable no-multi-assign */\n\nfunction deepFreeze(obj) {\n  if (obj instanceof Map) {\n    obj.clear =\n      obj.delete =\n      obj.set =\n        function () {\n          throw new Error('map is read-only');\n        };\n  } else if (obj instanceof Set) {\n    obj.add =\n      obj.clear =\n      obj.delete =\n        function () {\n          throw new Error('set is read-only');\n        };\n  }\n\n  // Freeze self\n  Object.freeze(obj);\n\n  Object.getOwnPropertyNames(obj).forEach((name) => {\n    const prop = obj[name];\n    const type = typeof prop;\n\n    // Freeze prop if it is an object or function and also not already frozen\n    if ((type === 'object' || type === 'function') && !Object.isFrozen(prop)) {\n      deepFreeze(prop);\n    }\n  });\n\n  return obj;\n}\n\n/** @typedef {import('highlight.js').CallbackResponse} CallbackResponse */\n/** @typedef {import('highlight.js').CompiledMode} CompiledMode */\n/** @implements CallbackResponse */\n\nclass Response {\n  /**\n   * @param {CompiledMode} mode\n   */\n  constructor(mode) {\n    // eslint-disable-next-line no-undefined\n    if (mode.data === undefined) mode.data = {};\n\n    this.data = mode.data;\n    this.isMatchIgnored = false;\n  }\n\n  ignoreMatch() {\n    this.isMatchIgnored = true;\n  }\n}\n\n/**\n * @param {string} value\n * @returns {string}\n */\nfunction escapeHTML(value) {\n  return value\n    .replace(/&/g, '&amp;')\n    .replace(/</g, '&lt;')\n    .replace(/>/g, '&gt;')\n    .replace(/\"/g, '&quot;')\n    .replace(/'/g, '&#x27;');\n}\n\n/**\n * performs a shallow merge of multiple objects into one\n *\n * @template T\n * @param {T} original\n * @param {Record<string,any>[]} objects\n * @returns {T} a single new object\n */\nfunction inherit$1(original, ...objects) {\n  /** @type Record<string,any> */\n  const result = Object.create(null);\n\n  for (const key in original) {\n    result[key] = original[key];\n  }\n  objects.forEach(function(obj) {\n    for (const key in obj) {\n      result[key] = obj[key];\n    }\n  });\n  return /** @type {T} */ (result);\n}\n\n/**\n * @typedef {object} Renderer\n * @property {(text: string) => void} addText\n * @property {(node: Node) => void} openNode\n * @property {(node: Node) => void} closeNode\n * @property {() => string} value\n */\n\n/** @typedef {{scope?: string, language?: string, sublanguage?: boolean}} Node */\n/** @typedef {{walk: (r: Renderer) => void}} Tree */\n/** */\n\nconst SPAN_CLOSE = '</span>';\n\n/**\n * Determines if a node needs to be wrapped in <span>\n *\n * @param {Node} node */\nconst emitsWrappingTags = (node) => {\n  // rarely we can have a sublanguage where language is undefined\n  // TODO: track down why\n  return !!node.scope;\n};\n\n/**\n *\n * @param {string} name\n * @param {{prefix:string}} options\n */\nconst scopeToCSSClass = (name, { prefix }) => {\n  // sub-language\n  if (name.startsWith(\"language:\")) {\n    return name.replace(\"language:\", \"language-\");\n  }\n  // tiered scope: comment.line\n  if (name.includes(\".\")) {\n    const pieces = name.split(\".\");\n    return [\n      `${prefix}${pieces.shift()}`,\n      ...(pieces.map((x, i) => `${x}${\"_\".repeat(i + 1)}`))\n    ].join(\" \");\n  }\n  // simple scope\n  return `${prefix}${name}`;\n};\n\n/** @type {Renderer} */\nclass HTMLRenderer {\n  /**\n   * Creates a new HTMLRenderer\n   *\n   * @param {Tree} parseTree - the parse tree (must support `walk` API)\n   * @param {{classPrefix: string}} options\n   */\n  constructor(parseTree, options) {\n    this.buffer = \"\";\n    this.classPrefix = options.classPrefix;\n    parseTree.walk(this);\n  }\n\n  /**\n   * Adds texts to the output stream\n   *\n   * @param {string} text */\n  addText(text) {\n    this.buffer += escapeHTML(text);\n  }\n\n  /**\n   * Adds a node open to the output stream (if needed)\n   *\n   * @param {Node} node */\n  openNode(node) {\n    if (!emitsWrappingTags(node)) return;\n\n    const className = scopeToCSSClass(node.scope,\n      { prefix: this.classPrefix });\n    this.span(className);\n  }\n\n  /**\n   * Adds a node close to the output stream (if needed)\n   *\n   * @param {Node} node */\n  closeNode(node) {\n    if (!emitsWrappingTags(node)) return;\n\n    this.buffer += SPAN_CLOSE;\n  }\n\n  /**\n   * returns the accumulated buffer\n  */\n  value() {\n    return this.buffer;\n  }\n\n  // helpers\n\n  /**\n   * Builds a span element\n   *\n   * @param {string} className */\n  span(className) {\n    this.buffer += `<span class=\"${className}\">`;\n  }\n}\n\n/** @typedef {{scope?: string, language?: string, children: Node[]} | string} Node */\n/** @typedef {{scope?: string, language?: string, children: Node[]} } DataNode */\n/** @typedef {import('highlight.js').Emitter} Emitter */\n/**  */\n\n/** @returns {DataNode} */\nconst newNode = (opts = {}) => {\n  /** @type DataNode */\n  const result = { children: [] };\n  Object.assign(result, opts);\n  return result;\n};\n\nclass TokenTree {\n  constructor() {\n    /** @type DataNode */\n    this.rootNode = newNode();\n    this.stack = [this.rootNode];\n  }\n\n  get top() {\n    return this.stack[this.stack.length - 1];\n  }\n\n  get root() { return this.rootNode; }\n\n  /** @param {Node} node */\n  add(node) {\n    this.top.children.push(node);\n  }\n\n  /** @param {string} scope */\n  openNode(scope) {\n    /** @type Node */\n    const node = newNode({ scope });\n    this.add(node);\n    this.stack.push(node);\n  }\n\n  closeNode() {\n    if (this.stack.length > 1) {\n      return this.stack.pop();\n    }\n    // eslint-disable-next-line no-undefined\n    return undefined;\n  }\n\n  closeAllNodes() {\n    while (this.closeNode());\n  }\n\n  toJSON() {\n    return JSON.stringify(this.rootNode, null, 4);\n  }\n\n  /**\n   * @typedef { import(\"./html_renderer\").Renderer } Renderer\n   * @param {Renderer} builder\n   */\n  walk(builder) {\n    // this does not\n    return this.constructor._walk(builder, this.rootNode);\n    // this works\n    // return TokenTree._walk(builder, this.rootNode);\n  }\n\n  /**\n   * @param {Renderer} builder\n   * @param {Node} node\n   */\n  static _walk(builder, node) {\n    if (typeof node === \"string\") {\n      builder.addText(node);\n    } else if (node.children) {\n      builder.openNode(node);\n      node.children.forEach((child) => this._walk(builder, child));\n      builder.closeNode(node);\n    }\n    return builder;\n  }\n\n  /**\n   * @param {Node} node\n   */\n  static _collapse(node) {\n    if (typeof node === \"string\") return;\n    if (!node.children) return;\n\n    if (node.children.every(el => typeof el === \"string\")) {\n      // node.text = node.children.join(\"\");\n      // delete node.children;\n      node.children = [node.children.join(\"\")];\n    } else {\n      node.children.forEach((child) => {\n        TokenTree._collapse(child);\n      });\n    }\n  }\n}\n\n/**\n  Currently this is all private API, but this is the minimal API necessary\n  that an Emitter must implement to fully support the parser.\n\n  Minimal interface:\n\n  - addText(text)\n  - __addSublanguage(emitter, subLanguageName)\n  - startScope(scope)\n  - endScope()\n  - finalize()\n  - toHTML()\n\n*/\n\n/**\n * @implements {Emitter}\n */\nclass TokenTreeEmitter extends TokenTree {\n  /**\n   * @param {*} options\n   */\n  constructor(options) {\n    super();\n    this.options = options;\n  }\n\n  /**\n   * @param {string} text\n   */\n  addText(text) {\n    if (text === \"\") { return; }\n\n    this.add(text);\n  }\n\n  /** @param {string} scope */\n  startScope(scope) {\n    this.openNode(scope);\n  }\n\n  endScope() {\n    this.closeNode();\n  }\n\n  /**\n   * @param {Emitter & {root: DataNode}} emitter\n   * @param {string} name\n   */\n  __addSublanguage(emitter, name) {\n    /** @type DataNode */\n    const node = emitter.root;\n    if (name) node.scope = `language:${name}`;\n\n    this.add(node);\n  }\n\n  toHTML() {\n    const renderer = new HTMLRenderer(this, this.options);\n    return renderer.value();\n  }\n\n  finalize() {\n    this.closeAllNodes();\n    return true;\n  }\n}\n\n/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction anyNumberOfTimes(re) {\n  return concat('(?:', re, ')*');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction optional(re) {\n  return concat('(?:', re, ')?');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * @param { Array<string | RegExp | Object> } args\n * @returns {object}\n */\nfunction stripOptionsFromArgs(args) {\n  const opts = args[args.length - 1];\n\n  if (typeof opts === 'object' && opts.constructor === Object) {\n    args.splice(args.length - 1, 1);\n    return opts;\n  } else {\n    return {};\n  }\n}\n\n/** @typedef { {capture?: boolean} } RegexEitherOptions */\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] | [...(RegExp | string)[], RegexEitherOptions]} args\n * @returns {string}\n */\nfunction either(...args) {\n  /** @type { object & {capture?: boolean} }  */\n  const opts = stripOptionsFromArgs(args);\n  const joined = '('\n    + (opts.capture ? \"\" : \"?:\")\n    + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/**\n * @param {RegExp | string} re\n * @returns {number}\n */\nfunction countMatchGroups(re) {\n  return (new RegExp(re.toString() + '|')).exec('').length - 1;\n}\n\n/**\n * Does lexeme start with a regular expression match at the beginning\n * @param {RegExp} re\n * @param {string} lexeme\n */\nfunction startsWith(re, lexeme) {\n  const match = re && re.exec(lexeme);\n  return match && match.index === 0;\n}\n\n// BACKREF_RE matches an open parenthesis or backreference. To avoid\n// an incorrect parse, it additionally matches the following:\n// - [...] elements, where the meaning of parentheses and escapes change\n// - other escape sequences, so we do not misparse escape sequences as\n//   interesting elements\n// - non-matching or lookahead parentheses, which do not capture. These\n//   follow the '(' with a '?'.\nconst BACKREF_RE = /\\[(?:[^\\\\\\]]|\\\\.)*\\]|\\(\\??|\\\\([1-9][0-9]*)|\\\\./;\n\n// **INTERNAL** Not intended for outside usage\n// join logically computes regexps.join(separator), but fixes the\n// backreferences so they continue to match.\n// it also places each individual regular expression into it's own\n// match group, keeping track of the sequencing of those match groups\n// is currently an exercise for the caller. :-)\n/**\n * @param {(string | RegExp)[]} regexps\n * @param {{joinWith: string}} opts\n * @returns {string}\n */\nfunction _rewriteBackreferences(regexps, { joinWith }) {\n  let numCaptures = 0;\n\n  return regexps.map((regex) => {\n    numCaptures += 1;\n    const offset = numCaptures;\n    let re = source(regex);\n    let out = '';\n\n    while (re.length > 0) {\n      const match = BACKREF_RE.exec(re);\n      if (!match) {\n        out += re;\n        break;\n      }\n      out += re.substring(0, match.index);\n      re = re.substring(match.index + match[0].length);\n      if (match[0][0] === '\\\\' && match[1]) {\n        // Adjust the backreference.\n        out += '\\\\' + String(Number(match[1]) + offset);\n      } else {\n        out += match[0];\n        if (match[0] === '(') {\n          numCaptures++;\n        }\n      }\n    }\n    return out;\n  }).map(re => `(${re})`).join(joinWith);\n}\n\n/** @typedef {import('highlight.js').Mode} Mode */\n/** @typedef {import('highlight.js').ModeCallback} ModeCallback */\n\n// Common regexps\nconst MATCH_NOTHING_RE = /\\b\\B/;\nconst IDENT_RE = '[a-zA-Z]\\\\w*';\nconst UNDERSCORE_IDENT_RE = '[a-zA-Z_]\\\\w*';\nconst NUMBER_RE = '\\\\b\\\\d+(\\\\.\\\\d+)?';\nconst C_NUMBER_RE = '(-?)(\\\\b0[xX][a-fA-F0-9]+|(\\\\b\\\\d+(\\\\.\\\\d*)?|\\\\.\\\\d+)([eE][-+]?\\\\d+)?)'; // 0x..., 0..., decimal, float\nconst BINARY_NUMBER_RE = '\\\\b(0b[01]+)'; // 0b...\nconst RE_STARTERS_RE = '!|!=|!==|%|%=|&|&&|&=|\\\\*|\\\\*=|\\\\+|\\\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\\\?|\\\\[|\\\\{|\\\\(|\\\\^|\\\\^=|\\\\||\\\\|=|\\\\|\\\\||~';\n\n/**\n* @param { Partial<Mode> & {binary?: string | RegExp} } opts\n*/\nconst SHEBANG = (opts = {}) => {\n  const beginShebang = /^#![ ]*\\//;\n  if (opts.binary) {\n    opts.begin = concat(\n      beginShebang,\n      /.*\\b/,\n      opts.binary,\n      /\\b.*/);\n  }\n  return inherit$1({\n    scope: 'meta',\n    begin: beginShebang,\n    end: /$/,\n    relevance: 0,\n    /** @type {ModeCallback} */\n    \"on:begin\": (m, resp) => {\n      if (m.index !== 0) resp.ignoreMatch();\n    }\n  }, opts);\n};\n\n// Common modes\nconst BACKSLASH_ESCAPE = {\n  begin: '\\\\\\\\[\\\\s\\\\S]', relevance: 0\n};\nconst APOS_STRING_MODE = {\n  scope: 'string',\n  begin: '\\'',\n  end: '\\'',\n  illegal: '\\\\n',\n  contains: [BACKSLASH_ESCAPE]\n};\nconst QUOTE_STRING_MODE = {\n  scope: 'string',\n  begin: '\"',\n  end: '\"',\n  illegal: '\\\\n',\n  contains: [BACKSLASH_ESCAPE]\n};\nconst PHRASAL_WORDS_MODE = {\n  begin: /\\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\\b/\n};\n/**\n * Creates a comment mode\n *\n * @param {string | RegExp} begin\n * @param {string | RegExp} end\n * @param {Mode | {}} [modeOptions]\n * @returns {Partial<Mode>}\n */\nconst COMMENT = function(begin, end, modeOptions = {}) {\n  const mode = inherit$1(\n    {\n      scope: 'comment',\n      begin,\n      end,\n      contains: []\n    },\n    modeOptions\n  );\n  mode.contains.push({\n    scope: 'doctag',\n    // hack to avoid the space from being included. the space is necessary to\n    // match here to prevent the plain text rule below from gobbling up doctags\n    begin: '[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)',\n    end: /(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,\n    excludeBegin: true,\n    relevance: 0\n  });\n  const ENGLISH_WORD = either(\n    // list of common 1 and 2 letter words in English\n    \"I\",\n    \"a\",\n    \"is\",\n    \"so\",\n    \"us\",\n    \"to\",\n    \"at\",\n    \"if\",\n    \"in\",\n    \"it\",\n    \"on\",\n    // note: this is not an exhaustive list of contractions, just popular ones\n    /[A-Za-z]+['](d|ve|re|ll|t|s|n)/, // contractions - can't we'd they're let's, etc\n    /[A-Za-z]+[-][a-z]+/, // `no-way`, etc.\n    /[A-Za-z][a-z]{2,}/ // allow capitalized words at beginning of sentences\n  );\n  // looking like plain text, more likely to be a comment\n  mode.contains.push(\n    {\n      // TODO: how to include \", (, ) without breaking grammars that use these for\n      // comment delimiters?\n      // begin: /[ ]+([()\"]?([A-Za-z'-]{3,}|is|a|I|so|us|[tT][oO]|at|if|in|it|on)[.]?[()\":]?([.][ ]|[ ]|\\))){3}/\n      // ---\n\n      // this tries to find sequences of 3 english words in a row (without any\n      // \"programming\" type syntax) this gives us a strong signal that we've\n      // TRULY found a comment - vs perhaps scanning with the wrong language.\n      // It's possible to find something that LOOKS like the start of the\n      // comment - but then if there is no readable text - good chance it is a\n      // false match and not a comment.\n      //\n      // for a visual example please see:\n      // https://github.com/highlightjs/highlight.js/issues/2827\n\n      begin: concat(\n        /[ ]+/, // necessary to prevent us gobbling up doctags like /* <AUTHOR> Mcgill */\n        '(',\n        ENGLISH_WORD,\n        /[.]?[:]?([.][ ]|[ ])/,\n        '){3}') // look for 3 words in a row\n    }\n  );\n  return mode;\n};\nconst C_LINE_COMMENT_MODE = COMMENT('//', '$');\nconst C_BLOCK_COMMENT_MODE = COMMENT('/\\\\*', '\\\\*/');\nconst HASH_COMMENT_MODE = COMMENT('#', '$');\nconst NUMBER_MODE = {\n  scope: 'number',\n  begin: NUMBER_RE,\n  relevance: 0\n};\nconst C_NUMBER_MODE = {\n  scope: 'number',\n  begin: C_NUMBER_RE,\n  relevance: 0\n};\nconst BINARY_NUMBER_MODE = {\n  scope: 'number',\n  begin: BINARY_NUMBER_RE,\n  relevance: 0\n};\nconst REGEXP_MODE = {\n  scope: \"regexp\",\n  begin: /\\/(?=[^/\\n]*\\/)/,\n  end: /\\/[gimuy]*/,\n  contains: [\n    BACKSLASH_ESCAPE,\n    {\n      begin: /\\[/,\n      end: /\\]/,\n      relevance: 0,\n      contains: [BACKSLASH_ESCAPE]\n    }\n  ]\n};\nconst TITLE_MODE = {\n  scope: 'title',\n  begin: IDENT_RE,\n  relevance: 0\n};\nconst UNDERSCORE_TITLE_MODE = {\n  scope: 'title',\n  begin: UNDERSCORE_IDENT_RE,\n  relevance: 0\n};\nconst METHOD_GUARD = {\n  // excludes method names from keyword processing\n  begin: '\\\\.\\\\s*' + UNDERSCORE_IDENT_RE,\n  relevance: 0\n};\n\n/**\n * Adds end same as begin mechanics to a mode\n *\n * Your mode must include at least a single () match group as that first match\n * group is what is used for comparison\n * @param {Partial<Mode>} mode\n */\nconst END_SAME_AS_BEGIN = function(mode) {\n  return Object.assign(mode,\n    {\n      /** @type {ModeCallback} */\n      'on:begin': (m, resp) => { resp.data._beginMatch = m[1]; },\n      /** @type {ModeCallback} */\n      'on:end': (m, resp) => { if (resp.data._beginMatch !== m[1]) resp.ignoreMatch(); }\n    });\n};\n\nvar MODES = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  APOS_STRING_MODE: APOS_STRING_MODE,\n  BACKSLASH_ESCAPE: BACKSLASH_ESCAPE,\n  BINARY_NUMBER_MODE: BINARY_NUMBER_MODE,\n  BINARY_NUMBER_RE: BINARY_NUMBER_RE,\n  COMMENT: COMMENT,\n  C_BLOCK_COMMENT_MODE: C_BLOCK_COMMENT_MODE,\n  C_LINE_COMMENT_MODE: C_LINE_COMMENT_MODE,\n  C_NUMBER_MODE: C_NUMBER_MODE,\n  C_NUMBER_RE: C_NUMBER_RE,\n  END_SAME_AS_BEGIN: END_SAME_AS_BEGIN,\n  HASH_COMMENT_MODE: HASH_COMMENT_MODE,\n  IDENT_RE: IDENT_RE,\n  MATCH_NOTHING_RE: MATCH_NOTHING_RE,\n  METHOD_GUARD: METHOD_GUARD,\n  NUMBER_MODE: NUMBER_MODE,\n  NUMBER_RE: NUMBER_RE,\n  PHRASAL_WORDS_MODE: PHRASAL_WORDS_MODE,\n  QUOTE_STRING_MODE: QUOTE_STRING_MODE,\n  REGEXP_MODE: REGEXP_MODE,\n  RE_STARTERS_RE: RE_STARTERS_RE,\n  SHEBANG: SHEBANG,\n  TITLE_MODE: TITLE_MODE,\n  UNDERSCORE_IDENT_RE: UNDERSCORE_IDENT_RE,\n  UNDERSCORE_TITLE_MODE: UNDERSCORE_TITLE_MODE\n});\n\n/**\n@typedef {import('highlight.js').CallbackResponse} CallbackResponse\n@typedef {import('highlight.js').CompilerExt} CompilerExt\n*/\n\n// Grammar extensions / plugins\n// See: https://github.com/highlightjs/highlight.js/issues/2833\n\n// Grammar extensions allow \"syntactic sugar\" to be added to the grammar modes\n// without requiring any underlying changes to the compiler internals.\n\n// `compileMatch` being the perfect small example of now allowing a grammar\n// author to write `match` when they desire to match a single expression rather\n// than being forced to use `begin`.  The extension then just moves `match` into\n// `begin` when it runs.  Ie, no features have been added, but we've just made\n// the experience of writing (and reading grammars) a little bit nicer.\n\n// ------\n\n// TODO: We need negative look-behind support to do this properly\n/**\n * Skip a match if it has a preceding dot\n *\n * This is used for `beginKeywords` to prevent matching expressions such as\n * `bob.keyword.do()`. The mode compiler automatically wires this up as a\n * special _internal_ 'on:begin' callback for modes with `beginKeywords`\n * @param {RegExpMatchArray} match\n * @param {CallbackResponse} response\n */\nfunction skipIfHasPrecedingDot(match, response) {\n  const before = match.input[match.index - 1];\n  if (before === \".\") {\n    response.ignoreMatch();\n  }\n}\n\n/**\n *\n * @type {CompilerExt}\n */\nfunction scopeClassName(mode, _parent) {\n  // eslint-disable-next-line no-undefined\n  if (mode.className !== undefined) {\n    mode.scope = mode.className;\n    delete mode.className;\n  }\n}\n\n/**\n * `beginKeywords` syntactic sugar\n * @type {CompilerExt}\n */\nfunction beginKeywords(mode, parent) {\n  if (!parent) return;\n  if (!mode.beginKeywords) return;\n\n  // for languages with keywords that include non-word characters checking for\n  // a word boundary is not sufficient, so instead we check for a word boundary\n  // or whitespace - this does no harm in any case since our keyword engine\n  // doesn't allow spaces in keywords anyways and we still check for the boundary\n  // first\n  mode.begin = '\\\\b(' + mode.beginKeywords.split(' ').join('|') + ')(?!\\\\.)(?=\\\\b|\\\\s)';\n  mode.__beforeBegin = skipIfHasPrecedingDot;\n  mode.keywords = mode.keywords || mode.beginKeywords;\n  delete mode.beginKeywords;\n\n  // prevents double relevance, the keywords themselves provide\n  // relevance, the mode doesn't need to double it\n  // eslint-disable-next-line no-undefined\n  if (mode.relevance === undefined) mode.relevance = 0;\n}\n\n/**\n * Allow `illegal` to contain an array of illegal values\n * @type {CompilerExt}\n */\nfunction compileIllegal(mode, _parent) {\n  if (!Array.isArray(mode.illegal)) return;\n\n  mode.illegal = either(...mode.illegal);\n}\n\n/**\n * `match` to match a single expression for readability\n * @type {CompilerExt}\n */\nfunction compileMatch(mode, _parent) {\n  if (!mode.match) return;\n  if (mode.begin || mode.end) throw new Error(\"begin & end are not supported with match\");\n\n  mode.begin = mode.match;\n  delete mode.match;\n}\n\n/**\n * provides the default 1 relevance to all modes\n * @type {CompilerExt}\n */\nfunction compileRelevance(mode, _parent) {\n  // eslint-disable-next-line no-undefined\n  if (mode.relevance === undefined) mode.relevance = 1;\n}\n\n// allow beforeMatch to act as a \"qualifier\" for the match\n// the full match begin must be [beforeMatch][begin]\nconst beforeMatchExt = (mode, parent) => {\n  if (!mode.beforeMatch) return;\n  // starts conflicts with endsParent which we need to make sure the child\n  // rule is not matched multiple times\n  if (mode.starts) throw new Error(\"beforeMatch cannot be used with starts\");\n\n  const originalMode = Object.assign({}, mode);\n  Object.keys(mode).forEach((key) => { delete mode[key]; });\n\n  mode.keywords = originalMode.keywords;\n  mode.begin = concat(originalMode.beforeMatch, lookahead(originalMode.begin));\n  mode.starts = {\n    relevance: 0,\n    contains: [\n      Object.assign(originalMode, { endsParent: true })\n    ]\n  };\n  mode.relevance = 0;\n\n  delete originalMode.beforeMatch;\n};\n\n// keywords that should have no default relevance value\nconst COMMON_KEYWORDS = [\n  'of',\n  'and',\n  'for',\n  'in',\n  'not',\n  'or',\n  'if',\n  'then',\n  'parent', // common variable name\n  'list', // common variable name\n  'value' // common variable name\n];\n\nconst DEFAULT_KEYWORD_SCOPE = \"keyword\";\n\n/**\n * Given raw keywords from a language definition, compile them.\n *\n * @param {string | Record<string,string|string[]> | Array<string>} rawKeywords\n * @param {boolean} caseInsensitive\n */\nfunction compileKeywords(rawKeywords, caseInsensitive, scopeName = DEFAULT_KEYWORD_SCOPE) {\n  /** @type {import(\"highlight.js/private\").KeywordDict} */\n  const compiledKeywords = Object.create(null);\n\n  // input can be a string of keywords, an array of keywords, or a object with\n  // named keys representing scopeName (which can then point to a string or array)\n  if (typeof rawKeywords === 'string') {\n    compileList(scopeName, rawKeywords.split(\" \"));\n  } else if (Array.isArray(rawKeywords)) {\n    compileList(scopeName, rawKeywords);\n  } else {\n    Object.keys(rawKeywords).forEach(function(scopeName) {\n      // collapse all our objects back into the parent object\n      Object.assign(\n        compiledKeywords,\n        compileKeywords(rawKeywords[scopeName], caseInsensitive, scopeName)\n      );\n    });\n  }\n  return compiledKeywords;\n\n  // ---\n\n  /**\n   * Compiles an individual list of keywords\n   *\n   * Ex: \"for if when while|5\"\n   *\n   * @param {string} scopeName\n   * @param {Array<string>} keywordList\n   */\n  function compileList(scopeName, keywordList) {\n    if (caseInsensitive) {\n      keywordList = keywordList.map(x => x.toLowerCase());\n    }\n    keywordList.forEach(function(keyword) {\n      const pair = keyword.split('|');\n      compiledKeywords[pair[0]] = [scopeName, scoreForKeyword(pair[0], pair[1])];\n    });\n  }\n}\n\n/**\n * Returns the proper score for a given keyword\n *\n * Also takes into account comment keywords, which will be scored 0 UNLESS\n * another score has been manually assigned.\n * @param {string} keyword\n * @param {string} [providedScore]\n */\nfunction scoreForKeyword(keyword, providedScore) {\n  // manual scores always win over common keywords\n  // so you can force a score of 1 if you really insist\n  if (providedScore) {\n    return Number(providedScore);\n  }\n\n  return commonKeyword(keyword) ? 0 : 1;\n}\n\n/**\n * Determines if a given keyword is common or not\n *\n * @param {string} keyword */\nfunction commonKeyword(keyword) {\n  return COMMON_KEYWORDS.includes(keyword.toLowerCase());\n}\n\n/*\n\nFor the reasoning behind this please see:\nhttps://github.com/highlightjs/highlight.js/issues/2880#issuecomment-*********\n\n*/\n\n/**\n * @type {Record<string, boolean>}\n */\nconst seenDeprecations = {};\n\n/**\n * @param {string} message\n */\nconst error = (message) => {\n  console.error(message);\n};\n\n/**\n * @param {string} message\n * @param {any} args\n */\nconst warn = (message, ...args) => {\n  console.log(`WARN: ${message}`, ...args);\n};\n\n/**\n * @param {string} version\n * @param {string} message\n */\nconst deprecated = (version, message) => {\n  if (seenDeprecations[`${version}/${message}`]) return;\n\n  console.log(`Deprecated as of ${version}. ${message}`);\n  seenDeprecations[`${version}/${message}`] = true;\n};\n\n/* eslint-disable no-throw-literal */\n\n/**\n@typedef {import('highlight.js').CompiledMode} CompiledMode\n*/\n\nconst MultiClassError = new Error();\n\n/**\n * Renumbers labeled scope names to account for additional inner match\n * groups that otherwise would break everything.\n *\n * Lets say we 3 match scopes:\n *\n *   { 1 => ..., 2 => ..., 3 => ... }\n *\n * So what we need is a clean match like this:\n *\n *   (a)(b)(c) => [ \"a\", \"b\", \"c\" ]\n *\n * But this falls apart with inner match groups:\n *\n * (a)(((b)))(c) => [\"a\", \"b\", \"b\", \"b\", \"c\" ]\n *\n * Our scopes are now \"out of alignment\" and we're repeating `b` 3 times.\n * What needs to happen is the numbers are remapped:\n *\n *   { 1 => ..., 2 => ..., 5 => ... }\n *\n * We also need to know that the ONLY groups that should be output\n * are 1, 2, and 5.  This function handles this behavior.\n *\n * @param {CompiledMode} mode\n * @param {Array<RegExp | string>} regexes\n * @param {{key: \"beginScope\"|\"endScope\"}} opts\n */\nfunction remapScopeNames(mode, regexes, { key }) {\n  let offset = 0;\n  const scopeNames = mode[key];\n  /** @type Record<number,boolean> */\n  const emit = {};\n  /** @type Record<number,string> */\n  const positions = {};\n\n  for (let i = 1; i <= regexes.length; i++) {\n    positions[i + offset] = scopeNames[i];\n    emit[i + offset] = true;\n    offset += countMatchGroups(regexes[i - 1]);\n  }\n  // we use _emit to keep track of which match groups are \"top-level\" to avoid double\n  // output from inside match groups\n  mode[key] = positions;\n  mode[key]._emit = emit;\n  mode[key]._multi = true;\n}\n\n/**\n * @param {CompiledMode} mode\n */\nfunction beginMultiClass(mode) {\n  if (!Array.isArray(mode.begin)) return;\n\n  if (mode.skip || mode.excludeBegin || mode.returnBegin) {\n    error(\"skip, excludeBegin, returnBegin not compatible with beginScope: {}\");\n    throw MultiClassError;\n  }\n\n  if (typeof mode.beginScope !== \"object\" || mode.beginScope === null) {\n    error(\"beginScope must be object\");\n    throw MultiClassError;\n  }\n\n  remapScopeNames(mode, mode.begin, { key: \"beginScope\" });\n  mode.begin = _rewriteBackreferences(mode.begin, { joinWith: \"\" });\n}\n\n/**\n * @param {CompiledMode} mode\n */\nfunction endMultiClass(mode) {\n  if (!Array.isArray(mode.end)) return;\n\n  if (mode.skip || mode.excludeEnd || mode.returnEnd) {\n    error(\"skip, excludeEnd, returnEnd not compatible with endScope: {}\");\n    throw MultiClassError;\n  }\n\n  if (typeof mode.endScope !== \"object\" || mode.endScope === null) {\n    error(\"endScope must be object\");\n    throw MultiClassError;\n  }\n\n  remapScopeNames(mode, mode.end, { key: \"endScope\" });\n  mode.end = _rewriteBackreferences(mode.end, { joinWith: \"\" });\n}\n\n/**\n * this exists only to allow `scope: {}` to be used beside `match:`\n * Otherwise `beginScope` would necessary and that would look weird\n\n  {\n    match: [ /def/, /\\w+/ ]\n    scope: { 1: \"keyword\" , 2: \"title\" }\n  }\n\n * @param {CompiledMode} mode\n */\nfunction scopeSugar(mode) {\n  if (mode.scope && typeof mode.scope === \"object\" && mode.scope !== null) {\n    mode.beginScope = mode.scope;\n    delete mode.scope;\n  }\n}\n\n/**\n * @param {CompiledMode} mode\n */\nfunction MultiClass(mode) {\n  scopeSugar(mode);\n\n  if (typeof mode.beginScope === \"string\") {\n    mode.beginScope = { _wrap: mode.beginScope };\n  }\n  if (typeof mode.endScope === \"string\") {\n    mode.endScope = { _wrap: mode.endScope };\n  }\n\n  beginMultiClass(mode);\n  endMultiClass(mode);\n}\n\n/**\n@typedef {import('highlight.js').Mode} Mode\n@typedef {import('highlight.js').CompiledMode} CompiledMode\n@typedef {import('highlight.js').Language} Language\n@typedef {import('highlight.js').HLJSPlugin} HLJSPlugin\n@typedef {import('highlight.js').CompiledLanguage} CompiledLanguage\n*/\n\n// compilation\n\n/**\n * Compiles a language definition result\n *\n * Given the raw result of a language definition (Language), compiles this so\n * that it is ready for highlighting code.\n * @param {Language} language\n * @returns {CompiledLanguage}\n */\nfunction compileLanguage(language) {\n  /**\n   * Builds a regex with the case sensitivity of the current language\n   *\n   * @param {RegExp | string} value\n   * @param {boolean} [global]\n   */\n  function langRe(value, global) {\n    return new RegExp(\n      source(value),\n      'm'\n      + (language.case_insensitive ? 'i' : '')\n      + (language.unicodeRegex ? 'u' : '')\n      + (global ? 'g' : '')\n    );\n  }\n\n  /**\n    Stores multiple regular expressions and allows you to quickly search for\n    them all in a string simultaneously - returning the first match.  It does\n    this by creating a huge (a|b|c) regex - each individual item wrapped with ()\n    and joined by `|` - using match groups to track position.  When a match is\n    found checking which position in the array has content allows us to figure\n    out which of the original regexes / match groups triggered the match.\n\n    The match object itself (the result of `Regex.exec`) is returned but also\n    enhanced by merging in any meta-data that was registered with the regex.\n    This is how we keep track of which mode matched, and what type of rule\n    (`illegal`, `begin`, end, etc).\n  */\n  class MultiRegex {\n    constructor() {\n      this.matchIndexes = {};\n      // @ts-ignore\n      this.regexes = [];\n      this.matchAt = 1;\n      this.position = 0;\n    }\n\n    // @ts-ignore\n    addRule(re, opts) {\n      opts.position = this.position++;\n      // @ts-ignore\n      this.matchIndexes[this.matchAt] = opts;\n      this.regexes.push([opts, re]);\n      this.matchAt += countMatchGroups(re) + 1;\n    }\n\n    compile() {\n      if (this.regexes.length === 0) {\n        // avoids the need to check length every time exec is called\n        // @ts-ignore\n        this.exec = () => null;\n      }\n      const terminators = this.regexes.map(el => el[1]);\n      this.matcherRe = langRe(_rewriteBackreferences(terminators, { joinWith: '|' }), true);\n      this.lastIndex = 0;\n    }\n\n    /** @param {string} s */\n    exec(s) {\n      this.matcherRe.lastIndex = this.lastIndex;\n      const match = this.matcherRe.exec(s);\n      if (!match) { return null; }\n\n      // eslint-disable-next-line no-undefined\n      const i = match.findIndex((el, i) => i > 0 && el !== undefined);\n      // @ts-ignore\n      const matchData = this.matchIndexes[i];\n      // trim off any earlier non-relevant match groups (ie, the other regex\n      // match groups that make up the multi-matcher)\n      match.splice(0, i);\n\n      return Object.assign(match, matchData);\n    }\n  }\n\n  /*\n    Created to solve the key deficiently with MultiRegex - there is no way to\n    test for multiple matches at a single location.  Why would we need to do\n    that?  In the future a more dynamic engine will allow certain matches to be\n    ignored.  An example: if we matched say the 3rd regex in a large group but\n    decided to ignore it - we'd need to started testing again at the 4th\n    regex... but MultiRegex itself gives us no real way to do that.\n\n    So what this class creates MultiRegexs on the fly for whatever search\n    position they are needed.\n\n    NOTE: These additional MultiRegex objects are created dynamically.  For most\n    grammars most of the time we will never actually need anything more than the\n    first MultiRegex - so this shouldn't have too much overhead.\n\n    Say this is our search group, and we match regex3, but wish to ignore it.\n\n      regex1 | regex2 | regex3 | regex4 | regex5    ' ie, startAt = 0\n\n    What we need is a new MultiRegex that only includes the remaining\n    possibilities:\n\n      regex4 | regex5                               ' ie, startAt = 3\n\n    This class wraps all that complexity up in a simple API... `startAt` decides\n    where in the array of expressions to start doing the matching. It\n    auto-increments, so if a match is found at position 2, then startAt will be\n    set to 3.  If the end is reached startAt will return to 0.\n\n    MOST of the time the parser will be setting startAt manually to 0.\n  */\n  class ResumableMultiRegex {\n    constructor() {\n      // @ts-ignore\n      this.rules = [];\n      // @ts-ignore\n      this.multiRegexes = [];\n      this.count = 0;\n\n      this.lastIndex = 0;\n      this.regexIndex = 0;\n    }\n\n    // @ts-ignore\n    getMatcher(index) {\n      if (this.multiRegexes[index]) return this.multiRegexes[index];\n\n      const matcher = new MultiRegex();\n      this.rules.slice(index).forEach(([re, opts]) => matcher.addRule(re, opts));\n      matcher.compile();\n      this.multiRegexes[index] = matcher;\n      return matcher;\n    }\n\n    resumingScanAtSamePosition() {\n      return this.regexIndex !== 0;\n    }\n\n    considerAll() {\n      this.regexIndex = 0;\n    }\n\n    // @ts-ignore\n    addRule(re, opts) {\n      this.rules.push([re, opts]);\n      if (opts.type === \"begin\") this.count++;\n    }\n\n    /** @param {string} s */\n    exec(s) {\n      const m = this.getMatcher(this.regexIndex);\n      m.lastIndex = this.lastIndex;\n      let result = m.exec(s);\n\n      // The following is because we have no easy way to say \"resume scanning at the\n      // existing position but also skip the current rule ONLY\". What happens is\n      // all prior rules are also skipped which can result in matching the wrong\n      // thing. Example of matching \"booger\":\n\n      // our matcher is [string, \"booger\", number]\n      //\n      // ....booger....\n\n      // if \"booger\" is ignored then we'd really need a regex to scan from the\n      // SAME position for only: [string, number] but ignoring \"booger\" (if it\n      // was the first match), a simple resume would scan ahead who knows how\n      // far looking only for \"number\", ignoring potential string matches (or\n      // future \"booger\" matches that might be valid.)\n\n      // So what we do: We execute two matchers, one resuming at the same\n      // position, but the second full matcher starting at the position after:\n\n      //     /--- resume first regex match here (for [number])\n      //     |/---- full match here for [string, \"booger\", number]\n      //     vv\n      // ....booger....\n\n      // Which ever results in a match first is then used. So this 3-4 step\n      // process essentially allows us to say \"match at this position, excluding\n      // a prior rule that was ignored\".\n      //\n      // 1. Match \"booger\" first, ignore. Also proves that [string] does non match.\n      // 2. Resume matching for [number]\n      // 3. Match at index + 1 for [string, \"booger\", number]\n      // 4. If #2 and #3 result in matches, which came first?\n      if (this.resumingScanAtSamePosition()) {\n        if (result && result.index === this.lastIndex) ; else { // use the second matcher result\n          const m2 = this.getMatcher(0);\n          m2.lastIndex = this.lastIndex + 1;\n          result = m2.exec(s);\n        }\n      }\n\n      if (result) {\n        this.regexIndex += result.position + 1;\n        if (this.regexIndex === this.count) {\n          // wrap-around to considering all matches again\n          this.considerAll();\n        }\n      }\n\n      return result;\n    }\n  }\n\n  /**\n   * Given a mode, builds a huge ResumableMultiRegex that can be used to walk\n   * the content and find matches.\n   *\n   * @param {CompiledMode} mode\n   * @returns {ResumableMultiRegex}\n   */\n  function buildModeRegex(mode) {\n    const mm = new ResumableMultiRegex();\n\n    mode.contains.forEach(term => mm.addRule(term.begin, { rule: term, type: \"begin\" }));\n\n    if (mode.terminatorEnd) {\n      mm.addRule(mode.terminatorEnd, { type: \"end\" });\n    }\n    if (mode.illegal) {\n      mm.addRule(mode.illegal, { type: \"illegal\" });\n    }\n\n    return mm;\n  }\n\n  /** skip vs abort vs ignore\n   *\n   * @skip   - The mode is still entered and exited normally (and contains rules apply),\n   *           but all content is held and added to the parent buffer rather than being\n   *           output when the mode ends.  Mostly used with `sublanguage` to build up\n   *           a single large buffer than can be parsed by sublanguage.\n   *\n   *             - The mode begin ands ends normally.\n   *             - Content matched is added to the parent mode buffer.\n   *             - The parser cursor is moved forward normally.\n   *\n   * @abort  - A hack placeholder until we have ignore.  Aborts the mode (as if it\n   *           never matched) but DOES NOT continue to match subsequent `contains`\n   *           modes.  Abort is bad/suboptimal because it can result in modes\n   *           farther down not getting applied because an earlier rule eats the\n   *           content but then aborts.\n   *\n   *             - The mode does not begin.\n   *             - Content matched by `begin` is added to the mode buffer.\n   *             - The parser cursor is moved forward accordingly.\n   *\n   * @ignore - Ignores the mode (as if it never matched) and continues to match any\n   *           subsequent `contains` modes.  Ignore isn't technically possible with\n   *           the current parser implementation.\n   *\n   *             - The mode does not begin.\n   *             - Content matched by `begin` is ignored.\n   *             - The parser cursor is not moved forward.\n   */\n\n  /**\n   * Compiles an individual mode\n   *\n   * This can raise an error if the mode contains certain detectable known logic\n   * issues.\n   * @param {Mode} mode\n   * @param {CompiledMode | null} [parent]\n   * @returns {CompiledMode | never}\n   */\n  function compileMode(mode, parent) {\n    const cmode = /** @type CompiledMode */ (mode);\n    if (mode.isCompiled) return cmode;\n\n    [\n      scopeClassName,\n      // do this early so compiler extensions generally don't have to worry about\n      // the distinction between match/begin\n      compileMatch,\n      MultiClass,\n      beforeMatchExt\n    ].forEach(ext => ext(mode, parent));\n\n    language.compilerExtensions.forEach(ext => ext(mode, parent));\n\n    // __beforeBegin is considered private API, internal use only\n    mode.__beforeBegin = null;\n\n    [\n      beginKeywords,\n      // do this later so compiler extensions that come earlier have access to the\n      // raw array if they wanted to perhaps manipulate it, etc.\n      compileIllegal,\n      // default to 1 relevance if not specified\n      compileRelevance\n    ].forEach(ext => ext(mode, parent));\n\n    mode.isCompiled = true;\n\n    let keywordPattern = null;\n    if (typeof mode.keywords === \"object\" && mode.keywords.$pattern) {\n      // we need a copy because keywords might be compiled multiple times\n      // so we can't go deleting $pattern from the original on the first\n      // pass\n      mode.keywords = Object.assign({}, mode.keywords);\n      keywordPattern = mode.keywords.$pattern;\n      delete mode.keywords.$pattern;\n    }\n    keywordPattern = keywordPattern || /\\w+/;\n\n    if (mode.keywords) {\n      mode.keywords = compileKeywords(mode.keywords, language.case_insensitive);\n    }\n\n    cmode.keywordPatternRe = langRe(keywordPattern, true);\n\n    if (parent) {\n      if (!mode.begin) mode.begin = /\\B|\\b/;\n      cmode.beginRe = langRe(cmode.begin);\n      if (!mode.end && !mode.endsWithParent) mode.end = /\\B|\\b/;\n      if (mode.end) cmode.endRe = langRe(cmode.end);\n      cmode.terminatorEnd = source(cmode.end) || '';\n      if (mode.endsWithParent && parent.terminatorEnd) {\n        cmode.terminatorEnd += (mode.end ? '|' : '') + parent.terminatorEnd;\n      }\n    }\n    if (mode.illegal) cmode.illegalRe = langRe(/** @type {RegExp | string} */ (mode.illegal));\n    if (!mode.contains) mode.contains = [];\n\n    mode.contains = [].concat(...mode.contains.map(function(c) {\n      return expandOrCloneMode(c === 'self' ? mode : c);\n    }));\n    mode.contains.forEach(function(c) { compileMode(/** @type Mode */ (c), cmode); });\n\n    if (mode.starts) {\n      compileMode(mode.starts, parent);\n    }\n\n    cmode.matcher = buildModeRegex(cmode);\n    return cmode;\n  }\n\n  if (!language.compilerExtensions) language.compilerExtensions = [];\n\n  // self is not valid at the top-level\n  if (language.contains && language.contains.includes('self')) {\n    throw new Error(\"ERR: contains `self` is not supported at the top-level of a language.  See documentation.\");\n  }\n\n  // we need a null object, which inherit will guarantee\n  language.classNameAliases = inherit$1(language.classNameAliases || {});\n\n  return compileMode(/** @type Mode */ (language));\n}\n\n/**\n * Determines if a mode has a dependency on it's parent or not\n *\n * If a mode does have a parent dependency then often we need to clone it if\n * it's used in multiple places so that each copy points to the correct parent,\n * where-as modes without a parent can often safely be re-used at the bottom of\n * a mode chain.\n *\n * @param {Mode | null} mode\n * @returns {boolean} - is there a dependency on the parent?\n * */\nfunction dependencyOnParent(mode) {\n  if (!mode) return false;\n\n  return mode.endsWithParent || dependencyOnParent(mode.starts);\n}\n\n/**\n * Expands a mode or clones it if necessary\n *\n * This is necessary for modes with parental dependenceis (see notes on\n * `dependencyOnParent`) and for nodes that have `variants` - which must then be\n * exploded into their own individual modes at compile time.\n *\n * @param {Mode} mode\n * @returns {Mode | Mode[]}\n * */\nfunction expandOrCloneMode(mode) {\n  if (mode.variants && !mode.cachedVariants) {\n    mode.cachedVariants = mode.variants.map(function(variant) {\n      return inherit$1(mode, { variants: null }, variant);\n    });\n  }\n\n  // EXPAND\n  // if we have variants then essentially \"replace\" the mode with the variants\n  // this happens in compileMode, where this function is called from\n  if (mode.cachedVariants) {\n    return mode.cachedVariants;\n  }\n\n  // CLONE\n  // if we have dependencies on parents then we need a unique\n  // instance of ourselves, so we can be reused with many\n  // different parents without issue\n  if (dependencyOnParent(mode)) {\n    return inherit$1(mode, { starts: mode.starts ? inherit$1(mode.starts) : null });\n  }\n\n  if (Object.isFrozen(mode)) {\n    return inherit$1(mode);\n  }\n\n  // no special dependency issues, just return ourselves\n  return mode;\n}\n\nvar version = \"11.11.1\";\n\nclass HTMLInjectionError extends Error {\n  constructor(reason, html) {\n    super(reason);\n    this.name = \"HTMLInjectionError\";\n    this.html = html;\n  }\n}\n\n/*\nSyntax highlighting with language autodetection.\nhttps://highlightjs.org/\n*/\n\n\n\n/**\n@typedef {import('highlight.js').Mode} Mode\n@typedef {import('highlight.js').CompiledMode} CompiledMode\n@typedef {import('highlight.js').CompiledScope} CompiledScope\n@typedef {import('highlight.js').Language} Language\n@typedef {import('highlight.js').HLJSApi} HLJSApi\n@typedef {import('highlight.js').HLJSPlugin} HLJSPlugin\n@typedef {import('highlight.js').PluginEvent} PluginEvent\n@typedef {import('highlight.js').HLJSOptions} HLJSOptions\n@typedef {import('highlight.js').LanguageFn} LanguageFn\n@typedef {import('highlight.js').HighlightedHTMLElement} HighlightedHTMLElement\n@typedef {import('highlight.js').BeforeHighlightContext} BeforeHighlightContext\n@typedef {import('highlight.js/private').MatchType} MatchType\n@typedef {import('highlight.js/private').KeywordData} KeywordData\n@typedef {import('highlight.js/private').EnhancedMatch} EnhancedMatch\n@typedef {import('highlight.js/private').AnnotatedError} AnnotatedError\n@typedef {import('highlight.js').AutoHighlightResult} AutoHighlightResult\n@typedef {import('highlight.js').HighlightOptions} HighlightOptions\n@typedef {import('highlight.js').HighlightResult} HighlightResult\n*/\n\n\nconst escape = escapeHTML;\nconst inherit = inherit$1;\nconst NO_MATCH = Symbol(\"nomatch\");\nconst MAX_KEYWORD_HITS = 7;\n\n/**\n * @param {any} hljs - object that is extended (legacy)\n * @returns {HLJSApi}\n */\nconst HLJS = function(hljs) {\n  // Global internal variables used within the highlight.js library.\n  /** @type {Record<string, Language>} */\n  const languages = Object.create(null);\n  /** @type {Record<string, string>} */\n  const aliases = Object.create(null);\n  /** @type {HLJSPlugin[]} */\n  const plugins = [];\n\n  // safe/production mode - swallows more errors, tries to keep running\n  // even if a single syntax or parse hits a fatal error\n  let SAFE_MODE = true;\n  const LANGUAGE_NOT_FOUND = \"Could not find the language '{}', did you forget to load/include a language module?\";\n  /** @type {Language} */\n  const PLAINTEXT_LANGUAGE = { disableAutodetect: true, name: 'Plain text', contains: [] };\n\n  // Global options used when within external APIs. This is modified when\n  // calling the `hljs.configure` function.\n  /** @type HLJSOptions */\n  let options = {\n    ignoreUnescapedHTML: false,\n    throwUnescapedHTML: false,\n    noHighlightRe: /^(no-?highlight)$/i,\n    languageDetectRe: /\\blang(?:uage)?-([\\w-]+)\\b/i,\n    classPrefix: 'hljs-',\n    cssSelector: 'pre code',\n    languages: null,\n    // beta configuration options, subject to change, welcome to discuss\n    // https://github.com/highlightjs/highlight.js/issues/1086\n    __emitter: TokenTreeEmitter\n  };\n\n  /* Utility functions */\n\n  /**\n   * Tests a language name to see if highlighting should be skipped\n   * @param {string} languageName\n   */\n  function shouldNotHighlight(languageName) {\n    return options.noHighlightRe.test(languageName);\n  }\n\n  /**\n   * @param {HighlightedHTMLElement} block - the HTML element to determine language for\n   */\n  function blockLanguage(block) {\n    let classes = block.className + ' ';\n\n    classes += block.parentNode ? block.parentNode.className : '';\n\n    // language-* takes precedence over non-prefixed class names.\n    const match = options.languageDetectRe.exec(classes);\n    if (match) {\n      const language = getLanguage(match[1]);\n      if (!language) {\n        warn(LANGUAGE_NOT_FOUND.replace(\"{}\", match[1]));\n        warn(\"Falling back to no-highlight mode for this block.\", block);\n      }\n      return language ? match[1] : 'no-highlight';\n    }\n\n    return classes\n      .split(/\\s+/)\n      .find((_class) => shouldNotHighlight(_class) || getLanguage(_class));\n  }\n\n  /**\n   * Core highlighting function.\n   *\n   * OLD API\n   * highlight(lang, code, ignoreIllegals, continuation)\n   *\n   * NEW API\n   * highlight(code, {lang, ignoreIllegals})\n   *\n   * @param {string} codeOrLanguageName - the language to use for highlighting\n   * @param {string | HighlightOptions} optionsOrCode - the code to highlight\n   * @param {boolean} [ignoreIllegals] - whether to ignore illegal matches, default is to bail\n   *\n   * @returns {HighlightResult} Result - an object that represents the result\n   * @property {string} language - the language name\n   * @property {number} relevance - the relevance score\n   * @property {string} value - the highlighted HTML code\n   * @property {string} code - the original raw code\n   * @property {CompiledMode} top - top of the current mode stack\n   * @property {boolean} illegal - indicates whether any illegal matches were found\n  */\n  function highlight(codeOrLanguageName, optionsOrCode, ignoreIllegals) {\n    let code = \"\";\n    let languageName = \"\";\n    if (typeof optionsOrCode === \"object\") {\n      code = codeOrLanguageName;\n      ignoreIllegals = optionsOrCode.ignoreIllegals;\n      languageName = optionsOrCode.language;\n    } else {\n      // old API\n      deprecated(\"10.7.0\", \"highlight(lang, code, ...args) has been deprecated.\");\n      deprecated(\"10.7.0\", \"Please use highlight(code, options) instead.\\nhttps://github.com/highlightjs/highlight.js/issues/2277\");\n      languageName = codeOrLanguageName;\n      code = optionsOrCode;\n    }\n\n    // https://github.com/highlightjs/highlight.js/issues/3149\n    // eslint-disable-next-line no-undefined\n    if (ignoreIllegals === undefined) { ignoreIllegals = true; }\n\n    /** @type {BeforeHighlightContext} */\n    const context = {\n      code,\n      language: languageName\n    };\n    // the plugin can change the desired language or the code to be highlighted\n    // just be changing the object it was passed\n    fire(\"before:highlight\", context);\n\n    // a before plugin can usurp the result completely by providing it's own\n    // in which case we don't even need to call highlight\n    const result = context.result\n      ? context.result\n      : _highlight(context.language, context.code, ignoreIllegals);\n\n    result.code = context.code;\n    // the plugin can change anything in result to suite it\n    fire(\"after:highlight\", result);\n\n    return result;\n  }\n\n  /**\n   * private highlight that's used internally and does not fire callbacks\n   *\n   * @param {string} languageName - the language to use for highlighting\n   * @param {string} codeToHighlight - the code to highlight\n   * @param {boolean?} [ignoreIllegals] - whether to ignore illegal matches, default is to bail\n   * @param {CompiledMode?} [continuation] - current continuation mode, if any\n   * @returns {HighlightResult} - result of the highlight operation\n  */\n  function _highlight(languageName, codeToHighlight, ignoreIllegals, continuation) {\n    const keywordHits = Object.create(null);\n\n    /**\n     * Return keyword data if a match is a keyword\n     * @param {CompiledMode} mode - current mode\n     * @param {string} matchText - the textual match\n     * @returns {KeywordData | false}\n     */\n    function keywordData(mode, matchText) {\n      return mode.keywords[matchText];\n    }\n\n    function processKeywords() {\n      if (!top.keywords) {\n        emitter.addText(modeBuffer);\n        return;\n      }\n\n      let lastIndex = 0;\n      top.keywordPatternRe.lastIndex = 0;\n      let match = top.keywordPatternRe.exec(modeBuffer);\n      let buf = \"\";\n\n      while (match) {\n        buf += modeBuffer.substring(lastIndex, match.index);\n        const word = language.case_insensitive ? match[0].toLowerCase() : match[0];\n        const data = keywordData(top, word);\n        if (data) {\n          const [kind, keywordRelevance] = data;\n          emitter.addText(buf);\n          buf = \"\";\n\n          keywordHits[word] = (keywordHits[word] || 0) + 1;\n          if (keywordHits[word] <= MAX_KEYWORD_HITS) relevance += keywordRelevance;\n          if (kind.startsWith(\"_\")) {\n            // _ implied for relevance only, do not highlight\n            // by applying a class name\n            buf += match[0];\n          } else {\n            const cssClass = language.classNameAliases[kind] || kind;\n            emitKeyword(match[0], cssClass);\n          }\n        } else {\n          buf += match[0];\n        }\n        lastIndex = top.keywordPatternRe.lastIndex;\n        match = top.keywordPatternRe.exec(modeBuffer);\n      }\n      buf += modeBuffer.substring(lastIndex);\n      emitter.addText(buf);\n    }\n\n    function processSubLanguage() {\n      if (modeBuffer === \"\") return;\n      /** @type HighlightResult */\n      let result = null;\n\n      if (typeof top.subLanguage === 'string') {\n        if (!languages[top.subLanguage]) {\n          emitter.addText(modeBuffer);\n          return;\n        }\n        result = _highlight(top.subLanguage, modeBuffer, true, continuations[top.subLanguage]);\n        continuations[top.subLanguage] = /** @type {CompiledMode} */ (result._top);\n      } else {\n        result = highlightAuto(modeBuffer, top.subLanguage.length ? top.subLanguage : null);\n      }\n\n      // Counting embedded language score towards the host language may be disabled\n      // with zeroing the containing mode relevance. Use case in point is Markdown that\n      // allows XML everywhere and makes every XML snippet to have a much larger Markdown\n      // score.\n      if (top.relevance > 0) {\n        relevance += result.relevance;\n      }\n      emitter.__addSublanguage(result._emitter, result.language);\n    }\n\n    function processBuffer() {\n      if (top.subLanguage != null) {\n        processSubLanguage();\n      } else {\n        processKeywords();\n      }\n      modeBuffer = '';\n    }\n\n    /**\n     * @param {string} text\n     * @param {string} scope\n     */\n    function emitKeyword(keyword, scope) {\n      if (keyword === \"\") return;\n\n      emitter.startScope(scope);\n      emitter.addText(keyword);\n      emitter.endScope();\n    }\n\n    /**\n     * @param {CompiledScope} scope\n     * @param {RegExpMatchArray} match\n     */\n    function emitMultiClass(scope, match) {\n      let i = 1;\n      const max = match.length - 1;\n      while (i <= max) {\n        if (!scope._emit[i]) { i++; continue; }\n        const klass = language.classNameAliases[scope[i]] || scope[i];\n        const text = match[i];\n        if (klass) {\n          emitKeyword(text, klass);\n        } else {\n          modeBuffer = text;\n          processKeywords();\n          modeBuffer = \"\";\n        }\n        i++;\n      }\n    }\n\n    /**\n     * @param {CompiledMode} mode - new mode to start\n     * @param {RegExpMatchArray} match\n     */\n    function startNewMode(mode, match) {\n      if (mode.scope && typeof mode.scope === \"string\") {\n        emitter.openNode(language.classNameAliases[mode.scope] || mode.scope);\n      }\n      if (mode.beginScope) {\n        // beginScope just wraps the begin match itself in a scope\n        if (mode.beginScope._wrap) {\n          emitKeyword(modeBuffer, language.classNameAliases[mode.beginScope._wrap] || mode.beginScope._wrap);\n          modeBuffer = \"\";\n        } else if (mode.beginScope._multi) {\n          // at this point modeBuffer should just be the match\n          emitMultiClass(mode.beginScope, match);\n          modeBuffer = \"\";\n        }\n      }\n\n      top = Object.create(mode, { parent: { value: top } });\n      return top;\n    }\n\n    /**\n     * @param {CompiledMode } mode - the mode to potentially end\n     * @param {RegExpMatchArray} match - the latest match\n     * @param {string} matchPlusRemainder - match plus remainder of content\n     * @returns {CompiledMode | void} - the next mode, or if void continue on in current mode\n     */\n    function endOfMode(mode, match, matchPlusRemainder) {\n      let matched = startsWith(mode.endRe, matchPlusRemainder);\n\n      if (matched) {\n        if (mode[\"on:end\"]) {\n          const resp = new Response(mode);\n          mode[\"on:end\"](match, resp);\n          if (resp.isMatchIgnored) matched = false;\n        }\n\n        if (matched) {\n          while (mode.endsParent && mode.parent) {\n            mode = mode.parent;\n          }\n          return mode;\n        }\n      }\n      // even if on:end fires an `ignore` it's still possible\n      // that we might trigger the end node because of a parent mode\n      if (mode.endsWithParent) {\n        return endOfMode(mode.parent, match, matchPlusRemainder);\n      }\n    }\n\n    /**\n     * Handle matching but then ignoring a sequence of text\n     *\n     * @param {string} lexeme - string containing full match text\n     */\n    function doIgnore(lexeme) {\n      if (top.matcher.regexIndex === 0) {\n        // no more regexes to potentially match here, so we move the cursor forward one\n        // space\n        modeBuffer += lexeme[0];\n        return 1;\n      } else {\n        // no need to move the cursor, we still have additional regexes to try and\n        // match at this very spot\n        resumeScanAtSamePosition = true;\n        return 0;\n      }\n    }\n\n    /**\n     * Handle the start of a new potential mode match\n     *\n     * @param {EnhancedMatch} match - the current match\n     * @returns {number} how far to advance the parse cursor\n     */\n    function doBeginMatch(match) {\n      const lexeme = match[0];\n      const newMode = match.rule;\n\n      const resp = new Response(newMode);\n      // first internal before callbacks, then the public ones\n      const beforeCallbacks = [newMode.__beforeBegin, newMode[\"on:begin\"]];\n      for (const cb of beforeCallbacks) {\n        if (!cb) continue;\n        cb(match, resp);\n        if (resp.isMatchIgnored) return doIgnore(lexeme);\n      }\n\n      if (newMode.skip) {\n        modeBuffer += lexeme;\n      } else {\n        if (newMode.excludeBegin) {\n          modeBuffer += lexeme;\n        }\n        processBuffer();\n        if (!newMode.returnBegin && !newMode.excludeBegin) {\n          modeBuffer = lexeme;\n        }\n      }\n      startNewMode(newMode, match);\n      return newMode.returnBegin ? 0 : lexeme.length;\n    }\n\n    /**\n     * Handle the potential end of mode\n     *\n     * @param {RegExpMatchArray} match - the current match\n     */\n    function doEndMatch(match) {\n      const lexeme = match[0];\n      const matchPlusRemainder = codeToHighlight.substring(match.index);\n\n      const endMode = endOfMode(top, match, matchPlusRemainder);\n      if (!endMode) { return NO_MATCH; }\n\n      const origin = top;\n      if (top.endScope && top.endScope._wrap) {\n        processBuffer();\n        emitKeyword(lexeme, top.endScope._wrap);\n      } else if (top.endScope && top.endScope._multi) {\n        processBuffer();\n        emitMultiClass(top.endScope, match);\n      } else if (origin.skip) {\n        modeBuffer += lexeme;\n      } else {\n        if (!(origin.returnEnd || origin.excludeEnd)) {\n          modeBuffer += lexeme;\n        }\n        processBuffer();\n        if (origin.excludeEnd) {\n          modeBuffer = lexeme;\n        }\n      }\n      do {\n        if (top.scope) {\n          emitter.closeNode();\n        }\n        if (!top.skip && !top.subLanguage) {\n          relevance += top.relevance;\n        }\n        top = top.parent;\n      } while (top !== endMode.parent);\n      if (endMode.starts) {\n        startNewMode(endMode.starts, match);\n      }\n      return origin.returnEnd ? 0 : lexeme.length;\n    }\n\n    function processContinuations() {\n      const list = [];\n      for (let current = top; current !== language; current = current.parent) {\n        if (current.scope) {\n          list.unshift(current.scope);\n        }\n      }\n      list.forEach(item => emitter.openNode(item));\n    }\n\n    /** @type {{type?: MatchType, index?: number, rule?: Mode}}} */\n    let lastMatch = {};\n\n    /**\n     *  Process an individual match\n     *\n     * @param {string} textBeforeMatch - text preceding the match (since the last match)\n     * @param {EnhancedMatch} [match] - the match itself\n     */\n    function processLexeme(textBeforeMatch, match) {\n      const lexeme = match && match[0];\n\n      // add non-matched text to the current mode buffer\n      modeBuffer += textBeforeMatch;\n\n      if (lexeme == null) {\n        processBuffer();\n        return 0;\n      }\n\n      // we've found a 0 width match and we're stuck, so we need to advance\n      // this happens when we have badly behaved rules that have optional matchers to the degree that\n      // sometimes they can end up matching nothing at all\n      // Ref: https://github.com/highlightjs/highlight.js/issues/2140\n      if (lastMatch.type === \"begin\" && match.type === \"end\" && lastMatch.index === match.index && lexeme === \"\") {\n        // spit the \"skipped\" character that our regex choked on back into the output sequence\n        modeBuffer += codeToHighlight.slice(match.index, match.index + 1);\n        if (!SAFE_MODE) {\n          /** @type {AnnotatedError} */\n          const err = new Error(`0 width match regex (${languageName})`);\n          err.languageName = languageName;\n          err.badRule = lastMatch.rule;\n          throw err;\n        }\n        return 1;\n      }\n      lastMatch = match;\n\n      if (match.type === \"begin\") {\n        return doBeginMatch(match);\n      } else if (match.type === \"illegal\" && !ignoreIllegals) {\n        // illegal match, we do not continue processing\n        /** @type {AnnotatedError} */\n        const err = new Error('Illegal lexeme \"' + lexeme + '\" for mode \"' + (top.scope || '<unnamed>') + '\"');\n        err.mode = top;\n        throw err;\n      } else if (match.type === \"end\") {\n        const processed = doEndMatch(match);\n        if (processed !== NO_MATCH) {\n          return processed;\n        }\n      }\n\n      // edge case for when illegal matches $ (end of line) which is technically\n      // a 0 width match but not a begin/end match so it's not caught by the\n      // first handler (when ignoreIllegals is true)\n      if (match.type === \"illegal\" && lexeme === \"\") {\n        // advance so we aren't stuck in an infinite loop\n        modeBuffer += \"\\n\";\n        return 1;\n      }\n\n      // infinite loops are BAD, this is a last ditch catch all. if we have a\n      // decent number of iterations yet our index (cursor position in our\n      // parsing) still 3x behind our index then something is very wrong\n      // so we bail\n      if (iterations > 100000 && iterations > match.index * 3) {\n        const err = new Error('potential infinite loop, way more iterations than matches');\n        throw err;\n      }\n\n      /*\n      Why might be find ourselves here?  An potential end match that was\n      triggered but could not be completed.  IE, `doEndMatch` returned NO_MATCH.\n      (this could be because a callback requests the match be ignored, etc)\n\n      This causes no real harm other than stopping a few times too many.\n      */\n\n      modeBuffer += lexeme;\n      return lexeme.length;\n    }\n\n    const language = getLanguage(languageName);\n    if (!language) {\n      error(LANGUAGE_NOT_FOUND.replace(\"{}\", languageName));\n      throw new Error('Unknown language: \"' + languageName + '\"');\n    }\n\n    const md = compileLanguage(language);\n    let result = '';\n    /** @type {CompiledMode} */\n    let top = continuation || md;\n    /** @type Record<string,CompiledMode> */\n    const continuations = {}; // keep continuations for sub-languages\n    const emitter = new options.__emitter(options);\n    processContinuations();\n    let modeBuffer = '';\n    let relevance = 0;\n    let index = 0;\n    let iterations = 0;\n    let resumeScanAtSamePosition = false;\n\n    try {\n      if (!language.__emitTokens) {\n        top.matcher.considerAll();\n\n        for (;;) {\n          iterations++;\n          if (resumeScanAtSamePosition) {\n            // only regexes not matched previously will now be\n            // considered for a potential match\n            resumeScanAtSamePosition = false;\n          } else {\n            top.matcher.considerAll();\n          }\n          top.matcher.lastIndex = index;\n\n          const match = top.matcher.exec(codeToHighlight);\n          // console.log(\"match\", match[0], match.rule && match.rule.begin)\n\n          if (!match) break;\n\n          const beforeMatch = codeToHighlight.substring(index, match.index);\n          const processedCount = processLexeme(beforeMatch, match);\n          index = match.index + processedCount;\n        }\n        processLexeme(codeToHighlight.substring(index));\n      } else {\n        language.__emitTokens(codeToHighlight, emitter);\n      }\n\n      emitter.finalize();\n      result = emitter.toHTML();\n\n      return {\n        language: languageName,\n        value: result,\n        relevance,\n        illegal: false,\n        _emitter: emitter,\n        _top: top\n      };\n    } catch (err) {\n      if (err.message && err.message.includes('Illegal')) {\n        return {\n          language: languageName,\n          value: escape(codeToHighlight),\n          illegal: true,\n          relevance: 0,\n          _illegalBy: {\n            message: err.message,\n            index,\n            context: codeToHighlight.slice(index - 100, index + 100),\n            mode: err.mode,\n            resultSoFar: result\n          },\n          _emitter: emitter\n        };\n      } else if (SAFE_MODE) {\n        return {\n          language: languageName,\n          value: escape(codeToHighlight),\n          illegal: false,\n          relevance: 0,\n          errorRaised: err,\n          _emitter: emitter,\n          _top: top\n        };\n      } else {\n        throw err;\n      }\n    }\n  }\n\n  /**\n   * returns a valid highlight result, without actually doing any actual work,\n   * auto highlight starts with this and it's possible for small snippets that\n   * auto-detection may not find a better match\n   * @param {string} code\n   * @returns {HighlightResult}\n   */\n  function justTextHighlightResult(code) {\n    const result = {\n      value: escape(code),\n      illegal: false,\n      relevance: 0,\n      _top: PLAINTEXT_LANGUAGE,\n      _emitter: new options.__emitter(options)\n    };\n    result._emitter.addText(code);\n    return result;\n  }\n\n  /**\n  Highlighting with language detection. Accepts a string with the code to\n  highlight. Returns an object with the following properties:\n\n  - language (detected language)\n  - relevance (int)\n  - value (an HTML string with highlighting markup)\n  - secondBest (object with the same structure for second-best heuristically\n    detected language, may be absent)\n\n    @param {string} code\n    @param {Array<string>} [languageSubset]\n    @returns {AutoHighlightResult}\n  */\n  function highlightAuto(code, languageSubset) {\n    languageSubset = languageSubset || options.languages || Object.keys(languages);\n    const plaintext = justTextHighlightResult(code);\n\n    const results = languageSubset.filter(getLanguage).filter(autoDetection).map(name =>\n      _highlight(name, code, false)\n    );\n    results.unshift(plaintext); // plaintext is always an option\n\n    const sorted = results.sort((a, b) => {\n      // sort base on relevance\n      if (a.relevance !== b.relevance) return b.relevance - a.relevance;\n\n      // always award the tie to the base language\n      // ie if C++ and Arduino are tied, it's more likely to be C++\n      if (a.language && b.language) {\n        if (getLanguage(a.language).supersetOf === b.language) {\n          return 1;\n        } else if (getLanguage(b.language).supersetOf === a.language) {\n          return -1;\n        }\n      }\n\n      // otherwise say they are equal, which has the effect of sorting on\n      // relevance while preserving the original ordering - which is how ties\n      // have historically been settled, ie the language that comes first always\n      // wins in the case of a tie\n      return 0;\n    });\n\n    const [best, secondBest] = sorted;\n\n    /** @type {AutoHighlightResult} */\n    const result = best;\n    result.secondBest = secondBest;\n\n    return result;\n  }\n\n  /**\n   * Builds new class name for block given the language name\n   *\n   * @param {HTMLElement} element\n   * @param {string} [currentLang]\n   * @param {string} [resultLang]\n   */\n  function updateClassName(element, currentLang, resultLang) {\n    const language = (currentLang && aliases[currentLang]) || resultLang;\n\n    element.classList.add(\"hljs\");\n    element.classList.add(`language-${language}`);\n  }\n\n  /**\n   * Applies highlighting to a DOM node containing code.\n   *\n   * @param {HighlightedHTMLElement} element - the HTML element to highlight\n  */\n  function highlightElement(element) {\n    /** @type HTMLElement */\n    let node = null;\n    const language = blockLanguage(element);\n\n    if (shouldNotHighlight(language)) return;\n\n    fire(\"before:highlightElement\",\n      { el: element, language });\n\n    if (element.dataset.highlighted) {\n      console.log(\"Element previously highlighted. To highlight again, first unset `dataset.highlighted`.\", element);\n      return;\n    }\n\n    // we should be all text, no child nodes (unescaped HTML) - this is possibly\n    // an HTML injection attack - it's likely too late if this is already in\n    // production (the code has likely already done its damage by the time\n    // we're seeing it)... but we yell loudly about this so that hopefully it's\n    // more likely to be caught in development before making it to production\n    if (element.children.length > 0) {\n      if (!options.ignoreUnescapedHTML) {\n        console.warn(\"One of your code blocks includes unescaped HTML. This is a potentially serious security risk.\");\n        console.warn(\"https://github.com/highlightjs/highlight.js/wiki/security\");\n        console.warn(\"The element with unescaped HTML:\");\n        console.warn(element);\n      }\n      if (options.throwUnescapedHTML) {\n        const err = new HTMLInjectionError(\n          \"One of your code blocks includes unescaped HTML.\",\n          element.innerHTML\n        );\n        throw err;\n      }\n    }\n\n    node = element;\n    const text = node.textContent;\n    const result = language ? highlight(text, { language, ignoreIllegals: true }) : highlightAuto(text);\n\n    element.innerHTML = result.value;\n    element.dataset.highlighted = \"yes\";\n    updateClassName(element, language, result.language);\n    element.result = {\n      language: result.language,\n      // TODO: remove with version 11.0\n      re: result.relevance,\n      relevance: result.relevance\n    };\n    if (result.secondBest) {\n      element.secondBest = {\n        language: result.secondBest.language,\n        relevance: result.secondBest.relevance\n      };\n    }\n\n    fire(\"after:highlightElement\", { el: element, result, text });\n  }\n\n  /**\n   * Updates highlight.js global options with the passed options\n   *\n   * @param {Partial<HLJSOptions>} userOptions\n   */\n  function configure(userOptions) {\n    options = inherit(options, userOptions);\n  }\n\n  // TODO: remove v12, deprecated\n  const initHighlighting = () => {\n    highlightAll();\n    deprecated(\"10.6.0\", \"initHighlighting() deprecated.  Use highlightAll() now.\");\n  };\n\n  // TODO: remove v12, deprecated\n  function initHighlightingOnLoad() {\n    highlightAll();\n    deprecated(\"10.6.0\", \"initHighlightingOnLoad() deprecated.  Use highlightAll() now.\");\n  }\n\n  let wantsHighlight = false;\n\n  /**\n   * auto-highlights all pre>code elements on the page\n   */\n  function highlightAll() {\n    function boot() {\n      // if a highlight was requested before DOM was loaded, do now\n      highlightAll();\n    }\n\n    // if we are called too early in the loading process\n    if (document.readyState === \"loading\") {\n      // make sure the event listener is only added once\n      if (!wantsHighlight) {\n        window.addEventListener('DOMContentLoaded', boot, false);\n      }\n      wantsHighlight = true;\n      return;\n    }\n\n    const blocks = document.querySelectorAll(options.cssSelector);\n    blocks.forEach(highlightElement);\n  }\n\n  /**\n   * Register a language grammar module\n   *\n   * @param {string} languageName\n   * @param {LanguageFn} languageDefinition\n   */\n  function registerLanguage(languageName, languageDefinition) {\n    let lang = null;\n    try {\n      lang = languageDefinition(hljs);\n    } catch (error$1) {\n      error(\"Language definition for '{}' could not be registered.\".replace(\"{}\", languageName));\n      // hard or soft error\n      if (!SAFE_MODE) { throw error$1; } else { error(error$1); }\n      // languages that have serious errors are replaced with essentially a\n      // \"plaintext\" stand-in so that the code blocks will still get normal\n      // css classes applied to them - and one bad language won't break the\n      // entire highlighter\n      lang = PLAINTEXT_LANGUAGE;\n    }\n    // give it a temporary name if it doesn't have one in the meta-data\n    if (!lang.name) lang.name = languageName;\n    languages[languageName] = lang;\n    lang.rawDefinition = languageDefinition.bind(null, hljs);\n\n    if (lang.aliases) {\n      registerAliases(lang.aliases, { languageName });\n    }\n  }\n\n  /**\n   * Remove a language grammar module\n   *\n   * @param {string} languageName\n   */\n  function unregisterLanguage(languageName) {\n    delete languages[languageName];\n    for (const alias of Object.keys(aliases)) {\n      if (aliases[alias] === languageName) {\n        delete aliases[alias];\n      }\n    }\n  }\n\n  /**\n   * @returns {string[]} List of language internal names\n   */\n  function listLanguages() {\n    return Object.keys(languages);\n  }\n\n  /**\n   * @param {string} name - name of the language to retrieve\n   * @returns {Language | undefined}\n   */\n  function getLanguage(name) {\n    name = (name || '').toLowerCase();\n    return languages[name] || languages[aliases[name]];\n  }\n\n  /**\n   *\n   * @param {string|string[]} aliasList - single alias or list of aliases\n   * @param {{languageName: string}} opts\n   */\n  function registerAliases(aliasList, { languageName }) {\n    if (typeof aliasList === 'string') {\n      aliasList = [aliasList];\n    }\n    aliasList.forEach(alias => { aliases[alias.toLowerCase()] = languageName; });\n  }\n\n  /**\n   * Determines if a given language has auto-detection enabled\n   * @param {string} name - name of the language\n   */\n  function autoDetection(name) {\n    const lang = getLanguage(name);\n    return lang && !lang.disableAutodetect;\n  }\n\n  /**\n   * Upgrades the old highlightBlock plugins to the new\n   * highlightElement API\n   * @param {HLJSPlugin} plugin\n   */\n  function upgradePluginAPI(plugin) {\n    // TODO: remove with v12\n    if (plugin[\"before:highlightBlock\"] && !plugin[\"before:highlightElement\"]) {\n      plugin[\"before:highlightElement\"] = (data) => {\n        plugin[\"before:highlightBlock\"](\n          Object.assign({ block: data.el }, data)\n        );\n      };\n    }\n    if (plugin[\"after:highlightBlock\"] && !plugin[\"after:highlightElement\"]) {\n      plugin[\"after:highlightElement\"] = (data) => {\n        plugin[\"after:highlightBlock\"](\n          Object.assign({ block: data.el }, data)\n        );\n      };\n    }\n  }\n\n  /**\n   * @param {HLJSPlugin} plugin\n   */\n  function addPlugin(plugin) {\n    upgradePluginAPI(plugin);\n    plugins.push(plugin);\n  }\n\n  /**\n   * @param {HLJSPlugin} plugin\n   */\n  function removePlugin(plugin) {\n    const index = plugins.indexOf(plugin);\n    if (index !== -1) {\n      plugins.splice(index, 1);\n    }\n  }\n\n  /**\n   *\n   * @param {PluginEvent} event\n   * @param {any} args\n   */\n  function fire(event, args) {\n    const cb = event;\n    plugins.forEach(function(plugin) {\n      if (plugin[cb]) {\n        plugin[cb](args);\n      }\n    });\n  }\n\n  /**\n   * DEPRECATED\n   * @param {HighlightedHTMLElement} el\n   */\n  function deprecateHighlightBlock(el) {\n    deprecated(\"10.7.0\", \"highlightBlock will be removed entirely in v12.0\");\n    deprecated(\"10.7.0\", \"Please use highlightElement now.\");\n\n    return highlightElement(el);\n  }\n\n  /* Interface definition */\n  Object.assign(hljs, {\n    highlight,\n    highlightAuto,\n    highlightAll,\n    highlightElement,\n    // TODO: Remove with v12 API\n    highlightBlock: deprecateHighlightBlock,\n    configure,\n    initHighlighting,\n    initHighlightingOnLoad,\n    registerLanguage,\n    unregisterLanguage,\n    listLanguages,\n    getLanguage,\n    registerAliases,\n    autoDetection,\n    inherit,\n    addPlugin,\n    removePlugin\n  });\n\n  hljs.debugMode = function() { SAFE_MODE = false; };\n  hljs.safeMode = function() { SAFE_MODE = true; };\n  hljs.versionString = version;\n\n  hljs.regex = {\n    concat: concat,\n    lookahead: lookahead,\n    either: either,\n    optional: optional,\n    anyNumberOfTimes: anyNumberOfTimes\n  };\n\n  for (const key in MODES) {\n    // @ts-ignore\n    if (typeof MODES[key] === \"object\") {\n      // @ts-ignore\n      deepFreeze(MODES[key]);\n    }\n  }\n\n  // merge all the modes/regexes into our main object\n  Object.assign(hljs, MODES);\n\n  return hljs;\n};\n\n// Other names for the variable may break build script\nconst highlight = HLJS({});\n\n// returns a new instance of the highlighter to be used for extensions\n// check https://github.com/wooorm/lowlight/issues/47\nhighlight.newInstance = () => HLJS({});\n\nmodule.exports = highlight;\nhighlight.HighlightJS = highlight;\nhighlight.default = highlight;\n"], "names": [], "mappings": "AAAA,kCAAkC,GAElC,SAAS,WAAW,GAAG;IACrB,IAAI,eAAe,KAAK;QACtB,IAAI,KAAK,GACP,IAAI,MAAM,GACV,IAAI,GAAG,GACL;YACE,MAAM,IAAI,MAAM;QAClB;IACN,OAAO,IAAI,eAAe,KAAK;QAC7B,IAAI,GAAG,GACL,IAAI,KAAK,GACT,IAAI,MAAM,GACR;YACE,MAAM,IAAI,MAAM;QAClB;IACN;IAEA,cAAc;IACd,OAAO,MAAM,CAAC;IAEd,OAAO,mBAAmB,CAAC,KAAK,OAAO,CAAC,CAAC;QACvC,MAAM,OAAO,GAAG,CAAC,KAAK;QACtB,MAAM,OAAO,OAAO;QAEpB,yEAAyE;QACzE,IAAI,CAAC,SAAS,YAAY,SAAS,UAAU,KAAK,CAAC,OAAO,QAAQ,CAAC,OAAO;YACxE,WAAW;QACb;IACF;IAEA,OAAO;AACT;AAEA,wEAAwE,GACxE,gEAAgE,GAChE,iCAAiC,GAEjC,MAAM;IACJ;;GAEC,GACD,YAAY,IAAI,CAAE;QAChB,wCAAwC;QACxC,IAAI,KAAK,IAAI,KAAK,WAAW,KAAK,IAAI,GAAG,CAAC;QAE1C,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI;QACrB,IAAI,CAAC,cAAc,GAAG;IACxB;IAEA,cAAc;QACZ,IAAI,CAAC,cAAc,GAAG;IACxB;AACF;AAEA;;;CAGC,GACD,SAAS,WAAW,KAAK;IACvB,OAAO,MACJ,OAAO,CAAC,MAAM,SACd,OAAO,CAAC,MAAM,QACd,OAAO,CAAC,MAAM,QACd,OAAO,CAAC,MAAM,UACd,OAAO,CAAC,MAAM;AACnB;AAEA;;;;;;;CAOC,GACD,SAAS,UAAU,QAAQ,EAAE,GAAG,OAAO;IACrC,6BAA6B,GAC7B,MAAM,SAAS,OAAO,MAAM,CAAC;IAE7B,IAAK,MAAM,OAAO,SAAU;QAC1B,MAAM,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI;IAC7B;IACA,QAAQ,OAAO,CAAC,SAAS,GAAG;QAC1B,IAAK,MAAM,OAAO,IAAK;YACrB,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;QACxB;IACF;IACA,OAAyB;AAC3B;AAEA;;;;;;CAMC,GAED,+EAA+E,GAC/E,kDAAkD,GAClD,IAAI,GAEJ,MAAM,aAAa;AAEnB;;;sBAGsB,GACtB,MAAM,oBAAoB,CAAC;IACzB,+DAA+D;IAC/D,uBAAuB;IACvB,OAAO,CAAC,CAAC,KAAK,KAAK;AACrB;AAEA;;;;CAIC,GACD,MAAM,kBAAkB,CAAC,MAAM,EAAE,MAAM,EAAE;IACvC,eAAe;IACf,IAAI,KAAK,UAAU,CAAC,cAAc;QAChC,OAAO,KAAK,OAAO,CAAC,aAAa;IACnC;IACA,6BAA6B;IAC7B,IAAI,KAAK,QAAQ,CAAC,MAAM;QACtB,MAAM,SAAS,KAAK,KAAK,CAAC;QAC1B,OAAO;YACL,GAAG,SAAS,OAAO,KAAK,IAAI;eACxB,OAAO,GAAG,CAAC,CAAC,GAAG,IAAM,GAAG,IAAI,IAAI,MAAM,CAAC,IAAI,IAAI;SACpD,CAAC,IAAI,CAAC;IACT;IACA,eAAe;IACf,OAAO,GAAG,SAAS,MAAM;AAC3B;AAEA,qBAAqB,GACrB,MAAM;IACJ;;;;;GAKC,GACD,YAAY,SAAS,EAAE,OAAO,CAAE;QAC9B,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,WAAW,GAAG,QAAQ,WAAW;QACtC,UAAU,IAAI,CAAC,IAAI;IACrB;IAEA;;;0BAGwB,GACxB,QAAQ,IAAI,EAAE;QACZ,IAAI,CAAC,MAAM,IAAI,WAAW;IAC5B;IAEA;;;wBAGsB,GACtB,SAAS,IAAI,EAAE;QACb,IAAI,CAAC,kBAAkB,OAAO;QAE9B,MAAM,YAAY,gBAAgB,KAAK,KAAK,EAC1C;YAAE,QAAQ,IAAI,CAAC,WAAW;QAAC;QAC7B,IAAI,CAAC,IAAI,CAAC;IACZ;IAEA;;;wBAGsB,GACtB,UAAU,IAAI,EAAE;QACd,IAAI,CAAC,kBAAkB,OAAO;QAE9B,IAAI,CAAC,MAAM,IAAI;IACjB;IAEA;;EAEA,GACA,QAAQ;QACN,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA,UAAU;IAEV;;;+BAG6B,GAC7B,KAAK,SAAS,EAAE;QACd,IAAI,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,UAAU,EAAE,CAAC;IAC9C;AACF;AAEA,mFAAmF,GACnF,+EAA+E,GAC/E,sDAAsD,GACtD,KAAK,GAEL,wBAAwB,GACxB,MAAM,UAAU,CAAC,OAAO,CAAC,CAAC;IACxB,mBAAmB,GACnB,MAAM,SAAS;QAAE,UAAU,EAAE;IAAC;IAC9B,OAAO,MAAM,CAAC,QAAQ;IACtB,OAAO;AACT;AAEA,MAAM;IACJ,aAAc;QACZ,mBAAmB,GACnB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,KAAK,GAAG;YAAC,IAAI,CAAC,QAAQ;SAAC;IAC9B;IAEA,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;IAC1C;IAEA,IAAI,OAAO;QAAE,OAAO,IAAI,CAAC,QAAQ;IAAE;IAEnC,uBAAuB,GACvB,IAAI,IAAI,EAAE;QACR,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC;IACzB;IAEA,0BAA0B,GAC1B,SAAS,KAAK,EAAE;QACd,eAAe,GACf,MAAM,OAAO,QAAQ;YAAE;QAAM;QAC7B,IAAI,CAAC,GAAG,CAAC;QACT,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;IAClB;IAEA,YAAY;QACV,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;YACzB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG;QACvB;QACA,wCAAwC;QACxC,OAAO;IACT;IAEA,gBAAgB;QACd,MAAO,IAAI,CAAC,SAAS;IACvB;IAEA,SAAS;QACP,OAAO,KAAK,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM;IAC7C;IAEA;;;GAGC,GACD,KAAK,OAAO,EAAE;QACZ,gBAAgB;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,QAAQ;IACpD,aAAa;IACb,kDAAkD;IACpD;IAEA;;;GAGC,GACD,OAAO,MAAM,OAAO,EAAE,IAAI,EAAE;QAC1B,IAAI,OAAO,SAAS,UAAU;YAC5B,QAAQ,OAAO,CAAC;QAClB,OAAO,IAAI,KAAK,QAAQ,EAAE;YACxB,QAAQ,QAAQ,CAAC;YACjB,KAAK,QAAQ,CAAC,OAAO,CAAC,CAAC,QAAU,IAAI,CAAC,KAAK,CAAC,SAAS;YACrD,QAAQ,SAAS,CAAC;QACpB;QACA,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,UAAU,IAAI,EAAE;QACrB,IAAI,OAAO,SAAS,UAAU;QAC9B,IAAI,CAAC,KAAK,QAAQ,EAAE;QAEpB,IAAI,KAAK,QAAQ,CAAC,KAAK,CAAC,CAAA,KAAM,OAAO,OAAO,WAAW;YACrD,sCAAsC;YACtC,wBAAwB;YACxB,KAAK,QAAQ,GAAG;gBAAC,KAAK,QAAQ,CAAC,IAAI,CAAC;aAAI;QAC1C,OAAO;YACL,KAAK,QAAQ,CAAC,OAAO,CAAC,CAAC;gBACrB,UAAU,SAAS,CAAC;YACtB;QACF;IACF;AACF;AAEA;;;;;;;;;;;;;AAaA,GAEA;;CAEC,GACD,MAAM,yBAAyB;IAC7B;;GAEC,GACD,YAAY,OAAO,CAAE;QACnB,KAAK;QACL,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA;;GAEC,GACD,QAAQ,IAAI,EAAE;QACZ,IAAI,SAAS,IAAI;YAAE;QAAQ;QAE3B,IAAI,CAAC,GAAG,CAAC;IACX;IAEA,0BAA0B,GAC1B,WAAW,KAAK,EAAE;QAChB,IAAI,CAAC,QAAQ,CAAC;IAChB;IAEA,WAAW;QACT,IAAI,CAAC,SAAS;IAChB;IAEA;;;GAGC,GACD,iBAAiB,OAAO,EAAE,IAAI,EAAE;QAC9B,mBAAmB,GACnB,MAAM,OAAO,QAAQ,IAAI;QACzB,IAAI,MAAM,KAAK,KAAK,GAAG,CAAC,SAAS,EAAE,MAAM;QAEzC,IAAI,CAAC,GAAG,CAAC;IACX;IAEA,SAAS;QACP,MAAM,WAAW,IAAI,aAAa,IAAI,EAAE,IAAI,CAAC,OAAO;QACpD,OAAO,SAAS,KAAK;IACvB;IAEA,WAAW;QACT,IAAI,CAAC,aAAa;QAClB,OAAO;IACT;AACF;AAEA;;;GAGG,GAEH;;;CAGC,GACD,SAAS,OAAO,EAAE;IAChB,IAAI,CAAC,IAAI,OAAO;IAChB,IAAI,OAAO,OAAO,UAAU,OAAO;IAEnC,OAAO,GAAG,MAAM;AAClB;AAEA;;;CAGC,GACD,SAAS,UAAU,EAAE;IACnB,OAAO,OAAO,OAAO,IAAI;AAC3B;AAEA;;;CAGC,GACD,SAAS,iBAAiB,EAAE;IAC1B,OAAO,OAAO,OAAO,IAAI;AAC3B;AAEA;;;CAGC,GACD,SAAS,SAAS,EAAE;IAClB,OAAO,OAAO,OAAO,IAAI;AAC3B;AAEA;;;CAGC,GACD,SAAS,OAAO,GAAG,IAAI;IACrB,MAAM,SAAS,KAAK,GAAG,CAAC,CAAC,IAAM,OAAO,IAAI,IAAI,CAAC;IAC/C,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,qBAAqB,IAAI;IAChC,MAAM,OAAO,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;IAElC,IAAI,OAAO,SAAS,YAAY,KAAK,WAAW,KAAK,QAAQ;QAC3D,KAAK,MAAM,CAAC,KAAK,MAAM,GAAG,GAAG;QAC7B,OAAO;IACT,OAAO;QACL,OAAO,CAAC;IACV;AACF;AAEA,wDAAwD,GAExD;;;;;;CAMC,GACD,SAAS,OAAO,GAAG,IAAI;IACrB,4CAA4C,GAC5C,MAAM,OAAO,qBAAqB;IAClC,MAAM,SAAS,MACX,CAAC,KAAK,OAAO,GAAG,KAAK,IAAI,IACzB,KAAK,GAAG,CAAC,CAAC,IAAM,OAAO,IAAI,IAAI,CAAC,OAAO;IAC3C,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,iBAAiB,EAAE;IAC1B,OAAO,AAAC,IAAI,OAAO,GAAG,QAAQ,KAAK,KAAM,IAAI,CAAC,IAAI,MAAM,GAAG;AAC7D;AAEA;;;;CAIC,GACD,SAAS,WAAW,EAAE,EAAE,MAAM;IAC5B,MAAM,QAAQ,MAAM,GAAG,IAAI,CAAC;IAC5B,OAAO,SAAS,MAAM,KAAK,KAAK;AAClC;AAEA,oEAAoE;AACpE,6DAA6D;AAC7D,wEAAwE;AACxE,sEAAsE;AACtE,yBAAyB;AACzB,uEAAuE;AACvE,+BAA+B;AAC/B,MAAM,aAAa;AAEnB,8CAA8C;AAC9C,iEAAiE;AACjE,4CAA4C;AAC5C,kEAAkE;AAClE,qEAAqE;AACrE,+CAA+C;AAC/C;;;;CAIC,GACD,SAAS,uBAAuB,OAAO,EAAE,EAAE,QAAQ,EAAE;IACnD,IAAI,cAAc;IAElB,OAAO,QAAQ,GAAG,CAAC,CAAC;QAClB,eAAe;QACf,MAAM,SAAS;QACf,IAAI,KAAK,OAAO;QAChB,IAAI,MAAM;QAEV,MAAO,GAAG,MAAM,GAAG,EAAG;YACpB,MAAM,QAAQ,WAAW,IAAI,CAAC;YAC9B,IAAI,CAAC,OAAO;gBACV,OAAO;gBACP;YACF;YACA,OAAO,GAAG,SAAS,CAAC,GAAG,MAAM,KAAK;YAClC,KAAK,GAAG,SAAS,CAAC,MAAM,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM;YAC/C,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,QAAQ,KAAK,CAAC,EAAE,EAAE;gBACpC,4BAA4B;gBAC5B,OAAO,OAAO,OAAO,OAAO,KAAK,CAAC,EAAE,IAAI;YAC1C,OAAO;gBACL,OAAO,KAAK,CAAC,EAAE;gBACf,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK;oBACpB;gBACF;YACF;QACF;QACA,OAAO;IACT,GAAG,GAAG,CAAC,CAAA,KAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;AAC/B;AAEA,gDAAgD,GAChD,gEAAgE,GAEhE,iBAAiB;AACjB,MAAM,mBAAmB;AACzB,MAAM,WAAW;AACjB,MAAM,sBAAsB;AAC5B,MAAM,YAAY;AAClB,MAAM,cAAc,0EAA0E,8BAA8B;AAC5H,MAAM,mBAAmB,gBAAgB,QAAQ;AACjD,MAAM,iBAAiB;AAEvB;;AAEA,GACA,MAAM,UAAU,CAAC,OAAO,CAAC,CAAC;IACxB,MAAM,eAAe;IACrB,IAAI,KAAK,MAAM,EAAE;QACf,KAAK,KAAK,GAAG,OACX,cACA,QACA,KAAK,MAAM,EACX;IACJ;IACA,OAAO,UAAU;QACf,OAAO;QACP,OAAO;QACP,KAAK;QACL,WAAW;QACX,yBAAyB,GACzB,YAAY,CAAC,GAAG;YACd,IAAI,EAAE,KAAK,KAAK,GAAG,KAAK,WAAW;QACrC;IACF,GAAG;AACL;AAEA,eAAe;AACf,MAAM,mBAAmB;IACvB,OAAO;IAAgB,WAAW;AACpC;AACA,MAAM,mBAAmB;IACvB,OAAO;IACP,OAAO;IACP,KAAK;IACL,SAAS;IACT,UAAU;QAAC;KAAiB;AAC9B;AACA,MAAM,oBAAoB;IACxB,OAAO;IACP,OAAO;IACP,KAAK;IACL,SAAS;IACT,UAAU;QAAC;KAAiB;AAC9B;AACA,MAAM,qBAAqB;IACzB,OAAO;AACT;AACA;;;;;;;CAOC,GACD,MAAM,UAAU,SAAS,KAAK,EAAE,GAAG,EAAE,cAAc,CAAC,CAAC;IACnD,MAAM,OAAO,UACX;QACE,OAAO;QACP;QACA;QACA,UAAU,EAAE;IACd,GACA;IAEF,KAAK,QAAQ,CAAC,IAAI,CAAC;QACjB,OAAO;QACP,yEAAyE;QACzE,2EAA2E;QAC3E,OAAO;QACP,KAAK;QACL,cAAc;QACd,WAAW;IACb;IACA,MAAM,eAAe,OACnB,iDAAiD;IACjD,KACA,KACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,0EAA0E;IAC1E,kCACA,sBACA,oBAAoB,oDAAoD;;IAE1E,uDAAuD;IACvD,KAAK,QAAQ,CAAC,IAAI,CAChB;QACE,4EAA4E;QAC5E,sBAAsB;QACtB,0GAA0G;QAC1G,MAAM;QAEN,wEAAwE;QACxE,sEAAsE;QACtE,uEAAuE;QACvE,mEAAmE;QACnE,wEAAwE;QACxE,iCAAiC;QACjC,EAAE;QACF,mCAAmC;QACnC,0DAA0D;QAE1D,OAAO,OACL,QACA,KACA,cACA,wBACA,QAAQ,4BAA4B;IACxC;IAEF,OAAO;AACT;AACA,MAAM,sBAAsB,QAAQ,MAAM;AAC1C,MAAM,uBAAuB,QAAQ,QAAQ;AAC7C,MAAM,oBAAoB,QAAQ,KAAK;AACvC,MAAM,cAAc;IAClB,OAAO;IACP,OAAO;IACP,WAAW;AACb;AACA,MAAM,gBAAgB;IACpB,OAAO;IACP,OAAO;IACP,WAAW;AACb;AACA,MAAM,qBAAqB;IACzB,OAAO;IACP,OAAO;IACP,WAAW;AACb;AACA,MAAM,cAAc;IAClB,OAAO;IACP,OAAO;IACP,KAAK;IACL,UAAU;QACR;QACA;YACE,OAAO;YACP,KAAK;YACL,WAAW;YACX,UAAU;gBAAC;aAAiB;QAC9B;KACD;AACH;AACA,MAAM,aAAa;IACjB,OAAO;IACP,OAAO;IACP,WAAW;AACb;AACA,MAAM,wBAAwB;IAC5B,OAAO;IACP,OAAO;IACP,WAAW;AACb;AACA,MAAM,eAAe;IACnB,gDAAgD;IAChD,OAAO,YAAY;IACnB,WAAW;AACb;AAEA;;;;;;CAMC,GACD,MAAM,oBAAoB,SAAS,IAAI;IACrC,OAAO,OAAO,MAAM,CAAC,MACnB;QACE,yBAAyB,GACzB,YAAY,CAAC,GAAG;YAAW,KAAK,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE;QAAE;QACzD,yBAAyB,GACzB,UAAU,CAAC,GAAG;YAAW,IAAI,KAAK,IAAI,CAAC,WAAW,KAAK,CAAC,CAAC,EAAE,EAAE,KAAK,WAAW;QAAI;IACnF;AACJ;AAEA,IAAI,QAAQ,WAAW,GAAE,OAAO,MAAM,CAAC;IACrC,WAAW;IACX,kBAAkB;IAClB,kBAAkB;IAClB,oBAAoB;IACpB,kBAAkB;IAClB,SAAS;IACT,sBAAsB;IACtB,qBAAqB;IACrB,eAAe;IACf,aAAa;IACb,mBAAmB;IACnB,mBAAmB;IACnB,UAAU;IACV,kBAAkB;IAClB,cAAc;IACd,aAAa;IACb,WAAW;IACX,oBAAoB;IACpB,mBAAmB;IACnB,aAAa;IACb,gBAAgB;IAChB,SAAS;IACT,YAAY;IACZ,qBAAqB;IACrB,uBAAuB;AACzB;AAEA;;;AAGA,GAEA,+BAA+B;AAC/B,+DAA+D;AAE/D,8EAA8E;AAC9E,sEAAsE;AAEtE,2EAA2E;AAC3E,+EAA+E;AAC/E,gFAAgF;AAChF,8EAA8E;AAC9E,uEAAuE;AAEvE,SAAS;AAET,iEAAiE;AACjE;;;;;;;;CAQC,GACD,SAAS,sBAAsB,KAAK,EAAE,QAAQ;IAC5C,MAAM,SAAS,MAAM,KAAK,CAAC,MAAM,KAAK,GAAG,EAAE;IAC3C,IAAI,WAAW,KAAK;QAClB,SAAS,WAAW;IACtB;AACF;AAEA;;;CAGC,GACD,SAAS,eAAe,IAAI,EAAE,OAAO;IACnC,wCAAwC;IACxC,IAAI,KAAK,SAAS,KAAK,WAAW;QAChC,KAAK,KAAK,GAAG,KAAK,SAAS;QAC3B,OAAO,KAAK,SAAS;IACvB;AACF;AAEA;;;CAGC,GACD,SAAS,cAAc,IAAI,EAAE,MAAM;IACjC,IAAI,CAAC,QAAQ;IACb,IAAI,CAAC,KAAK,aAAa,EAAE;IAEzB,4EAA4E;IAC5E,6EAA6E;IAC7E,yEAAyE;IACzE,+EAA+E;IAC/E,QAAQ;IACR,KAAK,KAAK,GAAG,SAAS,KAAK,aAAa,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,OAAO;IAChE,KAAK,aAAa,GAAG;IACrB,KAAK,QAAQ,GAAG,KAAK,QAAQ,IAAI,KAAK,aAAa;IACnD,OAAO,KAAK,aAAa;IAEzB,6DAA6D;IAC7D,gDAAgD;IAChD,wCAAwC;IACxC,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,SAAS,GAAG;AACrD;AAEA;;;CAGC,GACD,SAAS,eAAe,IAAI,EAAE,OAAO;IACnC,IAAI,CAAC,MAAM,OAAO,CAAC,KAAK,OAAO,GAAG;IAElC,KAAK,OAAO,GAAG,UAAU,KAAK,OAAO;AACvC;AAEA;;;CAGC,GACD,SAAS,aAAa,IAAI,EAAE,OAAO;IACjC,IAAI,CAAC,KAAK,KAAK,EAAE;IACjB,IAAI,KAAK,KAAK,IAAI,KAAK,GAAG,EAAE,MAAM,IAAI,MAAM;IAE5C,KAAK,KAAK,GAAG,KAAK,KAAK;IACvB,OAAO,KAAK,KAAK;AACnB;AAEA;;;CAGC,GACD,SAAS,iBAAiB,IAAI,EAAE,OAAO;IACrC,wCAAwC;IACxC,IAAI,KAAK,SAAS,KAAK,WAAW,KAAK,SAAS,GAAG;AACrD;AAEA,0DAA0D;AAC1D,oDAAoD;AACpD,MAAM,iBAAiB,CAAC,MAAM;IAC5B,IAAI,CAAC,KAAK,WAAW,EAAE;IACvB,wEAAwE;IACxE,qCAAqC;IACrC,IAAI,KAAK,MAAM,EAAE,MAAM,IAAI,MAAM;IAEjC,MAAM,eAAe,OAAO,MAAM,CAAC,CAAC,GAAG;IACvC,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC;QAAU,OAAO,IAAI,CAAC,IAAI;IAAE;IAEvD,KAAK,QAAQ,GAAG,aAAa,QAAQ;IACrC,KAAK,KAAK,GAAG,OAAO,aAAa,WAAW,EAAE,UAAU,aAAa,KAAK;IAC1E,KAAK,MAAM,GAAG;QACZ,WAAW;QACX,UAAU;YACR,OAAO,MAAM,CAAC,cAAc;gBAAE,YAAY;YAAK;SAChD;IACH;IACA,KAAK,SAAS,GAAG;IAEjB,OAAO,aAAa,WAAW;AACjC;AAEA,uDAAuD;AACvD,MAAM,kBAAkB;IACtB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,QAAQ,uBAAuB;CAChC;AAED,MAAM,wBAAwB;AAE9B;;;;;CAKC,GACD,SAAS,gBAAgB,WAAW,EAAE,eAAe,EAAE,YAAY,qBAAqB;IACtF,uDAAuD,GACvD,MAAM,mBAAmB,OAAO,MAAM,CAAC;IAEvC,4EAA4E;IAC5E,gFAAgF;IAChF,IAAI,OAAO,gBAAgB,UAAU;QACnC,YAAY,WAAW,YAAY,KAAK,CAAC;IAC3C,OAAO,IAAI,MAAM,OAAO,CAAC,cAAc;QACrC,YAAY,WAAW;IACzB,OAAO;QACL,OAAO,IAAI,CAAC,aAAa,OAAO,CAAC,SAAS,SAAS;YACjD,uDAAuD;YACvD,OAAO,MAAM,CACX,kBACA,gBAAgB,WAAW,CAAC,UAAU,EAAE,iBAAiB;QAE7D;IACF;IACA,OAAO;;IAEP,MAAM;IAEN;;;;;;;GAOC,GACD,SAAS,YAAY,SAAS,EAAE,WAAW;QACzC,IAAI,iBAAiB;YACnB,cAAc,YAAY,GAAG,CAAC,CAAA,IAAK,EAAE,WAAW;QAClD;QACA,YAAY,OAAO,CAAC,SAAS,OAAO;YAClC,MAAM,OAAO,QAAQ,KAAK,CAAC;YAC3B,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG;gBAAC;gBAAW,gBAAgB,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;aAAE;QAC5E;IACF;AACF;AAEA;;;;;;;CAOC,GACD,SAAS,gBAAgB,OAAO,EAAE,aAAa;IAC7C,gDAAgD;IAChD,qDAAqD;IACrD,IAAI,eAAe;QACjB,OAAO,OAAO;IAChB;IAEA,OAAO,cAAc,WAAW,IAAI;AACtC;AAEA;;;2BAG2B,GAC3B,SAAS,cAAc,OAAO;IAC5B,OAAO,gBAAgB,QAAQ,CAAC,QAAQ,WAAW;AACrD;AAEA;;;;;AAKA,GAEA;;CAEC,GACD,MAAM,mBAAmB,CAAC;AAE1B;;CAEC,GACD,MAAM,QAAQ,CAAC;IACb,QAAQ,KAAK,CAAC;AAChB;AAEA;;;CAGC,GACD,MAAM,OAAO,CAAC,SAAS,GAAG;IACxB,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,SAAS,KAAK;AACrC;AAEA;;;CAGC,GACD,MAAM,aAAa,CAAC,SAAS;IAC3B,IAAI,gBAAgB,CAAC,GAAG,QAAQ,CAAC,EAAE,SAAS,CAAC,EAAE;IAE/C,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,QAAQ,EAAE,EAAE,SAAS;IACrD,gBAAgB,CAAC,GAAG,QAAQ,CAAC,EAAE,SAAS,CAAC,GAAG;AAC9C;AAEA,mCAAmC,GAEnC;;AAEA,GAEA,MAAM,kBAAkB,IAAI;AAE5B;;;;;;;;;;;;;;;;;;;;;;;;;;;CA2BC,GACD,SAAS,gBAAgB,IAAI,EAAE,OAAO,EAAE,EAAE,GAAG,EAAE;IAC7C,IAAI,SAAS;IACb,MAAM,aAAa,IAAI,CAAC,IAAI;IAC5B,iCAAiC,GACjC,MAAM,OAAO,CAAC;IACd,gCAAgC,GAChC,MAAM,YAAY,CAAC;IAEnB,IAAK,IAAI,IAAI,GAAG,KAAK,QAAQ,MAAM,EAAE,IAAK;QACxC,SAAS,CAAC,IAAI,OAAO,GAAG,UAAU,CAAC,EAAE;QACrC,IAAI,CAAC,IAAI,OAAO,GAAG;QACnB,UAAU,iBAAiB,OAAO,CAAC,IAAI,EAAE;IAC3C;IACA,mFAAmF;IACnF,kCAAkC;IAClC,IAAI,CAAC,IAAI,GAAG;IACZ,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG;IAClB,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG;AACrB;AAEA;;CAEC,GACD,SAAS,gBAAgB,IAAI;IAC3B,IAAI,CAAC,MAAM,OAAO,CAAC,KAAK,KAAK,GAAG;IAEhC,IAAI,KAAK,IAAI,IAAI,KAAK,YAAY,IAAI,KAAK,WAAW,EAAE;QACtD,MAAM;QACN,MAAM;IACR;IAEA,IAAI,OAAO,KAAK,UAAU,KAAK,YAAY,KAAK,UAAU,KAAK,MAAM;QACnE,MAAM;QACN,MAAM;IACR;IAEA,gBAAgB,MAAM,KAAK,KAAK,EAAE;QAAE,KAAK;IAAa;IACtD,KAAK,KAAK,GAAG,uBAAuB,KAAK,KAAK,EAAE;QAAE,UAAU;IAAG;AACjE;AAEA;;CAEC,GACD,SAAS,cAAc,IAAI;IACzB,IAAI,CAAC,MAAM,OAAO,CAAC,KAAK,GAAG,GAAG;IAE9B,IAAI,KAAK,IAAI,IAAI,KAAK,UAAU,IAAI,KAAK,SAAS,EAAE;QAClD,MAAM;QACN,MAAM;IACR;IAEA,IAAI,OAAO,KAAK,QAAQ,KAAK,YAAY,KAAK,QAAQ,KAAK,MAAM;QAC/D,MAAM;QACN,MAAM;IACR;IAEA,gBAAgB,MAAM,KAAK,GAAG,EAAE;QAAE,KAAK;IAAW;IAClD,KAAK,GAAG,GAAG,uBAAuB,KAAK,GAAG,EAAE;QAAE,UAAU;IAAG;AAC7D;AAEA;;;;;;;;;;CAUC,GACD,SAAS,WAAW,IAAI;IACtB,IAAI,KAAK,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,YAAY,KAAK,KAAK,KAAK,MAAM;QACvE,KAAK,UAAU,GAAG,KAAK,KAAK;QAC5B,OAAO,KAAK,KAAK;IACnB;AACF;AAEA;;CAEC,GACD,SAAS,WAAW,IAAI;IACtB,WAAW;IAEX,IAAI,OAAO,KAAK,UAAU,KAAK,UAAU;QACvC,KAAK,UAAU,GAAG;YAAE,OAAO,KAAK,UAAU;QAAC;IAC7C;IACA,IAAI,OAAO,KAAK,QAAQ,KAAK,UAAU;QACrC,KAAK,QAAQ,GAAG;YAAE,OAAO,KAAK,QAAQ;QAAC;IACzC;IAEA,gBAAgB;IAChB,cAAc;AAChB;AAEA;;;;;;AAMA,GAEA,cAAc;AAEd;;;;;;;CAOC,GACD,SAAS,gBAAgB,QAAQ;IAC/B;;;;;GAKC,GACD,SAAS,OAAO,KAAK,EAAE,MAAM;QAC3B,OAAO,IAAI,OACT,OAAO,QACP,MACE,CAAC,SAAS,gBAAgB,GAAG,MAAM,EAAE,IACrC,CAAC,SAAS,YAAY,GAAG,MAAM,EAAE,IACjC,CAAC,SAAS,MAAM,EAAE;IAExB;IAEA;;;;;;;;;;;;EAYA,GACA,MAAM;QACJ,aAAc;YACZ,IAAI,CAAC,YAAY,GAAG,CAAC;YACrB,aAAa;YACb,IAAI,CAAC,OAAO,GAAG,EAAE;YACjB,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,QAAQ,GAAG;QAClB;QAEA,aAAa;QACb,QAAQ,EAAE,EAAE,IAAI,EAAE;YAChB,KAAK,QAAQ,GAAG,IAAI,CAAC,QAAQ;YAC7B,aAAa;YACb,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG;YAClC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;gBAAC;gBAAM;aAAG;YAC5B,IAAI,CAAC,OAAO,IAAI,iBAAiB,MAAM;QACzC;QAEA,UAAU;YACR,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,GAAG;gBAC7B,4DAA4D;gBAC5D,aAAa;gBACb,IAAI,CAAC,IAAI,GAAG,IAAM;YACpB;YACA,MAAM,cAAc,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA,KAAM,EAAE,CAAC,EAAE;YAChD,IAAI,CAAC,SAAS,GAAG,OAAO,uBAAuB,aAAa;gBAAE,UAAU;YAAI,IAAI;YAChF,IAAI,CAAC,SAAS,GAAG;QACnB;QAEA,sBAAsB,GACtB,KAAK,CAAC,EAAE;YACN,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS;YACzC,MAAM,QAAQ,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YAClC,IAAI,CAAC,OAAO;gBAAE,OAAO;YAAM;YAE3B,wCAAwC;YACxC,MAAM,IAAI,MAAM,SAAS,CAAC,CAAC,IAAI,IAAM,IAAI,KAAK,OAAO;YACrD,aAAa;YACb,MAAM,YAAY,IAAI,CAAC,YAAY,CAAC,EAAE;YACtC,sEAAsE;YACtE,+CAA+C;YAC/C,MAAM,MAAM,CAAC,GAAG;YAEhB,OAAO,OAAO,MAAM,CAAC,OAAO;QAC9B;IACF;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA8BA,GACA,MAAM;QACJ,aAAc;YACZ,aAAa;YACb,IAAI,CAAC,KAAK,GAAG,EAAE;YACf,aAAa;YACb,IAAI,CAAC,YAAY,GAAG,EAAE;YACtB,IAAI,CAAC,KAAK,GAAG;YAEb,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,CAAC,UAAU,GAAG;QACpB;QAEA,aAAa;QACb,WAAW,KAAK,EAAE;YAChB,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM;YAE7D,MAAM,UAAU,IAAI;YACpB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,OAAO,CAAC,CAAC,CAAC,IAAI,KAAK,GAAK,QAAQ,OAAO,CAAC,IAAI;YACpE,QAAQ,OAAO;YACf,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG;YAC3B,OAAO;QACT;QAEA,6BAA6B;YAC3B,OAAO,IAAI,CAAC,UAAU,KAAK;QAC7B;QAEA,cAAc;YACZ,IAAI,CAAC,UAAU,GAAG;QACpB;QAEA,aAAa;QACb,QAAQ,EAAE,EAAE,IAAI,EAAE;YAChB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;gBAAC;gBAAI;aAAK;YAC1B,IAAI,KAAK,IAAI,KAAK,SAAS,IAAI,CAAC,KAAK;QACvC;QAEA,sBAAsB,GACtB,KAAK,CAAC,EAAE;YACN,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU;YACzC,EAAE,SAAS,GAAG,IAAI,CAAC,SAAS;YAC5B,IAAI,SAAS,EAAE,IAAI,CAAC;YAEpB,8EAA8E;YAC9E,0EAA0E;YAC1E,0EAA0E;YAC1E,uCAAuC;YAEvC,4CAA4C;YAC5C,EAAE;YACF,iBAAiB;YAEjB,wEAAwE;YACxE,wEAAwE;YACxE,uEAAuE;YACvE,uEAAuE;YACvE,gDAAgD;YAEhD,mEAAmE;YACnE,wEAAwE;YAExE,wDAAwD;YACxD,4DAA4D;YAC5D,SAAS;YACT,iBAAiB;YAEjB,qEAAqE;YACrE,0EAA0E;YAC1E,kCAAkC;YAClC,EAAE;YACF,6EAA6E;YAC7E,kCAAkC;YAClC,uDAAuD;YACvD,uDAAuD;YACvD,IAAI,IAAI,CAAC,0BAA0B,IAAI;gBACrC,IAAI,UAAU,OAAO,KAAK,KAAK,IAAI,CAAC,SAAS;qBAAS;oBACpD,MAAM,KAAK,IAAI,CAAC,UAAU,CAAC;oBAC3B,GAAG,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG;oBAChC,SAAS,GAAG,IAAI,CAAC;gBACnB;YACF;YAEA,IAAI,QAAQ;gBACV,IAAI,CAAC,UAAU,IAAI,OAAO,QAAQ,GAAG;gBACrC,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,KAAK,EAAE;oBAClC,+CAA+C;oBAC/C,IAAI,CAAC,WAAW;gBAClB;YACF;YAEA,OAAO;QACT;IACF;IAEA;;;;;;GAMC,GACD,SAAS,eAAe,IAAI;QAC1B,MAAM,KAAK,IAAI;QAEf,KAAK,QAAQ,CAAC,OAAO,CAAC,CAAA,OAAQ,GAAG,OAAO,CAAC,KAAK,KAAK,EAAE;gBAAE,MAAM;gBAAM,MAAM;YAAQ;QAEjF,IAAI,KAAK,aAAa,EAAE;YACtB,GAAG,OAAO,CAAC,KAAK,aAAa,EAAE;gBAAE,MAAM;YAAM;QAC/C;QACA,IAAI,KAAK,OAAO,EAAE;YAChB,GAAG,OAAO,CAAC,KAAK,OAAO,EAAE;gBAAE,MAAM;YAAU;QAC7C;QAEA,OAAO;IACT;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BC,GAED;;;;;;;;GAQC,GACD,SAAS,YAAY,IAAI,EAAE,MAAM;QAC/B,MAAM,QAAmC;QACzC,IAAI,KAAK,UAAU,EAAE,OAAO;QAE5B;YACE;YACA,2EAA2E;YAC3E,sCAAsC;YACtC;YACA;YACA;SACD,CAAC,OAAO,CAAC,CAAA,MAAO,IAAI,MAAM;QAE3B,SAAS,kBAAkB,CAAC,OAAO,CAAC,CAAA,MAAO,IAAI,MAAM;QAErD,6DAA6D;QAC7D,KAAK,aAAa,GAAG;QAErB;YACE;YACA,4EAA4E;YAC5E,0DAA0D;YAC1D;YACA,0CAA0C;YAC1C;SACD,CAAC,OAAO,CAAC,CAAA,MAAO,IAAI,MAAM;QAE3B,KAAK,UAAU,GAAG;QAElB,IAAI,iBAAiB;QACrB,IAAI,OAAO,KAAK,QAAQ,KAAK,YAAY,KAAK,QAAQ,CAAC,QAAQ,EAAE;YAC/D,mEAAmE;YACnE,kEAAkE;YAClE,OAAO;YACP,KAAK,QAAQ,GAAG,OAAO,MAAM,CAAC,CAAC,GAAG,KAAK,QAAQ;YAC/C,iBAAiB,KAAK,QAAQ,CAAC,QAAQ;YACvC,OAAO,KAAK,QAAQ,CAAC,QAAQ;QAC/B;QACA,iBAAiB,kBAAkB;QAEnC,IAAI,KAAK,QAAQ,EAAE;YACjB,KAAK,QAAQ,GAAG,gBAAgB,KAAK,QAAQ,EAAE,SAAS,gBAAgB;QAC1E;QAEA,MAAM,gBAAgB,GAAG,OAAO,gBAAgB;QAEhD,IAAI,QAAQ;YACV,IAAI,CAAC,KAAK,KAAK,EAAE,KAAK,KAAK,GAAG;YAC9B,MAAM,OAAO,GAAG,OAAO,MAAM,KAAK;YAClC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,cAAc,EAAE,KAAK,GAAG,GAAG;YAClD,IAAI,KAAK,GAAG,EAAE,MAAM,KAAK,GAAG,OAAO,MAAM,GAAG;YAC5C,MAAM,aAAa,GAAG,OAAO,MAAM,GAAG,KAAK;YAC3C,IAAI,KAAK,cAAc,IAAI,OAAO,aAAa,EAAE;gBAC/C,MAAM,aAAa,IAAI,CAAC,KAAK,GAAG,GAAG,MAAM,EAAE,IAAI,OAAO,aAAa;YACrE;QACF;QACA,IAAI,KAAK,OAAO,EAAE,MAAM,SAAS,GAAG,OAAuC,KAAK,OAAO;QACvF,IAAI,CAAC,KAAK,QAAQ,EAAE,KAAK,QAAQ,GAAG,EAAE;QAEtC,KAAK,QAAQ,GAAG,EAAE,CAAC,MAAM,IAAI,KAAK,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC;YACvD,OAAO,kBAAkB,MAAM,SAAS,OAAO;QACjD;QACA,KAAK,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC;YAAI,YAA+B,GAAI;QAAQ;QAE/E,IAAI,KAAK,MAAM,EAAE;YACf,YAAY,KAAK,MAAM,EAAE;QAC3B;QAEA,MAAM,OAAO,GAAG,eAAe;QAC/B,OAAO;IACT;IAEA,IAAI,CAAC,SAAS,kBAAkB,EAAE,SAAS,kBAAkB,GAAG,EAAE;IAElE,qCAAqC;IACrC,IAAI,SAAS,QAAQ,IAAI,SAAS,QAAQ,CAAC,QAAQ,CAAC,SAAS;QAC3D,MAAM,IAAI,MAAM;IAClB;IAEA,sDAAsD;IACtD,SAAS,gBAAgB,GAAG,UAAU,SAAS,gBAAgB,IAAI,CAAC;IAEpE,OAAO,YAA+B;AACxC;AAEA;;;;;;;;;;GAUG,GACH,SAAS,mBAAmB,IAAI;IAC9B,IAAI,CAAC,MAAM,OAAO;IAElB,OAAO,KAAK,cAAc,IAAI,mBAAmB,KAAK,MAAM;AAC9D;AAEA;;;;;;;;;GASG,GACH,SAAS,kBAAkB,IAAI;IAC7B,IAAI,KAAK,QAAQ,IAAI,CAAC,KAAK,cAAc,EAAE;QACzC,KAAK,cAAc,GAAG,KAAK,QAAQ,CAAC,GAAG,CAAC,SAAS,OAAO;YACtD,OAAO,UAAU,MAAM;gBAAE,UAAU;YAAK,GAAG;QAC7C;IACF;IAEA,SAAS;IACT,4EAA4E;IAC5E,kEAAkE;IAClE,IAAI,KAAK,cAAc,EAAE;QACvB,OAAO,KAAK,cAAc;IAC5B;IAEA,QAAQ;IACR,2DAA2D;IAC3D,uDAAuD;IACvD,kCAAkC;IAClC,IAAI,mBAAmB,OAAO;QAC5B,OAAO,UAAU,MAAM;YAAE,QAAQ,KAAK,MAAM,GAAG,UAAU,KAAK,MAAM,IAAI;QAAK;IAC/E;IAEA,IAAI,OAAO,QAAQ,CAAC,OAAO;QACzB,OAAO,UAAU;IACnB;IAEA,sDAAsD;IACtD,OAAO;AACT;AAEA,IAAI,UAAU;AAEd,MAAM,2BAA2B;IAC/B,YAAY,MAAM,EAAE,IAAI,CAAE;QACxB,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEA;;;AAGA,GAIA;;;;;;;;;;;;;;;;;;;AAmBA,GAGA,MAAM,SAAS;AACf,MAAM,UAAU;AAChB,MAAM,WAAW,OAAO;AACxB,MAAM,mBAAmB;AAEzB;;;CAGC,GACD,MAAM,OAAO,SAAS,IAAI;IACxB,kEAAkE;IAClE,qCAAqC,GACrC,MAAM,YAAY,OAAO,MAAM,CAAC;IAChC,mCAAmC,GACnC,MAAM,UAAU,OAAO,MAAM,CAAC;IAC9B,yBAAyB,GACzB,MAAM,UAAU,EAAE;IAElB,qEAAqE;IACrE,sDAAsD;IACtD,IAAI,YAAY;IAChB,MAAM,qBAAqB;IAC3B,qBAAqB,GACrB,MAAM,qBAAqB;QAAE,mBAAmB;QAAM,MAAM;QAAc,UAAU,EAAE;IAAC;IAEvF,uEAAuE;IACvE,yCAAyC;IACzC,sBAAsB,GACtB,IAAI,UAAU;QACZ,qBAAqB;QACrB,oBAAoB;QACpB,eAAe;QACf,kBAAkB;QAClB,aAAa;QACb,aAAa;QACb,WAAW;QACX,oEAAoE;QACpE,0DAA0D;QAC1D,WAAW;IACb;IAEA,qBAAqB,GAErB;;;GAGC,GACD,SAAS,mBAAmB,YAAY;QACtC,OAAO,QAAQ,aAAa,CAAC,IAAI,CAAC;IACpC;IAEA;;GAEC,GACD,SAAS,cAAc,KAAK;QAC1B,IAAI,UAAU,MAAM,SAAS,GAAG;QAEhC,WAAW,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,SAAS,GAAG;QAE3D,6DAA6D;QAC7D,MAAM,QAAQ,QAAQ,gBAAgB,CAAC,IAAI,CAAC;QAC5C,IAAI,OAAO;YACT,MAAM,WAAW,YAAY,KAAK,CAAC,EAAE;YACrC,IAAI,CAAC,UAAU;gBACb,KAAK,mBAAmB,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC9C,KAAK,qDAAqD;YAC5D;YACA,OAAO,WAAW,KAAK,CAAC,EAAE,GAAG;QAC/B;QAEA,OAAO,QACJ,KAAK,CAAC,OACN,IAAI,CAAC,CAAC,SAAW,mBAAmB,WAAW,YAAY;IAChE;IAEA;;;;;;;;;;;;;;;;;;;;EAoBA,GACA,SAAS,UAAU,kBAAkB,EAAE,aAAa,EAAE,cAAc;QAClE,IAAI,OAAO;QACX,IAAI,eAAe;QACnB,IAAI,OAAO,kBAAkB,UAAU;YACrC,OAAO;YACP,iBAAiB,cAAc,cAAc;YAC7C,eAAe,cAAc,QAAQ;QACvC,OAAO;YACL,UAAU;YACV,WAAW,UAAU;YACrB,WAAW,UAAU;YACrB,eAAe;YACf,OAAO;QACT;QAEA,0DAA0D;QAC1D,wCAAwC;QACxC,IAAI,mBAAmB,WAAW;YAAE,iBAAiB;QAAM;QAE3D,mCAAmC,GACnC,MAAM,UAAU;YACd;YACA,UAAU;QACZ;QACA,2EAA2E;QAC3E,4CAA4C;QAC5C,KAAK,oBAAoB;QAEzB,wEAAwE;QACxE,qDAAqD;QACrD,MAAM,SAAS,QAAQ,MAAM,GACzB,QAAQ,MAAM,GACd,WAAW,QAAQ,QAAQ,EAAE,QAAQ,IAAI,EAAE;QAE/C,OAAO,IAAI,GAAG,QAAQ,IAAI;QAC1B,uDAAuD;QACvD,KAAK,mBAAmB;QAExB,OAAO;IACT;IAEA;;;;;;;;EAQA,GACA,SAAS,WAAW,YAAY,EAAE,eAAe,EAAE,cAAc,EAAE,YAAY;QAC7E,MAAM,cAAc,OAAO,MAAM,CAAC;QAElC;;;;;KAKC,GACD,SAAS,YAAY,IAAI,EAAE,SAAS;YAClC,OAAO,KAAK,QAAQ,CAAC,UAAU;QACjC;QAEA,SAAS;YACP,IAAI,CAAC,IAAI,QAAQ,EAAE;gBACjB,QAAQ,OAAO,CAAC;gBAChB;YACF;YAEA,IAAI,YAAY;YAChB,IAAI,gBAAgB,CAAC,SAAS,GAAG;YACjC,IAAI,QAAQ,IAAI,gBAAgB,CAAC,IAAI,CAAC;YACtC,IAAI,MAAM;YAEV,MAAO,MAAO;gBACZ,OAAO,WAAW,SAAS,CAAC,WAAW,MAAM,KAAK;gBAClD,MAAM,OAAO,SAAS,gBAAgB,GAAG,KAAK,CAAC,EAAE,CAAC,WAAW,KAAK,KAAK,CAAC,EAAE;gBAC1E,MAAM,OAAO,YAAY,KAAK;gBAC9B,IAAI,MAAM;oBACR,MAAM,CAAC,MAAM,iBAAiB,GAAG;oBACjC,QAAQ,OAAO,CAAC;oBAChB,MAAM;oBAEN,WAAW,CAAC,KAAK,GAAG,CAAC,WAAW,CAAC,KAAK,IAAI,CAAC,IAAI;oBAC/C,IAAI,WAAW,CAAC,KAAK,IAAI,kBAAkB,aAAa;oBACxD,IAAI,KAAK,UAAU,CAAC,MAAM;wBACxB,iDAAiD;wBACjD,2BAA2B;wBAC3B,OAAO,KAAK,CAAC,EAAE;oBACjB,OAAO;wBACL,MAAM,WAAW,SAAS,gBAAgB,CAAC,KAAK,IAAI;wBACpD,YAAY,KAAK,CAAC,EAAE,EAAE;oBACxB;gBACF,OAAO;oBACL,OAAO,KAAK,CAAC,EAAE;gBACjB;gBACA,YAAY,IAAI,gBAAgB,CAAC,SAAS;gBAC1C,QAAQ,IAAI,gBAAgB,CAAC,IAAI,CAAC;YACpC;YACA,OAAO,WAAW,SAAS,CAAC;YAC5B,QAAQ,OAAO,CAAC;QAClB;QAEA,SAAS;YACP,IAAI,eAAe,IAAI;YACvB,0BAA0B,GAC1B,IAAI,SAAS;YAEb,IAAI,OAAO,IAAI,WAAW,KAAK,UAAU;gBACvC,IAAI,CAAC,SAAS,CAAC,IAAI,WAAW,CAAC,EAAE;oBAC/B,QAAQ,OAAO,CAAC;oBAChB;gBACF;gBACA,SAAS,WAAW,IAAI,WAAW,EAAE,YAAY,MAAM,aAAa,CAAC,IAAI,WAAW,CAAC;gBACrF,aAAa,CAAC,IAAI,WAAW,CAAC,GAAgC,OAAO,IAAI;YAC3E,OAAO;gBACL,SAAS,cAAc,YAAY,IAAI,WAAW,CAAC,MAAM,GAAG,IAAI,WAAW,GAAG;YAChF;YAEA,6EAA6E;YAC7E,iFAAiF;YACjF,mFAAmF;YACnF,SAAS;YACT,IAAI,IAAI,SAAS,GAAG,GAAG;gBACrB,aAAa,OAAO,SAAS;YAC/B;YACA,QAAQ,gBAAgB,CAAC,OAAO,QAAQ,EAAE,OAAO,QAAQ;QAC3D;QAEA,SAAS;YACP,IAAI,IAAI,WAAW,IAAI,MAAM;gBAC3B;YACF,OAAO;gBACL;YACF;YACA,aAAa;QACf;QAEA;;;KAGC,GACD,SAAS,YAAY,OAAO,EAAE,KAAK;YACjC,IAAI,YAAY,IAAI;YAEpB,QAAQ,UAAU,CAAC;YACnB,QAAQ,OAAO,CAAC;YAChB,QAAQ,QAAQ;QAClB;QAEA;;;KAGC,GACD,SAAS,eAAe,KAAK,EAAE,KAAK;YAClC,IAAI,IAAI;YACR,MAAM,MAAM,MAAM,MAAM,GAAG;YAC3B,MAAO,KAAK,IAAK;gBACf,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,EAAE;oBAAE;oBAAK;gBAAU;gBACtC,MAAM,QAAQ,SAAS,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,EAAE;gBAC7D,MAAM,OAAO,KAAK,CAAC,EAAE;gBACrB,IAAI,OAAO;oBACT,YAAY,MAAM;gBACpB,OAAO;oBACL,aAAa;oBACb;oBACA,aAAa;gBACf;gBACA;YACF;QACF;QAEA;;;KAGC,GACD,SAAS,aAAa,IAAI,EAAE,KAAK;YAC/B,IAAI,KAAK,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,UAAU;gBAChD,QAAQ,QAAQ,CAAC,SAAS,gBAAgB,CAAC,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK;YACtE;YACA,IAAI,KAAK,UAAU,EAAE;gBACnB,0DAA0D;gBAC1D,IAAI,KAAK,UAAU,CAAC,KAAK,EAAE;oBACzB,YAAY,YAAY,SAAS,gBAAgB,CAAC,KAAK,UAAU,CAAC,KAAK,CAAC,IAAI,KAAK,UAAU,CAAC,KAAK;oBACjG,aAAa;gBACf,OAAO,IAAI,KAAK,UAAU,CAAC,MAAM,EAAE;oBACjC,oDAAoD;oBACpD,eAAe,KAAK,UAAU,EAAE;oBAChC,aAAa;gBACf;YACF;YAEA,MAAM,OAAO,MAAM,CAAC,MAAM;gBAAE,QAAQ;oBAAE,OAAO;gBAAI;YAAE;YACnD,OAAO;QACT;QAEA;;;;;KAKC,GACD,SAAS,UAAU,IAAI,EAAE,KAAK,EAAE,kBAAkB;YAChD,IAAI,UAAU,WAAW,KAAK,KAAK,EAAE;YAErC,IAAI,SAAS;gBACX,IAAI,IAAI,CAAC,SAAS,EAAE;oBAClB,MAAM,OAAO,IAAI,SAAS;oBAC1B,IAAI,CAAC,SAAS,CAAC,OAAO;oBACtB,IAAI,KAAK,cAAc,EAAE,UAAU;gBACrC;gBAEA,IAAI,SAAS;oBACX,MAAO,KAAK,UAAU,IAAI,KAAK,MAAM,CAAE;wBACrC,OAAO,KAAK,MAAM;oBACpB;oBACA,OAAO;gBACT;YACF;YACA,uDAAuD;YACvD,8DAA8D;YAC9D,IAAI,KAAK,cAAc,EAAE;gBACvB,OAAO,UAAU,KAAK,MAAM,EAAE,OAAO;YACvC;QACF;QAEA;;;;KAIC,GACD,SAAS,SAAS,MAAM;YACtB,IAAI,IAAI,OAAO,CAAC,UAAU,KAAK,GAAG;gBAChC,+EAA+E;gBAC/E,QAAQ;gBACR,cAAc,MAAM,CAAC,EAAE;gBACvB,OAAO;YACT,OAAO;gBACL,0EAA0E;gBAC1E,0BAA0B;gBAC1B,2BAA2B;gBAC3B,OAAO;YACT;QACF;QAEA;;;;;KAKC,GACD,SAAS,aAAa,KAAK;YACzB,MAAM,SAAS,KAAK,CAAC,EAAE;YACvB,MAAM,UAAU,MAAM,IAAI;YAE1B,MAAM,OAAO,IAAI,SAAS;YAC1B,wDAAwD;YACxD,MAAM,kBAAkB;gBAAC,QAAQ,aAAa;gBAAE,OAAO,CAAC,WAAW;aAAC;YACpE,KAAK,MAAM,MAAM,gBAAiB;gBAChC,IAAI,CAAC,IAAI;gBACT,GAAG,OAAO;gBACV,IAAI,KAAK,cAAc,EAAE,OAAO,SAAS;YAC3C;YAEA,IAAI,QAAQ,IAAI,EAAE;gBAChB,cAAc;YAChB,OAAO;gBACL,IAAI,QAAQ,YAAY,EAAE;oBACxB,cAAc;gBAChB;gBACA;gBACA,IAAI,CAAC,QAAQ,WAAW,IAAI,CAAC,QAAQ,YAAY,EAAE;oBACjD,aAAa;gBACf;YACF;YACA,aAAa,SAAS;YACtB,OAAO,QAAQ,WAAW,GAAG,IAAI,OAAO,MAAM;QAChD;QAEA;;;;KAIC,GACD,SAAS,WAAW,KAAK;YACvB,MAAM,SAAS,KAAK,CAAC,EAAE;YACvB,MAAM,qBAAqB,gBAAgB,SAAS,CAAC,MAAM,KAAK;YAEhE,MAAM,UAAU,UAAU,KAAK,OAAO;YACtC,IAAI,CAAC,SAAS;gBAAE,OAAO;YAAU;YAEjC,MAAM,SAAS;YACf,IAAI,IAAI,QAAQ,IAAI,IAAI,QAAQ,CAAC,KAAK,EAAE;gBACtC;gBACA,YAAY,QAAQ,IAAI,QAAQ,CAAC,KAAK;YACxC,OAAO,IAAI,IAAI,QAAQ,IAAI,IAAI,QAAQ,CAAC,MAAM,EAAE;gBAC9C;gBACA,eAAe,IAAI,QAAQ,EAAE;YAC/B,OAAO,IAAI,OAAO,IAAI,EAAE;gBACtB,cAAc;YAChB,OAAO;gBACL,IAAI,CAAC,CAAC,OAAO,SAAS,IAAI,OAAO,UAAU,GAAG;oBAC5C,cAAc;gBAChB;gBACA;gBACA,IAAI,OAAO,UAAU,EAAE;oBACrB,aAAa;gBACf;YACF;YACA,GAAG;gBACD,IAAI,IAAI,KAAK,EAAE;oBACb,QAAQ,SAAS;gBACnB;gBACA,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,WAAW,EAAE;oBACjC,aAAa,IAAI,SAAS;gBAC5B;gBACA,MAAM,IAAI,MAAM;YAClB,QAAS,QAAQ,QAAQ,MAAM,CAAE;YACjC,IAAI,QAAQ,MAAM,EAAE;gBAClB,aAAa,QAAQ,MAAM,EAAE;YAC/B;YACA,OAAO,OAAO,SAAS,GAAG,IAAI,OAAO,MAAM;QAC7C;QAEA,SAAS;YACP,MAAM,OAAO,EAAE;YACf,IAAK,IAAI,UAAU,KAAK,YAAY,UAAU,UAAU,QAAQ,MAAM,CAAE;gBACtE,IAAI,QAAQ,KAAK,EAAE;oBACjB,KAAK,OAAO,CAAC,QAAQ,KAAK;gBAC5B;YACF;YACA,KAAK,OAAO,CAAC,CAAA,OAAQ,QAAQ,QAAQ,CAAC;QACxC;QAEA,6DAA6D,GAC7D,IAAI,YAAY,CAAC;QAEjB;;;;;KAKC,GACD,SAAS,cAAc,eAAe,EAAE,KAAK;YAC3C,MAAM,SAAS,SAAS,KAAK,CAAC,EAAE;YAEhC,kDAAkD;YAClD,cAAc;YAEd,IAAI,UAAU,MAAM;gBAClB;gBACA,OAAO;YACT;YAEA,qEAAqE;YACrE,+FAA+F;YAC/F,oDAAoD;YACpD,+DAA+D;YAC/D,IAAI,UAAU,IAAI,KAAK,WAAW,MAAM,IAAI,KAAK,SAAS,UAAU,KAAK,KAAK,MAAM,KAAK,IAAI,WAAW,IAAI;gBAC1G,sFAAsF;gBACtF,cAAc,gBAAgB,KAAK,CAAC,MAAM,KAAK,EAAE,MAAM,KAAK,GAAG;gBAC/D,IAAI,CAAC,WAAW;oBACd,2BAA2B,GAC3B,MAAM,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,aAAa,CAAC,CAAC;oBAC7D,IAAI,YAAY,GAAG;oBACnB,IAAI,OAAO,GAAG,UAAU,IAAI;oBAC5B,MAAM;gBACR;gBACA,OAAO;YACT;YACA,YAAY;YAEZ,IAAI,MAAM,IAAI,KAAK,SAAS;gBAC1B,OAAO,aAAa;YACtB,OAAO,IAAI,MAAM,IAAI,KAAK,aAAa,CAAC,gBAAgB;gBACtD,+CAA+C;gBAC/C,2BAA2B,GAC3B,MAAM,MAAM,IAAI,MAAM,qBAAqB,SAAS,iBAAiB,CAAC,IAAI,KAAK,IAAI,WAAW,IAAI;gBAClG,IAAI,IAAI,GAAG;gBACX,MAAM;YACR,OAAO,IAAI,MAAM,IAAI,KAAK,OAAO;gBAC/B,MAAM,YAAY,WAAW;gBAC7B,IAAI,cAAc,UAAU;oBAC1B,OAAO;gBACT;YACF;YAEA,0EAA0E;YAC1E,sEAAsE;YACtE,8CAA8C;YAC9C,IAAI,MAAM,IAAI,KAAK,aAAa,WAAW,IAAI;gBAC7C,iDAAiD;gBACjD,cAAc;gBACd,OAAO;YACT;YAEA,uEAAuE;YACvE,oEAAoE;YACpE,kEAAkE;YAClE,aAAa;YACb,IAAI,aAAa,UAAU,aAAa,MAAM,KAAK,GAAG,GAAG;gBACvD,MAAM,MAAM,IAAI,MAAM;gBACtB,MAAM;YACR;YAEA;;;;;;MAMA,GAEA,cAAc;YACd,OAAO,OAAO,MAAM;QACtB;QAEA,MAAM,WAAW,YAAY;QAC7B,IAAI,CAAC,UAAU;YACb,MAAM,mBAAmB,OAAO,CAAC,MAAM;YACvC,MAAM,IAAI,MAAM,wBAAwB,eAAe;QACzD;QAEA,MAAM,KAAK,gBAAgB;QAC3B,IAAI,SAAS;QACb,yBAAyB,GACzB,IAAI,MAAM,gBAAgB;QAC1B,sCAAsC,GACtC,MAAM,gBAAgB,CAAC,GAAG,uCAAuC;QACjE,MAAM,UAAU,IAAI,QAAQ,SAAS,CAAC;QACtC;QACA,IAAI,aAAa;QACjB,IAAI,YAAY;QAChB,IAAI,QAAQ;QACZ,IAAI,aAAa;QACjB,IAAI,2BAA2B;QAE/B,IAAI;YACF,IAAI,CAAC,SAAS,YAAY,EAAE;gBAC1B,IAAI,OAAO,CAAC,WAAW;gBAEvB,OAAS;oBACP;oBACA,IAAI,0BAA0B;wBAC5B,kDAAkD;wBAClD,mCAAmC;wBACnC,2BAA2B;oBAC7B,OAAO;wBACL,IAAI,OAAO,CAAC,WAAW;oBACzB;oBACA,IAAI,OAAO,CAAC,SAAS,GAAG;oBAExB,MAAM,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC;oBAC/B,iEAAiE;oBAEjE,IAAI,CAAC,OAAO;oBAEZ,MAAM,cAAc,gBAAgB,SAAS,CAAC,OAAO,MAAM,KAAK;oBAChE,MAAM,iBAAiB,cAAc,aAAa;oBAClD,QAAQ,MAAM,KAAK,GAAG;gBACxB;gBACA,cAAc,gBAAgB,SAAS,CAAC;YAC1C,OAAO;gBACL,SAAS,YAAY,CAAC,iBAAiB;YACzC;YAEA,QAAQ,QAAQ;YAChB,SAAS,QAAQ,MAAM;YAEvB,OAAO;gBACL,UAAU;gBACV,OAAO;gBACP;gBACA,SAAS;gBACT,UAAU;gBACV,MAAM;YACR;QACF,EAAE,OAAO,KAAK;YACZ,IAAI,IAAI,OAAO,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY;gBAClD,OAAO;oBACL,UAAU;oBACV,OAAO,OAAO;oBACd,SAAS;oBACT,WAAW;oBACX,YAAY;wBACV,SAAS,IAAI,OAAO;wBACpB;wBACA,SAAS,gBAAgB,KAAK,CAAC,QAAQ,KAAK,QAAQ;wBACpD,MAAM,IAAI,IAAI;wBACd,aAAa;oBACf;oBACA,UAAU;gBACZ;YACF,OAAO,IAAI,WAAW;gBACpB,OAAO;oBACL,UAAU;oBACV,OAAO,OAAO;oBACd,SAAS;oBACT,WAAW;oBACX,aAAa;oBACb,UAAU;oBACV,MAAM;gBACR;YACF,OAAO;gBACL,MAAM;YACR;QACF;IACF;IAEA;;;;;;GAMC,GACD,SAAS,wBAAwB,IAAI;QACnC,MAAM,SAAS;YACb,OAAO,OAAO;YACd,SAAS;YACT,WAAW;YACX,MAAM;YACN,UAAU,IAAI,QAAQ,SAAS,CAAC;QAClC;QACA,OAAO,QAAQ,CAAC,OAAO,CAAC;QACxB,OAAO;IACT;IAEA;;;;;;;;;;;;;EAaA,GACA,SAAS,cAAc,IAAI,EAAE,cAAc;QACzC,iBAAiB,kBAAkB,QAAQ,SAAS,IAAI,OAAO,IAAI,CAAC;QACpE,MAAM,YAAY,wBAAwB;QAE1C,MAAM,UAAU,eAAe,MAAM,CAAC,aAAa,MAAM,CAAC,eAAe,GAAG,CAAC,CAAA,OAC3E,WAAW,MAAM,MAAM;QAEzB,QAAQ,OAAO,CAAC,YAAY,gCAAgC;QAE5D,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAC,GAAG;YAC9B,yBAAyB;YACzB,IAAI,EAAE,SAAS,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,GAAG,EAAE,SAAS;YAEjE,4CAA4C;YAC5C,6DAA6D;YAC7D,IAAI,EAAE,QAAQ,IAAI,EAAE,QAAQ,EAAE;gBAC5B,IAAI,YAAY,EAAE,QAAQ,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE;oBACrD,OAAO;gBACT,OAAO,IAAI,YAAY,EAAE,QAAQ,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE;oBAC5D,OAAO,CAAC;gBACV;YACF;YAEA,mEAAmE;YACnE,uEAAuE;YACvE,0EAA0E;YAC1E,4BAA4B;YAC5B,OAAO;QACT;QAEA,MAAM,CAAC,MAAM,WAAW,GAAG;QAE3B,gCAAgC,GAChC,MAAM,SAAS;QACf,OAAO,UAAU,GAAG;QAEpB,OAAO;IACT;IAEA;;;;;;GAMC,GACD,SAAS,gBAAgB,OAAO,EAAE,WAAW,EAAE,UAAU;QACvD,MAAM,WAAW,AAAC,eAAe,OAAO,CAAC,YAAY,IAAK;QAE1D,QAAQ,SAAS,CAAC,GAAG,CAAC;QACtB,QAAQ,SAAS,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;IAC9C;IAEA;;;;EAIA,GACA,SAAS,iBAAiB,OAAO;QAC/B,sBAAsB,GACtB,IAAI,OAAO;QACX,MAAM,WAAW,cAAc;QAE/B,IAAI,mBAAmB,WAAW;QAElC,KAAK,2BACH;YAAE,IAAI;YAAS;QAAS;QAE1B,IAAI,QAAQ,OAAO,CAAC,WAAW,EAAE;YAC/B,QAAQ,GAAG,CAAC,0FAA0F;YACtG;QACF;QAEA,4EAA4E;QAC5E,wEAAwE;QACxE,sEAAsE;QACtE,2EAA2E;QAC3E,yEAAyE;QACzE,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,GAAG;YAC/B,IAAI,CAAC,QAAQ,mBAAmB,EAAE;gBAChC,QAAQ,IAAI,CAAC;gBACb,QAAQ,IAAI,CAAC;gBACb,QAAQ,IAAI,CAAC;gBACb,QAAQ,IAAI,CAAC;YACf;YACA,IAAI,QAAQ,kBAAkB,EAAE;gBAC9B,MAAM,MAAM,IAAI,mBACd,oDACA,QAAQ,SAAS;gBAEnB,MAAM;YACR;QACF;QAEA,OAAO;QACP,MAAM,OAAO,KAAK,WAAW;QAC7B,MAAM,SAAS,WAAW,UAAU,MAAM;YAAE;YAAU,gBAAgB;QAAK,KAAK,cAAc;QAE9F,QAAQ,SAAS,GAAG,OAAO,KAAK;QAChC,QAAQ,OAAO,CAAC,WAAW,GAAG;QAC9B,gBAAgB,SAAS,UAAU,OAAO,QAAQ;QAClD,QAAQ,MAAM,GAAG;YACf,UAAU,OAAO,QAAQ;YACzB,iCAAiC;YACjC,IAAI,OAAO,SAAS;YACpB,WAAW,OAAO,SAAS;QAC7B;QACA,IAAI,OAAO,UAAU,EAAE;YACrB,QAAQ,UAAU,GAAG;gBACnB,UAAU,OAAO,UAAU,CAAC,QAAQ;gBACpC,WAAW,OAAO,UAAU,CAAC,SAAS;YACxC;QACF;QAEA,KAAK,0BAA0B;YAAE,IAAI;YAAS;YAAQ;QAAK;IAC7D;IAEA;;;;GAIC,GACD,SAAS,UAAU,WAAW;QAC5B,UAAU,QAAQ,SAAS;IAC7B;IAEA,+BAA+B;IAC/B,MAAM,mBAAmB;QACvB;QACA,WAAW,UAAU;IACvB;IAEA,+BAA+B;IAC/B,SAAS;QACP;QACA,WAAW,UAAU;IACvB;IAEA,IAAI,iBAAiB;IAErB;;GAEC,GACD,SAAS;QACP,SAAS;YACP,6DAA6D;YAC7D;QACF;QAEA,oDAAoD;QACpD,IAAI,SAAS,UAAU,KAAK,WAAW;YACrC,kDAAkD;YAClD,IAAI,CAAC,gBAAgB;gBACnB,OAAO,gBAAgB,CAAC,oBAAoB,MAAM;YACpD;YACA,iBAAiB;YACjB;QACF;QAEA,MAAM,SAAS,SAAS,gBAAgB,CAAC,QAAQ,WAAW;QAC5D,OAAO,OAAO,CAAC;IACjB;IAEA;;;;;GAKC,GACD,SAAS,iBAAiB,YAAY,EAAE,kBAAkB;QACxD,IAAI,OAAO;QACX,IAAI;YACF,OAAO,mBAAmB;QAC5B,EAAE,OAAO,SAAS;YAChB,MAAM,wDAAwD,OAAO,CAAC,MAAM;YAC5E,qBAAqB;YACrB,IAAI,CAAC,WAAW;gBAAE,MAAM;YAAS,OAAO;gBAAE,MAAM;YAAU;YAC1D,qEAAqE;YACrE,qEAAqE;YACrE,qEAAqE;YACrE,qBAAqB;YACrB,OAAO;QACT;QACA,mEAAmE;QACnE,IAAI,CAAC,KAAK,IAAI,EAAE,KAAK,IAAI,GAAG;QAC5B,SAAS,CAAC,aAAa,GAAG;QAC1B,KAAK,aAAa,GAAG,mBAAmB,IAAI,CAAC,MAAM;QAEnD,IAAI,KAAK,OAAO,EAAE;YAChB,gBAAgB,KAAK,OAAO,EAAE;gBAAE;YAAa;QAC/C;IACF;IAEA;;;;GAIC,GACD,SAAS,mBAAmB,YAAY;QACtC,OAAO,SAAS,CAAC,aAAa;QAC9B,KAAK,MAAM,SAAS,OAAO,IAAI,CAAC,SAAU;YACxC,IAAI,OAAO,CAAC,MAAM,KAAK,cAAc;gBACnC,OAAO,OAAO,CAAC,MAAM;YACvB;QACF;IACF;IAEA;;GAEC,GACD,SAAS;QACP,OAAO,OAAO,IAAI,CAAC;IACrB;IAEA;;;GAGC,GACD,SAAS,YAAY,IAAI;QACvB,OAAO,CAAC,QAAQ,EAAE,EAAE,WAAW;QAC/B,OAAO,SAAS,CAAC,KAAK,IAAI,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC;IACpD;IAEA;;;;GAIC,GACD,SAAS,gBAAgB,SAAS,EAAE,EAAE,YAAY,EAAE;QAClD,IAAI,OAAO,cAAc,UAAU;YACjC,YAAY;gBAAC;aAAU;QACzB;QACA,UAAU,OAAO,CAAC,CAAA;YAAW,OAAO,CAAC,MAAM,WAAW,GAAG,GAAG;QAAc;IAC5E;IAEA;;;GAGC,GACD,SAAS,cAAc,IAAI;QACzB,MAAM,OAAO,YAAY;QACzB,OAAO,QAAQ,CAAC,KAAK,iBAAiB;IACxC;IAEA;;;;GAIC,GACD,SAAS,iBAAiB,MAAM;QAC9B,wBAAwB;QACxB,IAAI,MAAM,CAAC,wBAAwB,IAAI,CAAC,MAAM,CAAC,0BAA0B,EAAE;YACzE,MAAM,CAAC,0BAA0B,GAAG,CAAC;gBACnC,MAAM,CAAC,wBAAwB,CAC7B,OAAO,MAAM,CAAC;oBAAE,OAAO,KAAK,EAAE;gBAAC,GAAG;YAEtC;QACF;QACA,IAAI,MAAM,CAAC,uBAAuB,IAAI,CAAC,MAAM,CAAC,yBAAyB,EAAE;YACvE,MAAM,CAAC,yBAAyB,GAAG,CAAC;gBAClC,MAAM,CAAC,uBAAuB,CAC5B,OAAO,MAAM,CAAC;oBAAE,OAAO,KAAK,EAAE;gBAAC,GAAG;YAEtC;QACF;IACF;IAEA;;GAEC,GACD,SAAS,UAAU,MAAM;QACvB,iBAAiB;QACjB,QAAQ,IAAI,CAAC;IACf;IAEA;;GAEC,GACD,SAAS,aAAa,MAAM;QAC1B,MAAM,QAAQ,QAAQ,OAAO,CAAC;QAC9B,IAAI,UAAU,CAAC,GAAG;YAChB,QAAQ,MAAM,CAAC,OAAO;QACxB;IACF;IAEA;;;;GAIC,GACD,SAAS,KAAK,KAAK,EAAE,IAAI;QACvB,MAAM,KAAK;QACX,QAAQ,OAAO,CAAC,SAAS,MAAM;YAC7B,IAAI,MAAM,CAAC,GAAG,EAAE;gBACd,MAAM,CAAC,GAAG,CAAC;YACb;QACF;IACF;IAEA;;;GAGC,GACD,SAAS,wBAAwB,EAAE;QACjC,WAAW,UAAU;QACrB,WAAW,UAAU;QAErB,OAAO,iBAAiB;IAC1B;IAEA,wBAAwB,GACxB,OAAO,MAAM,CAAC,MAAM;QAClB;QACA;QACA;QACA;QACA,4BAA4B;QAC5B,gBAAgB;QAChB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,KAAK,SAAS,GAAG;QAAa,YAAY;IAAO;IACjD,KAAK,QAAQ,GAAG;QAAa,YAAY;IAAM;IAC/C,KAAK,aAAa,GAAG;IAErB,KAAK,KAAK,GAAG;QACX,QAAQ;QACR,WAAW;QACX,QAAQ;QACR,UAAU;QACV,kBAAkB;IACpB;IAEA,IAAK,MAAM,OAAO,MAAO;QACvB,aAAa;QACb,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,UAAU;YAClC,aAAa;YACb,WAAW,KAAK,CAAC,IAAI;QACvB;IACF;IAEA,mDAAmD;IACnD,OAAO,MAAM,CAAC,MAAM;IAEpB,OAAO;AACT;AAEA,sDAAsD;AACtD,MAAM,YAAY,KAAK,CAAC;AAExB,sEAAsE;AACtE,qDAAqD;AACrD,UAAU,WAAW,GAAG,IAAM,KAAK,CAAC;AAEpC,OAAO,OAAO,GAAG;AACjB,UAAU,WAAW,GAAG;AACxB,UAAU,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2211, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/lib/index.js"], "sourcesContent": ["var hljs = require('./core');\n\nhljs.registerLanguage('1c', require('./languages/1c'));\nhljs.registerLanguage('abnf', require('./languages/abnf'));\nhljs.registerLanguage('accesslog', require('./languages/accesslog'));\nhljs.registerLanguage('actionscript', require('./languages/actionscript'));\nhljs.registerLanguage('ada', require('./languages/ada'));\nhljs.registerLanguage('angelscript', require('./languages/angelscript'));\nhljs.registerLanguage('apache', require('./languages/apache'));\nhljs.registerLanguage('applescript', require('./languages/applescript'));\nhljs.registerLanguage('arcade', require('./languages/arcade'));\nhljs.registerLanguage('arduino', require('./languages/arduino'));\nhljs.registerLanguage('armasm', require('./languages/armasm'));\nhljs.registerLanguage('xml', require('./languages/xml'));\nhljs.registerLanguage('asciidoc', require('./languages/asciidoc'));\nhljs.registerLanguage('aspectj', require('./languages/aspectj'));\nhljs.registerLanguage('autohotkey', require('./languages/autohotkey'));\nhljs.registerLanguage('autoit', require('./languages/autoit'));\nhljs.registerLanguage('avrasm', require('./languages/avrasm'));\nhljs.registerLanguage('awk', require('./languages/awk'));\nhljs.registerLanguage('axapta', require('./languages/axapta'));\nhljs.registerLanguage('bash', require('./languages/bash'));\nhljs.registerLanguage('basic', require('./languages/basic'));\nhljs.registerLanguage('bnf', require('./languages/bnf'));\nhljs.registerLanguage('brainfuck', require('./languages/brainfuck'));\nhljs.registerLanguage('c', require('./languages/c'));\nhljs.registerLanguage('cal', require('./languages/cal'));\nhljs.registerLanguage('capnproto', require('./languages/capnproto'));\nhljs.registerLanguage('ceylon', require('./languages/ceylon'));\nhljs.registerLanguage('clean', require('./languages/clean'));\nhljs.registerLanguage('clojure', require('./languages/clojure'));\nhljs.registerLanguage('clojure-repl', require('./languages/clojure-repl'));\nhljs.registerLanguage('cmake', require('./languages/cmake'));\nhljs.registerLanguage('coffeescript', require('./languages/coffeescript'));\nhljs.registerLanguage('coq', require('./languages/coq'));\nhljs.registerLanguage('cos', require('./languages/cos'));\nhljs.registerLanguage('cpp', require('./languages/cpp'));\nhljs.registerLanguage('crmsh', require('./languages/crmsh'));\nhljs.registerLanguage('crystal', require('./languages/crystal'));\nhljs.registerLanguage('csharp', require('./languages/csharp'));\nhljs.registerLanguage('csp', require('./languages/csp'));\nhljs.registerLanguage('css', require('./languages/css'));\nhljs.registerLanguage('d', require('./languages/d'));\nhljs.registerLanguage('markdown', require('./languages/markdown'));\nhljs.registerLanguage('dart', require('./languages/dart'));\nhljs.registerLanguage('delphi', require('./languages/delphi'));\nhljs.registerLanguage('diff', require('./languages/diff'));\nhljs.registerLanguage('django', require('./languages/django'));\nhljs.registerLanguage('dns', require('./languages/dns'));\nhljs.registerLanguage('dockerfile', require('./languages/dockerfile'));\nhljs.registerLanguage('dos', require('./languages/dos'));\nhljs.registerLanguage('dsconfig', require('./languages/dsconfig'));\nhljs.registerLanguage('dts', require('./languages/dts'));\nhljs.registerLanguage('dust', require('./languages/dust'));\nhljs.registerLanguage('ebnf', require('./languages/ebnf'));\nhljs.registerLanguage('elixir', require('./languages/elixir'));\nhljs.registerLanguage('elm', require('./languages/elm'));\nhljs.registerLanguage('ruby', require('./languages/ruby'));\nhljs.registerLanguage('erb', require('./languages/erb'));\nhljs.registerLanguage('erlang-repl', require('./languages/erlang-repl'));\nhljs.registerLanguage('erlang', require('./languages/erlang'));\nhljs.registerLanguage('excel', require('./languages/excel'));\nhljs.registerLanguage('fix', require('./languages/fix'));\nhljs.registerLanguage('flix', require('./languages/flix'));\nhljs.registerLanguage('fortran', require('./languages/fortran'));\nhljs.registerLanguage('fsharp', require('./languages/fsharp'));\nhljs.registerLanguage('gams', require('./languages/gams'));\nhljs.registerLanguage('gauss', require('./languages/gauss'));\nhljs.registerLanguage('gcode', require('./languages/gcode'));\nhljs.registerLanguage('gherkin', require('./languages/gherkin'));\nhljs.registerLanguage('glsl', require('./languages/glsl'));\nhljs.registerLanguage('gml', require('./languages/gml'));\nhljs.registerLanguage('go', require('./languages/go'));\nhljs.registerLanguage('golo', require('./languages/golo'));\nhljs.registerLanguage('gradle', require('./languages/gradle'));\nhljs.registerLanguage('graphql', require('./languages/graphql'));\nhljs.registerLanguage('groovy', require('./languages/groovy'));\nhljs.registerLanguage('haml', require('./languages/haml'));\nhljs.registerLanguage('handlebars', require('./languages/handlebars'));\nhljs.registerLanguage('haskell', require('./languages/haskell'));\nhljs.registerLanguage('haxe', require('./languages/haxe'));\nhljs.registerLanguage('hsp', require('./languages/hsp'));\nhljs.registerLanguage('http', require('./languages/http'));\nhljs.registerLanguage('hy', require('./languages/hy'));\nhljs.registerLanguage('inform7', require('./languages/inform7'));\nhljs.registerLanguage('ini', require('./languages/ini'));\nhljs.registerLanguage('irpf90', require('./languages/irpf90'));\nhljs.registerLanguage('isbl', require('./languages/isbl'));\nhljs.registerLanguage('java', require('./languages/java'));\nhljs.registerLanguage('javascript', require('./languages/javascript'));\nhljs.registerLanguage('jboss-cli', require('./languages/jboss-cli'));\nhljs.registerLanguage('json', require('./languages/json'));\nhljs.registerLanguage('julia', require('./languages/julia'));\nhljs.registerLanguage('julia-repl', require('./languages/julia-repl'));\nhljs.registerLanguage('kotlin', require('./languages/kotlin'));\nhljs.registerLanguage('lasso', require('./languages/lasso'));\nhljs.registerLanguage('latex', require('./languages/latex'));\nhljs.registerLanguage('ldif', require('./languages/ldif'));\nhljs.registerLanguage('leaf', require('./languages/leaf'));\nhljs.registerLanguage('less', require('./languages/less'));\nhljs.registerLanguage('lisp', require('./languages/lisp'));\nhljs.registerLanguage('livecodeserver', require('./languages/livecodeserver'));\nhljs.registerLanguage('livescript', require('./languages/livescript'));\nhljs.registerLanguage('llvm', require('./languages/llvm'));\nhljs.registerLanguage('lsl', require('./languages/lsl'));\nhljs.registerLanguage('lua', require('./languages/lua'));\nhljs.registerLanguage('makefile', require('./languages/makefile'));\nhljs.registerLanguage('mathematica', require('./languages/mathematica'));\nhljs.registerLanguage('matlab', require('./languages/matlab'));\nhljs.registerLanguage('maxima', require('./languages/maxima'));\nhljs.registerLanguage('mel', require('./languages/mel'));\nhljs.registerLanguage('mercury', require('./languages/mercury'));\nhljs.registerLanguage('mipsasm', require('./languages/mipsasm'));\nhljs.registerLanguage('mizar', require('./languages/mizar'));\nhljs.registerLanguage('perl', require('./languages/perl'));\nhljs.registerLanguage('mojolicious', require('./languages/mojolicious'));\nhljs.registerLanguage('monkey', require('./languages/monkey'));\nhljs.registerLanguage('moonscript', require('./languages/moonscript'));\nhljs.registerLanguage('n1ql', require('./languages/n1ql'));\nhljs.registerLanguage('nestedtext', require('./languages/nestedtext'));\nhljs.registerLanguage('nginx', require('./languages/nginx'));\nhljs.registerLanguage('nim', require('./languages/nim'));\nhljs.registerLanguage('nix', require('./languages/nix'));\nhljs.registerLanguage('node-repl', require('./languages/node-repl'));\nhljs.registerLanguage('nsis', require('./languages/nsis'));\nhljs.registerLanguage('objectivec', require('./languages/objectivec'));\nhljs.registerLanguage('ocaml', require('./languages/ocaml'));\nhljs.registerLanguage('openscad', require('./languages/openscad'));\nhljs.registerLanguage('oxygene', require('./languages/oxygene'));\nhljs.registerLanguage('parser3', require('./languages/parser3'));\nhljs.registerLanguage('pf', require('./languages/pf'));\nhljs.registerLanguage('pgsql', require('./languages/pgsql'));\nhljs.registerLanguage('php', require('./languages/php'));\nhljs.registerLanguage('php-template', require('./languages/php-template'));\nhljs.registerLanguage('plaintext', require('./languages/plaintext'));\nhljs.registerLanguage('pony', require('./languages/pony'));\nhljs.registerLanguage('powershell', require('./languages/powershell'));\nhljs.registerLanguage('processing', require('./languages/processing'));\nhljs.registerLanguage('profile', require('./languages/profile'));\nhljs.registerLanguage('prolog', require('./languages/prolog'));\nhljs.registerLanguage('properties', require('./languages/properties'));\nhljs.registerLanguage('protobuf', require('./languages/protobuf'));\nhljs.registerLanguage('puppet', require('./languages/puppet'));\nhljs.registerLanguage('purebasic', require('./languages/purebasic'));\nhljs.registerLanguage('python', require('./languages/python'));\nhljs.registerLanguage('python-repl', require('./languages/python-repl'));\nhljs.registerLanguage('q', require('./languages/q'));\nhljs.registerLanguage('qml', require('./languages/qml'));\nhljs.registerLanguage('r', require('./languages/r'));\nhljs.registerLanguage('reasonml', require('./languages/reasonml'));\nhljs.registerLanguage('rib', require('./languages/rib'));\nhljs.registerLanguage('roboconf', require('./languages/roboconf'));\nhljs.registerLanguage('routeros', require('./languages/routeros'));\nhljs.registerLanguage('rsl', require('./languages/rsl'));\nhljs.registerLanguage('ruleslanguage', require('./languages/ruleslanguage'));\nhljs.registerLanguage('rust', require('./languages/rust'));\nhljs.registerLanguage('sas', require('./languages/sas'));\nhljs.registerLanguage('scala', require('./languages/scala'));\nhljs.registerLanguage('scheme', require('./languages/scheme'));\nhljs.registerLanguage('scilab', require('./languages/scilab'));\nhljs.registerLanguage('scss', require('./languages/scss'));\nhljs.registerLanguage('shell', require('./languages/shell'));\nhljs.registerLanguage('smali', require('./languages/smali'));\nhljs.registerLanguage('smalltalk', require('./languages/smalltalk'));\nhljs.registerLanguage('sml', require('./languages/sml'));\nhljs.registerLanguage('sqf', require('./languages/sqf'));\nhljs.registerLanguage('sql', require('./languages/sql'));\nhljs.registerLanguage('stan', require('./languages/stan'));\nhljs.registerLanguage('stata', require('./languages/stata'));\nhljs.registerLanguage('step21', require('./languages/step21'));\nhljs.registerLanguage('stylus', require('./languages/stylus'));\nhljs.registerLanguage('subunit', require('./languages/subunit'));\nhljs.registerLanguage('swift', require('./languages/swift'));\nhljs.registerLanguage('taggerscript', require('./languages/taggerscript'));\nhljs.registerLanguage('yaml', require('./languages/yaml'));\nhljs.registerLanguage('tap', require('./languages/tap'));\nhljs.registerLanguage('tcl', require('./languages/tcl'));\nhljs.registerLanguage('thrift', require('./languages/thrift'));\nhljs.registerLanguage('tp', require('./languages/tp'));\nhljs.registerLanguage('twig', require('./languages/twig'));\nhljs.registerLanguage('typescript', require('./languages/typescript'));\nhljs.registerLanguage('vala', require('./languages/vala'));\nhljs.registerLanguage('vbnet', require('./languages/vbnet'));\nhljs.registerLanguage('vbscript', require('./languages/vbscript'));\nhljs.registerLanguage('vbscript-html', require('./languages/vbscript-html'));\nhljs.registerLanguage('verilog', require('./languages/verilog'));\nhljs.registerLanguage('vhdl', require('./languages/vhdl'));\nhljs.registerLanguage('vim', require('./languages/vim'));\nhljs.registerLanguage('wasm', require('./languages/wasm'));\nhljs.registerLanguage('wren', require('./languages/wren'));\nhljs.registerLanguage('x86asm', require('./languages/x86asm'));\nhljs.registerLanguage('xl', require('./languages/xl'));\nhljs.registerLanguage('xquery', require('./languages/xquery'));\nhljs.registerLanguage('zephir', require('./languages/zephir'));\n\nhljs.HighlightJS = hljs\nhljs.default = hljs\nmodule.exports = hljs;"], "names": [], "mappings": "AAAA,IAAI;AAEJ,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AACtB,KAAK,gBAAgB,CAAC;AAEtB,KAAK,WAAW,GAAG;AACnB,KAAK,OAAO,GAAG;AACf,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}]}