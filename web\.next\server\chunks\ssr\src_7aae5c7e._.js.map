{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/lib/utils.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AAE/B;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/components/magicui/number-ticker.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useInView, useMotionValue, useSpring } from \"motion/react\";\r\nimport { type ComponentPropsWithoutRef, useEffect, useRef } from \"react\";\r\n\r\nimport { cn } from \"~/lib/utils\";\r\n\r\ninterface NumberTickerProps extends ComponentPropsWithoutRef<\"span\"> {\r\n  value: number;\r\n  startValue?: number;\r\n  direction?: \"up\" | \"down\";\r\n  delay?: number;\r\n  decimalPlaces?: number;\r\n}\r\n\r\nexport function NumberTicker({\r\n  value,\r\n  startValue = 0,\r\n  direction = \"up\",\r\n  delay = 0,\r\n  className,\r\n  decimalPlaces = 0,\r\n  ...props\r\n}: NumberTickerProps) {\r\n  const ref = useRef<HTMLSpanElement>(null);\r\n  const motionValue = useMotionValue(direction === \"down\" ? value : startValue);\r\n  const springValue = useSpring(motionValue, {\r\n    damping: 60,\r\n    stiffness: 100,\r\n  });\r\n  const isInView = useInView(ref, { once: true, margin: \"0px\" });\r\n\r\n  useEffect(() => {\r\n    if (isInView) {\r\n      const timer = setTimeout(() => {\r\n        motionValue.set(direction === \"down\" ? startValue : value);\r\n      }, delay * 1000);\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [motionValue, isInView, delay, value, direction, startValue]);\r\n\r\n  useEffect(\r\n    () =>\r\n      springValue.on(\"change\", (latest) => {\r\n        if (ref.current) {\r\n          ref.current.textContent = Intl.NumberFormat(\"en-US\", {\r\n            minimumFractionDigits: decimalPlaces,\r\n            maximumFractionDigits: decimalPlaces,\r\n          }).format(Number(latest.toFixed(decimalPlaces)));\r\n        }\r\n      }),\r\n    [springValue, decimalPlaces],\r\n  );\r\n\r\n  return (\r\n    <span\r\n      ref={ref}\r\n      className={cn(\r\n        \"inline-block tracking-wider text-black tabular-nums dark:text-white\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      {startValue}\r\n    </span>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AAEA;AALA;;;;;AAeO,SAAS,aAAa,EAC3B,KAAK,EACL,aAAa,CAAC,EACd,YAAY,IAAI,EAChB,QAAQ,CAAC,EACT,SAAS,EACT,gBAAgB,CAAC,EACjB,GAAG,OACe;IAClB,MAAM,MAAM,CAAA,GAAA,8SAAA,CAAA,SAAM,AAAD,EAAmB;IACpC,MAAM,cAAc,CAAA,GAAA,2UAAA,CAAA,iBAAc,AAAD,EAAE,cAAc,SAAS,QAAQ;IAClE,MAAM,cAAc,CAAA,GAAA,kUAAA,CAAA,YAAS,AAAD,EAAE,aAAa;QACzC,SAAS;QACT,WAAW;IACb;IACA,MAAM,WAAW,CAAA,GAAA,sUAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAM;IAE5D,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ,MAAM,QAAQ,WAAW;gBACvB,YAAY,GAAG,CAAC,cAAc,SAAS,aAAa;YACtD,GAAG,QAAQ;YACX,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAa;QAAU;QAAO;QAAO;QAAW;KAAW;IAE/D,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EACN,IACE,YAAY,EAAE,CAAC,UAAU,CAAC;YACxB,IAAI,IAAI,OAAO,EAAE;gBACf,IAAI,OAAO,CAAC,WAAW,GAAG,KAAK,YAAY,CAAC,SAAS;oBACnD,uBAAuB;oBACvB,uBAAuB;gBACzB,GAAG,MAAM,CAAC,OAAO,OAAO,OAAO,CAAC;YAClC;QACF,IACF;QAAC;QAAa;KAAc;IAG9B,qBACE,uVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uEACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/components/magicui/aurora-text.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { memo } from \"react\";\r\n\r\ninterface AuroraTextProps {\r\n  children: React.ReactNode;\r\n  className?: string;\r\n  colors?: string[];\r\n  speed?: number;\r\n}\r\n\r\nexport const AuroraText = memo(\r\n  ({\r\n    children,\r\n    className = \"\",\r\n    colors = [\"#FF0080\", \"#7928CA\", \"#0070F3\", \"#38bdf8\"],\r\n    speed = 1,\r\n  }: AuroraTextProps) => {\r\n    const gradientStyle = {\r\n      backgroundImage: `linear-gradient(135deg, ${colors.join(\", \")}, ${\r\n        colors[0]\r\n      })`,\r\n      WebkitBackgroundClip: \"text\",\r\n      WebkitTextFillColor: \"transparent\",\r\n      animationDuration: `${10 / speed}s`,\r\n    };\r\n\r\n    return (\r\n      <span className={`relative inline-block ${className}`}>\r\n        <span className=\"sr-only\">{children}</span>\r\n        <span\r\n          className=\"relative animate-aurora bg-[length:200%_auto] bg-clip-text text-transparent\"\r\n          style={gradientStyle}\r\n          aria-hidden=\"true\"\r\n        >\r\n          {children}\r\n        </span>\r\n      </span>\r\n    );\r\n  },\r\n);\r\n\r\nAuroraText.displayName = \"AuroraText\";\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAWO,MAAM,2BAAa,CAAA,GAAA,8SAAA,CAAA,OAAI,AAAD,EAC3B,CAAC,EACC,QAAQ,EACR,YAAY,EAAE,EACd,SAAS;IAAC;IAAW;IAAW;IAAW;CAAU,EACrD,QAAQ,CAAC,EACO;IAChB,MAAM,gBAAgB;QACpB,iBAAiB,CAAC,wBAAwB,EAAE,OAAO,IAAI,CAAC,MAAM,EAAE,EAC9D,MAAM,CAAC,EAAE,CACV,CAAC,CAAC;QACH,sBAAsB;QACtB,qBAAqB;QACrB,mBAAmB,GAAG,KAAK,MAAM,CAAC,CAAC;IACrC;IAEA,qBACE,uVAAC;QAAK,WAAW,CAAC,sBAAsB,EAAE,WAAW;;0BACnD,uVAAC;gBAAK,WAAU;0BAAW;;;;;;0BAC3B,uVAAC;gBACC,WAAU;gBACV,OAAO;gBACP,eAAY;0BAEX;;;;;;;;;;;;AAIT;AAGF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/components/magicui/flickering-grid.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { cn } from \"~/lib/utils\";\r\nimport React, {\r\n  useCallback,\r\n  useEffect,\r\n  useMemo,\r\n  useRef,\r\n  useState,\r\n} from \"react\";\r\n\r\ninterface FlickeringGridProps extends React.HTMLAttributes<HTMLDivElement> {\r\n  squareSize?: number;\r\n  gridGap?: number;\r\n  flickerChance?: number;\r\n  color?: string;\r\n  width?: number;\r\n  height?: number;\r\n  className?: string;\r\n  maxOpacity?: number;\r\n}\r\n\r\nexport const FlickeringGrid: React.FC<FlickeringGridProps> = ({\r\n  squareSize = 4,\r\n  gridGap = 6,\r\n  flickerChance = 0.3,\r\n  color = \"rgb(0, 0, 0)\",\r\n  width,\r\n  height,\r\n  className,\r\n  maxOpacity = 0.3,\r\n  ...props\r\n}) => {\r\n  const canvasRef = useRef<HTMLCanvasElement>(null);\r\n  const containerRef = useRef<HTMLDivElement>(null);\r\n  const [isInView, setIsInView] = useState(false);\r\n  const [canvasSize, setCanvasSize] = useState({ width: 0, height: 0 });\r\n\r\n  const memoizedColor = useMemo(() => {\r\n    const toRGBA = (color: string) => {\r\n      if (typeof window === \"undefined\") {\r\n        return `rgba(0, 0, 0,`;\r\n      }\r\n      const canvas = document.createElement(\"canvas\");\r\n      canvas.width = canvas.height = 1;\r\n      const ctx = canvas.getContext(\"2d\");\r\n      if (!ctx) return \"rgba(255, 0, 0,\";\r\n      ctx.fillStyle = color;\r\n      ctx.fillRect(0, 0, 1, 1);\r\n      const [r, g, b] = Array.from(ctx.getImageData(0, 0, 1, 1).data);\r\n      return `rgba(${r}, ${g}, ${b},`;\r\n    };\r\n    return toRGBA(color);\r\n  }, [color]);\r\n\r\n  const setupCanvas = useCallback(\r\n    (canvas: HTMLCanvasElement, width: number, height: number) => {\r\n      const dpr = window.devicePixelRatio || 1;\r\n      canvas.width = width * dpr;\r\n      canvas.height = height * dpr;\r\n      canvas.style.width = `${width}px`;\r\n      canvas.style.height = `${height}px`;\r\n      const cols = Math.floor(width / (squareSize + gridGap));\r\n      const rows = Math.floor(height / (squareSize + gridGap));\r\n\r\n      const squares = new Float32Array(cols * rows);\r\n      for (let i = 0; i < squares.length; i++) {\r\n        squares[i] = Math.random() * maxOpacity;\r\n      }\r\n\r\n      return { cols, rows, squares, dpr };\r\n    },\r\n    [squareSize, gridGap, maxOpacity],\r\n  );\r\n\r\n  const updateSquares = useCallback(\r\n    (squares: Float32Array, deltaTime: number) => {\r\n      for (let i = 0; i < squares.length; i++) {\r\n        if (Math.random() < flickerChance * deltaTime) {\r\n          squares[i] = Math.random() * maxOpacity;\r\n        }\r\n      }\r\n    },\r\n    [flickerChance, maxOpacity],\r\n  );\r\n\r\n  const drawGrid = useCallback(\r\n    (\r\n      ctx: CanvasRenderingContext2D,\r\n      width: number,\r\n      height: number,\r\n      cols: number,\r\n      rows: number,\r\n      squares: Float32Array,\r\n      dpr: number,\r\n    ) => {\r\n      ctx.clearRect(0, 0, width, height);\r\n      ctx.fillStyle = \"transparent\";\r\n      ctx.fillRect(0, 0, width, height);\r\n\r\n      for (let i = 0; i < cols; i++) {\r\n        for (let j = 0; j < rows; j++) {\r\n          const opacity = squares[i * rows + j];\r\n          ctx.fillStyle = `${memoizedColor}${opacity})`;\r\n          ctx.fillRect(\r\n            i * (squareSize + gridGap) * dpr,\r\n            j * (squareSize + gridGap) * dpr,\r\n            squareSize * dpr,\r\n            squareSize * dpr,\r\n          );\r\n        }\r\n      }\r\n    },\r\n    [memoizedColor, squareSize, gridGap],\r\n  );\r\n\r\n  useEffect(() => {\r\n    const canvas = canvasRef.current;\r\n    const container = containerRef.current;\r\n    if (!canvas || !container) return;\r\n\r\n    const ctx = canvas.getContext(\"2d\");\r\n    if (!ctx) return;\r\n\r\n    let animationFrameId: number;\r\n    let gridParams: ReturnType<typeof setupCanvas>;\r\n\r\n    const updateCanvasSize = () => {\r\n      const newWidth = width || container.clientWidth;\r\n      const newHeight = height || container.clientHeight;\r\n      setCanvasSize({ width: newWidth, height: newHeight });\r\n      gridParams = setupCanvas(canvas, newWidth, newHeight);\r\n    };\r\n\r\n    updateCanvasSize();\r\n\r\n    let lastTime = 0;\r\n    const animate = (time: number) => {\r\n      if (!isInView) return;\r\n\r\n      const deltaTime = (time - lastTime) / 1000;\r\n      lastTime = time;\r\n\r\n      updateSquares(gridParams.squares, deltaTime);\r\n      drawGrid(\r\n        ctx,\r\n        canvas.width,\r\n        canvas.height,\r\n        gridParams.cols,\r\n        gridParams.rows,\r\n        gridParams.squares,\r\n        gridParams.dpr,\r\n      );\r\n      animationFrameId = requestAnimationFrame(animate);\r\n    };\r\n\r\n    const resizeObserver = new ResizeObserver(() => {\r\n      updateCanvasSize();\r\n    });\r\n\r\n    resizeObserver.observe(container);\r\n\r\n    const intersectionObserver = new IntersectionObserver(\r\n      ([entry]) => {\r\n        setIsInView(entry!.isIntersecting);\r\n      },\r\n      { threshold: 0 },\r\n    );\r\n\r\n    intersectionObserver.observe(canvas);\r\n\r\n    if (isInView) {\r\n      animationFrameId = requestAnimationFrame(animate);\r\n    }\r\n\r\n    return () => {\r\n      cancelAnimationFrame(animationFrameId);\r\n      resizeObserver.disconnect();\r\n      intersectionObserver.disconnect();\r\n    };\r\n  }, [setupCanvas, updateSquares, drawGrid, width, height, isInView]);\r\n\r\n  return (\r\n    <div\r\n      ref={containerRef}\r\n      className={cn(`h-full w-full ${className}`)}\r\n      {...props}\r\n    >\r\n      <canvas\r\n        ref={canvasRef}\r\n        className=\"pointer-events-none\"\r\n        style={{\r\n          width: canvasSize.width,\r\n          height: canvasSize.height,\r\n        }}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAsBO,MAAM,iBAAgD,CAAC,EAC5D,aAAa,CAAC,EACd,UAAU,CAAC,EACX,gBAAgB,GAAG,EACnB,QAAQ,cAAc,EACtB,KAAK,EACL,MAAM,EACN,SAAS,EACT,aAAa,GAAG,EAChB,GAAG,OACJ;IACC,MAAM,YAAY,CAAA,GAAA,8SAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,eAAe,CAAA,GAAA,8SAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,OAAO;QAAG,QAAQ;IAAE;IAEnE,MAAM,gBAAgB,CAAA,GAAA,8SAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,MAAM,SAAS,CAAC;YACd,wCAAmC;gBACjC,OAAO,CAAC,aAAa,CAAC;YACxB;;YACA,MAAM;YAEN,MAAM;YAIN,MAAO,eAAG,eAAG;QAEf;QACA,OAAO,OAAO;IAChB,GAAG;QAAC;KAAM;IAEV,MAAM,cAAc,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAC5B,CAAC,QAA2B,OAAe;QACzC,MAAM,MAAM,OAAO,gBAAgB,IAAI;QACvC,OAAO,KAAK,GAAG,QAAQ;QACvB,OAAO,MAAM,GAAG,SAAS;QACzB,OAAO,KAAK,CAAC,KAAK,GAAG,GAAG,MAAM,EAAE,CAAC;QACjC,OAAO,KAAK,CAAC,MAAM,GAAG,GAAG,OAAO,EAAE,CAAC;QACnC,MAAM,OAAO,KAAK,KAAK,CAAC,QAAQ,CAAC,aAAa,OAAO;QACrD,MAAM,OAAO,KAAK,KAAK,CAAC,SAAS,CAAC,aAAa,OAAO;QAEtD,MAAM,UAAU,IAAI,aAAa,OAAO;QACxC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,OAAO,CAAC,EAAE,GAAG,KAAK,MAAM,KAAK;QAC/B;QAEA,OAAO;YAAE;YAAM;YAAM;YAAS;QAAI;IACpC,GACA;QAAC;QAAY;QAAS;KAAW;IAGnC,MAAM,gBAAgB,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAC9B,CAAC,SAAuB;QACtB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,IAAI,KAAK,MAAM,KAAK,gBAAgB,WAAW;gBAC7C,OAAO,CAAC,EAAE,GAAG,KAAK,MAAM,KAAK;YAC/B;QACF;IACF,GACA;QAAC;QAAe;KAAW;IAG7B,MAAM,WAAW,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EACzB,CACE,KACA,OACA,QACA,MACA,MACA,SACA;QAEA,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO;QAC3B,IAAI,SAAS,GAAG;QAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,OAAO;QAE1B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;YAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;gBAC7B,MAAM,UAAU,OAAO,CAAC,IAAI,OAAO,EAAE;gBACrC,IAAI,SAAS,GAAG,GAAG,gBAAgB,QAAQ,CAAC,CAAC;gBAC7C,IAAI,QAAQ,CACV,IAAI,CAAC,aAAa,OAAO,IAAI,KAC7B,IAAI,CAAC,aAAa,OAAO,IAAI,KAC7B,aAAa,KACb,aAAa;YAEjB;QACF;IACF,GACA;QAAC;QAAe;QAAY;KAAQ;IAGtC,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,SAAS,UAAU,OAAO;QAChC,MAAM,YAAY,aAAa,OAAO;QACtC,IAAI,CAAC,UAAU,CAAC,WAAW;QAE3B,MAAM,MAAM,OAAO,UAAU,CAAC;QAC9B,IAAI,CAAC,KAAK;QAEV,IAAI;QACJ,IAAI;QAEJ,MAAM,mBAAmB;YACvB,MAAM,WAAW,SAAS,UAAU,WAAW;YAC/C,MAAM,YAAY,UAAU,UAAU,YAAY;YAClD,cAAc;gBAAE,OAAO;gBAAU,QAAQ;YAAU;YACnD,aAAa,YAAY,QAAQ,UAAU;QAC7C;QAEA;QAEA,IAAI,WAAW;QACf,MAAM,UAAU,CAAC;YACf,IAAI,CAAC,UAAU;YAEf,MAAM,YAAY,CAAC,OAAO,QAAQ,IAAI;YACtC,WAAW;YAEX,cAAc,WAAW,OAAO,EAAE;YAClC,SACE,KACA,OAAO,KAAK,EACZ,OAAO,MAAM,EACb,WAAW,IAAI,EACf,WAAW,IAAI,EACf,WAAW,OAAO,EAClB,WAAW,GAAG;YAEhB,mBAAmB,sBAAsB;QAC3C;QAEA,MAAM,iBAAiB,IAAI,eAAe;YACxC;QACF;QAEA,eAAe,OAAO,CAAC;QAEvB,MAAM,uBAAuB,IAAI,qBAC/B,CAAC,CAAC,MAAM;YACN,YAAY,MAAO,cAAc;QACnC,GACA;YAAE,WAAW;QAAE;QAGjB,qBAAqB,OAAO,CAAC;QAE7B,IAAI,UAAU;YACZ,mBAAmB,sBAAsB;QAC3C;QAEA,OAAO;YACL,qBAAqB;YACrB,eAAe,UAAU;YACzB,qBAAqB,UAAU;QACjC;IACF,GAAG;QAAC;QAAa;QAAe;QAAU;QAAO;QAAQ;KAAS;IAElE,qBACE,uVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAC,cAAc,EAAE,WAAW;QACzC,GAAG,KAAK;kBAET,cAAA,uVAAC;YACC,KAAK;YACL,WAAU;YACV,OAAO;gBACL,OAAO,WAAW,KAAK;gBACvB,QAAQ,WAAW,MAAM;YAC3B;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/components/ui/tooltip.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\n\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\";\r\n\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nfunction TooltipProvider({\r\n  delayDuration = 0,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\r\n  return (\r\n    <TooltipPrimitive.Provider\r\n      data-slot=\"tooltip-provider\"\r\n      delayDuration={delayDuration}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction Tooltip({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\r\n  return (\r\n    <TooltipProvider>\r\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\r\n    </TooltipProvider>\r\n  );\r\n}\r\n\r\nfunction TooltipTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\r\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />;\r\n}\r\n\r\nfunction TooltipContent({\r\n  className,\r\n  sideOffset = 0,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\r\n  return (\r\n    <TooltipPrimitive.Portal>\r\n      <TooltipPrimitive.Content\r\n        data-slot=\"tooltip-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\r\n          className,\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\r\n      </TooltipPrimitive.Content>\r\n    </TooltipPrimitive.Portal>\r\n  );\r\n}\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider };\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;;;;AAK/B;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,uVAAC,6QAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBACE,uVAAC;kBACC,cAAA,uVAAC,6QAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,uVAAC,6QAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,uVAAC,6QAAA,CAAA,SAAuB;kBACtB,cAAA,uVAAC,6QAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,uVAAC,6QAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C", "debugId": null}}, {"offset": {"line": 397, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/components/deer-flow/tooltip.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport type { CSSProperties } from \"react\";\r\n\r\nimport {\r\n  Tooltip as ShadcnTooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"~/components/ui/tooltip\";\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nexport function Tooltip({\r\n  className,\r\n  style,\r\n  children,\r\n  title,\r\n  open,\r\n  side,\r\n  sideOffset,\r\n  delayDuration = 750,\r\n}: {\r\n  className?: string;\r\n  style?: CSSProperties;\r\n  children: React.ReactNode;\r\n  title?: React.ReactNode;\r\n  open?: boolean;\r\n  side?: \"left\" | \"right\" | \"top\" | \"bottom\";\r\n  sideOffset?: number;\r\n  delayDuration?: number;\r\n}) {\r\n  return (\r\n    <TooltipProvider>\r\n      <ShadcnTooltip delayDuration={delayDuration} open={open}>\r\n        <TooltipTrigger asChild>{children}</TooltipTrigger>\r\n        <TooltipContent\r\n          className={cn(className)}\r\n          style={style}\r\n          side={side}\r\n          sideOffset={sideOffset}\r\n        >\r\n          {title}\r\n        </TooltipContent>\r\n      </ShadcnTooltip>\r\n    </TooltipProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAI/B;AAMA;;;;AAEO,SAAS,QAAQ,EACtB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,UAAU,EACV,gBAAgB,GAAG,EAUpB;IACC,qBACE,uVAAC,mIAAA,CAAA,kBAAe;kBACd,cAAA,uVAAC,mIAAA,CAAA,UAAa;YAAC,eAAe;YAAe,MAAM;;8BACjD,uVAAC,mIAAA,CAAA,iBAAc;oBAAC,OAAO;8BAAE;;;;;;8BACzB,uVAAC,mIAAA,CAAA,iBAAc;oBACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;oBACd,OAAO;oBACP,MAAM;oBACN,YAAY;8BAEX;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 451, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/components/magicui/shine-border.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\n\r\nimport { cn } from \"~/lib/utils\";\r\n\r\ninterface ShineBorderProps extends React.HTMLAttributes<HTMLDivElement> {\r\n  /**\r\n   * Width of the border in pixels\r\n   * @default 1\r\n   */\r\n  borderWidth?: number;\r\n  /**\r\n   * Duration of the animation in seconds\r\n   * @default 14\r\n   */\r\n  duration?: number;\r\n  /**\r\n   * Color of the border, can be a single color or an array of colors\r\n   * @default \"#000000\"\r\n   */\r\n  shineColor?: string | string[];\r\n}\r\n\r\n/**\r\n * Shine Border\r\n *\r\n * An animated background border effect component with configurable properties.\r\n */\r\nexport function ShineBorder({\r\n  borderWidth = 1,\r\n  duration = 14,\r\n  shineColor = \"#000000\",\r\n  className,\r\n  style,\r\n  ...props\r\n}: ShineBorderProps) {\r\n  return (\r\n    <div\r\n      style={\r\n        {\r\n          \"--border-width\": `${borderWidth}px`,\r\n          \"--duration\": `${duration}s`,\r\n          backgroundImage: `radial-gradient(transparent,transparent, ${\r\n            Array.isArray(shineColor) ? shineColor.join(\",\") : shineColor\r\n          },transparent,transparent)`,\r\n          backgroundSize: \"300% 300%\",\r\n          mask: `linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)`,\r\n          WebkitMask: `linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)`,\r\n          WebkitMaskComposite: \"xor\",\r\n          maskComposite: \"exclude\",\r\n          padding: \"var(--border-width)\",\r\n          ...style,\r\n        } as React.CSSProperties\r\n      }\r\n      className={cn(\r\n        \"pointer-events-none absolute inset-0 size-full rounded-[inherit] will-change-[background-position] motion-safe:animate-shine\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAJA;;;AA6BO,SAAS,YAAY,EAC1B,cAAc,CAAC,EACf,WAAW,EAAE,EACb,aAAa,SAAS,EACtB,SAAS,EACT,KAAK,EACL,GAAG,OACc;IACjB,qBACE,uVAAC;QACC,OACE;YACE,kBAAkB,GAAG,YAAY,EAAE,CAAC;YACpC,cAAc,GAAG,SAAS,CAAC,CAAC;YAC5B,iBAAiB,CAAC,yCAAyC,EACzD,MAAM,OAAO,CAAC,cAAc,WAAW,IAAI,CAAC,OAAO,WACpD,yBAAyB,CAAC;YAC3B,gBAAgB;YAChB,MAAM,CAAC,gEAAgE,CAAC;YACxE,YAAY,CAAC,gEAAgE,CAAC;YAC9E,qBAAqB;YACrB,eAAe;YACf,SAAS;YACT,GAAG,KAAK;QACV;QAEF,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gIACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 487, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/components/ui/button.tsx"], "sourcesContent": ["import { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\nimport * as React from \"react\";\r\n\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  },\r\n);\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean;\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(\r\n        buttonVariants({ variant, size, className }),\r\n        \"cursor-pointer active:scale-105\",\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAGA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,uVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,eAAe;YAAE;YAAS;YAAM;QAAU,IAC1C;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 544, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/components/ui/slider.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nfunction Slider({\r\n  className,\r\n  defaultValue,\r\n  value,\r\n  min = 0,\r\n  max = 100,\r\n  ...props\r\n}: React.ComponentProps<typeof SliderPrimitive.Root>) {\r\n  const _values = React.useMemo(\r\n    () =>\r\n      Array.isArray(value)\r\n        ? value\r\n        : Array.isArray(defaultValue)\r\n          ? defaultValue\r\n          : [min, max],\r\n    [value, defaultValue, min, max]\r\n  )\r\n\r\n  return (\r\n    <SliderPrimitive.Root\r\n      data-slot=\"slider\"\r\n      defaultValue={defaultValue}\r\n      value={value}\r\n      min={min}\r\n      max={max}\r\n      className={cn(\r\n        \"relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <SliderPrimitive.Track\r\n        data-slot=\"slider-track\"\r\n        className={cn(\r\n          \"bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5\"\r\n        )}\r\n      >\r\n        <SliderPrimitive.Range\r\n          data-slot=\"slider-range\"\r\n          className={cn(\r\n            \"bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full\"\r\n          )}\r\n        />\r\n      </SliderPrimitive.Track>\r\n      {Array.from({ length: _values.length }, (_, index) => (\r\n        <SliderPrimitive.Thumb\r\n          data-slot=\"slider-thumb\"\r\n          key={index}\r\n          className=\"border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50\"\r\n        />\r\n      ))}\r\n    </SliderPrimitive.Root>\r\n  )\r\n}\r\n\r\nexport { Slider }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,YAAY,EACZ,KAAK,EACL,MAAM,CAAC,EACP,MAAM,GAAG,EACT,GAAG,OAC+C;IAClD,MAAM,UAAU,CAAA,GAAA,8SAAA,CAAA,UAAa,AAAD,EAC1B,IACE,MAAM,OAAO,CAAC,SACV,QACA,MAAM,OAAO,CAAC,gBACZ,eACA;YAAC;YAAK;SAAI,EAClB;QAAC;QAAO;QAAc;QAAK;KAAI;IAGjC,qBACE,uVAAC,+QAAA,CAAA,OAAoB;QACnB,aAAU;QACV,cAAc;QACd,OAAO;QACP,KAAK;QACL,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uOACA;QAED,GAAG,KAAK;;0BAET,uVAAC,+QAAA,CAAA,QAAqB;gBACpB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;0BAGF,cAAA,uVAAC,+QAAA,CAAA,QAAqB;oBACpB,aAAU;oBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;YAIL,MAAM,IAAI,CAAC;gBAAE,QAAQ,QAAQ,MAAM;YAAC,GAAG,CAAC,GAAG,sBAC1C,uVAAC,+QAAA,CAAA,QAAqB;oBACpB,aAAU;oBAEV,WAAU;mBADL;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 615, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/hooks/use-intersection-observer.ts"], "sourcesContent": ["import { useEffect, useRef, useState } from \"react\";\r\n\r\ntype State = {\r\n  isIntersecting: boolean;\r\n  entry?: IntersectionObserverEntry;\r\n};\r\n\r\ntype UseIntersectionObserverOptions = {\r\n  root?: Element | Document | null;\r\n  rootMargin?: string;\r\n  threshold?: number | number[];\r\n  freezeOnceVisible?: boolean;\r\n  onChange?: (\r\n    isIntersecting: boolean,\r\n    entry: IntersectionObserverEntry,\r\n  ) => void;\r\n  initialIsIntersecting?: boolean;\r\n};\r\n\r\ntype IntersectionReturn = [\r\n  (node?: Element | null) => void,\r\n  boolean,\r\n  IntersectionObserverEntry | undefined,\r\n] & {\r\n  ref: (node?: Element | null) => void;\r\n  isIntersecting: boolean;\r\n  entry?: IntersectionObserverEntry;\r\n};\r\n\r\nexport function useIntersectionObserver({\r\n  threshold = 0,\r\n  root = null,\r\n  rootMargin = \"0%\",\r\n  freezeOnceVisible = false,\r\n  initialIsIntersecting = false,\r\n  onChange,\r\n}: UseIntersectionObserverOptions = {}): IntersectionReturn {\r\n  const [ref, setRef] = useState<Element | null>(null);\r\n\r\n  const [state, setState] = useState<State>(() => ({\r\n    isIntersecting: initialIsIntersecting,\r\n    entry: undefined,\r\n  }));\r\n\r\n  const callbackRef =\r\n    useRef<UseIntersectionObserverOptions[\"onChange\"]>(undefined);\r\n\r\n  callbackRef.current = onChange;\r\n\r\n  const frozen = state.entry?.isIntersecting && freezeOnceVisible;\r\n\r\n  useEffect(() => {\r\n    // Ensure we have a ref to observe\r\n    if (!ref) return;\r\n\r\n    // Ensure the browser supports the Intersection Observer API\r\n    if (!(\"IntersectionObserver\" in window)) return;\r\n\r\n    // Skip if frozen\r\n    if (frozen) return;\r\n\r\n    let unobserve: (() => void) | undefined;\r\n\r\n    const observer = new IntersectionObserver(\r\n      (entries: IntersectionObserverEntry[]): void => {\r\n        const thresholds = Array.isArray(observer.thresholds)\r\n          ? observer.thresholds\r\n          : [observer.thresholds];\r\n\r\n        entries.forEach((entry) => {\r\n          const isIntersecting =\r\n            entry.isIntersecting &&\r\n            thresholds.some(\r\n              (threshold) => entry.intersectionRatio >= threshold,\r\n            );\r\n\r\n          setState({ isIntersecting, entry });\r\n\r\n          if (callbackRef.current) {\r\n            callbackRef.current(isIntersecting, entry);\r\n          }\r\n\r\n          if (isIntersecting && freezeOnceVisible && unobserve) {\r\n            unobserve();\r\n            unobserve = undefined;\r\n          }\r\n        });\r\n      },\r\n      { threshold, root, rootMargin },\r\n    );\r\n\r\n    observer.observe(ref);\r\n\r\n    return () => {\r\n      observer.disconnect();\r\n    };\r\n\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [\r\n    ref,\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n    JSON.stringify(threshold),\r\n    root,\r\n    rootMargin,\r\n    frozen,\r\n    freezeOnceVisible,\r\n  ]);\r\n\r\n  // ensures that if the observed element changes, the intersection observer is reinitialized\r\n  const prevRef = useRef<Element | null>(null);\r\n\r\n  useEffect(() => {\r\n    if (\r\n      !ref &&\r\n      state.entry?.target &&\r\n      !freezeOnceVisible &&\r\n      !frozen &&\r\n      prevRef.current !== state.entry.target\r\n    ) {\r\n      prevRef.current = state.entry.target;\r\n      setState({ isIntersecting: initialIsIntersecting, entry: undefined });\r\n    }\r\n  }, [ref, state.entry, freezeOnceVisible, frozen, initialIsIntersecting]);\r\n\r\n  const result = [\r\n    setRef,\r\n    !!state.isIntersecting,\r\n    state.entry,\r\n  ] as IntersectionReturn;\r\n\r\n  // Support object destructuring, by adding the specific values.\r\n  result.ref = result[0];\r\n  result.isIntersecting = result[1];\r\n  result.entry = result[2];\r\n\r\n  return result;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AA6BO,SAAS,wBAAwB,EACtC,YAAY,CAAC,EACb,OAAO,IAAI,EACX,aAAa,IAAI,EACjB,oBAAoB,KAAK,EACzB,wBAAwB,KAAK,EAC7B,QAAQ,EACuB,GAAG,CAAC,CAAC;IACpC,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAkB;IAE/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAS,IAAM,CAAC;YAC/C,gBAAgB;YAChB,OAAO;QACT,CAAC;IAED,MAAM,cACJ,CAAA,GAAA,8SAAA,CAAA,SAAM,AAAD,EAA8C;IAErD,YAAY,OAAO,GAAG;IAEtB,MAAM,SAAS,MAAM,KAAK,EAAE,kBAAkB;IAE9C,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,kCAAkC;QAClC,IAAI,CAAC,KAAK;QAEV,4DAA4D;QAC5D,IAAI,CAAC,CAAC,0BAA0B,MAAM,GAAG;QAEzC,iBAAiB;QACjB,IAAI,QAAQ;QAEZ,IAAI;QAEJ,MAAM,WAAW,IAAI,qBACnB,CAAC;YACC,MAAM,aAAa,MAAM,OAAO,CAAC,SAAS,UAAU,IAChD,SAAS,UAAU,GACnB;gBAAC,SAAS,UAAU;aAAC;YAEzB,QAAQ,OAAO,CAAC,CAAC;gBACf,MAAM,iBACJ,MAAM,cAAc,IACpB,WAAW,IAAI,CACb,CAAC,YAAc,MAAM,iBAAiB,IAAI;gBAG9C,SAAS;oBAAE;oBAAgB;gBAAM;gBAEjC,IAAI,YAAY,OAAO,EAAE;oBACvB,YAAY,OAAO,CAAC,gBAAgB;gBACtC;gBAEA,IAAI,kBAAkB,qBAAqB,WAAW;oBACpD;oBACA,YAAY;gBACd;YACF;QACF,GACA;YAAE;YAAW;YAAM;QAAW;QAGhC,SAAS,OAAO,CAAC;QAEjB,OAAO;YACL,SAAS,UAAU;QACrB;IAEA,uDAAuD;IACzD,GAAG;QACD;QACA,uDAAuD;QACvD,KAAK,SAAS,CAAC;QACf;QACA;QACA;QACA;KACD;IAED,2FAA2F;IAC3F,MAAM,UAAU,CAAA,GAAA,8SAAA,CAAA,SAAM,AAAD,EAAkB;IAEvC,CAAA,GAAA,8SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IACE,CAAC,OACD,MAAM,KAAK,EAAE,UACb,CAAC,qBACD,CAAC,UACD,QAAQ,OAAO,KAAK,MAAM,KAAK,CAAC,MAAM,EACtC;YACA,QAAQ,OAAO,GAAG,MAAM,KAAK,CAAC,MAAM;YACpC,SAAS;gBAAE,gBAAgB;gBAAuB,OAAO;YAAU;QACrE;IACF,GAAG;QAAC;QAAK,MAAM,KAAK;QAAE;QAAmB;QAAQ;KAAsB;IAEvE,MAAM,SAAS;QACb;QACA,CAAC,CAAC,MAAM,cAAc;QACtB,MAAM,KAAK;KACZ;IAED,+DAA+D;IAC/D,OAAO,GAAG,GAAG,MAAM,CAAC,EAAE;IACtB,OAAO,cAAc,GAAG,MAAM,CAAC,EAAE;IACjC,OAAO,KAAK,GAAG,MAAM,CAAC,EAAE;IAExB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 708, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/app/landing/store/graph.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport type { Edge, Node } from \"@xyflow/react\";\r\nimport {\r\n  Brain,\r\n  FilePen,\r\n  MessageSquareQuote,\r\n  Microscope,\r\n  SquareTerminal,\r\n  UserCheck,\r\n  Users,\r\n  type LucideIcon,\r\n} from \"lucide-react\";\r\n\r\nexport type GraphNode = Node<{\r\n  label: string;\r\n  icon?: LucideIcon;\r\n  active?: boolean;\r\n}>;\r\n\r\nexport type Graph = {\r\n  nodes: GraphNode[];\r\n  edges: Edge[];\r\n};\r\n\r\nconst ROW_HEIGHT = 85;\r\nconst ROW_1 = 0;\r\nconst ROW_2 = ROW_HEIGHT;\r\nconst ROW_3 = ROW_HEIGHT * 2;\r\nconst ROW_4 = ROW_HEIGHT * 2;\r\nconst ROW_5 = ROW_HEIGHT * 3;\r\nconst ROW_6 = ROW_HEIGHT * 4;\r\n\r\nexport const graph: Graph = {\r\n  nodes: [\r\n    {\r\n      id: \"Start\",\r\n      type: \"circle\",\r\n      data: { label: \"Start\" },\r\n      position: { x: -75, y: ROW_1 },\r\n    },\r\n    {\r\n      id: \"Coordinator\",\r\n      data: { icon: MessageSquareQuote, label: \"Coordinator\" },\r\n      position: { x: 150, y: ROW_1 },\r\n    },\r\n    {\r\n      id: \"Planner\",\r\n      data: { icon: Brain, label: \"Planner\" },\r\n      position: { x: 150, y: ROW_2 },\r\n    },\r\n    {\r\n      id: \"Reporter\",\r\n      data: { icon: FilePen, label: \"Reporter\" },\r\n      position: { x: 275, y: ROW_3 },\r\n    },\r\n    {\r\n      id: \"HumanFeedback\",\r\n      data: { icon: UserCheck, label: \"Human Feedback\" },\r\n      position: { x: 25, y: ROW_4 },\r\n    },\r\n    {\r\n      id: \"ResearchTeam\",\r\n      data: { icon: Users, label: \"Research Team\" },\r\n      position: { x: 25, y: ROW_5 },\r\n    },\r\n    {\r\n      id: \"Researcher\",\r\n      data: { icon: Microscope, label: \"Researcher\" },\r\n      position: { x: -75, y: ROW_6 },\r\n    },\r\n    {\r\n      id: \"Coder\",\r\n      data: { icon: SquareTerminal, label: \"Coder\" },\r\n      position: { x: 125, y: ROW_6 },\r\n    },\r\n    {\r\n      id: \"End\",\r\n      type: \"circle\",\r\n      data: { label: \"End\" },\r\n      position: { x: 330, y: ROW_6 },\r\n    },\r\n  ],\r\n  edges: [\r\n    {\r\n      id: \"Start->Coordinator\",\r\n      source: \"Start\",\r\n      target: \"Coordinator\",\r\n      sourceHandle: \"right\",\r\n      targetHandle: \"left\",\r\n      animated: true,\r\n    },\r\n    {\r\n      id: \"Coordinator->Planner\",\r\n      source: \"Coordinator\",\r\n      target: \"Planner\",\r\n      sourceHandle: \"bottom\",\r\n      targetHandle: \"top\",\r\n      animated: true,\r\n    },\r\n    {\r\n      id: \"Planner->Reporter\",\r\n      source: \"Planner\",\r\n      target: \"Reporter\",\r\n      sourceHandle: \"right\",\r\n      targetHandle: \"top\",\r\n      animated: true,\r\n    },\r\n    {\r\n      id: \"Planner->HumanFeedback\",\r\n      source: \"Planner\",\r\n      target: \"HumanFeedback\",\r\n      sourceHandle: \"left\",\r\n      targetHandle: \"top\",\r\n      animated: true,\r\n    },\r\n    {\r\n      id: \"HumanFeedback->Planner\",\r\n      source: \"HumanFeedback\",\r\n      target: \"Planner\",\r\n      sourceHandle: \"right\",\r\n      targetHandle: \"bottom\",\r\n      animated: true,\r\n    },\r\n    {\r\n      id: \"HumanFeedback->ResearchTeam\",\r\n      source: \"HumanFeedback\",\r\n      target: \"ResearchTeam\",\r\n      sourceHandle: \"bottom\",\r\n      targetHandle: \"top\",\r\n      animated: true,\r\n    },\r\n    {\r\n      id: \"Reporter->End\",\r\n      source: \"Reporter\",\r\n      target: \"End\",\r\n      sourceHandle: \"bottom\",\r\n      targetHandle: \"top\",\r\n      animated: true,\r\n    },\r\n    {\r\n      id: \"ResearchTeam->Researcher\",\r\n      source: \"ResearchTeam\",\r\n      target: \"Researcher\",\r\n      sourceHandle: \"left\",\r\n      targetHandle: \"top\",\r\n      animated: true,\r\n    },\r\n    {\r\n      id: \"ResearchTeam->Coder\",\r\n      source: \"ResearchTeam\",\r\n      target: \"Coder\",\r\n      sourceHandle: \"bottom\",\r\n      targetHandle: \"left\",\r\n      animated: true,\r\n    },\r\n    {\r\n      id: \"ResearchTeam->Planner\",\r\n      source: \"ResearchTeam\",\r\n      target: \"Planner\",\r\n      sourceHandle: \"right\",\r\n      targetHandle: \"bottom\",\r\n      animated: true,\r\n    },\r\n    {\r\n      id: \"Researcher->ResearchTeam\",\r\n      source: \"Researcher\",\r\n      target: \"ResearchTeam\",\r\n      sourceHandle: \"right\",\r\n      targetHandle: \"bottom\",\r\n      animated: true,\r\n    },\r\n    {\r\n      id: \"Coder->ResearchTeam\",\r\n      source: \"Coder\",\r\n      target: \"ResearchTeam\",\r\n      sourceHandle: \"top\",\r\n      targetHandle: \"right\",\r\n      animated: true,\r\n    },\r\n  ],\r\n};\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AAG/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAsBA,MAAM,aAAa;AACnB,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,QAAQ,aAAa;AAC3B,MAAM,QAAQ,aAAa;AAC3B,MAAM,QAAQ,aAAa;AAC3B,MAAM,QAAQ,aAAa;AAEpB,MAAM,QAAe;IAC1B,OAAO;QACL;YACE,IAAI;YACJ,MAAM;YACN,MAAM;gBAAE,OAAO;YAAQ;YACvB,UAAU;gBAAE,GAAG,CAAC;gBAAI,GAAG;YAAM;QAC/B;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,MAAM,0TAAA,CAAA,qBAAkB;gBAAE,OAAO;YAAc;YACvD,UAAU;gBAAE,GAAG;gBAAK,GAAG;YAAM;QAC/B;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,MAAM,wRAAA,CAAA,QAAK;gBAAE,OAAO;YAAU;YACtC,UAAU;gBAAE,GAAG;gBAAK,GAAG;YAAM;QAC/B;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,MAAM,gSAAA,CAAA,UAAO;gBAAE,OAAO;YAAW;YACzC,UAAU;gBAAE,GAAG;gBAAK,GAAG;YAAM;QAC/B;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,MAAM,oSAAA,CAAA,YAAS;gBAAE,OAAO;YAAiB;YACjD,UAAU;gBAAE,GAAG;gBAAI,GAAG;YAAM;QAC9B;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,MAAM,wRAAA,CAAA,QAAK;gBAAE,OAAO;YAAgB;YAC5C,UAAU;gBAAE,GAAG;gBAAI,GAAG;YAAM;QAC9B;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,MAAM,kSAAA,CAAA,aAAU;gBAAE,OAAO;YAAa;YAC9C,UAAU;gBAAE,GAAG,CAAC;gBAAI,GAAG;YAAM;QAC/B;QACA;YACE,IAAI;YACJ,MAAM;gBAAE,MAAM,8SAAA,CAAA,iBAAc;gBAAE,OAAO;YAAQ;YAC7C,UAAU;gBAAE,GAAG;gBAAK,GAAG;YAAM;QAC/B;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;gBAAE,OAAO;YAAM;YACrB,UAAU;gBAAE,GAAG;gBAAK,GAAG;YAAM;QAC/B;KACD;IACD,OAAO;QACL;YACE,IAAI;YACJ,QAAQ;YACR,QAAQ;YACR,cAAc;YACd,cAAc;YACd,UAAU;QACZ;QACA;YACE,IAAI;YACJ,QAAQ;YACR,QAAQ;YACR,cAAc;YACd,cAAc;YACd,UAAU;QACZ;QACA;YACE,IAAI;YACJ,QAAQ;YACR,QAAQ;YACR,cAAc;YACd,cAAc;YACd,UAAU;QACZ;QACA;YACE,IAAI;YACJ,QAAQ;YACR,QAAQ;YACR,cAAc;YACd,cAAc;YACd,UAAU;QACZ;QACA;YACE,IAAI;YACJ,QAAQ;YACR,QAAQ;YACR,cAAc;YACd,cAAc;YACd,UAAU;QACZ;QACA;YACE,IAAI;YACJ,QAAQ;YACR,QAAQ;YACR,cAAc;YACd,cAAc;YACd,UAAU;QACZ;QACA;YACE,IAAI;YACJ,QAAQ;YACR,QAAQ;YACR,cAAc;YACd,cAAc;YACd,UAAU;QACZ;QACA;YACE,IAAI;YACJ,QAAQ;YACR,QAAQ;YACR,cAAc;YACd,cAAc;YACd,UAAU;QACZ;QACA;YACE,IAAI;YACJ,QAAQ;YACR,QAAQ;YACR,cAAc;YACd,cAAc;YACd,UAAU;QACZ;QACA;YACE,IAAI;YACJ,QAAQ;YACR,QAAQ;YACR,cAAc;YACd,cAAc;YACd,UAAU;QACZ;QACA;YACE,IAAI;YACJ,QAAQ;YACR,QAAQ;YACR,cAAc;YACd,cAAc;YACd,UAAU;QACZ;QACA;YACE,IAAI;YACJ,QAAQ;YACR,QAAQ;YACR,cAAc;YACd,cAAc;YACd,UAAU;QACZ;KACD;AACH", "debugId": null}}, {"offset": {"line": 935, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/app/landing/store/playbook.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nexport const playbook = {\r\n  steps: [\r\n    {\r\n      description:\r\n        \"The Coordinator is responsible for engaging with the user to understand their problem and requirements.\",\r\n      activeNodes: [\"Start\", \"Coordinator\"],\r\n      activeEdges: [\"Start->Coordinator\"],\r\n      tooltipPosition: \"right\",\r\n    },\r\n    {\r\n      description:\r\n        \"If the user's problem is clearly defined, the Coordinator will hand it over to the Planner.\",\r\n      activeNodes: [\"Coordinator\", \"Planner\"],\r\n      activeEdges: [\"Coordinator->Planner\"],\r\n      tooltipPosition: \"left\",\r\n    },\r\n    {\r\n      description: \"Awaiting human feedback to refine the plan.\",\r\n      activeNodes: [\"Planner\", \"HumanFeedback\"],\r\n      activeEdges: [\"Planner->HumanFeedback\"],\r\n      tooltipPosition: \"left\",\r\n    },\r\n    {\r\n      description: \"Updating the plan based on human feedback.\",\r\n      activeNodes: [\"HumanFeedback\", \"Planner\"],\r\n      activeEdges: [\"HumanFeedback->Planner\"],\r\n      tooltipPosition: \"left\",\r\n    },\r\n    {\r\n      description:\r\n        \"The Research Team is responsible for conducting the core research tasks.\",\r\n      activeNodes: [\"Planner\", \"HumanFeedback\", \"ResearchTeam\"],\r\n      activeEdges: [\r\n        \"Planner->HumanFeedback\",\r\n        \"HumanFeedback->ResearchTeam\",\r\n        \"ResearchTeam->HumanFeedback\",\r\n      ],\r\n      tooltipPosition: \"left\",\r\n    },\r\n    {\r\n      description:\r\n        \"The Researcher is responsible for gathering information using search and crawling tools.\",\r\n      activeNodes: [\"ResearchTeam\", \"Researcher\"],\r\n      activeEdges: [\"ResearchTeam->Researcher\", \"Researcher->ResearchTeam\"],\r\n      tooltipPosition: \"left\",\r\n    },\r\n    {\r\n      description:\r\n        \"The Coder is responsible for writing Python code to solve math problems, data analysis, and more.\",\r\n      tooltipPosition: \"right\",\r\n      activeNodes: [\"ResearchTeam\", \"Coder\"],\r\n      activeEdges: [\"ResearchTeam->Coder\", \"Coder->ResearchTeam\"],\r\n    },\r\n    {\r\n      description:\r\n        \"Once the research tasks are completed, the Researcher will hand over to the Planner.\",\r\n      activeNodes: [\"ResearchTeam\", \"Planner\"],\r\n      activeEdges: [\"ResearchTeam->Planner\"],\r\n      tooltipPosition: \"left\",\r\n    },\r\n    {\r\n      description:\r\n        \"If no additional information is required, the Planner will handoff to the Reporter.\",\r\n      activeNodes: [\"Reporter\", \"Planner\"],\r\n      activeEdges: [\"Planner->Reporter\"],\r\n      tooltipPosition: \"right\",\r\n    },\r\n    {\r\n      description:\r\n        \"The Reporter will prepare a report summarizing the results.\",\r\n      activeNodes: [\"End\", \"Reporter\"],\r\n      activeEdges: [\"Reporter->End\"],\r\n      tooltipPosition: \"bottom\",\r\n    },\r\n  ],\r\n};\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AAExB,MAAM,WAAW;IACtB,OAAO;QACL;YACE,aACE;YACF,aAAa;gBAAC;gBAAS;aAAc;YACrC,aAAa;gBAAC;aAAqB;YACnC,iBAAiB;QACnB;QACA;YACE,aACE;YACF,aAAa;gBAAC;gBAAe;aAAU;YACvC,aAAa;gBAAC;aAAuB;YACrC,iBAAiB;QACnB;QACA;YACE,aAAa;YACb,aAAa;gBAAC;gBAAW;aAAgB;YACzC,aAAa;gBAAC;aAAyB;YACvC,iBAAiB;QACnB;QACA;YACE,aAAa;YACb,aAAa;gBAAC;gBAAiB;aAAU;YACzC,aAAa;gBAAC;aAAyB;YACvC,iBAAiB;QACnB;QACA;YACE,aACE;YACF,aAAa;gBAAC;gBAAW;gBAAiB;aAAe;YACzD,aAAa;gBACX;gBACA;gBACA;aACD;YACD,iBAAiB;QACnB;QACA;YACE,aACE;YACF,aAAa;gBAAC;gBAAgB;aAAa;YAC3C,aAAa;gBAAC;gBAA4B;aAA2B;YACrE,iBAAiB;QACnB;QACA;YACE,aACE;YACF,iBAAiB;YACjB,aAAa;gBAAC;gBAAgB;aAAQ;YACtC,aAAa;gBAAC;gBAAuB;aAAsB;QAC7D;QACA;YACE,aACE;YACF,aAAa;gBAAC;gBAAgB;aAAU;YACxC,aAAa;gBAAC;aAAwB;YACtC,iBAAiB;QACnB;QACA;YACE,aACE;YACF,aAAa;gBAAC;gBAAY;aAAU;YACpC,aAAa;gBAAC;aAAoB;YAClC,iBAAiB;QACnB;QACA;YACE,aACE;YACF,aAAa;gBAAC;gBAAO;aAAW;YAChC,aAAa;gBAAC;aAAgB;YAC9B,iBAAiB;QACnB;KACD;AACH", "debugId": null}}, {"offset": {"line": 1065, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/app/landing/store/index.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nexport * from \"./graph\";\r\nexport * from \"./playbook\";\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;AAE/B;AACA", "debugId": null}}, {"offset": {"line": 1088, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/core/utils/time.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nexport function sleep(ms: number) {\r\n  return new Promise((resolve) => setTimeout(resolve, ms));\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AAExB,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;AACtD", "debugId": null}}, {"offset": {"line": 1102, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/core/utils/json.ts"], "sourcesContent": ["import { parse } from \"best-effort-json-parser\";\r\n\r\nexport function parseJSON<T>(json: string | null | undefined, fallback: T) {\r\n  if (!json) {\r\n    return fallback;\r\n  }\r\n  try {\r\n    const raw = json\r\n      .trim()\r\n      .replace(/^```json\\s*/, \"\")\r\n      .replace(/^```\\s*/, \"\")\r\n      .replace(/\\s*```$/, \"\");\r\n    return parse(raw) as T;\r\n  } catch {\r\n    return fallback;\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEO,SAAS,UAAa,IAA+B,EAAE,QAAW;IACvE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IACA,IAAI;QACF,MAAM,MAAM,KACT,IAAI,GACJ,OAAO,CAAC,eAAe,IACvB,OAAO,CAAC,WAAW,IACnB,OAAO,CAAC,WAAW;QACtB,OAAO,CAAA,GAAA,8OAAA,CAAA,QAAK,AAAD,EAAE;IACf,EAAE,OAAM;QACN,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1124, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/core/utils/deep-clone.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nexport function deepClone<T>(value: T): T {\r\n  return JSON.parse(JSON.stringify(value));\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AAExB,SAAS,UAAa,KAAQ;IACnC,OAAO,KAAK,KAAK,CAAC,KAAK,SAAS,CAAC;AACnC", "debugId": null}}, {"offset": {"line": 1138, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/core/utils/index.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nexport * from \"./time\";\r\nexport * from \"./json\";\r\nexport * from \"./deep-clone\";\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;AAE/B;AACA;AACA", "debugId": null}}, {"offset": {"line": 1164, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/app/landing/store/mav-store.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { create } from \"zustand\";\r\n\r\nimport { sleep } from \"~/core/utils\";\r\n\r\nimport { graph, type Graph } from \"./graph\";\r\nimport { playbook } from \"./playbook\";\r\n\r\n// Store for MAV(Multi-Agent Visualization)\r\nexport const useMAVStore = create<{\r\n  graph: Graph;\r\n  activeStepIndex: number;\r\n  playing: boolean;\r\n}>(() => ({\r\n  graph,\r\n  activeStepIndex: -1,\r\n  playing: false,\r\n}));\r\n\r\nexport function activateStep(stepIndex: number) {\r\n  const nextStep = playbook.steps[stepIndex]!;\r\n  const currentGraph = useMAVStore.getState().graph;\r\n  const nextGraph: Graph = {\r\n    nodes: currentGraph.nodes.map((node) => ({\r\n      ...node,\r\n      data: {\r\n        ...node.data,\r\n        active: nextStep.activeNodes.includes(node.id),\r\n        stepDescription:\r\n          nextStep.activeNodes.indexOf(node.id) ===\r\n            nextStep.activeNodes.length - 1 && nextStep.description,\r\n        stepTooltipPosition:\r\n          nextStep.activeNodes.indexOf(node.id) ===\r\n            nextStep.activeNodes.length - 1 && nextStep.tooltipPosition,\r\n      },\r\n    })),\r\n    edges: currentGraph.edges.map((edge) => ({\r\n      ...edge,\r\n      animated: nextStep.activeEdges.includes(edge.id),\r\n    })),\r\n  };\r\n  useMAVStore.setState({\r\n    activeStepIndex: stepIndex,\r\n    graph: nextGraph,\r\n  });\r\n}\r\n\r\nexport function nextStep() {\r\n  let stepIndex = useMAVStore.getState().activeStepIndex;\r\n  if (stepIndex >= playbook.steps.length - 1) {\r\n    stepIndex = 0;\r\n  } else {\r\n    stepIndex++;\r\n  }\r\n  activateStep(stepIndex);\r\n}\r\n\r\nexport function prevStep() {\r\n  let stepIndex = useMAVStore.getState().activeStepIndex;\r\n  if (stepIndex <= 0) {\r\n    stepIndex = playbook.steps.length - 1;\r\n  } else {\r\n    stepIndex--;\r\n  }\r\n  activateStep(stepIndex);\r\n}\r\n\r\nexport async function play() {\r\n  const state = useMAVStore.getState();\r\n  const activeStepIndex = state.activeStepIndex;\r\n  if (activeStepIndex >= playbook.steps.length - 1) {\r\n    if (state.playing) {\r\n      stop();\r\n      return;\r\n    }\r\n  }\r\n  useMAVStore.setState({\r\n    playing: true,\r\n  });\r\n  nextStep();\r\n  await sleep(3000);\r\n  const playing = useMAVStore.getState().playing;\r\n  if (playing) {\r\n    await play();\r\n  }\r\n}\r\n\r\nexport function pause() {\r\n  useMAVStore.setState({\r\n    playing: false,\r\n  });\r\n}\r\n\r\nexport async function togglePlay() {\r\n  const playing = useMAVStore.getState().playing;\r\n  if (playing) {\r\n    pause();\r\n  } else {\r\n    await play();\r\n  }\r\n}\r\n\r\nexport function stop() {\r\n  useMAVStore.setState({\r\n    playing: false,\r\n    activeStepIndex: -1,\r\n    graph,\r\n  });\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;;;;;;;AAE/B;AAEA;AAAA;AAEA;AACA;;;;;AAGO,MAAM,cAAc,CAAA,GAAA,oPAAA,CAAA,SAAM,AAAD,EAI7B,IAAM,CAAC;QACR,OAAA,uIAAA,CAAA,QAAK;QACL,iBAAiB,CAAC;QAClB,SAAS;IACX,CAAC;AAEM,SAAS,aAAa,SAAiB;IAC5C,MAAM,WAAW,0IAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,UAAU;IAC1C,MAAM,eAAe,YAAY,QAAQ,GAAG,KAAK;IACjD,MAAM,YAAmB;QACvB,OAAO,aAAa,KAAK,CAAC,GAAG,CAAC,CAAC,OAAS,CAAC;gBACvC,GAAG,IAAI;gBACP,MAAM;oBACJ,GAAG,KAAK,IAAI;oBACZ,QAAQ,SAAS,WAAW,CAAC,QAAQ,CAAC,KAAK,EAAE;oBAC7C,iBACE,SAAS,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,MAClC,SAAS,WAAW,CAAC,MAAM,GAAG,KAAK,SAAS,WAAW;oBAC3D,qBACE,SAAS,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,MAClC,SAAS,WAAW,CAAC,MAAM,GAAG,KAAK,SAAS,eAAe;gBACjE;YACF,CAAC;QACD,OAAO,aAAa,KAAK,CAAC,GAAG,CAAC,CAAC,OAAS,CAAC;gBACvC,GAAG,IAAI;gBACP,UAAU,SAAS,WAAW,CAAC,QAAQ,CAAC,KAAK,EAAE;YACjD,CAAC;IACH;IACA,YAAY,QAAQ,CAAC;QACnB,iBAAiB;QACjB,OAAO;IACT;AACF;AAEO,SAAS;IACd,IAAI,YAAY,YAAY,QAAQ,GAAG,eAAe;IACtD,IAAI,aAAa,0IAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;QAC1C,YAAY;IACd,OAAO;QACL;IACF;IACA,aAAa;AACf;AAEO,SAAS;IACd,IAAI,YAAY,YAAY,QAAQ,GAAG,eAAe;IACtD,IAAI,aAAa,GAAG;QAClB,YAAY,0IAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,MAAM,GAAG;IACtC,OAAO;QACL;IACF;IACA,aAAa;AACf;AAEO,eAAe;IACpB,MAAM,QAAQ,YAAY,QAAQ;IAClC,MAAM,kBAAkB,MAAM,eAAe;IAC7C,IAAI,mBAAmB,0IAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;QAChD,IAAI,MAAM,OAAO,EAAE;YACjB;YACA;QACF;IACF;IACA,YAAY,QAAQ,CAAC;QACnB,SAAS;IACX;IACA;IACA,MAAM,CAAA,GAAA,4HAAA,CAAA,QAAK,AAAD,EAAE;IACZ,MAAM,UAAU,YAAY,QAAQ,GAAG,OAAO;IAC9C,IAAI,SAAS;QACX,MAAM;IACR;AACF;AAEO,SAAS;IACd,YAAY,QAAQ,CAAC;QACnB,SAAS;IACX;AACF;AAEO,eAAe;IACpB,MAAM,UAAU,YAAY,QAAQ,GAAG,OAAO;IAC9C,IAAI,SAAS;QACX;IACF,OAAO;QACL,MAAM;IACR;AACF;AAEO,SAAS;IACd,YAAY,QAAQ,CAAC;QACnB,SAAS;QACT,iBAAiB,CAAC;QAClB,OAAA,uIAAA,CAAA,QAAK;IACP;AACF", "debugId": null}}, {"offset": {"line": 1276, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/app/landing/components/multi-agent-visualization.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\n\"use client\";\r\n\r\nimport {\r\n  ReactFlow,\r\n  Background,\r\n  Handle,\r\n  Position,\r\n  type Edge,\r\n  type ReactFlowInstance,\r\n} from \"@xyflow/react\";\r\nimport {\r\n  Play,\r\n  type LucideIcon,\r\n  ChevronRight,\r\n  ChevronLeft,\r\n  Pause,\r\n  Fullscreen,\r\n  Minimize,\r\n} from \"lucide-react\";\r\nimport \"@xyflow/react/dist/style.css\";\r\nimport { useCallback, useRef, useState } from \"react\";\r\n\r\nimport { Tooltip } from \"~/components/deer-flow/tooltip\";\r\nimport { ShineBorder } from \"~/components/magicui/shine-border\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport { Slider } from \"~/components/ui/slider\";\r\nimport { useIntersectionObserver } from \"~/hooks/use-intersection-observer\";\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nimport { playbook, type GraphNode } from \"../store\";\r\nimport {\r\n  activateStep,\r\n  nextStep,\r\n  play,\r\n  prevStep,\r\n  togglePlay,\r\n  useMAVStore,\r\n} from \"../store/mav-store\";\r\n\r\nconst nodeTypes = {\r\n  circle: CircleNode,\r\n  agent: AgentNode,\r\n  default: AgentNode,\r\n};\r\n\r\nexport function MultiAgentVisualization({ className }: { className?: string }) {\r\n  const {\r\n    graph: { nodes, edges },\r\n    activeStepIndex,\r\n    playing,\r\n  } = useMAVStore((state) => state);\r\n  const flowRef = useRef<ReactFlowInstance<GraphNode, Edge>>(null);\r\n  const containerRef = useRef<HTMLDivElement>(null);\r\n  const [hasPlayed, setHasPlayed] = useState(false);\r\n  const [fullscreen, setFullscreen] = useState(false);\r\n  const { ref: anchorRef } = useIntersectionObserver({\r\n    rootMargin: \"0%\",\r\n    threshold: 0,\r\n    onChange: (isIntersecting) => {\r\n      if (isIntersecting && !playing && !hasPlayed) {\r\n        setHasPlayed(true);\r\n        void play();\r\n      }\r\n    },\r\n  });\r\n  const toggleFullscreen = useCallback(async () => {\r\n    if (containerRef.current) {\r\n      if (!document.fullscreenElement) {\r\n        setFullscreen(true);\r\n        await containerRef.current.requestFullscreen();\r\n        setTimeout(() => {\r\n          void flowRef.current?.fitView({ maxZoom: 2.5 });\r\n        }, 100);\r\n      } else {\r\n        setFullscreen(false);\r\n        await document.exitFullscreen();\r\n        setTimeout(() => {\r\n          void flowRef.current?.fitView();\r\n        }, 100);\r\n      }\r\n    }\r\n  }, []);\r\n  return (\r\n    <div\r\n      ref={containerRef}\r\n      className={cn(\"flex h-full w-full flex-col pb-4\", className)}\r\n    >\r\n      <ReactFlow\r\n        className={cn(\"flex min-h-0 flex-grow\")}\r\n        style={{\r\n          [\"--xy-background-color-default\" as string]: \"transparent\",\r\n        }}\r\n        nodes={nodes}\r\n        edges={edges}\r\n        nodeTypes={nodeTypes}\r\n        fitView\r\n        proOptions={{ hideAttribution: true }}\r\n        colorMode=\"dark\"\r\n        panOnScroll={false}\r\n        zoomOnScroll={false}\r\n        preventScrolling={false}\r\n        panOnDrag={false}\r\n        onInit={(instance) => {\r\n          flowRef.current = instance;\r\n        }}\r\n      >\r\n        <Background\r\n          className=\"[mask-image:radial-gradient(800px_circle_at_center,white,transparent)]\"\r\n          bgColor=\"var(--background)\"\r\n        />\r\n        <div\r\n          ref={anchorRef}\r\n          id=\"auto-run-animation-trigger\"\r\n          className=\"absolute bottom-0 left-[50%] h-px w-px\"\r\n        />\r\n      </ReactFlow>\r\n      <div className=\"h-4 shrink-0\"></div>\r\n      <div className=\"flex h-6 w-full shrink-0 items-center justify-center\">\r\n        <div className=\"bg-muted/50 z-[200] flex rounded-3xl px-4 py-2\">\r\n          <Tooltip title=\"Move to the previous step\">\r\n            <Button variant=\"ghost\" onClick={prevStep}>\r\n              <ChevronLeft className=\"size-5\" />\r\n            </Button>\r\n          </Tooltip>\r\n          <Tooltip title=\"Play / Pause\">\r\n            <Button variant=\"ghost\" onClick={togglePlay}>\r\n              {playing ? (\r\n                <Pause className=\"size-5\" />\r\n              ) : (\r\n                <Play className=\"size-5\" />\r\n              )}\r\n            </Button>\r\n          </Tooltip>\r\n          <Tooltip title=\"Move to the next step\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              onClick={() => {\r\n                setHasPlayed(true);\r\n                nextStep();\r\n              }}\r\n            >\r\n              <ChevronRight className=\"size-5\" />\r\n            </Button>\r\n          </Tooltip>\r\n          <div className=\"text-muted-foreground ml-2 flex items-center justify-center\">\r\n            <Slider\r\n              className=\"w-40 sm:w-80 md:w-100 lg:w-120\"\r\n              max={playbook.steps.length - 1}\r\n              min={0}\r\n              step={1}\r\n              value={[activeStepIndex >= 0 ? activeStepIndex : 0]}\r\n              onValueChange={([value]) => {\r\n                setHasPlayed(true);\r\n                activateStep(value!);\r\n              }}\r\n            />\r\n          </div>\r\n          <Tooltip title=\"Toggle fullscreen\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              onClick={() => {\r\n                setHasPlayed(true);\r\n                void toggleFullscreen();\r\n              }}\r\n            >\r\n              {fullscreen ? (\r\n                <Minimize className=\"size-5\" />\r\n              ) : (\r\n                <Fullscreen className=\"size-5\" />\r\n              )}\r\n            </Button>\r\n          </Tooltip>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction CircleNode({ data }: { data: { label: string; active: boolean } }) {\r\n  return (\r\n    <>\r\n      {data.active && (\r\n        <ShineBorder\r\n          className=\"rounded-full\"\r\n          shineColor={[\"#A07CFE\", \"#FE8FB5\", \"#FFBE7B\"]}\r\n        />\r\n      )}\r\n      <div className=\"flex h-10 w-10 items-center justify-center rounded-full border bg-[var(--xy-node-background-color-default)]\">\r\n        <p className=\"text-xs\">{data.label}</p>\r\n      </div>\r\n      {data.label === \"Start\" && (\r\n        <Handle\r\n          className=\"invisible\"\r\n          type=\"source\"\r\n          position={Position.Right}\r\n          id=\"right\"\r\n        />\r\n      )}\r\n      {data.label === \"End\" && (\r\n        <Handle\r\n          className=\"invisible\"\r\n          type=\"target\"\r\n          position={Position.Top}\r\n          id=\"top\"\r\n        />\r\n      )}\r\n    </>\r\n  );\r\n}\r\n\r\nfunction AgentNode({\r\n  data,\r\n  id,\r\n}: {\r\n  data: {\r\n    icon?: LucideIcon;\r\n    label: string;\r\n    active: boolean;\r\n    stepDescription?: string;\r\n    stepTooltipPosition?: \"left\" | \"right\" | \"top\" | \"bottom\";\r\n  };\r\n  id: string;\r\n}) {\r\n  return (\r\n    <>\r\n      {data.active && (\r\n        <ShineBorder\r\n          shineColor={[\"#A07CFE\", \"#FE8FB5\", \"#FFBE7B\"]}\r\n          className=\"rounded-[2px]\"\r\n        />\r\n      )}\r\n      <Tooltip\r\n        className=\"max-w-50 text-[15px] font-light opacity-70\"\r\n        style={{\r\n          [\"--primary\" as string]: \"#333\",\r\n          [\"--primary-foreground\" as string]: \"white\",\r\n        }}\r\n        open={data.active && !!data.stepDescription}\r\n        title={data.stepDescription}\r\n        side={data.stepTooltipPosition}\r\n        sideOffset={20}\r\n      >\r\n        <div\r\n          id={id}\r\n          className=\"relative flex w-full items-center justify-center text-xs\"\r\n        >\r\n          <div className=\"flex items-center gap-2\">\r\n            {data.icon && <data.icon className=\"h-[1rem] w-[1rem]\" />}\r\n            <span>{data.label}</span>\r\n          </div>\r\n        </div>\r\n      </Tooltip>\r\n      <Handle\r\n        className=\"invisible\"\r\n        type=\"source\"\r\n        position={Position.Left}\r\n        id=\"left\"\r\n      />\r\n      <Handle\r\n        className=\"invisible\"\r\n        type=\"source\"\r\n        position={Position.Right}\r\n        id=\"right\"\r\n      />\r\n      <Handle\r\n        className=\"invisible\"\r\n        type=\"source\"\r\n        position={Position.Top}\r\n        id=\"top\"\r\n      />\r\n      <Handle\r\n        className=\"invisible\"\r\n        type=\"source\"\r\n        position={Position.Bottom}\r\n        id=\"bottom\"\r\n      />\r\n      <Handle\r\n        className=\"invisible\"\r\n        type=\"target\"\r\n        position={Position.Left}\r\n        id=\"left\"\r\n      />\r\n      <Handle\r\n        className=\"invisible\"\r\n        type=\"target\"\r\n        position={Position.Right}\r\n        id=\"right\"\r\n      />\r\n      <Handle\r\n        className=\"invisible\"\r\n        type=\"target\"\r\n        position={Position.Top}\r\n        id=\"top\"\r\n      />\r\n      <Handle\r\n        className=\"invisible\"\r\n        type=\"target\"\r\n        position={Position.Bottom}\r\n        id=\"bottom\"\r\n      />\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAI/B;AAAA;AAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AACA;AA9BA;;;;;;;;;;;;;;AAuCA,MAAM,YAAY;IAChB,QAAQ;IACR,OAAO;IACP,SAAS;AACX;AAEO,SAAS,wBAAwB,EAAE,SAAS,EAA0B;IAC3E,MAAM,EACJ,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EACvB,eAAe,EACf,OAAO,EACR,GAAG,CAAA,GAAA,8IAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAU;IAC3B,MAAM,UAAU,CAAA,GAAA,8SAAA,CAAA,SAAM,AAAD,EAAsC;IAC3D,MAAM,eAAe,CAAA,GAAA,8SAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,8SAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,EAAE,KAAK,SAAS,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,0BAAuB,AAAD,EAAE;QACjD,YAAY;QACZ,WAAW;QACX,UAAU,CAAC;YACT,IAAI,kBAAkB,CAAC,WAAW,CAAC,WAAW;gBAC5C,aAAa;gBACb,KAAK,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD;YACV;QACF;IACF;IACA,MAAM,mBAAmB,CAAA,GAAA,8SAAA,CAAA,cAAW,AAAD,EAAE;QACnC,IAAI,aAAa,OAAO,EAAE;YACxB,IAAI,CAAC,SAAS,iBAAiB,EAAE;gBAC/B,cAAc;gBACd,MAAM,aAAa,OAAO,CAAC,iBAAiB;gBAC5C,WAAW;oBACT,KAAK,QAAQ,OAAO,EAAE,QAAQ;wBAAE,SAAS;oBAAI;gBAC/C,GAAG;YACL,OAAO;gBACL,cAAc;gBACd,MAAM,SAAS,cAAc;gBAC7B,WAAW;oBACT,KAAK,QAAQ,OAAO,EAAE;gBACxB,GAAG;YACL;QACF;IACF,GAAG,EAAE;IACL,qBACE,uVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;;0BAElD,uVAAC,mRAAA,CAAA,YAAS;gBACR,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;gBACd,OAAO;oBACL,CAAC,gCAA0C,EAAE;gBAC/C;gBACA,OAAO;gBACP,OAAO;gBACP,WAAW;gBACX,OAAO;gBACP,YAAY;oBAAE,iBAAiB;gBAAK;gBACpC,WAAU;gBACV,aAAa;gBACb,cAAc;gBACd,kBAAkB;gBAClB,WAAW;gBACX,QAAQ,CAAC;oBACP,QAAQ,OAAO,GAAG;gBACpB;;kCAEA,uVAAC,mRAAA,CAAA,aAAU;wBACT,WAAU;wBACV,SAAQ;;;;;;kCAEV,uVAAC;wBACC,KAAK;wBACL,IAAG;wBACH,WAAU;;;;;;;;;;;;0BAGd,uVAAC;gBAAI,WAAU;;;;;;0BACf,uVAAC;gBAAI,WAAU;0BACb,cAAA,uVAAC;oBAAI,WAAU;;sCACb,uVAAC,6IAAA,CAAA,UAAO;4BAAC,OAAM;sCACb,cAAA,uVAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,SAAS,8IAAA,CAAA,WAAQ;0CACvC,cAAA,uVAAC,wSAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAG3B,uVAAC,6IAAA,CAAA,UAAO;4BAAC,OAAM;sCACb,cAAA,uVAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,SAAS,8IAAA,CAAA,aAAU;0CACxC,wBACC,uVAAC,wRAAA,CAAA,QAAK;oCAAC,WAAU;;;;;yDAEjB,uVAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAItB,uVAAC,6IAAA,CAAA,UAAO;4BAAC,OAAM;sCACb,cAAA,uVAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;oCACP,aAAa;oCACb,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD;gCACT;0CAEA,cAAA,uVAAC,0SAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAG5B,uVAAC;4BAAI,WAAU;sCACb,cAAA,uVAAC,kIAAA,CAAA,SAAM;gCACL,WAAU;gCACV,KAAK,0IAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,MAAM,GAAG;gCAC7B,KAAK;gCACL,MAAM;gCACN,OAAO;oCAAC,mBAAmB,IAAI,kBAAkB;iCAAE;gCACnD,eAAe,CAAC,CAAC,MAAM;oCACrB,aAAa;oCACb,CAAA,GAAA,8IAAA,CAAA,eAAY,AAAD,EAAE;gCACf;;;;;;;;;;;sCAGJ,uVAAC,6IAAA,CAAA,UAAO;4BAAC,OAAM;sCACb,cAAA,uVAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;oCACP,aAAa;oCACb,KAAK;gCACP;0CAEC,2BACC,uVAAC,8RAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;yDAEpB,uVAAC,kSAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtC;AAEA,SAAS,WAAW,EAAE,IAAI,EAAgD;IACxE,qBACE;;YACG,KAAK,MAAM,kBACV,uVAAC,gJAAA,CAAA,cAAW;gBACV,WAAU;gBACV,YAAY;oBAAC;oBAAW;oBAAW;iBAAU;;;;;;0BAGjD,uVAAC;gBAAI,WAAU;0BACb,cAAA,uVAAC;oBAAE,WAAU;8BAAW,KAAK,KAAK;;;;;;;;;;;YAEnC,KAAK,KAAK,KAAK,yBACd,uVAAC,mRAAA,CAAA,SAAM;gBACL,WAAU;gBACV,MAAK;gBACL,UAAU,0NAAA,CAAA,WAAQ,CAAC,KAAK;gBACxB,IAAG;;;;;;YAGN,KAAK,KAAK,KAAK,uBACd,uVAAC,mRAAA,CAAA,SAAM;gBACL,WAAU;gBACV,MAAK;gBACL,UAAU,0NAAA,CAAA,WAAQ,CAAC,GAAG;gBACtB,IAAG;;;;;;;;AAKb;AAEA,SAAS,UAAU,EACjB,IAAI,EACJ,EAAE,EAUH;IACC,qBACE;;YACG,KAAK,MAAM,kBACV,uVAAC,gJAAA,CAAA,cAAW;gBACV,YAAY;oBAAC;oBAAW;oBAAW;iBAAU;gBAC7C,WAAU;;;;;;0BAGd,uVAAC,6IAAA,CAAA,UAAO;gBACN,WAAU;gBACV,OAAO;oBACL,CAAC,YAAsB,EAAE;oBACzB,CAAC,uBAAiC,EAAE;gBACtC;gBACA,MAAM,KAAK,MAAM,IAAI,CAAC,CAAC,KAAK,eAAe;gBAC3C,OAAO,KAAK,eAAe;gBAC3B,MAAM,KAAK,mBAAmB;gBAC9B,YAAY;0BAEZ,cAAA,uVAAC;oBACC,IAAI;oBACJ,WAAU;8BAEV,cAAA,uVAAC;wBAAI,WAAU;;4BACZ,KAAK,IAAI,kBAAI,uVAAC,KAAK,IAAI;gCAAC,WAAU;;;;;;0CACnC,uVAAC;0CAAM,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;0BAIvB,uVAAC,mRAAA,CAAA,SAAM;gBACL,WAAU;gBACV,MAAK;gBACL,UAAU,0NAAA,CAAA,WAAQ,CAAC,IAAI;gBACvB,IAAG;;;;;;0BAEL,uVAAC,mRAAA,CAAA,SAAM;gBACL,WAAU;gBACV,MAAK;gBACL,UAAU,0NAAA,CAAA,WAAQ,CAAC,KAAK;gBACxB,IAAG;;;;;;0BAEL,uVAAC,mRAAA,CAAA,SAAM;gBACL,WAAU;gBACV,MAAK;gBACL,UAAU,0NAAA,CAAA,WAAQ,CAAC,GAAG;gBACtB,IAAG;;;;;;0BAEL,uVAAC,mRAAA,CAAA,SAAM;gBACL,WAAU;gBACV,MAAK;gBACL,UAAU,0NAAA,CAAA,WAAQ,CAAC,MAAM;gBACzB,IAAG;;;;;;0BAEL,uVAAC,mRAAA,CAAA,SAAM;gBACL,WAAU;gBACV,MAAK;gBACL,UAAU,0NAAA,CAAA,WAAQ,CAAC,IAAI;gBACvB,IAAG;;;;;;0BAEL,uVAAC,mRAAA,CAAA,SAAM;gBACL,WAAU;gBACV,MAAK;gBACL,UAAU,0NAAA,CAAA,WAAQ,CAAC,KAAK;gBACxB,IAAG;;;;;;0BAEL,uVAAC,mRAAA,CAAA,SAAM;gBACL,WAAU;gBACV,MAAK;gBACL,UAAU,0NAAA,CAAA,WAAQ,CAAC,GAAG;gBACtB,IAAG;;;;;;0BAEL,uVAAC,mRAAA,CAAA,SAAM;gBACL,WAAU;gBACV,MAAK;gBACL,UAAU,0NAAA,CAAA,WAAQ,CAAC,MAAM;gBACzB,IAAG;;;;;;;;AAIX", "debugId": null}}]}