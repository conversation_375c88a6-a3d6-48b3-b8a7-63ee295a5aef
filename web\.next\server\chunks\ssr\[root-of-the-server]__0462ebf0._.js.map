{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_31c89cc1.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_31c89cc1-module__hy39Qq__className\",\n  \"variable\": \"geist_31c89cc1-module__hy39Qq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_31c89cc1.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-geist-sans%22}],%22variableName%22:%22geist%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/components/deer-flow/theme-provider-wrapper.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeProviderWrapper = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProviderWrapper() from the server but ThemeProviderWrapper is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/deer-flow/theme-provider-wrapper.tsx <module evaluation>\",\n    \"ThemeProviderWrapper\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,uBAAuB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,qFACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/components/deer-flow/theme-provider-wrapper.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ThemeProviderWrapper = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProviderWrapper() from the server but ThemeProviderWrapper is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/deer-flow/theme-provider-wrapper.tsx\",\n    \"ThemeProviderWrapper\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,uBAAuB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,iEACA", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/env.js"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { createEnv } from \"@t3-oss/env-nextjs\";\r\nimport { z } from \"zod\";\r\n\r\nexport const env = createEnv({\r\n  /**\r\n   * Specify your server-side environment variables schema here. This way you can ensure the app\r\n   * isn't built with invalid env vars.\r\n   */\r\n  server: {\r\n    NODE_ENV: z.enum([\"development\", \"test\", \"production\"]),\r\n    AMPLITUDE_API_KEY: z.string().optional(),\r\n    GITHUB_OAUTH_TOKEN: z.string().optional(),\r\n  },\r\n\r\n  /**\r\n   * Specify your client-side environment variables schema here. This way you can ensure the app\r\n   * isn't built with invalid env vars. To expose them to the client, prefix them with\r\n   * `NEXT_PUBLIC_`.\r\n   */\r\n  client: {\r\n    NEXT_PUBLIC_API_URL: z.string().optional(),\r\n    NEXT_PUBLIC_STATIC_WEBSITE_ONLY: z.boolean().optional(),\r\n  },\r\n\r\n  /**\r\n   * You can't destruct `process.env` as a regular object in the Next.js edge runtimes (e.g.\r\n   * middlewares) or client-side so we need to destruct manually.\r\n   */\r\n  runtimeEnv: {\r\n    NODE_ENV: process.env.NODE_ENV,\r\n    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,\r\n    NEXT_PUBLIC_STATIC_WEBSITE_ONLY:\r\n      process.env.NEXT_PUBLIC_STATIC_WEBSITE_ONLY === \"true\",\r\n    AMPLITUDE_API_KEY: process.env.AMPLITUDE_API_KEY,\r\n    GITHUB_OAUTH_TOKEN: process.env.GITHUB_OAUTH_TOKEN,\r\n  },\r\n  /**\r\n   * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially\r\n   * useful for Docker builds.\r\n   */\r\n  skipValidation: !!process.env.SKIP_ENV_VALIDATION,\r\n  /**\r\n   * Makes it so that empty strings are treated as undefined. `SOME_VAR: z.string()` and\r\n   * `SOME_VAR=''` will throw an error.\r\n   */\r\n  emptyStringAsUndefined: true,\r\n});\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AAE/B;AACA;;;AAEO,MAAM,MAAM,CAAA,GAAA,qRAAA,CAAA,YAAS,AAAD,EAAE;IAC3B;;;GAGC,GACD,QAAQ;QACN,UAAU,qLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;YAAC;YAAe;YAAQ;SAAa;QACtD,mBAAmB,qLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACtC,oBAAoB,qLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACzC;IAEA;;;;GAIC,GACD,QAAQ;QACN,qBAAqB,qLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACxC,iCAAiC,qLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;IACvD;IAEA;;;GAGC,GACD,YAAY;QACV,QAAQ;QACR,qBAAqB,QAAQ,GAAG,CAAC,mBAAmB;QACpD,iCACE,QAAQ,GAAG,CAAC,+BAA+B,KAAK;QAClD,mBAAmB,QAAQ,GAAG,CAAC,iBAAiB;QAChD,oBAAoB,QAAQ,GAAG,CAAC,kBAAkB;IACpD;IACA;;;GAGC,GACD,gBAAgB,CAAC,CAAC,QAAQ,GAAG,CAAC,mBAAmB;IACjD;;;GAGC,GACD,wBAAwB;AAC1B", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/components/deer-flow/toaster.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/deer-flow/toaster.tsx <module evaluation>\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,sEACA", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/components/deer-flow/toaster.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/deer-flow/toaster.tsx\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,kDACA", "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/app/layout.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport \"~/styles/globals.css\";\r\n\r\nimport { type Metadata } from \"next\";\r\nimport { <PERSON><PERSON><PERSON> } from \"next/font/google\";\r\nimport Script from \"next/script\";\r\n\r\nimport { ThemeProviderWrapper } from \"~/components/deer-flow/theme-provider-wrapper\";\r\nimport { env } from \"~/env\";\r\n\r\nimport { Toaster } from \"../components/deer-flow/toaster\";\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"🦌 DeerFlow\",\r\n  description:\r\n    \"Deep Exploration and Efficient Research, an AI tool that combines language models with specialized tools for research tasks.\",\r\n  icons: [{ rel: \"icon\", url: \"/favicon.ico\" }],\r\n};\r\n\r\nconst geist = Geist({\r\n  subsets: [\"latin\"],\r\n  variable: \"--font-geist-sans\",\r\n});\r\n\r\nexport default function RootLayout({\r\n  children,\r\n}: Readonly<{ children: React.ReactNode }>) {\r\n  return (\r\n    <html lang=\"en\" className={`${geist.variable}`} suppressHydrationWarning>\r\n      <head>\r\n        {/* Define isSpace function globally to fix markdown-it issues with Next.js + Turbopack\r\n          https://github.com/markdown-it/markdown-it/issues/1082#issuecomment-********** */}\r\n        <Script id=\"markdown-it-fix\" strategy=\"beforeInteractive\">\r\n          {`\r\n            if (typeof window !== 'undefined' && typeof window.isSpace === 'undefined') {\r\n              window.isSpace = function(code) {\r\n                return code === 0x20 || code === 0x09 || code === 0x0A || code === 0x0B || code === 0x0C || code === 0x0D;\r\n              };\r\n            }\r\n          `}\r\n        </Script>\r\n      </head>\r\n      <body className=\"bg-app\">\r\n        <ThemeProviderWrapper>{children}</ThemeProviderWrapper>\r\n        <Toaster />\r\n        {\r\n          // NO USER BEHAVIOR TRACKING OR PRIVATE DATA COLLECTION BY DEFAULT\r\n          //\r\n          // When `NEXT_PUBLIC_STATIC_WEBSITE_ONLY` is `true`, the script will be injected\r\n          // into the page only when `AMPLITUDE_API_KEY` is provided in `.env`\r\n        }\r\n        {env.NEXT_PUBLIC_STATIC_WEBSITE_ONLY && env.AMPLITUDE_API_KEY && (\r\n          <>\r\n            <Script src=\"https://cdn.amplitude.com/script/d2197dd1df3f2959f26295bb0e7e849f.js\"></Script>\r\n            <Script id=\"amplitude-init\" strategy=\"lazyOnload\">\r\n              {`window.amplitude.init('${env.AMPLITUDE_API_KEY}', {\"fetchRemoteConfig\":true,\"autocapture\":true});`}\r\n            </Script>\r\n          </>\r\n        )}\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;;;AAM/B;AAEA;AACA;AAEA;;;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aACE;IACF,OAAO;QAAC;YAAE,KAAK;YAAQ,KAAK;QAAe;KAAE;AAC/C;AAOe,SAAS,WAAW,EACjC,QAAQ,EACgC;IACxC,qBACE,uVAAC;QAAK,MAAK;QAAK,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,QAAQ,EAAE;QAAE,wBAAwB;;0BACtE,uVAAC;0BAGC,cAAA,uVAAC,uOAAA,CAAA,UAAM;oBAAC,IAAG;oBAAkB,UAAS;8BACnC,CAAC;;;;;;UAMF,CAAC;;;;;;;;;;;0BAGL,uVAAC;gBAAK,WAAU;;kCACd,uVAAC,kKAAA,CAAA,uBAAoB;kCAAE;;;;;;kCACvB,uVAAC,6IAAA,CAAA,UAAO;;;;;oBAOP,0GAAA,CAAA,MAAG,CAAC,+BAA+B,IAAI,0GAAA,CAAA,MAAG,CAAC,iBAAiB,kBAC3D;;0CACE,uVAAC,uOAAA,CAAA,UAAM;gCAAC,KAAI;;;;;;0CACZ,uVAAC,uOAAA,CAAA,UAAM;gCAAC,IAAG;gCAAiB,UAAS;0CAClC,CAAC,uBAAuB,EAAE,0GAAA,CAAA,MAAG,CAAC,iBAAiB,CAAC,kDAAkD,CAAC;;;;;;;;;;;;;;;;;;;;AAOlH", "debugId": null}}]}