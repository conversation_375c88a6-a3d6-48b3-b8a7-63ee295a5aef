{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/arduino.js"], "sourcesContent": ["/*\nLanguage: C++\nCategory: common, system\nWebsite: https://isocpp.org\n*/\n\n/** @type LanguageFn */\nfunction cPlusPlus(hljs) {\n  const regex = hljs.regex;\n  // added for historic reasons because `hljs.C_LINE_COMMENT_MODE` does\n  // not include such support nor can we be sure all the grammars depending\n  // on it would desire this behavior\n  const C_LINE_COMMENT_MODE = hljs.COMMENT('//', '$', { contains: [ { begin: /\\\\\\n/ } ] });\n  const DECLTYPE_AUTO_RE = 'decltype\\\\(auto\\\\)';\n  const NAMESPACE_RE = '[a-zA-Z_]\\\\w*::';\n  const TEMPLATE_ARGUMENT_RE = '<[^<>]+>';\n  const FUNCTION_TYPE_RE = '(?!struct)('\n    + DECLTYPE_AUTO_RE + '|'\n    + regex.optional(NAMESPACE_RE)\n    + '[a-zA-Z_]\\\\w*' + regex.optional(TEMPLATE_ARGUMENT_RE)\n  + ')';\n\n  const CPP_PRIMITIVE_TYPES = {\n    className: 'type',\n    begin: '\\\\b[a-z\\\\d_]*_t\\\\b'\n  };\n\n  // https://en.cppreference.com/w/cpp/language/escape\n  // \\\\ \\x \\xFF \\u2837 \\u00323747 \\374\n  const CHARACTER_ESCAPES = '\\\\\\\\(x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4,8}|[0-7]{3}|\\\\S)';\n  const STRINGS = {\n    className: 'string',\n    variants: [\n      {\n        begin: '(u8?|U|L)?\"',\n        end: '\"',\n        illegal: '\\\\n',\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      },\n      {\n        begin: '(u8?|U|L)?\\'(' + CHARACTER_ESCAPES + '|.)',\n        end: '\\'',\n        illegal: '.'\n      },\n      hljs.END_SAME_AS_BEGIN({\n        begin: /(?:u8?|U|L)?R\"([^()\\\\ ]{0,16})\\(/,\n        end: /\\)([^()\\\\ ]{0,16})\"/\n      })\n    ]\n  };\n\n  const NUMBERS = {\n    className: 'number',\n    variants: [\n      // Floating-point literal.\n      { begin:\n        \"[+-]?(?:\" // Leading sign.\n          // Decimal.\n          + \"(?:\"\n            +\"[0-9](?:'?[0-9])*\\\\.(?:[0-9](?:'?[0-9])*)?\"\n            + \"|\\\\.[0-9](?:'?[0-9])*\"\n          + \")(?:[Ee][+-]?[0-9](?:'?[0-9])*)?\"\n          + \"|[0-9](?:'?[0-9])*[Ee][+-]?[0-9](?:'?[0-9])*\"\n          // Hexadecimal.\n          + \"|0[Xx](?:\"\n            +\"[0-9A-Fa-f](?:'?[0-9A-Fa-f])*(?:\\\\.(?:[0-9A-Fa-f](?:'?[0-9A-Fa-f])*)?)?\"\n            + \"|\\\\.[0-9A-Fa-f](?:'?[0-9A-Fa-f])*\"\n          + \")[Pp][+-]?[0-9](?:'?[0-9])*\"\n        + \")(?:\" // Literal suffixes.\n          + \"[Ff](?:16|32|64|128)?\"\n          + \"|(BF|bf)16\"\n          + \"|[Ll]\"\n          + \"|\" // Literal suffix is optional.\n        + \")\"\n      },\n      // Integer literal.\n      { begin:\n        \"[+-]?\\\\b(?:\" // Leading sign.\n          + \"0[Bb][01](?:'?[01])*\" // Binary.\n          + \"|0[Xx][0-9A-Fa-f](?:'?[0-9A-Fa-f])*\" // Hexadecimal.\n          + \"|0(?:'?[0-7])*\" // Octal or just a lone zero.\n          + \"|[1-9](?:'?[0-9])*\" // Decimal.\n        + \")(?:\" // Literal suffixes.\n          + \"[Uu](?:LL?|ll?)\"\n          + \"|[Uu][Zz]?\"\n          + \"|(?:LL?|ll?)[Uu]?\"\n          + \"|[Zz][Uu]\"\n          + \"|\" // Literal suffix is optional.\n        + \")\"\n        // Note: there are user-defined literal suffixes too, but perhaps having the custom suffix not part of the\n        // literal highlight actually makes it stand out more.\n      }\n    ],\n    relevance: 0\n  };\n\n  const PREPROCESSOR = {\n    className: 'meta',\n    begin: /#\\s*[a-z]+\\b/,\n    end: /$/,\n    keywords: { keyword:\n        'if else elif endif define undef warning error line '\n        + 'pragma _Pragma ifdef ifndef include' },\n    contains: [\n      {\n        begin: /\\\\\\n/,\n        relevance: 0\n      },\n      hljs.inherit(STRINGS, { className: 'string' }),\n      {\n        className: 'string',\n        begin: /<.*?>/\n      },\n      C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE\n    ]\n  };\n\n  const TITLE_MODE = {\n    className: 'title',\n    begin: regex.optional(NAMESPACE_RE) + hljs.IDENT_RE,\n    relevance: 0\n  };\n\n  const FUNCTION_TITLE = regex.optional(NAMESPACE_RE) + hljs.IDENT_RE + '\\\\s*\\\\(';\n\n  // https://en.cppreference.com/w/cpp/keyword\n  const RESERVED_KEYWORDS = [\n    'alignas',\n    'alignof',\n    'and',\n    'and_eq',\n    'asm',\n    'atomic_cancel',\n    'atomic_commit',\n    'atomic_noexcept',\n    'auto',\n    'bitand',\n    'bitor',\n    'break',\n    'case',\n    'catch',\n    'class',\n    'co_await',\n    'co_return',\n    'co_yield',\n    'compl',\n    'concept',\n    'const_cast|10',\n    'consteval',\n    'constexpr',\n    'constinit',\n    'continue',\n    'decltype',\n    'default',\n    'delete',\n    'do',\n    'dynamic_cast|10',\n    'else',\n    'enum',\n    'explicit',\n    'export',\n    'extern',\n    'false',\n    'final',\n    'for',\n    'friend',\n    'goto',\n    'if',\n    'import',\n    'inline',\n    'module',\n    'mutable',\n    'namespace',\n    'new',\n    'noexcept',\n    'not',\n    'not_eq',\n    'nullptr',\n    'operator',\n    'or',\n    'or_eq',\n    'override',\n    'private',\n    'protected',\n    'public',\n    'reflexpr',\n    'register',\n    'reinterpret_cast|10',\n    'requires',\n    'return',\n    'sizeof',\n    'static_assert',\n    'static_cast|10',\n    'struct',\n    'switch',\n    'synchronized',\n    'template',\n    'this',\n    'thread_local',\n    'throw',\n    'transaction_safe',\n    'transaction_safe_dynamic',\n    'true',\n    'try',\n    'typedef',\n    'typeid',\n    'typename',\n    'union',\n    'using',\n    'virtual',\n    'volatile',\n    'while',\n    'xor',\n    'xor_eq'\n  ];\n\n  // https://en.cppreference.com/w/cpp/keyword\n  const RESERVED_TYPES = [\n    'bool',\n    'char',\n    'char16_t',\n    'char32_t',\n    'char8_t',\n    'double',\n    'float',\n    'int',\n    'long',\n    'short',\n    'void',\n    'wchar_t',\n    'unsigned',\n    'signed',\n    'const',\n    'static'\n  ];\n\n  const TYPE_HINTS = [\n    'any',\n    'auto_ptr',\n    'barrier',\n    'binary_semaphore',\n    'bitset',\n    'complex',\n    'condition_variable',\n    'condition_variable_any',\n    'counting_semaphore',\n    'deque',\n    'false_type',\n    'flat_map',\n    'flat_set',\n    'future',\n    'imaginary',\n    'initializer_list',\n    'istringstream',\n    'jthread',\n    'latch',\n    'lock_guard',\n    'multimap',\n    'multiset',\n    'mutex',\n    'optional',\n    'ostringstream',\n    'packaged_task',\n    'pair',\n    'promise',\n    'priority_queue',\n    'queue',\n    'recursive_mutex',\n    'recursive_timed_mutex',\n    'scoped_lock',\n    'set',\n    'shared_future',\n    'shared_lock',\n    'shared_mutex',\n    'shared_timed_mutex',\n    'shared_ptr',\n    'stack',\n    'string_view',\n    'stringstream',\n    'timed_mutex',\n    'thread',\n    'true_type',\n    'tuple',\n    'unique_lock',\n    'unique_ptr',\n    'unordered_map',\n    'unordered_multimap',\n    'unordered_multiset',\n    'unordered_set',\n    'variant',\n    'vector',\n    'weak_ptr',\n    'wstring',\n    'wstring_view'\n  ];\n\n  const FUNCTION_HINTS = [\n    'abort',\n    'abs',\n    'acos',\n    'apply',\n    'as_const',\n    'asin',\n    'atan',\n    'atan2',\n    'calloc',\n    'ceil',\n    'cerr',\n    'cin',\n    'clog',\n    'cos',\n    'cosh',\n    'cout',\n    'declval',\n    'endl',\n    'exchange',\n    'exit',\n    'exp',\n    'fabs',\n    'floor',\n    'fmod',\n    'forward',\n    'fprintf',\n    'fputs',\n    'free',\n    'frexp',\n    'fscanf',\n    'future',\n    'invoke',\n    'isalnum',\n    'isalpha',\n    'iscntrl',\n    'isdigit',\n    'isgraph',\n    'islower',\n    'isprint',\n    'ispunct',\n    'isspace',\n    'isupper',\n    'isxdigit',\n    'labs',\n    'launder',\n    'ldexp',\n    'log',\n    'log10',\n    'make_pair',\n    'make_shared',\n    'make_shared_for_overwrite',\n    'make_tuple',\n    'make_unique',\n    'malloc',\n    'memchr',\n    'memcmp',\n    'memcpy',\n    'memset',\n    'modf',\n    'move',\n    'pow',\n    'printf',\n    'putchar',\n    'puts',\n    'realloc',\n    'scanf',\n    'sin',\n    'sinh',\n    'snprintf',\n    'sprintf',\n    'sqrt',\n    'sscanf',\n    'std',\n    'stderr',\n    'stdin',\n    'stdout',\n    'strcat',\n    'strchr',\n    'strcmp',\n    'strcpy',\n    'strcspn',\n    'strlen',\n    'strncat',\n    'strncmp',\n    'strncpy',\n    'strpbrk',\n    'strrchr',\n    'strspn',\n    'strstr',\n    'swap',\n    'tan',\n    'tanh',\n    'terminate',\n    'to_underlying',\n    'tolower',\n    'toupper',\n    'vfprintf',\n    'visit',\n    'vprintf',\n    'vsprintf'\n  ];\n\n  const LITERALS = [\n    'NULL',\n    'false',\n    'nullopt',\n    'nullptr',\n    'true'\n  ];\n\n  // https://en.cppreference.com/w/cpp/keyword\n  const BUILT_IN = [ '_Pragma' ];\n\n  const CPP_KEYWORDS = {\n    type: RESERVED_TYPES,\n    keyword: RESERVED_KEYWORDS,\n    literal: LITERALS,\n    built_in: BUILT_IN,\n    _type_hints: TYPE_HINTS\n  };\n\n  const FUNCTION_DISPATCH = {\n    className: 'function.dispatch',\n    relevance: 0,\n    keywords: {\n      // Only for relevance, not highlighting.\n      _hint: FUNCTION_HINTS },\n    begin: regex.concat(\n      /\\b/,\n      /(?!decltype)/,\n      /(?!if)/,\n      /(?!for)/,\n      /(?!switch)/,\n      /(?!while)/,\n      hljs.IDENT_RE,\n      regex.lookahead(/(<[^<>]+>|)\\s*\\(/))\n  };\n\n  const EXPRESSION_CONTAINS = [\n    FUNCTION_DISPATCH,\n    PREPROCESSOR,\n    CPP_PRIMITIVE_TYPES,\n    C_LINE_COMMENT_MODE,\n    hljs.C_BLOCK_COMMENT_MODE,\n    NUMBERS,\n    STRINGS\n  ];\n\n  const EXPRESSION_CONTEXT = {\n    // This mode covers expression context where we can't expect a function\n    // definition and shouldn't highlight anything that looks like one:\n    // `return some()`, `else if()`, `(x*sum(1, 2))`\n    variants: [\n      {\n        begin: /=/,\n        end: /;/\n      },\n      {\n        begin: /\\(/,\n        end: /\\)/\n      },\n      {\n        beginKeywords: 'new throw return else',\n        end: /;/\n      }\n    ],\n    keywords: CPP_KEYWORDS,\n    contains: EXPRESSION_CONTAINS.concat([\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: CPP_KEYWORDS,\n        contains: EXPRESSION_CONTAINS.concat([ 'self' ]),\n        relevance: 0\n      }\n    ]),\n    relevance: 0\n  };\n\n  const FUNCTION_DECLARATION = {\n    className: 'function',\n    begin: '(' + FUNCTION_TYPE_RE + '[\\\\*&\\\\s]+)+' + FUNCTION_TITLE,\n    returnBegin: true,\n    end: /[{;=]/,\n    excludeEnd: true,\n    keywords: CPP_KEYWORDS,\n    illegal: /[^\\w\\s\\*&:<>.]/,\n    contains: [\n      { // to prevent it from being confused as the function title\n        begin: DECLTYPE_AUTO_RE,\n        keywords: CPP_KEYWORDS,\n        relevance: 0\n      },\n      {\n        begin: FUNCTION_TITLE,\n        returnBegin: true,\n        contains: [ TITLE_MODE ],\n        relevance: 0\n      },\n      // needed because we do not have look-behind on the below rule\n      // to prevent it from grabbing the final : in a :: pair\n      {\n        begin: /::/,\n        relevance: 0\n      },\n      // initializers\n      {\n        begin: /:/,\n        endsWithParent: true,\n        contains: [\n          STRINGS,\n          NUMBERS\n        ]\n      },\n      // allow for multiple declarations, e.g.:\n      // extern void f(int), g(char);\n      {\n        relevance: 0,\n        match: /,/\n      },\n      {\n        className: 'params',\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: CPP_KEYWORDS,\n        relevance: 0,\n        contains: [\n          C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE,\n          STRINGS,\n          NUMBERS,\n          CPP_PRIMITIVE_TYPES,\n          // Count matching parentheses.\n          {\n            begin: /\\(/,\n            end: /\\)/,\n            keywords: CPP_KEYWORDS,\n            relevance: 0,\n            contains: [\n              'self',\n              C_LINE_COMMENT_MODE,\n              hljs.C_BLOCK_COMMENT_MODE,\n              STRINGS,\n              NUMBERS,\n              CPP_PRIMITIVE_TYPES\n            ]\n          }\n        ]\n      },\n      CPP_PRIMITIVE_TYPES,\n      C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      PREPROCESSOR\n    ]\n  };\n\n  return {\n    name: 'C++',\n    aliases: [\n      'cc',\n      'c++',\n      'h++',\n      'hpp',\n      'hh',\n      'hxx',\n      'cxx'\n    ],\n    keywords: CPP_KEYWORDS,\n    illegal: '</',\n    classNameAliases: { 'function.dispatch': 'built_in' },\n    contains: [].concat(\n      EXPRESSION_CONTEXT,\n      FUNCTION_DECLARATION,\n      FUNCTION_DISPATCH,\n      EXPRESSION_CONTAINS,\n      [\n        PREPROCESSOR,\n        { // containers: ie, `vector <int> rooms (9);`\n          begin: '\\\\b(deque|list|queue|priority_queue|pair|stack|vector|map|set|bitset|multiset|multimap|unordered_map|unordered_set|unordered_multiset|unordered_multimap|array|tuple|optional|variant|function|flat_map|flat_set)\\\\s*<(?!<)',\n          end: '>',\n          keywords: CPP_KEYWORDS,\n          contains: [\n            'self',\n            CPP_PRIMITIVE_TYPES\n          ]\n        },\n        {\n          begin: hljs.IDENT_RE + '::',\n          keywords: CPP_KEYWORDS\n        },\n        {\n          match: [\n            // extra complexity to deal with `enum class` and `enum struct`\n            /\\b(?:enum(?:\\s+(?:class|struct))?|class|struct|union)/,\n            /\\s+/,\n            /\\w+/\n          ],\n          className: {\n            1: 'keyword',\n            3: 'title.class'\n          }\n        }\n      ])\n  };\n}\n\n/*\nLanguage: Arduino\nAuthor: Stefania Mellai <<EMAIL>>\nDescription: The Arduino® Language is a superset of C++. This rules are designed to highlight the Arduino® source code. For info about language see http://www.arduino.cc.\nWebsite: https://www.arduino.cc\nCategory: system\n*/\n\n\n/** @type LanguageFn */\nfunction arduino(hljs) {\n  const ARDUINO_KW = {\n    type: [\n      \"boolean\",\n      \"byte\",\n      \"word\",\n      \"String\"\n    ],\n    built_in: [\n      \"KeyboardController\",\n      \"MouseController\",\n      \"SoftwareSerial\",\n      \"EthernetServer\",\n      \"EthernetClient\",\n      \"LiquidCrystal\",\n      \"RobotControl\",\n      \"GSMVoiceCall\",\n      \"EthernetUDP\",\n      \"EsploraTFT\",\n      \"HttpClient\",\n      \"RobotMotor\",\n      \"WiFiClient\",\n      \"GSMScanner\",\n      \"FileSystem\",\n      \"Scheduler\",\n      \"GSMServer\",\n      \"YunClient\",\n      \"YunServer\",\n      \"IPAddress\",\n      \"GSMClient\",\n      \"GSMModem\",\n      \"Keyboard\",\n      \"Ethernet\",\n      \"Console\",\n      \"GSMBand\",\n      \"Esplora\",\n      \"Stepper\",\n      \"Process\",\n      \"WiFiUDP\",\n      \"GSM_SMS\",\n      \"Mailbox\",\n      \"USBHost\",\n      \"Firmata\",\n      \"PImage\",\n      \"Client\",\n      \"Server\",\n      \"GSMPIN\",\n      \"FileIO\",\n      \"Bridge\",\n      \"Serial\",\n      \"EEPROM\",\n      \"Stream\",\n      \"Mouse\",\n      \"Audio\",\n      \"Servo\",\n      \"File\",\n      \"Task\",\n      \"GPRS\",\n      \"WiFi\",\n      \"Wire\",\n      \"TFT\",\n      \"GSM\",\n      \"SPI\",\n      \"SD\"\n    ],\n    _hints: [\n      \"setup\",\n      \"loop\",\n      \"runShellCommandAsynchronously\",\n      \"analogWriteResolution\",\n      \"retrieveCallingNumber\",\n      \"printFirmwareVersion\",\n      \"analogReadResolution\",\n      \"sendDigitalPortPair\",\n      \"noListenOnLocalhost\",\n      \"readJoystickButton\",\n      \"setFirmwareVersion\",\n      \"readJoystickSwitch\",\n      \"scrollDisplayRight\",\n      \"getVoiceCallStatus\",\n      \"scrollDisplayLeft\",\n      \"writeMicroseconds\",\n      \"delayMicroseconds\",\n      \"beginTransmission\",\n      \"getSignalStrength\",\n      \"runAsynchronously\",\n      \"getAsynchronously\",\n      \"listenOnLocalhost\",\n      \"getCurrentCarrier\",\n      \"readAccelerometer\",\n      \"messageAvailable\",\n      \"sendDigitalPorts\",\n      \"lineFollowConfig\",\n      \"countryNameWrite\",\n      \"runShellCommand\",\n      \"readStringUntil\",\n      \"rewindDirectory\",\n      \"readTemperature\",\n      \"setClockDivider\",\n      \"readLightSensor\",\n      \"endTransmission\",\n      \"analogReference\",\n      \"detachInterrupt\",\n      \"countryNameRead\",\n      \"attachInterrupt\",\n      \"encryptionType\",\n      \"readBytesUntil\",\n      \"robotNameWrite\",\n      \"readMicrophone\",\n      \"robotNameRead\",\n      \"cityNameWrite\",\n      \"userNameWrite\",\n      \"readJoystickY\",\n      \"readJoystickX\",\n      \"mouseReleased\",\n      \"openNextFile\",\n      \"scanNetworks\",\n      \"noInterrupts\",\n      \"digitalWrite\",\n      \"beginSpeaker\",\n      \"mousePressed\",\n      \"isActionDone\",\n      \"mouseDragged\",\n      \"displayLogos\",\n      \"noAutoscroll\",\n      \"addParameter\",\n      \"remoteNumber\",\n      \"getModifiers\",\n      \"keyboardRead\",\n      \"userNameRead\",\n      \"waitContinue\",\n      \"processInput\",\n      \"parseCommand\",\n      \"printVersion\",\n      \"readNetworks\",\n      \"writeMessage\",\n      \"blinkVersion\",\n      \"cityNameRead\",\n      \"readMessage\",\n      \"setDataMode\",\n      \"parsePacket\",\n      \"isListening\",\n      \"setBitOrder\",\n      \"beginPacket\",\n      \"isDirectory\",\n      \"motorsWrite\",\n      \"drawCompass\",\n      \"digitalRead\",\n      \"clearScreen\",\n      \"serialEvent\",\n      \"rightToLeft\",\n      \"setTextSize\",\n      \"leftToRight\",\n      \"requestFrom\",\n      \"keyReleased\",\n      \"compassRead\",\n      \"analogWrite\",\n      \"interrupts\",\n      \"WiFiServer\",\n      \"disconnect\",\n      \"playMelody\",\n      \"parseFloat\",\n      \"autoscroll\",\n      \"getPINUsed\",\n      \"setPINUsed\",\n      \"setTimeout\",\n      \"sendAnalog\",\n      \"readSlider\",\n      \"analogRead\",\n      \"beginWrite\",\n      \"createChar\",\n      \"motorsStop\",\n      \"keyPressed\",\n      \"tempoWrite\",\n      \"readButton\",\n      \"subnetMask\",\n      \"debugPrint\",\n      \"macAddress\",\n      \"writeGreen\",\n      \"randomSeed\",\n      \"attachGPRS\",\n      \"readString\",\n      \"sendString\",\n      \"remotePort\",\n      \"releaseAll\",\n      \"mouseMoved\",\n      \"background\",\n      \"getXChange\",\n      \"getYChange\",\n      \"answerCall\",\n      \"getResult\",\n      \"voiceCall\",\n      \"endPacket\",\n      \"constrain\",\n      \"getSocket\",\n      \"writeJSON\",\n      \"getButton\",\n      \"available\",\n      \"connected\",\n      \"findUntil\",\n      \"readBytes\",\n      \"exitValue\",\n      \"readGreen\",\n      \"writeBlue\",\n      \"startLoop\",\n      \"IPAddress\",\n      \"isPressed\",\n      \"sendSysex\",\n      \"pauseMode\",\n      \"gatewayIP\",\n      \"setCursor\",\n      \"getOemKey\",\n      \"tuneWrite\",\n      \"noDisplay\",\n      \"loadImage\",\n      \"switchPIN\",\n      \"onRequest\",\n      \"onReceive\",\n      \"changePIN\",\n      \"playFile\",\n      \"noBuffer\",\n      \"parseInt\",\n      \"overflow\",\n      \"checkPIN\",\n      \"knobRead\",\n      \"beginTFT\",\n      \"bitClear\",\n      \"updateIR\",\n      \"bitWrite\",\n      \"position\",\n      \"writeRGB\",\n      \"highByte\",\n      \"writeRed\",\n      \"setSpeed\",\n      \"readBlue\",\n      \"noStroke\",\n      \"remoteIP\",\n      \"transfer\",\n      \"shutdown\",\n      \"hangCall\",\n      \"beginSMS\",\n      \"endWrite\",\n      \"attached\",\n      \"maintain\",\n      \"noCursor\",\n      \"checkReg\",\n      \"checkPUK\",\n      \"shiftOut\",\n      \"isValid\",\n      \"shiftIn\",\n      \"pulseIn\",\n      \"connect\",\n      \"println\",\n      \"localIP\",\n      \"pinMode\",\n      \"getIMEI\",\n      \"display\",\n      \"noBlink\",\n      \"process\",\n      \"getBand\",\n      \"running\",\n      \"beginSD\",\n      \"drawBMP\",\n      \"lowByte\",\n      \"setBand\",\n      \"release\",\n      \"bitRead\",\n      \"prepare\",\n      \"pointTo\",\n      \"readRed\",\n      \"setMode\",\n      \"noFill\",\n      \"remove\",\n      \"listen\",\n      \"stroke\",\n      \"detach\",\n      \"attach\",\n      \"noTone\",\n      \"exists\",\n      \"buffer\",\n      \"height\",\n      \"bitSet\",\n      \"circle\",\n      \"config\",\n      \"cursor\",\n      \"random\",\n      \"IRread\",\n      \"setDNS\",\n      \"endSMS\",\n      \"getKey\",\n      \"micros\",\n      \"millis\",\n      \"begin\",\n      \"print\",\n      \"write\",\n      \"ready\",\n      \"flush\",\n      \"width\",\n      \"isPIN\",\n      \"blink\",\n      \"clear\",\n      \"press\",\n      \"mkdir\",\n      \"rmdir\",\n      \"close\",\n      \"point\",\n      \"yield\",\n      \"image\",\n      \"BSSID\",\n      \"click\",\n      \"delay\",\n      \"read\",\n      \"text\",\n      \"move\",\n      \"peek\",\n      \"beep\",\n      \"rect\",\n      \"line\",\n      \"open\",\n      \"seek\",\n      \"fill\",\n      \"size\",\n      \"turn\",\n      \"stop\",\n      \"home\",\n      \"find\",\n      \"step\",\n      \"tone\",\n      \"sqrt\",\n      \"RSSI\",\n      \"SSID\",\n      \"end\",\n      \"bit\",\n      \"tan\",\n      \"cos\",\n      \"sin\",\n      \"pow\",\n      \"map\",\n      \"abs\",\n      \"max\",\n      \"min\",\n      \"get\",\n      \"run\",\n      \"put\"\n    ],\n    literal: [\n      \"DIGITAL_MESSAGE\",\n      \"FIRMATA_STRING\",\n      \"ANALOG_MESSAGE\",\n      \"REPORT_DIGITAL\",\n      \"REPORT_ANALOG\",\n      \"INPUT_PULLUP\",\n      \"SET_PIN_MODE\",\n      \"INTERNAL2V56\",\n      \"SYSTEM_RESET\",\n      \"LED_BUILTIN\",\n      \"INTERNAL1V1\",\n      \"SYSEX_START\",\n      \"INTERNAL\",\n      \"EXTERNAL\",\n      \"DEFAULT\",\n      \"OUTPUT\",\n      \"INPUT\",\n      \"HIGH\",\n      \"LOW\"\n    ]\n  };\n\n  const ARDUINO = cPlusPlus(hljs);\n\n  const kws = /** @type {Record<string,any>} */ (ARDUINO.keywords);\n\n  kws.type = [\n    ...kws.type,\n    ...ARDUINO_KW.type\n  ];\n  kws.literal = [\n    ...kws.literal,\n    ...ARDUINO_KW.literal\n  ];\n  kws.built_in = [\n    ...kws.built_in,\n    ...ARDUINO_KW.built_in\n  ];\n  kws._hints = ARDUINO_KW._hints;\n\n  ARDUINO.name = 'Arduino';\n  ARDUINO.aliases = [ 'ino' ];\n  ARDUINO.supersetOf = \"cpp\";\n\n  return ARDUINO;\n}\n\nexport { arduino as default };\n"], "names": [], "mappings": "AAAA;;;;AAIA,GAEA,qBAAqB;;;AACrB,SAAS,UAAU,IAAI;IACrB,MAAM,QAAQ,KAAK,KAAK;IACxB,qEAAqE;IACrE,yEAAyE;IACzE,mCAAmC;IACnC,MAAM,sBAAsB,KAAK,OAAO,CAAC,MAAM,KAAK;QAAE,UAAU;YAAE;gBAAE,OAAO;YAAO;SAAG;IAAC;IACtF,MAAM,mBAAmB;IACzB,MAAM,eAAe;IACrB,MAAM,uBAAuB;IAC7B,MAAM,mBAAmB,gBACrB,mBAAmB,MACnB,MAAM,QAAQ,CAAC,gBACf,kBAAkB,MAAM,QAAQ,CAAC,wBACnC;IAEF,MAAM,sBAAsB;QAC1B,WAAW;QACX,OAAO;IACT;IAEA,oDAAoD;IACpD,oCAAoC;IACpC,MAAM,oBAAoB;IAC1B,MAAM,UAAU;QACd,WAAW;QACX,UAAU;YACR;gBACE,OAAO;gBACP,KAAK;gBACL,SAAS;gBACT,UAAU;oBAAE,KAAK,gBAAgB;iBAAE;YACrC;YACA;gBACE,OAAO,kBAAkB,oBAAoB;gBAC7C,KAAK;gBACL,SAAS;YACX;YACA,KAAK,iBAAiB,CAAC;gBACrB,OAAO;gBACP,KAAK;YACP;SACD;IACH;IAEA,MAAM,UAAU;QACd,WAAW;QACX,UAAU;YACR,0BAA0B;YAC1B;gBAAE,OACA,WAAW,gBAAgB;mBAEvB,QACC,+CACC,0BACF,qCACA,iDAEA,cACC,4EACC,sCACF,gCACF,OAAO,oBAAoB;mBACzB,0BACA,eACA,UACA,IAAI,8BAA8B;mBACpC;YACJ;YACA,mBAAmB;YACnB;gBAAE,OACA,cAAc,gBAAgB;mBAC1B,uBAAuB,UAAU;mBACjC,sCAAsC,eAAe;mBACrD,iBAAiB,6BAA6B;mBAC9C,qBAAqB,WAAW;mBAClC,OAAO,oBAAoB;mBACzB,oBACA,eACA,sBACA,cACA,IAAI,8BAA8B;mBACpC;YAGJ;SACD;QACD,WAAW;IACb;IAEA,MAAM,eAAe;QACnB,WAAW;QACX,OAAO;QACP,KAAK;QACL,UAAU;YAAE,SACR,wDACE;QAAsC;QAC5C,UAAU;YACR;gBACE,OAAO;gBACP,WAAW;YACb;YACA,KAAK,OAAO,CAAC,SAAS;gBAAE,WAAW;YAAS;YAC5C;gBACE,WAAW;gBACX,OAAO;YACT;YACA;YACA,KAAK,oBAAoB;SAC1B;IACH;IAEA,MAAM,aAAa;QACjB,WAAW;QACX,OAAO,MAAM,QAAQ,CAAC,gBAAgB,KAAK,QAAQ;QACnD,WAAW;IACb;IAEA,MAAM,iBAAiB,MAAM,QAAQ,CAAC,gBAAgB,KAAK,QAAQ,GAAG;IAEtE,4CAA4C;IAC5C,MAAM,oBAAoB;QACxB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,4CAA4C;IAC5C,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,aAAa;QACjB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;KACD;IAED,4CAA4C;IAC5C,MAAM,WAAW;QAAE;KAAW;IAE9B,MAAM,eAAe;QACnB,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;QACV,aAAa;IACf;IAEA,MAAM,oBAAoB;QACxB,WAAW;QACX,WAAW;QACX,UAAU;YACR,wCAAwC;YACxC,OAAO;QAAe;QACxB,OAAO,MAAM,MAAM,CACjB,MACA,gBACA,UACA,WACA,cACA,aACA,KAAK,QAAQ,EACb,MAAM,SAAS,CAAC;IACpB;IAEA,MAAM,sBAAsB;QAC1B;QACA;QACA;QACA;QACA,KAAK,oBAAoB;QACzB;QACA;KACD;IAED,MAAM,qBAAqB;QACzB,uEAAuE;QACvE,mEAAmE;QACnE,gDAAgD;QAChD,UAAU;YACR;gBACE,OAAO;gBACP,KAAK;YACP;YACA;gBACE,OAAO;gBACP,KAAK;YACP;YACA;gBACE,eAAe;gBACf,KAAK;YACP;SACD;QACD,UAAU;QACV,UAAU,oBAAoB,MAAM,CAAC;YACnC;gBACE,OAAO;gBACP,KAAK;gBACL,UAAU;gBACV,UAAU,oBAAoB,MAAM,CAAC;oBAAE;iBAAQ;gBAC/C,WAAW;YACb;SACD;QACD,WAAW;IACb;IAEA,MAAM,uBAAuB;QAC3B,WAAW;QACX,OAAO,MAAM,mBAAmB,iBAAiB;QACjD,aAAa;QACb,KAAK;QACL,YAAY;QACZ,UAAU;QACV,SAAS;QACT,UAAU;YACR;gBACE,OAAO;gBACP,UAAU;gBACV,WAAW;YACb;YACA;gBACE,OAAO;gBACP,aAAa;gBACb,UAAU;oBAAE;iBAAY;gBACxB,WAAW;YACb;YACA,8DAA8D;YAC9D,uDAAuD;YACvD;gBACE,OAAO;gBACP,WAAW;YACb;YACA,eAAe;YACf;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;oBACR;oBACA;iBACD;YACH;YACA,yCAAyC;YACzC,+BAA+B;YAC/B;gBACE,WAAW;gBACX,OAAO;YACT;YACA;gBACE,WAAW;gBACX,OAAO;gBACP,KAAK;gBACL,UAAU;gBACV,WAAW;gBACX,UAAU;oBACR;oBACA,KAAK,oBAAoB;oBACzB;oBACA;oBACA;oBACA,8BAA8B;oBAC9B;wBACE,OAAO;wBACP,KAAK;wBACL,UAAU;wBACV,WAAW;wBACX,UAAU;4BACR;4BACA;4BACA,KAAK,oBAAoB;4BACzB;4BACA;4BACA;yBACD;oBACH;iBACD;YACH;YACA;YACA;YACA,KAAK,oBAAoB;YACzB;SACD;IACH;IAEA,OAAO;QACL,MAAM;QACN,SAAS;YACP;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,SAAS;QACT,kBAAkB;YAAE,qBAAqB;QAAW;QACpD,UAAU,EAAE,CAAC,MAAM,CACjB,oBACA,sBACA,mBACA,qBACA;YACE;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,UAAU;gBACV,UAAU;oBACR;oBACA;iBACD;YACH;YACA;gBACE,OAAO,KAAK,QAAQ,GAAG;gBACvB,UAAU;YACZ;YACA;gBACE,OAAO;oBACL,+DAA+D;oBAC/D;oBACA;oBACA;iBACD;gBACD,WAAW;oBACT,GAAG;oBACH,GAAG;gBACL;YACF;SACD;IACL;AACF;AAEA;;;;;;AAMA,GAGA,qBAAqB,GACrB,SAAS,QAAQ,IAAI;IACnB,MAAM,aAAa;QACjB,MAAM;YACJ;YACA;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,QAAQ;YACN;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,SAAS;YACP;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,MAAM,UAAU,UAAU;IAE1B,MAAM,MAAyC,QAAQ,QAAQ;IAE/D,IAAI,IAAI,GAAG;WACN,IAAI,IAAI;WACR,WAAW,IAAI;KACnB;IACD,IAAI,OAAO,GAAG;WACT,IAAI,OAAO;WACX,WAAW,OAAO;KACtB;IACD,IAAI,QAAQ,GAAG;WACV,IAAI,QAAQ;WACZ,WAAW,QAAQ;KACvB;IACD,IAAI,MAAM,GAAG,WAAW,MAAM;IAE9B,QAAQ,IAAI,GAAG;IACf,QAAQ,OAAO,GAAG;QAAE;KAAO;IAC3B,QAAQ,UAAU,GAAG;IAErB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 976, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/bash.js"], "sourcesContent": ["/*\nLanguage: Bash\nAuthor: vah <vah<PERSON><EMAIL>>\nContributrors: <PERSON> <<EMAIL>>\nWebsite: https://www.gnu.org/software/bash/\nCategory: common, scripting\n*/\n\n/** @type LanguageFn */\nfunction bash(hljs) {\n  const regex = hljs.regex;\n  const VAR = {};\n  const BRACED_VAR = {\n    begin: /\\$\\{/,\n    end: /\\}/,\n    contains: [\n      \"self\",\n      {\n        begin: /:-/,\n        contains: [ VAR ]\n      } // default values\n    ]\n  };\n  Object.assign(VAR, {\n    className: 'variable',\n    variants: [\n      { begin: regex.concat(/\\$[\\w\\d#@][\\w\\d_]*/,\n        // negative look-ahead tries to avoid matching patterns that are not\n        // Perl at all like $ident$, @ident@, etc.\n        `(?![\\\\w\\\\d])(?![$])`) },\n      BRACED_VAR\n    ]\n  });\n\n  const SUBST = {\n    className: 'subst',\n    begin: /\\$\\(/,\n    end: /\\)/,\n    contains: [ hljs.BACKSLASH_ESCAPE ]\n  };\n  const COMMENT = hljs.inherit(\n    hljs.COMMENT(),\n    {\n      match: [\n        /(^|\\s)/,\n        /#.*$/\n      ],\n      scope: {\n        2: 'comment'\n      }\n    }\n  );\n  const HERE_DOC = {\n    begin: /<<-?\\s*(?=\\w+)/,\n    starts: { contains: [\n      hljs.END_SAME_AS_BEGIN({\n        begin: /(\\w+)/,\n        end: /(\\w+)/,\n        className: 'string'\n      })\n    ] }\n  };\n  const QUOTE_STRING = {\n    className: 'string',\n    begin: /\"/,\n    end: /\"/,\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      VAR,\n      SUBST\n    ]\n  };\n  SUBST.contains.push(QUOTE_STRING);\n  const ESCAPED_QUOTE = {\n    match: /\\\\\"/\n  };\n  const APOS_STRING = {\n    className: 'string',\n    begin: /'/,\n    end: /'/\n  };\n  const ESCAPED_APOS = {\n    match: /\\\\'/\n  };\n  const ARITHMETIC = {\n    begin: /\\$?\\(\\(/,\n    end: /\\)\\)/,\n    contains: [\n      {\n        begin: /\\d+#[0-9a-f]+/,\n        className: \"number\"\n      },\n      hljs.NUMBER_MODE,\n      VAR\n    ]\n  };\n  const SH_LIKE_SHELLS = [\n    \"fish\",\n    \"bash\",\n    \"zsh\",\n    \"sh\",\n    \"csh\",\n    \"ksh\",\n    \"tcsh\",\n    \"dash\",\n    \"scsh\",\n  ];\n  const KNOWN_SHEBANG = hljs.SHEBANG({\n    binary: `(${SH_LIKE_SHELLS.join(\"|\")})`,\n    relevance: 10\n  });\n  const FUNCTION = {\n    className: 'function',\n    begin: /\\w[\\w\\d_]*\\s*\\(\\s*\\)\\s*\\{/,\n    returnBegin: true,\n    contains: [ hljs.inherit(hljs.TITLE_MODE, { begin: /\\w[\\w\\d_]*/ }) ],\n    relevance: 0\n  };\n\n  const KEYWORDS = [\n    \"if\",\n    \"then\",\n    \"else\",\n    \"elif\",\n    \"fi\",\n    \"time\",\n    \"for\",\n    \"while\",\n    \"until\",\n    \"in\",\n    \"do\",\n    \"done\",\n    \"case\",\n    \"esac\",\n    \"coproc\",\n    \"function\",\n    \"select\"\n  ];\n\n  const LITERALS = [\n    \"true\",\n    \"false\"\n  ];\n\n  // to consume paths to prevent keyword matches inside them\n  const PATH_MODE = { match: /(\\/[a-z._-]+)+/ };\n\n  // http://www.gnu.org/software/bash/manual/html_node/Shell-Builtin-Commands.html\n  const SHELL_BUILT_INS = [\n    \"break\",\n    \"cd\",\n    \"continue\",\n    \"eval\",\n    \"exec\",\n    \"exit\",\n    \"export\",\n    \"getopts\",\n    \"hash\",\n    \"pwd\",\n    \"readonly\",\n    \"return\",\n    \"shift\",\n    \"test\",\n    \"times\",\n    \"trap\",\n    \"umask\",\n    \"unset\"\n  ];\n\n  const BASH_BUILT_INS = [\n    \"alias\",\n    \"bind\",\n    \"builtin\",\n    \"caller\",\n    \"command\",\n    \"declare\",\n    \"echo\",\n    \"enable\",\n    \"help\",\n    \"let\",\n    \"local\",\n    \"logout\",\n    \"mapfile\",\n    \"printf\",\n    \"read\",\n    \"readarray\",\n    \"source\",\n    \"sudo\",\n    \"type\",\n    \"typeset\",\n    \"ulimit\",\n    \"unalias\"\n  ];\n\n  const ZSH_BUILT_INS = [\n    \"autoload\",\n    \"bg\",\n    \"bindkey\",\n    \"bye\",\n    \"cap\",\n    \"chdir\",\n    \"clone\",\n    \"comparguments\",\n    \"compcall\",\n    \"compctl\",\n    \"compdescribe\",\n    \"compfiles\",\n    \"compgroups\",\n    \"compquote\",\n    \"comptags\",\n    \"comptry\",\n    \"compvalues\",\n    \"dirs\",\n    \"disable\",\n    \"disown\",\n    \"echotc\",\n    \"echoti\",\n    \"emulate\",\n    \"fc\",\n    \"fg\",\n    \"float\",\n    \"functions\",\n    \"getcap\",\n    \"getln\",\n    \"history\",\n    \"integer\",\n    \"jobs\",\n    \"kill\",\n    \"limit\",\n    \"log\",\n    \"noglob\",\n    \"popd\",\n    \"print\",\n    \"pushd\",\n    \"pushln\",\n    \"rehash\",\n    \"sched\",\n    \"setcap\",\n    \"setopt\",\n    \"stat\",\n    \"suspend\",\n    \"ttyctl\",\n    \"unfunction\",\n    \"unhash\",\n    \"unlimit\",\n    \"unsetopt\",\n    \"vared\",\n    \"wait\",\n    \"whence\",\n    \"where\",\n    \"which\",\n    \"zcompile\",\n    \"zformat\",\n    \"zftp\",\n    \"zle\",\n    \"zmodload\",\n    \"zparseopts\",\n    \"zprof\",\n    \"zpty\",\n    \"zregexparse\",\n    \"zsocket\",\n    \"zstyle\",\n    \"ztcp\"\n  ];\n\n  const GNU_CORE_UTILS = [\n    \"chcon\",\n    \"chgrp\",\n    \"chown\",\n    \"chmod\",\n    \"cp\",\n    \"dd\",\n    \"df\",\n    \"dir\",\n    \"dircolors\",\n    \"ln\",\n    \"ls\",\n    \"mkdir\",\n    \"mkfifo\",\n    \"mknod\",\n    \"mktemp\",\n    \"mv\",\n    \"realpath\",\n    \"rm\",\n    \"rmdir\",\n    \"shred\",\n    \"sync\",\n    \"touch\",\n    \"truncate\",\n    \"vdir\",\n    \"b2sum\",\n    \"base32\",\n    \"base64\",\n    \"cat\",\n    \"cksum\",\n    \"comm\",\n    \"csplit\",\n    \"cut\",\n    \"expand\",\n    \"fmt\",\n    \"fold\",\n    \"head\",\n    \"join\",\n    \"md5sum\",\n    \"nl\",\n    \"numfmt\",\n    \"od\",\n    \"paste\",\n    \"ptx\",\n    \"pr\",\n    \"sha1sum\",\n    \"sha224sum\",\n    \"sha256sum\",\n    \"sha384sum\",\n    \"sha512sum\",\n    \"shuf\",\n    \"sort\",\n    \"split\",\n    \"sum\",\n    \"tac\",\n    \"tail\",\n    \"tr\",\n    \"tsort\",\n    \"unexpand\",\n    \"uniq\",\n    \"wc\",\n    \"arch\",\n    \"basename\",\n    \"chroot\",\n    \"date\",\n    \"dirname\",\n    \"du\",\n    \"echo\",\n    \"env\",\n    \"expr\",\n    \"factor\",\n    // \"false\", // keyword literal already\n    \"groups\",\n    \"hostid\",\n    \"id\",\n    \"link\",\n    \"logname\",\n    \"nice\",\n    \"nohup\",\n    \"nproc\",\n    \"pathchk\",\n    \"pinky\",\n    \"printenv\",\n    \"printf\",\n    \"pwd\",\n    \"readlink\",\n    \"runcon\",\n    \"seq\",\n    \"sleep\",\n    \"stat\",\n    \"stdbuf\",\n    \"stty\",\n    \"tee\",\n    \"test\",\n    \"timeout\",\n    // \"true\", // keyword literal already\n    \"tty\",\n    \"uname\",\n    \"unlink\",\n    \"uptime\",\n    \"users\",\n    \"who\",\n    \"whoami\",\n    \"yes\"\n  ];\n\n  return {\n    name: 'Bash',\n    aliases: [\n      'sh',\n      'zsh'\n    ],\n    keywords: {\n      $pattern: /\\b[a-z][a-z0-9._-]+\\b/,\n      keyword: KEYWORDS,\n      literal: LITERALS,\n      built_in: [\n        ...SHELL_BUILT_INS,\n        ...BASH_BUILT_INS,\n        // Shell modifiers\n        \"set\",\n        \"shopt\",\n        ...ZSH_BUILT_INS,\n        ...GNU_CORE_UTILS\n      ]\n    },\n    contains: [\n      KNOWN_SHEBANG, // to catch known shells and boost relevancy\n      hljs.SHEBANG(), // to catch unknown shells but still highlight the shebang\n      FUNCTION,\n      ARITHMETIC,\n      COMMENT,\n      HERE_DOC,\n      PATH_MODE,\n      QUOTE_STRING,\n      ESCAPED_QUOTE,\n      APOS_STRING,\n      ESCAPED_APOS,\n      VAR\n    ]\n  };\n}\n\nexport { bash as default };\n"], "names": [], "mappings": "AAAA;;;;;;AAMA,GAEA,qBAAqB;;;AACrB,SAAS,KAAK,IAAI;IAChB,MAAM,QAAQ,KAAK,KAAK;IACxB,MAAM,MAAM,CAAC;IACb,MAAM,aAAa;QACjB,OAAO;QACP,KAAK;QACL,UAAU;YACR;YACA;gBACE,OAAO;gBACP,UAAU;oBAAE;iBAAK;YACnB,EAAE,iBAAiB;SACpB;IACH;IACA,OAAO,MAAM,CAAC,KAAK;QACjB,WAAW;QACX,UAAU;YACR;gBAAE,OAAO,MAAM,MAAM,CAAC,sBACpB,oEAAoE;gBACpE,0CAA0C;gBAC1C,CAAC,mBAAmB,CAAC;YAAE;YACzB;SACD;IACH;IAEA,MAAM,QAAQ;QACZ,WAAW;QACX,OAAO;QACP,KAAK;QACL,UAAU;YAAE,KAAK,gBAAgB;SAAE;IACrC;IACA,MAAM,UAAU,KAAK,OAAO,CAC1B,KAAK,OAAO,IACZ;QACE,OAAO;YACL;YACA;SACD;QACD,OAAO;YACL,GAAG;QACL;IACF;IAEF,MAAM,WAAW;QACf,OAAO;QACP,QAAQ;YAAE,UAAU;gBAClB,KAAK,iBAAiB,CAAC;oBACrB,OAAO;oBACP,KAAK;oBACL,WAAW;gBACb;aACD;QAAC;IACJ;IACA,MAAM,eAAe;QACnB,WAAW;QACX,OAAO;QACP,KAAK;QACL,UAAU;YACR,KAAK,gBAAgB;YACrB;YACA;SACD;IACH;IACA,MAAM,QAAQ,CAAC,IAAI,CAAC;IACpB,MAAM,gBAAgB;QACpB,OAAO;IACT;IACA,MAAM,cAAc;QAClB,WAAW;QACX,OAAO;QACP,KAAK;IACP;IACA,MAAM,eAAe;QACnB,OAAO;IACT;IACA,MAAM,aAAa;QACjB,OAAO;QACP,KAAK;QACL,UAAU;YACR;gBACE,OAAO;gBACP,WAAW;YACb;YACA,KAAK,WAAW;YAChB;SACD;IACH;IACA,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,gBAAgB,KAAK,OAAO,CAAC;QACjC,QAAQ,CAAC,CAAC,EAAE,eAAe,IAAI,CAAC,KAAK,CAAC,CAAC;QACvC,WAAW;IACb;IACA,MAAM,WAAW;QACf,WAAW;QACX,OAAO;QACP,aAAa;QACb,UAAU;YAAE,KAAK,OAAO,CAAC,KAAK,UAAU,EAAE;gBAAE,OAAO;YAAa;SAAI;QACpE,WAAW;IACb;IAEA,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,WAAW;QACf;QACA;KACD;IAED,0DAA0D;IAC1D,MAAM,YAAY;QAAE,OAAO;IAAiB;IAE5C,gFAAgF;IAChF,MAAM,kBAAkB;QACtB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,gBAAgB;QACpB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,sCAAsC;QACtC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,qCAAqC;QACrC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OAAO;QACL,MAAM;QACN,SAAS;YACP;YACA;SACD;QACD,UAAU;YACR,UAAU;YACV,SAAS;YACT,SAAS;YACT,UAAU;mBACL;mBACA;gBACH,kBAAkB;gBAClB;gBACA;mBACG;mBACA;aACJ;QACH;QACA,UAAU;YACR;YACA,KAAK,OAAO;YACZ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1391, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/c.js"], "sourcesContent": ["/*\nLanguage: C\nCategory: common, system\nWebsite: https://en.wikipedia.org/wiki/C_(programming_language)\n*/\n\n/** @type LanguageFn */\nfunction c(hljs) {\n  const regex = hljs.regex;\n  // added for historic reasons because `hljs.C_LINE_COMMENT_MODE` does\n  // not include such support nor can we be sure all the grammars depending\n  // on it would desire this behavior\n  const C_LINE_COMMENT_MODE = hljs.COMMENT('//', '$', { contains: [ { begin: /\\\\\\n/ } ] });\n  const DECLTYPE_AUTO_RE = 'decltype\\\\(auto\\\\)';\n  const NAMESPACE_RE = '[a-zA-Z_]\\\\w*::';\n  const TEMPLATE_ARGUMENT_RE = '<[^<>]+>';\n  const FUNCTION_TYPE_RE = '('\n    + DECLTYPE_AUTO_RE + '|'\n    + regex.optional(NAMESPACE_RE)\n    + '[a-zA-Z_]\\\\w*' + regex.optional(TEMPLATE_ARGUMENT_RE)\n  + ')';\n\n\n  const TYPES = {\n    className: 'type',\n    variants: [\n      { begin: '\\\\b[a-z\\\\d_]*_t\\\\b' },\n      { match: /\\batomic_[a-z]{3,6}\\b/ }\n    ]\n\n  };\n\n  // https://en.cppreference.com/w/cpp/language/escape\n  // \\\\ \\x \\xFF \\u2837 \\u00323747 \\374\n  const CHARACTER_ESCAPES = '\\\\\\\\(x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4,8}|[0-7]{3}|\\\\S)';\n  const STRINGS = {\n    className: 'string',\n    variants: [\n      {\n        begin: '(u8?|U|L)?\"',\n        end: '\"',\n        illegal: '\\\\n',\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      },\n      {\n        begin: '(u8?|U|L)?\\'(' + CHARACTER_ESCAPES + \"|.)\",\n        end: '\\'',\n        illegal: '.'\n      },\n      hljs.END_SAME_AS_BEGIN({\n        begin: /(?:u8?|U|L)?R\"([^()\\\\ ]{0,16})\\(/,\n        end: /\\)([^()\\\\ ]{0,16})\"/\n      })\n    ]\n  };\n\n  const NUMBERS = {\n    className: 'number',\n    variants: [\n      { match: /\\b(0b[01']+)/ },  \n      { match: /(-?)\\b([\\d']+(\\.[\\d']*)?|\\.[\\d']+)((ll|LL|l|L)(u|U)?|(u|U)(ll|LL|l|L)?|f|F|b|B)/ },  \n      { match: /(-?)\\b(0[xX][a-fA-F0-9]+(?:'[a-fA-F0-9]+)*(?:\\.[a-fA-F0-9]*(?:'[a-fA-F0-9]*)*)?(?:[pP][-+]?[0-9]+)?(l|L)?(u|U)?)/ },  \n      { match: /(-?)\\b\\d+(?:'\\d+)*(?:\\.\\d*(?:'\\d*)*)?(?:[eE][-+]?\\d+)?/ }  \n  ],\n    relevance: 0\n  };  \n  \n  const PREPROCESSOR = {\n    className: 'meta',\n    begin: /#\\s*[a-z]+\\b/,\n    end: /$/,\n    keywords: { keyword:\n        'if else elif endif define undef warning error line '\n        + 'pragma _Pragma ifdef ifndef elifdef elifndef include' },\n    contains: [\n      {\n        begin: /\\\\\\n/,\n        relevance: 0\n      },\n      hljs.inherit(STRINGS, { className: 'string' }),\n      {\n        className: 'string',\n        begin: /<.*?>/\n      },\n      C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE\n    ]\n  };\n\n  const TITLE_MODE = {\n    className: 'title',\n    begin: regex.optional(NAMESPACE_RE) + hljs.IDENT_RE,\n    relevance: 0\n  };\n\n  const FUNCTION_TITLE = regex.optional(NAMESPACE_RE) + hljs.IDENT_RE + '\\\\s*\\\\(';\n\n  const C_KEYWORDS = [\n    \"asm\",\n    \"auto\",\n    \"break\",\n    \"case\",\n    \"continue\",\n    \"default\",\n    \"do\",\n    \"else\",\n    \"enum\",\n    \"extern\",\n    \"for\",\n    \"fortran\",\n    \"goto\",\n    \"if\",\n    \"inline\",\n    \"register\",\n    \"restrict\",\n    \"return\",\n    \"sizeof\",\n    \"typeof\",\n    \"typeof_unqual\",\n    \"struct\",\n    \"switch\",\n    \"typedef\",\n    \"union\",\n    \"volatile\",\n    \"while\",\n    \"_Alignas\",\n    \"_Alignof\",\n    \"_Atomic\",\n    \"_Generic\",\n    \"_Noreturn\",\n    \"_Static_assert\",\n    \"_Thread_local\",\n    // aliases\n    \"alignas\",\n    \"alignof\",\n    \"noreturn\",\n    \"static_assert\",\n    \"thread_local\",\n    // not a C keyword but is, for all intents and purposes, treated exactly like one.\n    \"_Pragma\"\n  ];\n\n  const C_TYPES = [\n    \"float\",\n    \"double\",\n    \"signed\",\n    \"unsigned\",\n    \"int\",\n    \"short\",\n    \"long\",\n    \"char\",\n    \"void\",\n    \"_Bool\",\n    \"_BitInt\",\n    \"_Complex\",\n    \"_Imaginary\",\n    \"_Decimal32\",\n    \"_Decimal64\",\n    \"_Decimal96\",\n    \"_Decimal128\",\n    \"_Decimal64x\",\n    \"_Decimal128x\",\n    \"_Float16\",\n    \"_Float32\",\n    \"_Float64\",\n    \"_Float128\",\n    \"_Float32x\",\n    \"_Float64x\",\n    \"_Float128x\",\n    // modifiers\n    \"const\",\n    \"static\",\n    \"constexpr\",\n    // aliases\n    \"complex\",\n    \"bool\",\n    \"imaginary\"\n  ];\n\n  const KEYWORDS = {\n    keyword: C_KEYWORDS,\n    type: C_TYPES,\n    literal: 'true false NULL',\n    // TODO: apply hinting work similar to what was done in cpp.js\n    built_in: 'std string wstring cin cout cerr clog stdin stdout stderr stringstream istringstream ostringstream '\n      + 'auto_ptr deque list queue stack vector map set pair bitset multiset multimap unordered_set '\n      + 'unordered_map unordered_multiset unordered_multimap priority_queue make_pair array shared_ptr abort terminate abs acos '\n      + 'asin atan2 atan calloc ceil cosh cos exit exp fabs floor fmod fprintf fputs free frexp '\n      + 'fscanf future isalnum isalpha iscntrl isdigit isgraph islower isprint ispunct isspace isupper '\n      + 'isxdigit tolower toupper labs ldexp log10 log malloc realloc memchr memcmp memcpy memset modf pow '\n      + 'printf putchar puts scanf sinh sin snprintf sprintf sqrt sscanf strcat strchr strcmp '\n      + 'strcpy strcspn strlen strncat strncmp strncpy strpbrk strrchr strspn strstr tanh tan '\n      + 'vfprintf vprintf vsprintf endl initializer_list unique_ptr',\n  };\n\n  const EXPRESSION_CONTAINS = [\n    PREPROCESSOR,\n    TYPES,\n    C_LINE_COMMENT_MODE,\n    hljs.C_BLOCK_COMMENT_MODE,\n    NUMBERS,\n    STRINGS\n  ];\n\n  const EXPRESSION_CONTEXT = {\n    // This mode covers expression context where we can't expect a function\n    // definition and shouldn't highlight anything that looks like one:\n    // `return some()`, `else if()`, `(x*sum(1, 2))`\n    variants: [\n      {\n        begin: /=/,\n        end: /;/\n      },\n      {\n        begin: /\\(/,\n        end: /\\)/\n      },\n      {\n        beginKeywords: 'new throw return else',\n        end: /;/\n      }\n    ],\n    keywords: KEYWORDS,\n    contains: EXPRESSION_CONTAINS.concat([\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: KEYWORDS,\n        contains: EXPRESSION_CONTAINS.concat([ 'self' ]),\n        relevance: 0\n      }\n    ]),\n    relevance: 0\n  };\n\n  const FUNCTION_DECLARATION = {\n    begin: '(' + FUNCTION_TYPE_RE + '[\\\\*&\\\\s]+)+' + FUNCTION_TITLE,\n    returnBegin: true,\n    end: /[{;=]/,\n    excludeEnd: true,\n    keywords: KEYWORDS,\n    illegal: /[^\\w\\s\\*&:<>.]/,\n    contains: [\n      { // to prevent it from being confused as the function title\n        begin: DECLTYPE_AUTO_RE,\n        keywords: KEYWORDS,\n        relevance: 0\n      },\n      {\n        begin: FUNCTION_TITLE,\n        returnBegin: true,\n        contains: [ hljs.inherit(TITLE_MODE, { className: \"title.function\" }) ],\n        relevance: 0\n      },\n      // allow for multiple declarations, e.g.:\n      // extern void f(int), g(char);\n      {\n        relevance: 0,\n        match: /,/\n      },\n      {\n        className: 'params',\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: KEYWORDS,\n        relevance: 0,\n        contains: [\n          C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE,\n          STRINGS,\n          NUMBERS,\n          TYPES,\n          // Count matching parentheses.\n          {\n            begin: /\\(/,\n            end: /\\)/,\n            keywords: KEYWORDS,\n            relevance: 0,\n            contains: [\n              'self',\n              C_LINE_COMMENT_MODE,\n              hljs.C_BLOCK_COMMENT_MODE,\n              STRINGS,\n              NUMBERS,\n              TYPES\n            ]\n          }\n        ]\n      },\n      TYPES,\n      C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      PREPROCESSOR\n    ]\n  };\n\n  return {\n    name: \"C\",\n    aliases: [ 'h' ],\n    keywords: KEYWORDS,\n    // Until differentiations are added between `c` and `cpp`, `c` will\n    // not be auto-detected to avoid auto-detect conflicts between C and C++\n    disableAutodetect: true,\n    illegal: '</',\n    contains: [].concat(\n      EXPRESSION_CONTEXT,\n      FUNCTION_DECLARATION,\n      EXPRESSION_CONTAINS,\n      [\n        PREPROCESSOR,\n        {\n          begin: hljs.IDENT_RE + '::',\n          keywords: KEYWORDS\n        },\n        {\n          className: 'class',\n          beginKeywords: 'enum class struct union',\n          end: /[{;:<>=]/,\n          contains: [\n            { beginKeywords: \"final class struct\" },\n            hljs.TITLE_MODE\n          ]\n        }\n      ]),\n    exports: {\n      preprocessor: PREPROCESSOR,\n      strings: STRINGS,\n      keywords: KEYWORDS\n    }\n  };\n}\n\nexport { c as default };\n"], "names": [], "mappings": "AAAA;;;;AAIA,GAEA,qBAAqB;;;AACrB,SAAS,EAAE,IAAI;IACb,MAAM,QAAQ,KAAK,KAAK;IACxB,qEAAqE;IACrE,yEAAyE;IACzE,mCAAmC;IACnC,MAAM,sBAAsB,KAAK,OAAO,CAAC,MAAM,KAAK;QAAE,UAAU;YAAE;gBAAE,OAAO;YAAO;SAAG;IAAC;IACtF,MAAM,mBAAmB;IACzB,MAAM,eAAe;IACrB,MAAM,uBAAuB;IAC7B,MAAM,mBAAmB,MACrB,mBAAmB,MACnB,MAAM,QAAQ,CAAC,gBACf,kBAAkB,MAAM,QAAQ,CAAC,wBACnC;IAGF,MAAM,QAAQ;QACZ,WAAW;QACX,UAAU;YACR;gBAAE,OAAO;YAAqB;YAC9B;gBAAE,OAAO;YAAwB;SAClC;IAEH;IAEA,oDAAoD;IACpD,oCAAoC;IACpC,MAAM,oBAAoB;IAC1B,MAAM,UAAU;QACd,WAAW;QACX,UAAU;YACR;gBACE,OAAO;gBACP,KAAK;gBACL,SAAS;gBACT,UAAU;oBAAE,KAAK,gBAAgB;iBAAE;YACrC;YACA;gBACE,OAAO,kBAAkB,oBAAoB;gBAC7C,KAAK;gBACL,SAAS;YACX;YACA,KAAK,iBAAiB,CAAC;gBACrB,OAAO;gBACP,KAAK;YACP;SACD;IACH;IAEA,MAAM,UAAU;QACd,WAAW;QACX,UAAU;YACR;gBAAE,OAAO;YAAe;YACxB;gBAAE,OAAO;YAAkF;YAC3F;gBAAE,OAAO;YAAmH;YAC5H;gBAAE,OAAO;YAAyD;SACrE;QACC,WAAW;IACb;IAEA,MAAM,eAAe;QACnB,WAAW;QACX,OAAO;QACP,KAAK;QACL,UAAU;YAAE,SACR,wDACE;QAAuD;QAC7D,UAAU;YACR;gBACE,OAAO;gBACP,WAAW;YACb;YACA,KAAK,OAAO,CAAC,SAAS;gBAAE,WAAW;YAAS;YAC5C;gBACE,WAAW;gBACX,OAAO;YACT;YACA;YACA,KAAK,oBAAoB;SAC1B;IACH;IAEA,MAAM,aAAa;QACjB,WAAW;QACX,OAAO,MAAM,QAAQ,CAAC,gBAAgB,KAAK,QAAQ;QACnD,WAAW;IACb;IAEA,MAAM,iBAAiB,MAAM,QAAQ,CAAC,gBAAgB,KAAK,QAAQ,GAAG;IAEtE,MAAM,aAAa;QACjB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,UAAU;QACV;QACA;QACA;QACA;QACA;QACA,kFAAkF;QAClF;KACD;IAED,MAAM,UAAU;QACd;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,YAAY;QACZ;QACA;QACA;QACA,UAAU;QACV;QACA;QACA;KACD;IAED,MAAM,WAAW;QACf,SAAS;QACT,MAAM;QACN,SAAS;QACT,8DAA8D;QAC9D,UAAU,wGACN,gGACA,4HACA,4FACA,mGACA,uGACA,0FACA,0FACA;IACN;IAEA,MAAM,sBAAsB;QAC1B;QACA;QACA;QACA,KAAK,oBAAoB;QACzB;QACA;KACD;IAED,MAAM,qBAAqB;QACzB,uEAAuE;QACvE,mEAAmE;QACnE,gDAAgD;QAChD,UAAU;YACR;gBACE,OAAO;gBACP,KAAK;YACP;YACA;gBACE,OAAO;gBACP,KAAK;YACP;YACA;gBACE,eAAe;gBACf,KAAK;YACP;SACD;QACD,UAAU;QACV,UAAU,oBAAoB,MAAM,CAAC;YACnC;gBACE,OAAO;gBACP,KAAK;gBACL,UAAU;gBACV,UAAU,oBAAoB,MAAM,CAAC;oBAAE;iBAAQ;gBAC/C,WAAW;YACb;SACD;QACD,WAAW;IACb;IAEA,MAAM,uBAAuB;QAC3B,OAAO,MAAM,mBAAmB,iBAAiB;QACjD,aAAa;QACb,KAAK;QACL,YAAY;QACZ,UAAU;QACV,SAAS;QACT,UAAU;YACR;gBACE,OAAO;gBACP,UAAU;gBACV,WAAW;YACb;YACA;gBACE,OAAO;gBACP,aAAa;gBACb,UAAU;oBAAE,KAAK,OAAO,CAAC,YAAY;wBAAE,WAAW;oBAAiB;iBAAI;gBACvE,WAAW;YACb;YACA,yCAAyC;YACzC,+BAA+B;YAC/B;gBACE,WAAW;gBACX,OAAO;YACT;YACA;gBACE,WAAW;gBACX,OAAO;gBACP,KAAK;gBACL,UAAU;gBACV,WAAW;gBACX,UAAU;oBACR;oBACA,KAAK,oBAAoB;oBACzB;oBACA;oBACA;oBACA,8BAA8B;oBAC9B;wBACE,OAAO;wBACP,KAAK;wBACL,UAAU;wBACV,WAAW;wBACX,UAAU;4BACR;4BACA;4BACA,KAAK,oBAAoB;4BACzB;4BACA;4BACA;yBACD;oBACH;iBACD;YACH;YACA;YACA;YACA,KAAK,oBAAoB;YACzB;SACD;IACH;IAEA,OAAO;QACL,MAAM;QACN,SAAS;YAAE;SAAK;QAChB,UAAU;QACV,mEAAmE;QACnE,wEAAwE;QACxE,mBAAmB;QACnB,SAAS;QACT,UAAU,EAAE,CAAC,MAAM,CACjB,oBACA,sBACA,qBACA;YACE;YACA;gBACE,OAAO,KAAK,QAAQ,GAAG;gBACvB,UAAU;YACZ;YACA;gBACE,WAAW;gBACX,eAAe;gBACf,KAAK;gBACL,UAAU;oBACR;wBAAE,eAAe;oBAAqB;oBACtC,KAAK,UAAU;iBAChB;YACH;SACD;QACH,SAAS;YACP,cAAc;YACd,SAAS;YACT,UAAU;QACZ;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1730, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/cpp.js"], "sourcesContent": ["/*\nLanguage: C++\nCategory: common, system\nWebsite: https://isocpp.org\n*/\n\n/** @type LanguageFn */\nfunction cpp(hljs) {\n  const regex = hljs.regex;\n  // added for historic reasons because `hljs.C_LINE_COMMENT_MODE` does\n  // not include such support nor can we be sure all the grammars depending\n  // on it would desire this behavior\n  const C_LINE_COMMENT_MODE = hljs.COMMENT('//', '$', { contains: [ { begin: /\\\\\\n/ } ] });\n  const DECLTYPE_AUTO_RE = 'decltype\\\\(auto\\\\)';\n  const NAMESPACE_RE = '[a-zA-Z_]\\\\w*::';\n  const TEMPLATE_ARGUMENT_RE = '<[^<>]+>';\n  const FUNCTION_TYPE_RE = '(?!struct)('\n    + DECLTYPE_AUTO_RE + '|'\n    + regex.optional(NAMESPACE_RE)\n    + '[a-zA-Z_]\\\\w*' + regex.optional(TEMPLATE_ARGUMENT_RE)\n  + ')';\n\n  const CPP_PRIMITIVE_TYPES = {\n    className: 'type',\n    begin: '\\\\b[a-z\\\\d_]*_t\\\\b'\n  };\n\n  // https://en.cppreference.com/w/cpp/language/escape\n  // \\\\ \\x \\xFF \\u2837 \\u00323747 \\374\n  const CHARACTER_ESCAPES = '\\\\\\\\(x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4,8}|[0-7]{3}|\\\\S)';\n  const STRINGS = {\n    className: 'string',\n    variants: [\n      {\n        begin: '(u8?|U|L)?\"',\n        end: '\"',\n        illegal: '\\\\n',\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      },\n      {\n        begin: '(u8?|U|L)?\\'(' + CHARACTER_ESCAPES + '|.)',\n        end: '\\'',\n        illegal: '.'\n      },\n      hljs.END_SAME_AS_BEGIN({\n        begin: /(?:u8?|U|L)?R\"([^()\\\\ ]{0,16})\\(/,\n        end: /\\)([^()\\\\ ]{0,16})\"/\n      })\n    ]\n  };\n\n  const NUMBERS = {\n    className: 'number',\n    variants: [\n      // Floating-point literal.\n      { begin:\n        \"[+-]?(?:\" // Leading sign.\n          // Decimal.\n          + \"(?:\"\n            +\"[0-9](?:'?[0-9])*\\\\.(?:[0-9](?:'?[0-9])*)?\"\n            + \"|\\\\.[0-9](?:'?[0-9])*\"\n          + \")(?:[Ee][+-]?[0-9](?:'?[0-9])*)?\"\n          + \"|[0-9](?:'?[0-9])*[Ee][+-]?[0-9](?:'?[0-9])*\"\n          // Hexadecimal.\n          + \"|0[Xx](?:\"\n            +\"[0-9A-Fa-f](?:'?[0-9A-Fa-f])*(?:\\\\.(?:[0-9A-Fa-f](?:'?[0-9A-Fa-f])*)?)?\"\n            + \"|\\\\.[0-9A-Fa-f](?:'?[0-9A-Fa-f])*\"\n          + \")[Pp][+-]?[0-9](?:'?[0-9])*\"\n        + \")(?:\" // Literal suffixes.\n          + \"[Ff](?:16|32|64|128)?\"\n          + \"|(BF|bf)16\"\n          + \"|[Ll]\"\n          + \"|\" // Literal suffix is optional.\n        + \")\"\n      },\n      // Integer literal.\n      { begin:\n        \"[+-]?\\\\b(?:\" // Leading sign.\n          + \"0[Bb][01](?:'?[01])*\" // Binary.\n          + \"|0[Xx][0-9A-Fa-f](?:'?[0-9A-Fa-f])*\" // Hexadecimal.\n          + \"|0(?:'?[0-7])*\" // Octal or just a lone zero.\n          + \"|[1-9](?:'?[0-9])*\" // Decimal.\n        + \")(?:\" // Literal suffixes.\n          + \"[Uu](?:LL?|ll?)\"\n          + \"|[Uu][Zz]?\"\n          + \"|(?:LL?|ll?)[Uu]?\"\n          + \"|[Zz][Uu]\"\n          + \"|\" // Literal suffix is optional.\n        + \")\"\n        // Note: there are user-defined literal suffixes too, but perhaps having the custom suffix not part of the\n        // literal highlight actually makes it stand out more.\n      }\n    ],\n    relevance: 0\n  };\n\n  const PREPROCESSOR = {\n    className: 'meta',\n    begin: /#\\s*[a-z]+\\b/,\n    end: /$/,\n    keywords: { keyword:\n        'if else elif endif define undef warning error line '\n        + 'pragma _Pragma ifdef ifndef include' },\n    contains: [\n      {\n        begin: /\\\\\\n/,\n        relevance: 0\n      },\n      hljs.inherit(STRINGS, { className: 'string' }),\n      {\n        className: 'string',\n        begin: /<.*?>/\n      },\n      C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE\n    ]\n  };\n\n  const TITLE_MODE = {\n    className: 'title',\n    begin: regex.optional(NAMESPACE_RE) + hljs.IDENT_RE,\n    relevance: 0\n  };\n\n  const FUNCTION_TITLE = regex.optional(NAMESPACE_RE) + hljs.IDENT_RE + '\\\\s*\\\\(';\n\n  // https://en.cppreference.com/w/cpp/keyword\n  const RESERVED_KEYWORDS = [\n    'alignas',\n    'alignof',\n    'and',\n    'and_eq',\n    'asm',\n    'atomic_cancel',\n    'atomic_commit',\n    'atomic_noexcept',\n    'auto',\n    'bitand',\n    'bitor',\n    'break',\n    'case',\n    'catch',\n    'class',\n    'co_await',\n    'co_return',\n    'co_yield',\n    'compl',\n    'concept',\n    'const_cast|10',\n    'consteval',\n    'constexpr',\n    'constinit',\n    'continue',\n    'decltype',\n    'default',\n    'delete',\n    'do',\n    'dynamic_cast|10',\n    'else',\n    'enum',\n    'explicit',\n    'export',\n    'extern',\n    'false',\n    'final',\n    'for',\n    'friend',\n    'goto',\n    'if',\n    'import',\n    'inline',\n    'module',\n    'mutable',\n    'namespace',\n    'new',\n    'noexcept',\n    'not',\n    'not_eq',\n    'nullptr',\n    'operator',\n    'or',\n    'or_eq',\n    'override',\n    'private',\n    'protected',\n    'public',\n    'reflexpr',\n    'register',\n    'reinterpret_cast|10',\n    'requires',\n    'return',\n    'sizeof',\n    'static_assert',\n    'static_cast|10',\n    'struct',\n    'switch',\n    'synchronized',\n    'template',\n    'this',\n    'thread_local',\n    'throw',\n    'transaction_safe',\n    'transaction_safe_dynamic',\n    'true',\n    'try',\n    'typedef',\n    'typeid',\n    'typename',\n    'union',\n    'using',\n    'virtual',\n    'volatile',\n    'while',\n    'xor',\n    'xor_eq'\n  ];\n\n  // https://en.cppreference.com/w/cpp/keyword\n  const RESERVED_TYPES = [\n    'bool',\n    'char',\n    'char16_t',\n    'char32_t',\n    'char8_t',\n    'double',\n    'float',\n    'int',\n    'long',\n    'short',\n    'void',\n    'wchar_t',\n    'unsigned',\n    'signed',\n    'const',\n    'static'\n  ];\n\n  const TYPE_HINTS = [\n    'any',\n    'auto_ptr',\n    'barrier',\n    'binary_semaphore',\n    'bitset',\n    'complex',\n    'condition_variable',\n    'condition_variable_any',\n    'counting_semaphore',\n    'deque',\n    'false_type',\n    'flat_map',\n    'flat_set',\n    'future',\n    'imaginary',\n    'initializer_list',\n    'istringstream',\n    'jthread',\n    'latch',\n    'lock_guard',\n    'multimap',\n    'multiset',\n    'mutex',\n    'optional',\n    'ostringstream',\n    'packaged_task',\n    'pair',\n    'promise',\n    'priority_queue',\n    'queue',\n    'recursive_mutex',\n    'recursive_timed_mutex',\n    'scoped_lock',\n    'set',\n    'shared_future',\n    'shared_lock',\n    'shared_mutex',\n    'shared_timed_mutex',\n    'shared_ptr',\n    'stack',\n    'string_view',\n    'stringstream',\n    'timed_mutex',\n    'thread',\n    'true_type',\n    'tuple',\n    'unique_lock',\n    'unique_ptr',\n    'unordered_map',\n    'unordered_multimap',\n    'unordered_multiset',\n    'unordered_set',\n    'variant',\n    'vector',\n    'weak_ptr',\n    'wstring',\n    'wstring_view'\n  ];\n\n  const FUNCTION_HINTS = [\n    'abort',\n    'abs',\n    'acos',\n    'apply',\n    'as_const',\n    'asin',\n    'atan',\n    'atan2',\n    'calloc',\n    'ceil',\n    'cerr',\n    'cin',\n    'clog',\n    'cos',\n    'cosh',\n    'cout',\n    'declval',\n    'endl',\n    'exchange',\n    'exit',\n    'exp',\n    'fabs',\n    'floor',\n    'fmod',\n    'forward',\n    'fprintf',\n    'fputs',\n    'free',\n    'frexp',\n    'fscanf',\n    'future',\n    'invoke',\n    'isalnum',\n    'isalpha',\n    'iscntrl',\n    'isdigit',\n    'isgraph',\n    'islower',\n    'isprint',\n    'ispunct',\n    'isspace',\n    'isupper',\n    'isxdigit',\n    'labs',\n    'launder',\n    'ldexp',\n    'log',\n    'log10',\n    'make_pair',\n    'make_shared',\n    'make_shared_for_overwrite',\n    'make_tuple',\n    'make_unique',\n    'malloc',\n    'memchr',\n    'memcmp',\n    'memcpy',\n    'memset',\n    'modf',\n    'move',\n    'pow',\n    'printf',\n    'putchar',\n    'puts',\n    'realloc',\n    'scanf',\n    'sin',\n    'sinh',\n    'snprintf',\n    'sprintf',\n    'sqrt',\n    'sscanf',\n    'std',\n    'stderr',\n    'stdin',\n    'stdout',\n    'strcat',\n    'strchr',\n    'strcmp',\n    'strcpy',\n    'strcspn',\n    'strlen',\n    'strncat',\n    'strncmp',\n    'strncpy',\n    'strpbrk',\n    'strrchr',\n    'strspn',\n    'strstr',\n    'swap',\n    'tan',\n    'tanh',\n    'terminate',\n    'to_underlying',\n    'tolower',\n    'toupper',\n    'vfprintf',\n    'visit',\n    'vprintf',\n    'vsprintf'\n  ];\n\n  const LITERALS = [\n    'NULL',\n    'false',\n    'nullopt',\n    'nullptr',\n    'true'\n  ];\n\n  // https://en.cppreference.com/w/cpp/keyword\n  const BUILT_IN = [ '_Pragma' ];\n\n  const CPP_KEYWORDS = {\n    type: RESERVED_TYPES,\n    keyword: RESERVED_KEYWORDS,\n    literal: LITERALS,\n    built_in: BUILT_IN,\n    _type_hints: TYPE_HINTS\n  };\n\n  const FUNCTION_DISPATCH = {\n    className: 'function.dispatch',\n    relevance: 0,\n    keywords: {\n      // Only for relevance, not highlighting.\n      _hint: FUNCTION_HINTS },\n    begin: regex.concat(\n      /\\b/,\n      /(?!decltype)/,\n      /(?!if)/,\n      /(?!for)/,\n      /(?!switch)/,\n      /(?!while)/,\n      hljs.IDENT_RE,\n      regex.lookahead(/(<[^<>]+>|)\\s*\\(/))\n  };\n\n  const EXPRESSION_CONTAINS = [\n    FUNCTION_DISPATCH,\n    PREPROCESSOR,\n    CPP_PRIMITIVE_TYPES,\n    C_LINE_COMMENT_MODE,\n    hljs.C_BLOCK_COMMENT_MODE,\n    NUMBERS,\n    STRINGS\n  ];\n\n  const EXPRESSION_CONTEXT = {\n    // This mode covers expression context where we can't expect a function\n    // definition and shouldn't highlight anything that looks like one:\n    // `return some()`, `else if()`, `(x*sum(1, 2))`\n    variants: [\n      {\n        begin: /=/,\n        end: /;/\n      },\n      {\n        begin: /\\(/,\n        end: /\\)/\n      },\n      {\n        beginKeywords: 'new throw return else',\n        end: /;/\n      }\n    ],\n    keywords: CPP_KEYWORDS,\n    contains: EXPRESSION_CONTAINS.concat([\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: CPP_KEYWORDS,\n        contains: EXPRESSION_CONTAINS.concat([ 'self' ]),\n        relevance: 0\n      }\n    ]),\n    relevance: 0\n  };\n\n  const FUNCTION_DECLARATION = {\n    className: 'function',\n    begin: '(' + FUNCTION_TYPE_RE + '[\\\\*&\\\\s]+)+' + FUNCTION_TITLE,\n    returnBegin: true,\n    end: /[{;=]/,\n    excludeEnd: true,\n    keywords: CPP_KEYWORDS,\n    illegal: /[^\\w\\s\\*&:<>.]/,\n    contains: [\n      { // to prevent it from being confused as the function title\n        begin: DECLTYPE_AUTO_RE,\n        keywords: CPP_KEYWORDS,\n        relevance: 0\n      },\n      {\n        begin: FUNCTION_TITLE,\n        returnBegin: true,\n        contains: [ TITLE_MODE ],\n        relevance: 0\n      },\n      // needed because we do not have look-behind on the below rule\n      // to prevent it from grabbing the final : in a :: pair\n      {\n        begin: /::/,\n        relevance: 0\n      },\n      // initializers\n      {\n        begin: /:/,\n        endsWithParent: true,\n        contains: [\n          STRINGS,\n          NUMBERS\n        ]\n      },\n      // allow for multiple declarations, e.g.:\n      // extern void f(int), g(char);\n      {\n        relevance: 0,\n        match: /,/\n      },\n      {\n        className: 'params',\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: CPP_KEYWORDS,\n        relevance: 0,\n        contains: [\n          C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE,\n          STRINGS,\n          NUMBERS,\n          CPP_PRIMITIVE_TYPES,\n          // Count matching parentheses.\n          {\n            begin: /\\(/,\n            end: /\\)/,\n            keywords: CPP_KEYWORDS,\n            relevance: 0,\n            contains: [\n              'self',\n              C_LINE_COMMENT_MODE,\n              hljs.C_BLOCK_COMMENT_MODE,\n              STRINGS,\n              NUMBERS,\n              CPP_PRIMITIVE_TYPES\n            ]\n          }\n        ]\n      },\n      CPP_PRIMITIVE_TYPES,\n      C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      PREPROCESSOR\n    ]\n  };\n\n  return {\n    name: 'C++',\n    aliases: [\n      'cc',\n      'c++',\n      'h++',\n      'hpp',\n      'hh',\n      'hxx',\n      'cxx'\n    ],\n    keywords: CPP_KEYWORDS,\n    illegal: '</',\n    classNameAliases: { 'function.dispatch': 'built_in' },\n    contains: [].concat(\n      EXPRESSION_CONTEXT,\n      FUNCTION_DECLARATION,\n      FUNCTION_DISPATCH,\n      EXPRESSION_CONTAINS,\n      [\n        PREPROCESSOR,\n        { // containers: ie, `vector <int> rooms (9);`\n          begin: '\\\\b(deque|list|queue|priority_queue|pair|stack|vector|map|set|bitset|multiset|multimap|unordered_map|unordered_set|unordered_multiset|unordered_multimap|array|tuple|optional|variant|function|flat_map|flat_set)\\\\s*<(?!<)',\n          end: '>',\n          keywords: CPP_KEYWORDS,\n          contains: [\n            'self',\n            CPP_PRIMITIVE_TYPES\n          ]\n        },\n        {\n          begin: hljs.IDENT_RE + '::',\n          keywords: CPP_KEYWORDS\n        },\n        {\n          match: [\n            // extra complexity to deal with `enum class` and `enum struct`\n            /\\b(?:enum(?:\\s+(?:class|struct))?|class|struct|union)/,\n            /\\s+/,\n            /\\w+/\n          ],\n          className: {\n            1: 'keyword',\n            3: 'title.class'\n          }\n        }\n      ])\n  };\n}\n\nexport { cpp as default };\n"], "names": [], "mappings": "AAAA;;;;AAIA,GAEA,qBAAqB;;;AACrB,SAAS,IAAI,IAAI;IACf,MAAM,QAAQ,KAAK,KAAK;IACxB,qEAAqE;IACrE,yEAAyE;IACzE,mCAAmC;IACnC,MAAM,sBAAsB,KAAK,OAAO,CAAC,MAAM,KAAK;QAAE,UAAU;YAAE;gBAAE,OAAO;YAAO;SAAG;IAAC;IACtF,MAAM,mBAAmB;IACzB,MAAM,eAAe;IACrB,MAAM,uBAAuB;IAC7B,MAAM,mBAAmB,gBACrB,mBAAmB,MACnB,MAAM,QAAQ,CAAC,gBACf,kBAAkB,MAAM,QAAQ,CAAC,wBACnC;IAEF,MAAM,sBAAsB;QAC1B,WAAW;QACX,OAAO;IACT;IAEA,oDAAoD;IACpD,oCAAoC;IACpC,MAAM,oBAAoB;IAC1B,MAAM,UAAU;QACd,WAAW;QACX,UAAU;YACR;gBACE,OAAO;gBACP,KAAK;gBACL,SAAS;gBACT,UAAU;oBAAE,KAAK,gBAAgB;iBAAE;YACrC;YACA;gBACE,OAAO,kBAAkB,oBAAoB;gBAC7C,KAAK;gBACL,SAAS;YACX;YACA,KAAK,iBAAiB,CAAC;gBACrB,OAAO;gBACP,KAAK;YACP;SACD;IACH;IAEA,MAAM,UAAU;QACd,WAAW;QACX,UAAU;YACR,0BAA0B;YAC1B;gBAAE,OACA,WAAW,gBAAgB;mBAEvB,QACC,+CACC,0BACF,qCACA,iDAEA,cACC,4EACC,sCACF,gCACF,OAAO,oBAAoB;mBACzB,0BACA,eACA,UACA,IAAI,8BAA8B;mBACpC;YACJ;YACA,mBAAmB;YACnB;gBAAE,OACA,cAAc,gBAAgB;mBAC1B,uBAAuB,UAAU;mBACjC,sCAAsC,eAAe;mBACrD,iBAAiB,6BAA6B;mBAC9C,qBAAqB,WAAW;mBAClC,OAAO,oBAAoB;mBACzB,oBACA,eACA,sBACA,cACA,IAAI,8BAA8B;mBACpC;YAGJ;SACD;QACD,WAAW;IACb;IAEA,MAAM,eAAe;QACnB,WAAW;QACX,OAAO;QACP,KAAK;QACL,UAAU;YAAE,SACR,wDACE;QAAsC;QAC5C,UAAU;YACR;gBACE,OAAO;gBACP,WAAW;YACb;YACA,KAAK,OAAO,CAAC,SAAS;gBAAE,WAAW;YAAS;YAC5C;gBACE,WAAW;gBACX,OAAO;YACT;YACA;YACA,KAAK,oBAAoB;SAC1B;IACH;IAEA,MAAM,aAAa;QACjB,WAAW;QACX,OAAO,MAAM,QAAQ,CAAC,gBAAgB,KAAK,QAAQ;QACnD,WAAW;IACb;IAEA,MAAM,iBAAiB,MAAM,QAAQ,CAAC,gBAAgB,KAAK,QAAQ,GAAG;IAEtE,4CAA4C;IAC5C,MAAM,oBAAoB;QACxB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,4CAA4C;IAC5C,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,aAAa;QACjB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;KACD;IAED,4CAA4C;IAC5C,MAAM,WAAW;QAAE;KAAW;IAE9B,MAAM,eAAe;QACnB,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;QACV,aAAa;IACf;IAEA,MAAM,oBAAoB;QACxB,WAAW;QACX,WAAW;QACX,UAAU;YACR,wCAAwC;YACxC,OAAO;QAAe;QACxB,OAAO,MAAM,MAAM,CACjB,MACA,gBACA,UACA,WACA,cACA,aACA,KAAK,QAAQ,EACb,MAAM,SAAS,CAAC;IACpB;IAEA,MAAM,sBAAsB;QAC1B;QACA;QACA;QACA;QACA,KAAK,oBAAoB;QACzB;QACA;KACD;IAED,MAAM,qBAAqB;QACzB,uEAAuE;QACvE,mEAAmE;QACnE,gDAAgD;QAChD,UAAU;YACR;gBACE,OAAO;gBACP,KAAK;YACP;YACA;gBACE,OAAO;gBACP,KAAK;YACP;YACA;gBACE,eAAe;gBACf,KAAK;YACP;SACD;QACD,UAAU;QACV,UAAU,oBAAoB,MAAM,CAAC;YACnC;gBACE,OAAO;gBACP,KAAK;gBACL,UAAU;gBACV,UAAU,oBAAoB,MAAM,CAAC;oBAAE;iBAAQ;gBAC/C,WAAW;YACb;SACD;QACD,WAAW;IACb;IAEA,MAAM,uBAAuB;QAC3B,WAAW;QACX,OAAO,MAAM,mBAAmB,iBAAiB;QACjD,aAAa;QACb,KAAK;QACL,YAAY;QACZ,UAAU;QACV,SAAS;QACT,UAAU;YACR;gBACE,OAAO;gBACP,UAAU;gBACV,WAAW;YACb;YACA;gBACE,OAAO;gBACP,aAAa;gBACb,UAAU;oBAAE;iBAAY;gBACxB,WAAW;YACb;YACA,8DAA8D;YAC9D,uDAAuD;YACvD;gBACE,OAAO;gBACP,WAAW;YACb;YACA,eAAe;YACf;gBACE,OAAO;gBACP,gBAAgB;gBAChB,UAAU;oBACR;oBACA;iBACD;YACH;YACA,yCAAyC;YACzC,+BAA+B;YAC/B;gBACE,WAAW;gBACX,OAAO;YACT;YACA;gBACE,WAAW;gBACX,OAAO;gBACP,KAAK;gBACL,UAAU;gBACV,WAAW;gBACX,UAAU;oBACR;oBACA,KAAK,oBAAoB;oBACzB;oBACA;oBACA;oBACA,8BAA8B;oBAC9B;wBACE,OAAO;wBACP,KAAK;wBACL,UAAU;wBACV,WAAW;wBACX,UAAU;4BACR;4BACA;4BACA,KAAK,oBAAoB;4BACzB;4BACA;4BACA;yBACD;oBACH;iBACD;YACH;YACA;YACA;YACA,KAAK,oBAAoB;YACzB;SACD;IACH;IAEA,OAAO;QACL,MAAM;QACN,SAAS;YACP;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,SAAS;QACT,kBAAkB;YAAE,qBAAqB;QAAW;QACpD,UAAU,EAAE,CAAC,MAAM,CACjB,oBACA,sBACA,mBACA,qBACA;YACE;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,UAAU;gBACV,UAAU;oBACR;oBACA;iBACD;YACH;YACA;gBACE,OAAO,KAAK,QAAQ,GAAG;gBACvB,UAAU;YACZ;YACA;gBACE,OAAO;oBACL,+DAA+D;oBAC/D;oBACA;oBACA;iBACD;gBACD,WAAW;oBACT,GAAG;oBACH,GAAG;gBACL;YACF;SACD;IACL;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2304, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/csharp.js"], "sourcesContent": ["/*\nLanguage: C#\nAuthor: <PERSON> <<EMAIL>>\nContributor: <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>\nWebsite: https://docs.microsoft.com/dotnet/csharp/\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction csharp(hljs) {\n  const BUILT_IN_KEYWORDS = [\n    'bool',\n    'byte',\n    'char',\n    'decimal',\n    'delegate',\n    'double',\n    'dynamic',\n    'enum',\n    'float',\n    'int',\n    'long',\n    'nint',\n    'nuint',\n    'object',\n    'sbyte',\n    'short',\n    'string',\n    'ulong',\n    'uint',\n    'ushort'\n  ];\n  const FUNCTION_MODIFIERS = [\n    'public',\n    'private',\n    'protected',\n    'static',\n    'internal',\n    'protected',\n    'abstract',\n    'async',\n    'extern',\n    'override',\n    'unsafe',\n    'virtual',\n    'new',\n    'sealed',\n    'partial'\n  ];\n  const LITERAL_KEYWORDS = [\n    'default',\n    'false',\n    'null',\n    'true'\n  ];\n  const NORMAL_KEYWORDS = [\n    'abstract',\n    'as',\n    'base',\n    'break',\n    'case',\n    'catch',\n    'class',\n    'const',\n    'continue',\n    'do',\n    'else',\n    'event',\n    'explicit',\n    'extern',\n    'finally',\n    'fixed',\n    'for',\n    'foreach',\n    'goto',\n    'if',\n    'implicit',\n    'in',\n    'interface',\n    'internal',\n    'is',\n    'lock',\n    'namespace',\n    'new',\n    'operator',\n    'out',\n    'override',\n    'params',\n    'private',\n    'protected',\n    'public',\n    'readonly',\n    'record',\n    'ref',\n    'return',\n    'scoped',\n    'sealed',\n    'sizeof',\n    'stackalloc',\n    'static',\n    'struct',\n    'switch',\n    'this',\n    'throw',\n    'try',\n    'typeof',\n    'unchecked',\n    'unsafe',\n    'using',\n    'virtual',\n    'void',\n    'volatile',\n    'while'\n  ];\n  const CONTEXTUAL_KEYWORDS = [\n    'add',\n    'alias',\n    'and',\n    'ascending',\n    'args',\n    'async',\n    'await',\n    'by',\n    'descending',\n    'dynamic',\n    'equals',\n    'file',\n    'from',\n    'get',\n    'global',\n    'group',\n    'init',\n    'into',\n    'join',\n    'let',\n    'nameof',\n    'not',\n    'notnull',\n    'on',\n    'or',\n    'orderby',\n    'partial',\n    'record',\n    'remove',\n    'required',\n    'scoped',\n    'select',\n    'set',\n    'unmanaged',\n    'value|0',\n    'var',\n    'when',\n    'where',\n    'with',\n    'yield'\n  ];\n\n  const KEYWORDS = {\n    keyword: NORMAL_KEYWORDS.concat(CONTEXTUAL_KEYWORDS),\n    built_in: BUILT_IN_KEYWORDS,\n    literal: LITERAL_KEYWORDS\n  };\n  const TITLE_MODE = hljs.inherit(hljs.TITLE_MODE, { begin: '[a-zA-Z](\\\\.?\\\\w)*' });\n  const NUMBERS = {\n    className: 'number',\n    variants: [\n      { begin: '\\\\b(0b[01\\']+)' },\n      { begin: '(-?)\\\\b([\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)(u|U|l|L|ul|UL|f|F|b|B)' },\n      { begin: '(-?)(\\\\b0[xX][a-fA-F0-9\\']+|(\\\\b[\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)([eE][-+]?[\\\\d\\']+)?)' }\n    ],\n    relevance: 0\n  };\n  const RAW_STRING = {\n    className: 'string',\n    begin: /\"\"\"(\"*)(?!\")(.|\\n)*?\"\"\"\\1/,\n    relevance: 1\n  };\n  const VERBATIM_STRING = {\n    className: 'string',\n    begin: '@\"',\n    end: '\"',\n    contains: [ { begin: '\"\"' } ]\n  };\n  const VERBATIM_STRING_NO_LF = hljs.inherit(VERBATIM_STRING, { illegal: /\\n/ });\n  const SUBST = {\n    className: 'subst',\n    begin: /\\{/,\n    end: /\\}/,\n    keywords: KEYWORDS\n  };\n  const SUBST_NO_LF = hljs.inherit(SUBST, { illegal: /\\n/ });\n  const INTERPOLATED_STRING = {\n    className: 'string',\n    begin: /\\$\"/,\n    end: '\"',\n    illegal: /\\n/,\n    contains: [\n      { begin: /\\{\\{/ },\n      { begin: /\\}\\}/ },\n      hljs.BACKSLASH_ESCAPE,\n      SUBST_NO_LF\n    ]\n  };\n  const INTERPOLATED_VERBATIM_STRING = {\n    className: 'string',\n    begin: /\\$@\"/,\n    end: '\"',\n    contains: [\n      { begin: /\\{\\{/ },\n      { begin: /\\}\\}/ },\n      { begin: '\"\"' },\n      SUBST\n    ]\n  };\n  const INTERPOLATED_VERBATIM_STRING_NO_LF = hljs.inherit(INTERPOLATED_VERBATIM_STRING, {\n    illegal: /\\n/,\n    contains: [\n      { begin: /\\{\\{/ },\n      { begin: /\\}\\}/ },\n      { begin: '\"\"' },\n      SUBST_NO_LF\n    ]\n  });\n  SUBST.contains = [\n    INTERPOLATED_VERBATIM_STRING,\n    INTERPOLATED_STRING,\n    VERBATIM_STRING,\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE,\n    NUMBERS,\n    hljs.C_BLOCK_COMMENT_MODE\n  ];\n  SUBST_NO_LF.contains = [\n    INTERPOLATED_VERBATIM_STRING_NO_LF,\n    INTERPOLATED_STRING,\n    VERBATIM_STRING_NO_LF,\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE,\n    NUMBERS,\n    hljs.inherit(hljs.C_BLOCK_COMMENT_MODE, { illegal: /\\n/ })\n  ];\n  const STRING = { variants: [\n    RAW_STRING,\n    INTERPOLATED_VERBATIM_STRING,\n    INTERPOLATED_STRING,\n    VERBATIM_STRING,\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE\n  ] };\n\n  const GENERIC_MODIFIER = {\n    begin: \"<\",\n    end: \">\",\n    contains: [\n      { beginKeywords: \"in out\" },\n      TITLE_MODE\n    ]\n  };\n  const TYPE_IDENT_RE = hljs.IDENT_RE + '(<' + hljs.IDENT_RE + '(\\\\s*,\\\\s*' + hljs.IDENT_RE + ')*>)?(\\\\[\\\\])?';\n  const AT_IDENTIFIER = {\n    // prevents expressions like `@class` from incorrect flagging\n    // `class` as a keyword\n    begin: \"@\" + hljs.IDENT_RE,\n    relevance: 0\n  };\n\n  return {\n    name: 'C#',\n    aliases: [\n      'cs',\n      'c#'\n    ],\n    keywords: KEYWORDS,\n    illegal: /::/,\n    contains: [\n      hljs.COMMENT(\n        '///',\n        '$',\n        {\n          returnBegin: true,\n          contains: [\n            {\n              className: 'doctag',\n              variants: [\n                {\n                  begin: '///',\n                  relevance: 0\n                },\n                { begin: '<!--|-->' },\n                {\n                  begin: '</?',\n                  end: '>'\n                }\n              ]\n            }\n          ]\n        }\n      ),\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        className: 'meta',\n        begin: '#',\n        end: '$',\n        keywords: { keyword: 'if else elif endif define undef warning error line region endregion pragma checksum' }\n      },\n      STRING,\n      NUMBERS,\n      {\n        beginKeywords: 'class interface',\n        relevance: 0,\n        end: /[{;=]/,\n        illegal: /[^\\s:,]/,\n        contains: [\n          { beginKeywords: \"where class\" },\n          TITLE_MODE,\n          GENERIC_MODIFIER,\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        beginKeywords: 'namespace',\n        relevance: 0,\n        end: /[{;=]/,\n        illegal: /[^\\s:]/,\n        contains: [\n          TITLE_MODE,\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        beginKeywords: 'record',\n        relevance: 0,\n        end: /[{;=]/,\n        illegal: /[^\\s:]/,\n        contains: [\n          TITLE_MODE,\n          GENERIC_MODIFIER,\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        // [Attributes(\"\")]\n        className: 'meta',\n        begin: '^\\\\s*\\\\[(?=[\\\\w])',\n        excludeBegin: true,\n        end: '\\\\]',\n        excludeEnd: true,\n        contains: [\n          {\n            className: 'string',\n            begin: /\"/,\n            end: /\"/\n          }\n        ]\n      },\n      {\n        // Expression keywords prevent 'keyword Name(...)' from being\n        // recognized as a function definition\n        beginKeywords: 'new return throw await else',\n        relevance: 0\n      },\n      {\n        className: 'function',\n        begin: '(' + TYPE_IDENT_RE + '\\\\s+)+' + hljs.IDENT_RE + '\\\\s*(<[^=]+>\\\\s*)?\\\\(',\n        returnBegin: true,\n        end: /\\s*[{;=]/,\n        excludeEnd: true,\n        keywords: KEYWORDS,\n        contains: [\n          // prevents these from being highlighted `title`\n          {\n            beginKeywords: FUNCTION_MODIFIERS.join(\" \"),\n            relevance: 0\n          },\n          {\n            begin: hljs.IDENT_RE + '\\\\s*(<[^=]+>\\\\s*)?\\\\(',\n            returnBegin: true,\n            contains: [\n              hljs.TITLE_MODE,\n              GENERIC_MODIFIER\n            ],\n            relevance: 0\n          },\n          { match: /\\(\\)/ },\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            excludeBegin: true,\n            excludeEnd: true,\n            keywords: KEYWORDS,\n            relevance: 0,\n            contains: [\n              STRING,\n              NUMBERS,\n              hljs.C_BLOCK_COMMENT_MODE\n            ]\n          },\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      AT_IDENTIFIER\n    ]\n  };\n}\n\nexport { csharp as default };\n"], "names": [], "mappings": "AAAA;;;;;;AAMA,GAEA,qBAAqB;;;AACrB,SAAS,OAAO,IAAI;IAClB,MAAM,oBAAoB;QACxB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,qBAAqB;QACzB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,mBAAmB;QACvB;QACA;QACA;QACA;KACD;IACD,MAAM,kBAAkB;QACtB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,sBAAsB;QAC1B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,WAAW;QACf,SAAS,gBAAgB,MAAM,CAAC;QAChC,UAAU;QACV,SAAS;IACX;IACA,MAAM,aAAa,KAAK,OAAO,CAAC,KAAK,UAAU,EAAE;QAAE,OAAO;IAAqB;IAC/E,MAAM,UAAU;QACd,WAAW;QACX,UAAU;YACR;gBAAE,OAAO;YAAiB;YAC1B;gBAAE,OAAO;YAAqE;YAC9E;gBAAE,OAAO;YAA2F;SACrG;QACD,WAAW;IACb;IACA,MAAM,aAAa;QACjB,WAAW;QACX,OAAO;QACP,WAAW;IACb;IACA,MAAM,kBAAkB;QACtB,WAAW;QACX,OAAO;QACP,KAAK;QACL,UAAU;YAAE;gBAAE,OAAO;YAAK;SAAG;IAC/B;IACA,MAAM,wBAAwB,KAAK,OAAO,CAAC,iBAAiB;QAAE,SAAS;IAAK;IAC5E,MAAM,QAAQ;QACZ,WAAW;QACX,OAAO;QACP,KAAK;QACL,UAAU;IACZ;IACA,MAAM,cAAc,KAAK,OAAO,CAAC,OAAO;QAAE,SAAS;IAAK;IACxD,MAAM,sBAAsB;QAC1B,WAAW;QACX,OAAO;QACP,KAAK;QACL,SAAS;QACT,UAAU;YACR;gBAAE,OAAO;YAAO;YAChB;gBAAE,OAAO;YAAO;YAChB,KAAK,gBAAgB;YACrB;SACD;IACH;IACA,MAAM,+BAA+B;QACnC,WAAW;QACX,OAAO;QACP,KAAK;QACL,UAAU;YACR;gBAAE,OAAO;YAAO;YAChB;gBAAE,OAAO;YAAO;YAChB;gBAAE,OAAO;YAAK;YACd;SACD;IACH;IACA,MAAM,qCAAqC,KAAK,OAAO,CAAC,8BAA8B;QACpF,SAAS;QACT,UAAU;YACR;gBAAE,OAAO;YAAO;YAChB;gBAAE,OAAO;YAAO;YAChB;gBAAE,OAAO;YAAK;YACd;SACD;IACH;IACA,MAAM,QAAQ,GAAG;QACf;QACA;QACA;QACA,KAAK,gBAAgB;QACrB,KAAK,iBAAiB;QACtB;QACA,KAAK,oBAAoB;KAC1B;IACD,YAAY,QAAQ,GAAG;QACrB;QACA;QACA;QACA,KAAK,gBAAgB;QACrB,KAAK,iBAAiB;QACtB;QACA,KAAK,OAAO,CAAC,KAAK,oBAAoB,EAAE;YAAE,SAAS;QAAK;KACzD;IACD,MAAM,SAAS;QAAE,UAAU;YACzB;YACA;YACA;YACA;YACA,KAAK,gBAAgB;YACrB,KAAK,iBAAiB;SACvB;IAAC;IAEF,MAAM,mBAAmB;QACvB,OAAO;QACP,KAAK;QACL,UAAU;YACR;gBAAE,eAAe;YAAS;YAC1B;SACD;IACH;IACA,MAAM,gBAAgB,KAAK,QAAQ,GAAG,OAAO,KAAK,QAAQ,GAAG,eAAe,KAAK,QAAQ,GAAG;IAC5F,MAAM,gBAAgB;QACpB,6DAA6D;QAC7D,uBAAuB;QACvB,OAAO,MAAM,KAAK,QAAQ;QAC1B,WAAW;IACb;IAEA,OAAO;QACL,MAAM;QACN,SAAS;YACP;YACA;SACD;QACD,UAAU;QACV,SAAS;QACT,UAAU;YACR,KAAK,OAAO,CACV,OACA,KACA;gBACE,aAAa;gBACb,UAAU;oBACR;wBACE,WAAW;wBACX,UAAU;4BACR;gCACE,OAAO;gCACP,WAAW;4BACb;4BACA;gCAAE,OAAO;4BAAW;4BACpB;gCACE,OAAO;gCACP,KAAK;4BACP;yBACD;oBACH;iBACD;YACH;YAEF,KAAK,mBAAmB;YACxB,KAAK,oBAAoB;YACzB;gBACE,WAAW;gBACX,OAAO;gBACP,KAAK;gBACL,UAAU;oBAAE,SAAS;gBAAsF;YAC7G;YACA;YACA;YACA;gBACE,eAAe;gBACf,WAAW;gBACX,KAAK;gBACL,SAAS;gBACT,UAAU;oBACR;wBAAE,eAAe;oBAAc;oBAC/B;oBACA;oBACA,KAAK,mBAAmB;oBACxB,KAAK,oBAAoB;iBAC1B;YACH;YACA;gBACE,eAAe;gBACf,WAAW;gBACX,KAAK;gBACL,SAAS;gBACT,UAAU;oBACR;oBACA,KAAK,mBAAmB;oBACxB,KAAK,oBAAoB;iBAC1B;YACH;YACA;gBACE,eAAe;gBACf,WAAW;gBACX,KAAK;gBACL,SAAS;gBACT,UAAU;oBACR;oBACA;oBACA,KAAK,mBAAmB;oBACxB,KAAK,oBAAoB;iBAC1B;YACH;YACA;gBACE,mBAAmB;gBACnB,WAAW;gBACX,OAAO;gBACP,cAAc;gBACd,KAAK;gBACL,YAAY;gBACZ,UAAU;oBACR;wBACE,WAAW;wBACX,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,6DAA6D;gBAC7D,sCAAsC;gBACtC,eAAe;gBACf,WAAW;YACb;YACA;gBACE,WAAW;gBACX,OAAO,MAAM,gBAAgB,WAAW,KAAK,QAAQ,GAAG;gBACxD,aAAa;gBACb,KAAK;gBACL,YAAY;gBACZ,UAAU;gBACV,UAAU;oBACR,gDAAgD;oBAChD;wBACE,eAAe,mBAAmB,IAAI,CAAC;wBACvC,WAAW;oBACb;oBACA;wBACE,OAAO,KAAK,QAAQ,GAAG;wBACvB,aAAa;wBACb,UAAU;4BACR,KAAK,UAAU;4BACf;yBACD;wBACD,WAAW;oBACb;oBACA;wBAAE,OAAO;oBAAO;oBAChB;wBACE,WAAW;wBACX,OAAO;wBACP,KAAK;wBACL,cAAc;wBACd,YAAY;wBACZ,UAAU;wBACV,WAAW;wBACX,UAAU;4BACR;4BACA;4BACA,KAAK,oBAAoB;yBAC1B;oBACH;oBACA,KAAK,mBAAmB;oBACxB,KAAK,oBAAoB;iBAC1B;YACH;YACA;SACD;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2760, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/css.js"], "sourcesContent": ["const MODES = (hljs) => {\n  return {\n    IMPORTANT: {\n      scope: 'meta',\n      begin: '!important'\n    },\n    BLOCK_COMMENT: hljs.C_BLOCK_COMMENT_MODE,\n    HEXCOLOR: {\n      scope: 'number',\n      begin: /#(([0-9a-fA-F]{3,4})|(([0-9a-fA-F]{2}){3,4}))\\b/\n    },\n    FUNCTION_DISPATCH: {\n      className: \"built_in\",\n      begin: /[\\w-]+(?=\\()/\n    },\n    ATTRIBUTE_SELECTOR_MODE: {\n      scope: 'selector-attr',\n      begin: /\\[/,\n      end: /\\]/,\n      illegal: '$',\n      contains: [\n        hljs.APOS_STRING_MODE,\n        hljs.QUOTE_STRING_MODE\n      ]\n    },\n    CSS_NUMBER_MODE: {\n      scope: 'number',\n      begin: hljs.NUMBER_RE + '(' +\n        '%|em|ex|ch|rem' +\n        '|vw|vh|vmin|vmax' +\n        '|cm|mm|in|pt|pc|px' +\n        '|deg|grad|rad|turn' +\n        '|s|ms' +\n        '|Hz|kHz' +\n        '|dpi|dpcm|dppx' +\n        ')?',\n      relevance: 0\n    },\n    CSS_VARIABLE: {\n      className: \"attr\",\n      begin: /--[A-Za-z_][A-Za-z0-9_-]*/\n    }\n  };\n};\n\nconst HTML_TAGS = [\n  'a',\n  'abbr',\n  'address',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'blockquote',\n  'body',\n  'button',\n  'canvas',\n  'caption',\n  'cite',\n  'code',\n  'dd',\n  'del',\n  'details',\n  'dfn',\n  'div',\n  'dl',\n  'dt',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'header',\n  'hgroup',\n  'html',\n  'i',\n  'iframe',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'mark',\n  'menu',\n  'nav',\n  'object',\n  'ol',\n  'optgroup',\n  'option',\n  'p',\n  'picture',\n  'q',\n  'quote',\n  'samp',\n  'section',\n  'select',\n  'source',\n  'span',\n  'strong',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'ul',\n  'var',\n  'video'\n];\n\nconst SVG_TAGS = [\n  'defs',\n  'g',\n  'marker',\n  'mask',\n  'pattern',\n  'svg',\n  'switch',\n  'symbol',\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feFlood',\n  'feGaussianBlur',\n  'feImage',\n  'feMerge',\n  'feMorphology',\n  'feOffset',\n  'feSpecularLighting',\n  'feTile',\n  'feTurbulence',\n  'linearGradient',\n  'radialGradient',\n  'stop',\n  'circle',\n  'ellipse',\n  'image',\n  'line',\n  'path',\n  'polygon',\n  'polyline',\n  'rect',\n  'text',\n  'use',\n  'textPath',\n  'tspan',\n  'foreignObject',\n  'clipPath'\n];\n\nconst TAGS = [\n  ...HTML_TAGS,\n  ...SVG_TAGS,\n];\n\n// Sorting, then reversing makes sure longer attributes/elements like\n// `font-weight` are matched fully instead of getting false positives on say `font`\n\nconst MEDIA_FEATURES = [\n  'any-hover',\n  'any-pointer',\n  'aspect-ratio',\n  'color',\n  'color-gamut',\n  'color-index',\n  'device-aspect-ratio',\n  'device-height',\n  'device-width',\n  'display-mode',\n  'forced-colors',\n  'grid',\n  'height',\n  'hover',\n  'inverted-colors',\n  'monochrome',\n  'orientation',\n  'overflow-block',\n  'overflow-inline',\n  'pointer',\n  'prefers-color-scheme',\n  'prefers-contrast',\n  'prefers-reduced-motion',\n  'prefers-reduced-transparency',\n  'resolution',\n  'scan',\n  'scripting',\n  'update',\n  'width',\n  // TODO: find a better solution?\n  'min-width',\n  'max-width',\n  'min-height',\n  'max-height'\n].sort().reverse();\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-classes\nconst PSEUDO_CLASSES = [\n  'active',\n  'any-link',\n  'blank',\n  'checked',\n  'current',\n  'default',\n  'defined',\n  'dir', // dir()\n  'disabled',\n  'drop',\n  'empty',\n  'enabled',\n  'first',\n  'first-child',\n  'first-of-type',\n  'fullscreen',\n  'future',\n  'focus',\n  'focus-visible',\n  'focus-within',\n  'has', // has()\n  'host', // host or host()\n  'host-context', // host-context()\n  'hover',\n  'indeterminate',\n  'in-range',\n  'invalid',\n  'is', // is()\n  'lang', // lang()\n  'last-child',\n  'last-of-type',\n  'left',\n  'link',\n  'local-link',\n  'not', // not()\n  'nth-child', // nth-child()\n  'nth-col', // nth-col()\n  'nth-last-child', // nth-last-child()\n  'nth-last-col', // nth-last-col()\n  'nth-last-of-type', //nth-last-of-type()\n  'nth-of-type', //nth-of-type()\n  'only-child',\n  'only-of-type',\n  'optional',\n  'out-of-range',\n  'past',\n  'placeholder-shown',\n  'read-only',\n  'read-write',\n  'required',\n  'right',\n  'root',\n  'scope',\n  'target',\n  'target-within',\n  'user-invalid',\n  'valid',\n  'visited',\n  'where' // where()\n].sort().reverse();\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-elements\nconst PSEUDO_ELEMENTS = [\n  'after',\n  'backdrop',\n  'before',\n  'cue',\n  'cue-region',\n  'first-letter',\n  'first-line',\n  'grammar-error',\n  'marker',\n  'part',\n  'placeholder',\n  'selection',\n  'slotted',\n  'spelling-error'\n].sort().reverse();\n\nconst ATTRIBUTES = [\n  'accent-color',\n  'align-content',\n  'align-items',\n  'align-self',\n  'alignment-baseline',\n  'all',\n  'anchor-name',\n  'animation',\n  'animation-composition',\n  'animation-delay',\n  'animation-direction',\n  'animation-duration',\n  'animation-fill-mode',\n  'animation-iteration-count',\n  'animation-name',\n  'animation-play-state',\n  'animation-range',\n  'animation-range-end',\n  'animation-range-start',\n  'animation-timeline',\n  'animation-timing-function',\n  'appearance',\n  'aspect-ratio',\n  'backdrop-filter',\n  'backface-visibility',\n  'background',\n  'background-attachment',\n  'background-blend-mode',\n  'background-clip',\n  'background-color',\n  'background-image',\n  'background-origin',\n  'background-position',\n  'background-position-x',\n  'background-position-y',\n  'background-repeat',\n  'background-size',\n  'baseline-shift',\n  'block-size',\n  'border',\n  'border-block',\n  'border-block-color',\n  'border-block-end',\n  'border-block-end-color',\n  'border-block-end-style',\n  'border-block-end-width',\n  'border-block-start',\n  'border-block-start-color',\n  'border-block-start-style',\n  'border-block-start-width',\n  'border-block-style',\n  'border-block-width',\n  'border-bottom',\n  'border-bottom-color',\n  'border-bottom-left-radius',\n  'border-bottom-right-radius',\n  'border-bottom-style',\n  'border-bottom-width',\n  'border-collapse',\n  'border-color',\n  'border-end-end-radius',\n  'border-end-start-radius',\n  'border-image',\n  'border-image-outset',\n  'border-image-repeat',\n  'border-image-slice',\n  'border-image-source',\n  'border-image-width',\n  'border-inline',\n  'border-inline-color',\n  'border-inline-end',\n  'border-inline-end-color',\n  'border-inline-end-style',\n  'border-inline-end-width',\n  'border-inline-start',\n  'border-inline-start-color',\n  'border-inline-start-style',\n  'border-inline-start-width',\n  'border-inline-style',\n  'border-inline-width',\n  'border-left',\n  'border-left-color',\n  'border-left-style',\n  'border-left-width',\n  'border-radius',\n  'border-right',\n  'border-right-color',\n  'border-right-style',\n  'border-right-width',\n  'border-spacing',\n  'border-start-end-radius',\n  'border-start-start-radius',\n  'border-style',\n  'border-top',\n  'border-top-color',\n  'border-top-left-radius',\n  'border-top-right-radius',\n  'border-top-style',\n  'border-top-width',\n  'border-width',\n  'bottom',\n  'box-align',\n  'box-decoration-break',\n  'box-direction',\n  'box-flex',\n  'box-flex-group',\n  'box-lines',\n  'box-ordinal-group',\n  'box-orient',\n  'box-pack',\n  'box-shadow',\n  'box-sizing',\n  'break-after',\n  'break-before',\n  'break-inside',\n  'caption-side',\n  'caret-color',\n  'clear',\n  'clip',\n  'clip-path',\n  'clip-rule',\n  'color',\n  'color-interpolation',\n  'color-interpolation-filters',\n  'color-profile',\n  'color-rendering',\n  'color-scheme',\n  'column-count',\n  'column-fill',\n  'column-gap',\n  'column-rule',\n  'column-rule-color',\n  'column-rule-style',\n  'column-rule-width',\n  'column-span',\n  'column-width',\n  'columns',\n  'contain',\n  'contain-intrinsic-block-size',\n  'contain-intrinsic-height',\n  'contain-intrinsic-inline-size',\n  'contain-intrinsic-size',\n  'contain-intrinsic-width',\n  'container',\n  'container-name',\n  'container-type',\n  'content',\n  'content-visibility',\n  'counter-increment',\n  'counter-reset',\n  'counter-set',\n  'cue',\n  'cue-after',\n  'cue-before',\n  'cursor',\n  'cx',\n  'cy',\n  'direction',\n  'display',\n  'dominant-baseline',\n  'empty-cells',\n  'enable-background',\n  'field-sizing',\n  'fill',\n  'fill-opacity',\n  'fill-rule',\n  'filter',\n  'flex',\n  'flex-basis',\n  'flex-direction',\n  'flex-flow',\n  'flex-grow',\n  'flex-shrink',\n  'flex-wrap',\n  'float',\n  'flood-color',\n  'flood-opacity',\n  'flow',\n  'font',\n  'font-display',\n  'font-family',\n  'font-feature-settings',\n  'font-kerning',\n  'font-language-override',\n  'font-optical-sizing',\n  'font-palette',\n  'font-size',\n  'font-size-adjust',\n  'font-smooth',\n  'font-smoothing',\n  'font-stretch',\n  'font-style',\n  'font-synthesis',\n  'font-synthesis-position',\n  'font-synthesis-small-caps',\n  'font-synthesis-style',\n  'font-synthesis-weight',\n  'font-variant',\n  'font-variant-alternates',\n  'font-variant-caps',\n  'font-variant-east-asian',\n  'font-variant-emoji',\n  'font-variant-ligatures',\n  'font-variant-numeric',\n  'font-variant-position',\n  'font-variation-settings',\n  'font-weight',\n  'forced-color-adjust',\n  'gap',\n  'glyph-orientation-horizontal',\n  'glyph-orientation-vertical',\n  'grid',\n  'grid-area',\n  'grid-auto-columns',\n  'grid-auto-flow',\n  'grid-auto-rows',\n  'grid-column',\n  'grid-column-end',\n  'grid-column-start',\n  'grid-gap',\n  'grid-row',\n  'grid-row-end',\n  'grid-row-start',\n  'grid-template',\n  'grid-template-areas',\n  'grid-template-columns',\n  'grid-template-rows',\n  'hanging-punctuation',\n  'height',\n  'hyphenate-character',\n  'hyphenate-limit-chars',\n  'hyphens',\n  'icon',\n  'image-orientation',\n  'image-rendering',\n  'image-resolution',\n  'ime-mode',\n  'initial-letter',\n  'initial-letter-align',\n  'inline-size',\n  'inset',\n  'inset-area',\n  'inset-block',\n  'inset-block-end',\n  'inset-block-start',\n  'inset-inline',\n  'inset-inline-end',\n  'inset-inline-start',\n  'isolation',\n  'justify-content',\n  'justify-items',\n  'justify-self',\n  'kerning',\n  'left',\n  'letter-spacing',\n  'lighting-color',\n  'line-break',\n  'line-height',\n  'line-height-step',\n  'list-style',\n  'list-style-image',\n  'list-style-position',\n  'list-style-type',\n  'margin',\n  'margin-block',\n  'margin-block-end',\n  'margin-block-start',\n  'margin-bottom',\n  'margin-inline',\n  'margin-inline-end',\n  'margin-inline-start',\n  'margin-left',\n  'margin-right',\n  'margin-top',\n  'margin-trim',\n  'marker',\n  'marker-end',\n  'marker-mid',\n  'marker-start',\n  'marks',\n  'mask',\n  'mask-border',\n  'mask-border-mode',\n  'mask-border-outset',\n  'mask-border-repeat',\n  'mask-border-slice',\n  'mask-border-source',\n  'mask-border-width',\n  'mask-clip',\n  'mask-composite',\n  'mask-image',\n  'mask-mode',\n  'mask-origin',\n  'mask-position',\n  'mask-repeat',\n  'mask-size',\n  'mask-type',\n  'masonry-auto-flow',\n  'math-depth',\n  'math-shift',\n  'math-style',\n  'max-block-size',\n  'max-height',\n  'max-inline-size',\n  'max-width',\n  'min-block-size',\n  'min-height',\n  'min-inline-size',\n  'min-width',\n  'mix-blend-mode',\n  'nav-down',\n  'nav-index',\n  'nav-left',\n  'nav-right',\n  'nav-up',\n  'none',\n  'normal',\n  'object-fit',\n  'object-position',\n  'offset',\n  'offset-anchor',\n  'offset-distance',\n  'offset-path',\n  'offset-position',\n  'offset-rotate',\n  'opacity',\n  'order',\n  'orphans',\n  'outline',\n  'outline-color',\n  'outline-offset',\n  'outline-style',\n  'outline-width',\n  'overflow',\n  'overflow-anchor',\n  'overflow-block',\n  'overflow-clip-margin',\n  'overflow-inline',\n  'overflow-wrap',\n  'overflow-x',\n  'overflow-y',\n  'overlay',\n  'overscroll-behavior',\n  'overscroll-behavior-block',\n  'overscroll-behavior-inline',\n  'overscroll-behavior-x',\n  'overscroll-behavior-y',\n  'padding',\n  'padding-block',\n  'padding-block-end',\n  'padding-block-start',\n  'padding-bottom',\n  'padding-inline',\n  'padding-inline-end',\n  'padding-inline-start',\n  'padding-left',\n  'padding-right',\n  'padding-top',\n  'page',\n  'page-break-after',\n  'page-break-before',\n  'page-break-inside',\n  'paint-order',\n  'pause',\n  'pause-after',\n  'pause-before',\n  'perspective',\n  'perspective-origin',\n  'place-content',\n  'place-items',\n  'place-self',\n  'pointer-events',\n  'position',\n  'position-anchor',\n  'position-visibility',\n  'print-color-adjust',\n  'quotes',\n  'r',\n  'resize',\n  'rest',\n  'rest-after',\n  'rest-before',\n  'right',\n  'rotate',\n  'row-gap',\n  'ruby-align',\n  'ruby-position',\n  'scale',\n  'scroll-behavior',\n  'scroll-margin',\n  'scroll-margin-block',\n  'scroll-margin-block-end',\n  'scroll-margin-block-start',\n  'scroll-margin-bottom',\n  'scroll-margin-inline',\n  'scroll-margin-inline-end',\n  'scroll-margin-inline-start',\n  'scroll-margin-left',\n  'scroll-margin-right',\n  'scroll-margin-top',\n  'scroll-padding',\n  'scroll-padding-block',\n  'scroll-padding-block-end',\n  'scroll-padding-block-start',\n  'scroll-padding-bottom',\n  'scroll-padding-inline',\n  'scroll-padding-inline-end',\n  'scroll-padding-inline-start',\n  'scroll-padding-left',\n  'scroll-padding-right',\n  'scroll-padding-top',\n  'scroll-snap-align',\n  'scroll-snap-stop',\n  'scroll-snap-type',\n  'scroll-timeline',\n  'scroll-timeline-axis',\n  'scroll-timeline-name',\n  'scrollbar-color',\n  'scrollbar-gutter',\n  'scrollbar-width',\n  'shape-image-threshold',\n  'shape-margin',\n  'shape-outside',\n  'shape-rendering',\n  'speak',\n  'speak-as',\n  'src', // @font-face\n  'stop-color',\n  'stop-opacity',\n  'stroke',\n  'stroke-dasharray',\n  'stroke-dashoffset',\n  'stroke-linecap',\n  'stroke-linejoin',\n  'stroke-miterlimit',\n  'stroke-opacity',\n  'stroke-width',\n  'tab-size',\n  'table-layout',\n  'text-align',\n  'text-align-all',\n  'text-align-last',\n  'text-anchor',\n  'text-combine-upright',\n  'text-decoration',\n  'text-decoration-color',\n  'text-decoration-line',\n  'text-decoration-skip',\n  'text-decoration-skip-ink',\n  'text-decoration-style',\n  'text-decoration-thickness',\n  'text-emphasis',\n  'text-emphasis-color',\n  'text-emphasis-position',\n  'text-emphasis-style',\n  'text-indent',\n  'text-justify',\n  'text-orientation',\n  'text-overflow',\n  'text-rendering',\n  'text-shadow',\n  'text-size-adjust',\n  'text-transform',\n  'text-underline-offset',\n  'text-underline-position',\n  'text-wrap',\n  'text-wrap-mode',\n  'text-wrap-style',\n  'timeline-scope',\n  'top',\n  'touch-action',\n  'transform',\n  'transform-box',\n  'transform-origin',\n  'transform-style',\n  'transition',\n  'transition-behavior',\n  'transition-delay',\n  'transition-duration',\n  'transition-property',\n  'transition-timing-function',\n  'translate',\n  'unicode-bidi',\n  'user-modify',\n  'user-select',\n  'vector-effect',\n  'vertical-align',\n  'view-timeline',\n  'view-timeline-axis',\n  'view-timeline-inset',\n  'view-timeline-name',\n  'view-transition-name',\n  'visibility',\n  'voice-balance',\n  'voice-duration',\n  'voice-family',\n  'voice-pitch',\n  'voice-range',\n  'voice-rate',\n  'voice-stress',\n  'voice-volume',\n  'white-space',\n  'white-space-collapse',\n  'widows',\n  'width',\n  'will-change',\n  'word-break',\n  'word-spacing',\n  'word-wrap',\n  'writing-mode',\n  'x',\n  'y',\n  'z-index',\n  'zoom'\n].sort().reverse();\n\n/*\nLanguage: CSS\nCategory: common, css, web\nWebsite: https://developer.mozilla.org/en-US/docs/Web/CSS\n*/\n\n\n/** @type LanguageFn */\nfunction css(hljs) {\n  const regex = hljs.regex;\n  const modes = MODES(hljs);\n  const VENDOR_PREFIX = { begin: /-(webkit|moz|ms|o)-(?=[a-z])/ };\n  const AT_MODIFIERS = \"and or not only\";\n  const AT_PROPERTY_RE = /@-?\\w[\\w]*(-\\w+)*/; // @-webkit-keyframes\n  const IDENT_RE = '[a-zA-Z-][a-zA-Z0-9_-]*';\n  const STRINGS = [\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE\n  ];\n\n  return {\n    name: 'CSS',\n    case_insensitive: true,\n    illegal: /[=|'\\$]/,\n    keywords: { keyframePosition: \"from to\" },\n    classNameAliases: {\n      // for visual continuity with `tag {}` and because we\n      // don't have a great class for this?\n      keyframePosition: \"selector-tag\" },\n    contains: [\n      modes.BLOCK_COMMENT,\n      VENDOR_PREFIX,\n      // to recognize keyframe 40% etc which are outside the scope of our\n      // attribute value mode\n      modes.CSS_NUMBER_MODE,\n      {\n        className: 'selector-id',\n        begin: /#[A-Za-z0-9_-]+/,\n        relevance: 0\n      },\n      {\n        className: 'selector-class',\n        begin: '\\\\.' + IDENT_RE,\n        relevance: 0\n      },\n      modes.ATTRIBUTE_SELECTOR_MODE,\n      {\n        className: 'selector-pseudo',\n        variants: [\n          { begin: ':(' + PSEUDO_CLASSES.join('|') + ')' },\n          { begin: ':(:)?(' + PSEUDO_ELEMENTS.join('|') + ')' }\n        ]\n      },\n      // we may actually need this (12/2020)\n      // { // pseudo-selector params\n      //   begin: /\\(/,\n      //   end: /\\)/,\n      //   contains: [ hljs.CSS_NUMBER_MODE ]\n      // },\n      modes.CSS_VARIABLE,\n      {\n        className: 'attribute',\n        begin: '\\\\b(' + ATTRIBUTES.join('|') + ')\\\\b'\n      },\n      // attribute values\n      {\n        begin: /:/,\n        end: /[;}{]/,\n        contains: [\n          modes.BLOCK_COMMENT,\n          modes.HEXCOLOR,\n          modes.IMPORTANT,\n          modes.CSS_NUMBER_MODE,\n          ...STRINGS,\n          // needed to highlight these as strings and to avoid issues with\n          // illegal characters that might be inside urls that would tigger the\n          // languages illegal stack\n          {\n            begin: /(url|data-uri)\\(/,\n            end: /\\)/,\n            relevance: 0, // from keywords\n            keywords: { built_in: \"url data-uri\" },\n            contains: [\n              ...STRINGS,\n              {\n                className: \"string\",\n                // any character other than `)` as in `url()` will be the start\n                // of a string, which ends with `)` (from the parent mode)\n                begin: /[^)]/,\n                endsWithParent: true,\n                excludeEnd: true\n              }\n            ]\n          },\n          modes.FUNCTION_DISPATCH\n        ]\n      },\n      {\n        begin: regex.lookahead(/@/),\n        end: '[{;]',\n        relevance: 0,\n        illegal: /:/, // break on Less variables @var: ...\n        contains: [\n          {\n            className: 'keyword',\n            begin: AT_PROPERTY_RE\n          },\n          {\n            begin: /\\s/,\n            endsWithParent: true,\n            excludeEnd: true,\n            relevance: 0,\n            keywords: {\n              $pattern: /[a-z-]+/,\n              keyword: AT_MODIFIERS,\n              attribute: MEDIA_FEATURES.join(\" \")\n            },\n            contains: [\n              {\n                begin: /[a-z-]+(?=:)/,\n                className: \"attribute\"\n              },\n              ...STRINGS,\n              modes.CSS_NUMBER_MODE\n            ]\n          }\n        ]\n      },\n      {\n        className: 'selector-tag',\n        begin: '\\\\b(' + TAGS.join('|') + ')\\\\b'\n      }\n    ]\n  };\n}\n\nexport { css as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,QAAQ,CAAC;IACb,OAAO;QACL,WAAW;YACT,OAAO;YACP,OAAO;QACT;QACA,eAAe,KAAK,oBAAoB;QACxC,UAAU;YACR,OAAO;YACP,OAAO;QACT;QACA,mBAAmB;YACjB,WAAW;YACX,OAAO;QACT;QACA,yBAAyB;YACvB,OAAO;YACP,OAAO;YACP,KAAK;YACL,SAAS;YACT,UAAU;gBACR,KAAK,gBAAgB;gBACrB,KAAK,iBAAiB;aACvB;QACH;QACA,iBAAiB;YACf,OAAO;YACP,OAAO,KAAK,SAAS,GAAG,MACtB,mBACA,qBACA,uBACA,uBACA,UACA,YACA,mBACA;YACF,WAAW;QACb;QACA,cAAc;YACZ,WAAW;YACX,OAAO;QACT;IACF;AACF;AAEA,MAAM,YAAY;IAChB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,OAAO;OACR;OACA;CACJ;AAED,qEAAqE;AACrE,mFAAmF;AAEnF,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,gCAAgC;IAChC;IACA;IACA;IACA;CACD,CAAC,IAAI,GAAG,OAAO;AAEhB,kEAAkE;AAClE,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,QAAQ,UAAU;CACnB,CAAC,IAAI,GAAG,OAAO;AAEhB,mEAAmE;AACnE,MAAM,kBAAkB;IACtB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD,CAAC,IAAI,GAAG,OAAO;AAEhB,MAAM,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD,CAAC,IAAI,GAAG,OAAO;AAEhB;;;;AAIA,GAGA,qBAAqB,GACrB,SAAS,IAAI,IAAI;IACf,MAAM,QAAQ,KAAK,KAAK;IACxB,MAAM,QAAQ,MAAM;IACpB,MAAM,gBAAgB;QAAE,OAAO;IAA+B;IAC9D,MAAM,eAAe;IACrB,MAAM,iBAAiB,qBAAqB,qBAAqB;IACjE,MAAM,WAAW;IACjB,MAAM,UAAU;QACd,KAAK,gBAAgB;QACrB,KAAK,iBAAiB;KACvB;IAED,OAAO;QACL,MAAM;QACN,kBAAkB;QAClB,SAAS;QACT,UAAU;YAAE,kBAAkB;QAAU;QACxC,kBAAkB;YAChB,qDAAqD;YACrD,qCAAqC;YACrC,kBAAkB;QAAe;QACnC,UAAU;YACR,MAAM,aAAa;YACnB;YACA,mEAAmE;YACnE,uBAAuB;YACvB,MAAM,eAAe;YACrB;gBACE,WAAW;gBACX,OAAO;gBACP,WAAW;YACb;YACA;gBACE,WAAW;gBACX,OAAO,QAAQ;gBACf,WAAW;YACb;YACA,MAAM,uBAAuB;YAC7B;gBACE,WAAW;gBACX,UAAU;oBACR;wBAAE,OAAO,OAAO,eAAe,IAAI,CAAC,OAAO;oBAAI;oBAC/C;wBAAE,OAAO,WAAW,gBAAgB,IAAI,CAAC,OAAO;oBAAI;iBACrD;YACH;YACA,sCAAsC;YACtC,8BAA8B;YAC9B,iBAAiB;YACjB,eAAe;YACf,uCAAuC;YACvC,KAAK;YACL,MAAM,YAAY;YAClB;gBACE,WAAW;gBACX,OAAO,SAAS,WAAW,IAAI,CAAC,OAAO;YACzC;YACA,mBAAmB;YACnB;gBACE,OAAO;gBACP,KAAK;gBACL,UAAU;oBACR,MAAM,aAAa;oBACnB,MAAM,QAAQ;oBACd,MAAM,SAAS;oBACf,MAAM,eAAe;uBAClB;oBACH,gEAAgE;oBAChE,qEAAqE;oBACrE,0BAA0B;oBAC1B;wBACE,OAAO;wBACP,KAAK;wBACL,WAAW;wBACX,UAAU;4BAAE,UAAU;wBAAe;wBACrC,UAAU;+BACL;4BACH;gCACE,WAAW;gCACX,+DAA+D;gCAC/D,0DAA0D;gCAC1D,OAAO;gCACP,gBAAgB;gCAChB,YAAY;4BACd;yBACD;oBACH;oBACA,MAAM,iBAAiB;iBACxB;YACH;YACA;gBACE,OAAO,MAAM,SAAS,CAAC;gBACvB,KAAK;gBACL,WAAW;gBACX,SAAS;gBACT,UAAU;oBACR;wBACE,WAAW;wBACX,OAAO;oBACT;oBACA;wBACE,OAAO;wBACP,gBAAgB;wBAChB,YAAY;wBACZ,WAAW;wBACX,UAAU;4BACR,UAAU;4BACV,SAAS;4BACT,WAAW,eAAe,IAAI,CAAC;wBACjC;wBACA,UAAU;4BACR;gCACE,OAAO;gCACP,WAAW;4BACb;+BACG;4BACH,MAAM,eAAe;yBACtB;oBACH;iBACD;YACH;YACA;gBACE,WAAW;gBACX,OAAO,SAAS,KAAK,IAAI,CAAC,OAAO;YACnC;SACD;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3706, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/diff.js"], "sourcesContent": ["/*\nLanguage: Diff\nDescription: Unified and context diff\nAuthor: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.gnu.org/software/diffutils/\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction diff(hljs) {\n  const regex = hljs.regex;\n  return {\n    name: 'Diff',\n    aliases: [ 'patch' ],\n    contains: [\n      {\n        className: 'meta',\n        relevance: 10,\n        match: regex.either(\n          /^@@ +-\\d+,\\d+ +\\+\\d+,\\d+ +@@/,\n          /^\\*\\*\\* +\\d+,\\d+ +\\*\\*\\*\\*$/,\n          /^--- +\\d+,\\d+ +----$/\n        )\n      },\n      {\n        className: 'comment',\n        variants: [\n          {\n            begin: regex.either(\n              /Index: /,\n              /^index/,\n              /={3,}/,\n              /^-{3}/,\n              /^\\*{3} /,\n              /^\\+{3}/,\n              /^diff --git/\n            ),\n            end: /$/\n          },\n          { match: /^\\*{15}$/ }\n        ]\n      },\n      {\n        className: 'addition',\n        begin: /^\\+/,\n        end: /$/\n      },\n      {\n        className: 'deletion',\n        begin: /^-/,\n        end: /$/\n      },\n      {\n        className: 'addition',\n        begin: /^!/,\n        end: /$/\n      }\n    ]\n  };\n}\n\nexport { diff as default };\n"], "names": [], "mappings": "AAAA;;;;;;AAMA,GAEA,qBAAqB;;;AACrB,SAAS,KAAK,IAAI;IAChB,MAAM,QAAQ,KAAK,KAAK;IACxB,OAAO;QACL,MAAM;QACN,SAAS;YAAE;SAAS;QACpB,UAAU;YACR;gBACE,WAAW;gBACX,WAAW;gBACX,OAAO,MAAM,MAAM,CACjB,gCACA,+BACA;YAEJ;YACA;gBACE,WAAW;gBACX,UAAU;oBACR;wBACE,OAAO,MAAM,MAAM,CACjB,WACA,UACA,SACA,SACA,WACA,UACA;wBAEF,KAAK;oBACP;oBACA;wBAAE,OAAO;oBAAW;iBACrB;YACH;YACA;gBACE,WAAW;gBACX,OAAO;gBACP,KAAK;YACP;YACA;gBACE,WAAW;gBACX,OAAO;gBACP,KAAK;YACP;YACA;gBACE,WAAW;gBACX,OAAO;gBACP,KAAK;YACP;SACD;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3765, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/go.js"], "sourcesContent": ["/*\nLanguage: Go\nAuthor: <PERSON> aka StepLg <<EMAIL>>\nContributors: <PERSON><PERSON><PERSON> <<EMAIL>>\nDescription: Google go language (golang). For info about language\nWebsite: http://golang.org/\nCategory: common, system\n*/\n\nfunction go(hljs) {\n  const LITERALS = [\n    \"true\",\n    \"false\",\n    \"iota\",\n    \"nil\"\n  ];\n  const BUILT_INS = [\n    \"append\",\n    \"cap\",\n    \"close\",\n    \"complex\",\n    \"copy\",\n    \"imag\",\n    \"len\",\n    \"make\",\n    \"new\",\n    \"panic\",\n    \"print\",\n    \"println\",\n    \"real\",\n    \"recover\",\n    \"delete\"\n  ];\n  const TYPES = [\n    \"bool\",\n    \"byte\",\n    \"complex64\",\n    \"complex128\",\n    \"error\",\n    \"float32\",\n    \"float64\",\n    \"int8\",\n    \"int16\",\n    \"int32\",\n    \"int64\",\n    \"string\",\n    \"uint8\",\n    \"uint16\",\n    \"uint32\",\n    \"uint64\",\n    \"int\",\n    \"uint\",\n    \"uintptr\",\n    \"rune\"\n  ];\n  const KWS = [\n    \"break\",\n    \"case\",\n    \"chan\",\n    \"const\",\n    \"continue\",\n    \"default\",\n    \"defer\",\n    \"else\",\n    \"fallthrough\",\n    \"for\",\n    \"func\",\n    \"go\",\n    \"goto\",\n    \"if\",\n    \"import\",\n    \"interface\",\n    \"map\",\n    \"package\",\n    \"range\",\n    \"return\",\n    \"select\",\n    \"struct\",\n    \"switch\",\n    \"type\",\n    \"var\",\n  ];\n  const KEYWORDS = {\n    keyword: KWS,\n    type: TYPES,\n    literal: LITERALS,\n    built_in: BUILT_INS\n  };\n  return {\n    name: 'Go',\n    aliases: [ 'golang' ],\n    keywords: KEYWORDS,\n    illegal: '</',\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        className: 'string',\n        variants: [\n          hljs.QUOTE_STRING_MODE,\n          hljs.APOS_STRING_MODE,\n          {\n            begin: '`',\n            end: '`'\n          }\n        ]\n      },\n      {\n        className: 'number',\n        variants: [\n          {\n            match: /-?\\b0[xX]\\.[a-fA-F0-9](_?[a-fA-F0-9])*[pP][+-]?\\d(_?\\d)*i?/, // hex without a present digit before . (making a digit afterwards required)\n            relevance: 0\n          },\n          {\n            match: /-?\\b0[xX](_?[a-fA-F0-9])+((\\.([a-fA-F0-9](_?[a-fA-F0-9])*)?)?[pP][+-]?\\d(_?\\d)*)?i?/, // hex with a present digit before . (making a digit afterwards optional)\n            relevance: 0\n          },\n          {\n            match: /-?\\b0[oO](_?[0-7])*i?/, // leading 0o octal\n            relevance: 0\n          },\n          {\n            match: /-?\\.\\d(_?\\d)*([eE][+-]?\\d(_?\\d)*)?i?/, // decimal without a present digit before . (making a digit afterwards required)\n            relevance: 0\n          },\n          {\n            match: /-?\\b\\d(_?\\d)*(\\.(\\d(_?\\d)*)?)?([eE][+-]?\\d(_?\\d)*)?i?/, // decimal with a present digit before . (making a digit afterwards optional)\n            relevance: 0\n          }\n        ]\n      },\n      { begin: /:=/ // relevance booster\n      },\n      {\n        className: 'function',\n        beginKeywords: 'func',\n        end: '\\\\s*(\\\\{|$)',\n        excludeEnd: true,\n        contains: [\n          hljs.TITLE_MODE,\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            endsParent: true,\n            keywords: KEYWORDS,\n            illegal: /[\"']/\n          }\n        ]\n      }\n    ]\n  };\n}\n\nexport { go as default };\n"], "names": [], "mappings": "AAAA;;;;;;;AAOA;;;AAEA,SAAS,GAAG,IAAI;IACd,MAAM,WAAW;QACf;QACA;QACA;QACA;KACD;IACD,MAAM,YAAY;QAChB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,MAAM;QACV;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,WAAW;QACf,SAAS;QACT,MAAM;QACN,SAAS;QACT,UAAU;IACZ;IACA,OAAO;QACL,MAAM;QACN,SAAS;YAAE;SAAU;QACrB,UAAU;QACV,SAAS;QACT,UAAU;YACR,KAAK,mBAAmB;YACxB,KAAK,oBAAoB;YACzB;gBACE,WAAW;gBACX,UAAU;oBACR,KAAK,iBAAiB;oBACtB,KAAK,gBAAgB;oBACrB;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,WAAW;gBACX,UAAU;oBACR;wBACE,OAAO;wBACP,WAAW;oBACb;oBACA;wBACE,OAAO;wBACP,WAAW;oBACb;oBACA;wBACE,OAAO;wBACP,WAAW;oBACb;oBACA;wBACE,OAAO;wBACP,WAAW;oBACb;oBACA;wBACE,OAAO;wBACP,WAAW;oBACb;iBACD;YACH;YACA;gBAAE,OAAO,KAAK,oBAAoB;YAClC;YACA;gBACE,WAAW;gBACX,eAAe;gBACf,KAAK;gBACL,YAAY;gBACZ,UAAU;oBACR,KAAK,UAAU;oBACf;wBACE,WAAW;wBACX,OAAO;wBACP,KAAK;wBACL,YAAY;wBACZ,UAAU;wBACV,SAAS;oBACX;iBACD;YACH;SACD;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3930, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/graphql.js"], "sourcesContent": ["/*\n Language: GraphQL\n Author: <PERSON> (GH jf990), and others\n Description: GraphQL is a query language for APIs\n Category: web, common\n*/\n\n/** @type LanguageFn */\nfunction graphql(hljs) {\n  const regex = hljs.regex;\n  const GQL_NAME = /[_A-Za-z][_0-9A-Za-z]*/;\n  return {\n    name: \"GraphQL\",\n    aliases: [ \"gql\" ],\n    case_insensitive: true,\n    disableAutodetect: false,\n    keywords: {\n      keyword: [\n        \"query\",\n        \"mutation\",\n        \"subscription\",\n        \"type\",\n        \"input\",\n        \"schema\",\n        \"directive\",\n        \"interface\",\n        \"union\",\n        \"scalar\",\n        \"fragment\",\n        \"enum\",\n        \"on\"\n      ],\n      literal: [\n        \"true\",\n        \"false\",\n        \"null\"\n      ]\n    },\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.NUMBER_MODE,\n      {\n        scope: \"punctuation\",\n        match: /[.]{3}/,\n        relevance: 0\n      },\n      {\n        scope: \"punctuation\",\n        begin: /[\\!\\(\\)\\:\\=\\[\\]\\{\\|\\}]{1}/,\n        relevance: 0\n      },\n      {\n        scope: \"variable\",\n        begin: /\\$/,\n        end: /\\W/,\n        excludeEnd: true,\n        relevance: 0\n      },\n      {\n        scope: \"meta\",\n        match: /@\\w+/,\n        excludeEnd: true\n      },\n      {\n        scope: \"symbol\",\n        begin: regex.concat(GQL_NAME, regex.lookahead(/\\s*:/)),\n        relevance: 0\n      }\n    ],\n    illegal: [\n      /[;<']/,\n      /BEGIN/\n    ]\n  };\n}\n\nexport { graphql as default };\n"], "names": [], "mappings": "AAAA;;;;;AAKA,GAEA,qBAAqB;;;AACrB,SAAS,QAAQ,IAAI;IACnB,MAAM,QAAQ,KAAK,KAAK;IACxB,MAAM,WAAW;IACjB,OAAO;QACL,MAAM;QACN,SAAS;YAAE;SAAO;QAClB,kBAAkB;QAClB,mBAAmB;QACnB,UAAU;YACR,SAAS;gBACP;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,SAAS;gBACP;gBACA;gBACA;aACD;QACH;QACA,UAAU;YACR,KAAK,iBAAiB;YACtB,KAAK,iBAAiB;YACtB,KAAK,WAAW;YAChB;gBACE,OAAO;gBACP,OAAO;gBACP,WAAW;YACb;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,WAAW;YACb;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,KAAK;gBACL,YAAY;gBACZ,WAAW;YACb;YACA;gBACE,OAAO;gBACP,OAAO;gBACP,YAAY;YACd;YACA;gBACE,OAAO;gBACP,OAAO,MAAM,MAAM,CAAC,UAAU,MAAM,SAAS,CAAC;gBAC9C,WAAW;YACb;SACD;QACD,SAAS;YACP;YACA;SACD;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4015, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/ini.js"], "sourcesContent": ["/*\nLanguage: TOML, also INI\nDescription: TOML aims to be a minimal configuration file format that's easy to read due to obvious semantics.\nContributors: <PERSON> <<EMAIL>>\nCategory: common, config\nWebsite: https://github.com/toml-lang/toml\n*/\n\nfunction ini(hljs) {\n  const regex = hljs.regex;\n  const NUMBERS = {\n    className: 'number',\n    relevance: 0,\n    variants: [\n      { begin: /([+-]+)?[\\d]+_[\\d_]+/ },\n      { begin: hljs.NUMBER_RE }\n    ]\n  };\n  const COMMENTS = hljs.COMMENT();\n  COMMENTS.variants = [\n    {\n      begin: /;/,\n      end: /$/\n    },\n    {\n      begin: /#/,\n      end: /$/\n    }\n  ];\n  const VARIABLES = {\n    className: 'variable',\n    variants: [\n      { begin: /\\$[\\w\\d\"][\\w\\d_]*/ },\n      { begin: /\\$\\{(.*?)\\}/ }\n    ]\n  };\n  const LITERALS = {\n    className: 'literal',\n    begin: /\\bon|off|true|false|yes|no\\b/\n  };\n  const STRINGS = {\n    className: \"string\",\n    contains: [ hljs.BACKSLASH_ESCAPE ],\n    variants: [\n      {\n        begin: \"'''\",\n        end: \"'''\",\n        relevance: 10\n      },\n      {\n        begin: '\"\"\"',\n        end: '\"\"\"',\n        relevance: 10\n      },\n      {\n        begin: '\"',\n        end: '\"'\n      },\n      {\n        begin: \"'\",\n        end: \"'\"\n      }\n    ]\n  };\n  const ARRAY = {\n    begin: /\\[/,\n    end: /\\]/,\n    contains: [\n      COMMENTS,\n      LITERALS,\n      VARIABLES,\n      STRINGS,\n      NUMBERS,\n      'self'\n    ],\n    relevance: 0\n  };\n\n  const BARE_KEY = /[A-Za-z0-9_-]+/;\n  const QUOTED_KEY_DOUBLE_QUOTE = /\"(\\\\\"|[^\"])*\"/;\n  const QUOTED_KEY_SINGLE_QUOTE = /'[^']*'/;\n  const ANY_KEY = regex.either(\n    BARE_KEY, QUOTED_KEY_DOUBLE_QUOTE, QUOTED_KEY_SINGLE_QUOTE\n  );\n  const DOTTED_KEY = regex.concat(\n    ANY_KEY, '(\\\\s*\\\\.\\\\s*', ANY_KEY, ')*',\n    regex.lookahead(/\\s*=\\s*[^#\\s]/)\n  );\n\n  return {\n    name: 'TOML, also INI',\n    aliases: [ 'toml' ],\n    case_insensitive: true,\n    illegal: /\\S/,\n    contains: [\n      COMMENTS,\n      {\n        className: 'section',\n        begin: /\\[+/,\n        end: /\\]+/\n      },\n      {\n        begin: DOTTED_KEY,\n        className: 'attr',\n        starts: {\n          end: /$/,\n          contains: [\n            COMMENTS,\n            ARRAY,\n            LITERALS,\n            VARIABLES,\n            STRINGS,\n            NUMBERS\n          ]\n        }\n      }\n    ]\n  };\n}\n\nexport { ini as default };\n"], "names": [], "mappings": "AAAA;;;;;;AAMA;;;AAEA,SAAS,IAAI,IAAI;IACf,MAAM,QAAQ,KAAK,KAAK;IACxB,MAAM,UAAU;QACd,WAAW;QACX,WAAW;QACX,UAAU;YACR;gBAAE,OAAO;YAAuB;YAChC;gBAAE,OAAO,KAAK,SAAS;YAAC;SACzB;IACH;IACA,MAAM,WAAW,KAAK,OAAO;IAC7B,SAAS,QAAQ,GAAG;QAClB;YACE,OAAO;YACP,KAAK;QACP;QACA;YACE,OAAO;YACP,KAAK;QACP;KACD;IACD,MAAM,YAAY;QAChB,WAAW;QACX,UAAU;YACR;gBAAE,OAAO;YAAoB;YAC7B;gBAAE,OAAO;YAAc;SACxB;IACH;IACA,MAAM,WAAW;QACf,WAAW;QACX,OAAO;IACT;IACA,MAAM,UAAU;QACd,WAAW;QACX,UAAU;YAAE,KAAK,gBAAgB;SAAE;QACnC,UAAU;YACR;gBACE,OAAO;gBACP,KAAK;gBACL,WAAW;YACb;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,WAAW;YACb;YACA;gBACE,OAAO;gBACP,KAAK;YACP;YACA;gBACE,OAAO;gBACP,KAAK;YACP;SACD;IACH;IACA,MAAM,QAAQ;QACZ,OAAO;QACP,KAAK;QACL,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,WAAW;IACb;IAEA,MAAM,WAAW;IACjB,MAAM,0BAA0B;IAChC,MAAM,0BAA0B;IAChC,MAAM,UAAU,MAAM,MAAM,CAC1B,UAAU,yBAAyB;IAErC,MAAM,aAAa,MAAM,MAAM,CAC7B,SAAS,gBAAgB,SAAS,MAClC,MAAM,SAAS,CAAC;IAGlB,OAAO;QACL,MAAM;QACN,SAAS;YAAE;SAAQ;QACnB,kBAAkB;QAClB,SAAS;QACT,UAAU;YACR;YACA;gBACE,WAAW;gBACX,OAAO;gBACP,KAAK;YACP;YACA;gBACE,OAAO;gBACP,WAAW;gBACX,QAAQ;oBACN,KAAK;oBACL,UAAU;wBACR;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD;gBACH;YACF;SACD;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4147, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/java.js"], "sourcesContent": ["// https://docs.oracle.com/javase/specs/jls/se15/html/jls-3.html#jls-3.10\nvar decimalDigits = '[0-9](_*[0-9])*';\nvar frac = `\\\\.(${decimalDigits})`;\nvar hexDigits = '[0-9a-fA-F](_*[0-9a-fA-F])*';\nvar NUMERIC = {\n  className: 'number',\n  variants: [\n    // DecimalFloatingPointLiteral\n    // including ExponentPart\n    { begin: `(\\\\b(${decimalDigits})((${frac})|\\\\.)?|(${frac}))` +\n      `[eE][+-]?(${decimalDigits})[fFdD]?\\\\b` },\n    // excluding ExponentPart\n    { begin: `\\\\b(${decimalDigits})((${frac})[fFdD]?\\\\b|\\\\.([fFdD]\\\\b)?)` },\n    { begin: `(${frac})[fFdD]?\\\\b` },\n    { begin: `\\\\b(${decimalDigits})[fFdD]\\\\b` },\n\n    // HexadecimalFloatingPointLiteral\n    { begin: `\\\\b0[xX]((${hexDigits})\\\\.?|(${hexDigits})?\\\\.(${hexDigits}))` +\n      `[pP][+-]?(${decimalDigits})[fFdD]?\\\\b` },\n\n    // DecimalIntegerLiteral\n    { begin: '\\\\b(0|[1-9](_*[0-9])*)[lL]?\\\\b' },\n\n    // HexIntegerLiteral\n    { begin: `\\\\b0[xX](${hexDigits})[lL]?\\\\b` },\n\n    // OctalIntegerLiteral\n    { begin: '\\\\b0(_*[0-7])*[lL]?\\\\b' },\n\n    // BinaryIntegerLiteral\n    { begin: '\\\\b0[bB][01](_*[01])*[lL]?\\\\b' },\n  ],\n  relevance: 0\n};\n\n/*\nLanguage: Java\nAuthor: Vsevolod Solovyov <<EMAIL>>\nCategory: common, enterprise\nWebsite: https://www.java.com/\n*/\n\n\n/**\n * Allows recursive regex expressions to a given depth\n *\n * ie: recurRegex(\"(abc~~~)\", /~~~/g, 2) becomes:\n * (abc(abc(abc)))\n *\n * @param {string} re\n * @param {RegExp} substitution (should be a g mode regex)\n * @param {number} depth\n * @returns {string}``\n */\nfunction recurRegex(re, substitution, depth) {\n  if (depth === -1) return \"\";\n\n  return re.replace(substitution, _ => {\n    return recurRegex(re, substitution, depth - 1);\n  });\n}\n\n/** @type LanguageFn */\nfunction java(hljs) {\n  const regex = hljs.regex;\n  const JAVA_IDENT_RE = '[\\u00C0-\\u02B8a-zA-Z_$][\\u00C0-\\u02B8a-zA-Z_$0-9]*';\n  const GENERIC_IDENT_RE = JAVA_IDENT_RE\n    + recurRegex('(?:<' + JAVA_IDENT_RE + '~~~(?:\\\\s*,\\\\s*' + JAVA_IDENT_RE + '~~~)*>)?', /~~~/g, 2);\n  const MAIN_KEYWORDS = [\n    'synchronized',\n    'abstract',\n    'private',\n    'var',\n    'static',\n    'if',\n    'const ',\n    'for',\n    'while',\n    'strictfp',\n    'finally',\n    'protected',\n    'import',\n    'native',\n    'final',\n    'void',\n    'enum',\n    'else',\n    'break',\n    'transient',\n    'catch',\n    'instanceof',\n    'volatile',\n    'case',\n    'assert',\n    'package',\n    'default',\n    'public',\n    'try',\n    'switch',\n    'continue',\n    'throws',\n    'protected',\n    'public',\n    'private',\n    'module',\n    'requires',\n    'exports',\n    'do',\n    'sealed',\n    'yield',\n    'permits',\n    'goto',\n    'when'\n  ];\n\n  const BUILT_INS = [\n    'super',\n    'this'\n  ];\n\n  const LITERALS = [\n    'false',\n    'true',\n    'null'\n  ];\n\n  const TYPES = [\n    'char',\n    'boolean',\n    'long',\n    'float',\n    'int',\n    'byte',\n    'short',\n    'double'\n  ];\n\n  const KEYWORDS = {\n    keyword: MAIN_KEYWORDS,\n    literal: LITERALS,\n    type: TYPES,\n    built_in: BUILT_INS\n  };\n\n  const ANNOTATION = {\n    className: 'meta',\n    begin: '@' + JAVA_IDENT_RE,\n    contains: [\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        contains: [ \"self\" ] // allow nested () inside our annotation\n      }\n    ]\n  };\n  const PARAMS = {\n    className: 'params',\n    begin: /\\(/,\n    end: /\\)/,\n    keywords: KEYWORDS,\n    relevance: 0,\n    contains: [ hljs.C_BLOCK_COMMENT_MODE ],\n    endsParent: true\n  };\n\n  return {\n    name: 'Java',\n    aliases: [ 'jsp' ],\n    keywords: KEYWORDS,\n    illegal: /<\\/|#/,\n    contains: [\n      hljs.COMMENT(\n        '/\\\\*\\\\*',\n        '\\\\*/',\n        {\n          relevance: 0,\n          contains: [\n            {\n              // eat up @'s in emails to prevent them to be recognized as doctags\n              begin: /\\w+@/,\n              relevance: 0\n            },\n            {\n              className: 'doctag',\n              begin: '@[A-Za-z]+'\n            }\n          ]\n        }\n      ),\n      // relevance boost\n      {\n        begin: /import java\\.[a-z]+\\./,\n        keywords: \"import\",\n        relevance: 2\n      },\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        begin: /\"\"\"/,\n        end: /\"\"\"/,\n        className: \"string\",\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      },\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      {\n        match: [\n          /\\b(?:class|interface|enum|extends|implements|new)/,\n          /\\s+/,\n          JAVA_IDENT_RE\n        ],\n        className: {\n          1: \"keyword\",\n          3: \"title.class\"\n        }\n      },\n      {\n        // Exceptions for hyphenated keywords\n        match: /non-sealed/,\n        scope: \"keyword\"\n      },\n      {\n        begin: [\n          regex.concat(/(?!else)/, JAVA_IDENT_RE),\n          /\\s+/,\n          JAVA_IDENT_RE,\n          /\\s+/,\n          /=(?!=)/\n        ],\n        className: {\n          1: \"type\",\n          3: \"variable\",\n          5: \"operator\"\n        }\n      },\n      {\n        begin: [\n          /record/,\n          /\\s+/,\n          JAVA_IDENT_RE\n        ],\n        className: {\n          1: \"keyword\",\n          3: \"title.class\"\n        },\n        contains: [\n          PARAMS,\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        // Expression keywords prevent 'keyword Name(...)' from being\n        // recognized as a function definition\n        beginKeywords: 'new throw return else',\n        relevance: 0\n      },\n      {\n        begin: [\n          '(?:' + GENERIC_IDENT_RE + '\\\\s+)',\n          hljs.UNDERSCORE_IDENT_RE,\n          /\\s*(?=\\()/\n        ],\n        className: { 2: \"title.function\" },\n        keywords: KEYWORDS,\n        contains: [\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            keywords: KEYWORDS,\n            relevance: 0,\n            contains: [\n              ANNOTATION,\n              hljs.APOS_STRING_MODE,\n              hljs.QUOTE_STRING_MODE,\n              NUMERIC,\n              hljs.C_BLOCK_COMMENT_MODE\n            ]\n          },\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      NUMERIC,\n      ANNOTATION\n    ]\n  };\n}\n\nexport { java as default };\n"], "names": [], "mappings": "AAAA,yEAAyE;;;;AACzE,IAAI,gBAAgB;AACpB,IAAI,OAAO,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;AAClC,IAAI,YAAY;AAChB,IAAI,UAAU;IACZ,WAAW;IACX,UAAU;QACR,8BAA8B;QAC9B,yBAAyB;QACzB;YAAE,OAAO,CAAC,KAAK,EAAE,cAAc,GAAG,EAAE,KAAK,SAAS,EAAE,KAAK,EAAE,CAAC,GAC1D,CAAC,UAAU,EAAE,cAAc,WAAW,CAAC;QAAC;QAC1C,yBAAyB;QACzB;YAAE,OAAO,CAAC,IAAI,EAAE,cAAc,GAAG,EAAE,KAAK,4BAA4B,CAAC;QAAC;QACtE;YAAE,OAAO,CAAC,CAAC,EAAE,KAAK,WAAW,CAAC;QAAC;QAC/B;YAAE,OAAO,CAAC,IAAI,EAAE,cAAc,UAAU,CAAC;QAAC;QAE1C,kCAAkC;QAClC;YAAE,OAAO,CAAC,UAAU,EAAE,UAAU,OAAO,EAAE,UAAU,MAAM,EAAE,UAAU,EAAE,CAAC,GACtE,CAAC,UAAU,EAAE,cAAc,WAAW,CAAC;QAAC;QAE1C,wBAAwB;QACxB;YAAE,OAAO;QAAiC;QAE1C,oBAAoB;QACpB;YAAE,OAAO,CAAC,SAAS,EAAE,UAAU,SAAS,CAAC;QAAC;QAE1C,sBAAsB;QACtB;YAAE,OAAO;QAAyB;QAElC,uBAAuB;QACvB;YAAE,OAAO;QAAgC;KAC1C;IACD,WAAW;AACb;AAEA;;;;;AAKA,GAGA;;;;;;;;;;CAUC,GACD,SAAS,WAAW,EAAE,EAAE,YAAY,EAAE,KAAK;IACzC,IAAI,UAAU,CAAC,GAAG,OAAO;IAEzB,OAAO,GAAG,OAAO,CAAC,cAAc,CAAA;QAC9B,OAAO,WAAW,IAAI,cAAc,QAAQ;IAC9C;AACF;AAEA,qBAAqB,GACrB,SAAS,KAAK,IAAI;IAChB,MAAM,QAAQ,KAAK,KAAK;IACxB,MAAM,gBAAgB;IACtB,MAAM,mBAAmB,gBACrB,WAAW,SAAS,gBAAgB,oBAAoB,gBAAgB,YAAY,QAAQ;IAChG,MAAM,gBAAgB;QACpB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,YAAY;QAChB;QACA;KACD;IAED,MAAM,WAAW;QACf;QACA;QACA;KACD;IAED,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,WAAW;QACf,SAAS;QACT,SAAS;QACT,MAAM;QACN,UAAU;IACZ;IAEA,MAAM,aAAa;QACjB,WAAW;QACX,OAAO,MAAM;QACb,UAAU;YACR;gBACE,OAAO;gBACP,KAAK;gBACL,UAAU;oBAAE;iBAAQ,CAAC,wCAAwC;YAC/D;SACD;IACH;IACA,MAAM,SAAS;QACb,WAAW;QACX,OAAO;QACP,KAAK;QACL,UAAU;QACV,WAAW;QACX,UAAU;YAAE,KAAK,oBAAoB;SAAE;QACvC,YAAY;IACd;IAEA,OAAO;QACL,MAAM;QACN,SAAS;YAAE;SAAO;QAClB,UAAU;QACV,SAAS;QACT,UAAU;YACR,KAAK,OAAO,CACV,WACA,QACA;gBACE,WAAW;gBACX,UAAU;oBACR;wBACE,mEAAmE;wBACnE,OAAO;wBACP,WAAW;oBACb;oBACA;wBACE,WAAW;wBACX,OAAO;oBACT;iBACD;YACH;YAEF,kBAAkB;YAClB;gBACE,OAAO;gBACP,UAAU;gBACV,WAAW;YACb;YACA,KAAK,mBAAmB;YACxB,KAAK,oBAAoB;YACzB;gBACE,OAAO;gBACP,KAAK;gBACL,WAAW;gBACX,UAAU;oBAAE,KAAK,gBAAgB;iBAAE;YACrC;YACA,KAAK,gBAAgB;YACrB,KAAK,iBAAiB;YACtB;gBACE,OAAO;oBACL;oBACA;oBACA;iBACD;gBACD,WAAW;oBACT,GAAG;oBACH,GAAG;gBACL;YACF;YACA;gBACE,qCAAqC;gBACrC,OAAO;gBACP,OAAO;YACT;YACA;gBACE,OAAO;oBACL,MAAM,MAAM,CAAC,YAAY;oBACzB;oBACA;oBACA;oBACA;iBACD;gBACD,WAAW;oBACT,GAAG;oBACH,GAAG;oBACH,GAAG;gBACL;YACF;YACA;gBACE,OAAO;oBACL;oBACA;oBACA;iBACD;gBACD,WAAW;oBACT,GAAG;oBACH,GAAG;gBACL;gBACA,UAAU;oBACR;oBACA,KAAK,mBAAmB;oBACxB,KAAK,oBAAoB;iBAC1B;YACH;YACA;gBACE,6DAA6D;gBAC7D,sCAAsC;gBACtC,eAAe;gBACf,WAAW;YACb;YACA;gBACE,OAAO;oBACL,QAAQ,mBAAmB;oBAC3B,KAAK,mBAAmB;oBACxB;iBACD;gBACD,WAAW;oBAAE,GAAG;gBAAiB;gBACjC,UAAU;gBACV,UAAU;oBACR;wBACE,WAAW;wBACX,OAAO;wBACP,KAAK;wBACL,UAAU;wBACV,WAAW;wBACX,UAAU;4BACR;4BACA,KAAK,gBAAgB;4BACrB,KAAK,iBAAiB;4BACtB;4BACA,KAAK,oBAAoB;yBAC1B;oBACH;oBACA,KAAK,mBAAmB;oBACxB,KAAK,oBAAoB;iBAC1B;YACH;YACA;YACA;SACD;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4448, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/javascript.js"], "sourcesContent": ["const IDENT_RE = '[A-Za-z$_][0-9A-Za-z$_]*';\nconst KEYWORDS = [\n  \"as\", // for exports\n  \"in\",\n  \"of\",\n  \"if\",\n  \"for\",\n  \"while\",\n  \"finally\",\n  \"var\",\n  \"new\",\n  \"function\",\n  \"do\",\n  \"return\",\n  \"void\",\n  \"else\",\n  \"break\",\n  \"catch\",\n  \"instanceof\",\n  \"with\",\n  \"throw\",\n  \"case\",\n  \"default\",\n  \"try\",\n  \"switch\",\n  \"continue\",\n  \"typeof\",\n  \"delete\",\n  \"let\",\n  \"yield\",\n  \"const\",\n  \"class\",\n  // J<PERSON> handles these with a special rule\n  // \"get\",\n  // \"set\",\n  \"debugger\",\n  \"async\",\n  \"await\",\n  \"static\",\n  \"import\",\n  \"from\",\n  \"export\",\n  \"extends\",\n  // It's reached stage 3, which is \"recommended for implementation\":\n  \"using\"\n];\nconst LITERALS = [\n  \"true\",\n  \"false\",\n  \"null\",\n  \"undefined\",\n  \"NaN\",\n  \"Infinity\"\n];\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects\nconst TYPES = [\n  // Fundamental objects\n  \"Object\",\n  \"Function\",\n  \"Boolean\",\n  \"Symbol\",\n  // numbers and dates\n  \"Math\",\n  \"Date\",\n  \"Number\",\n  \"BigInt\",\n  // text\n  \"String\",\n  \"RegExp\",\n  // Indexed collections\n  \"Array\",\n  \"Float32Array\",\n  \"Float64Array\",\n  \"Int8Array\",\n  \"Uint8Array\",\n  \"Uint8ClampedArray\",\n  \"Int16Array\",\n  \"Int32Array\",\n  \"Uint16Array\",\n  \"Uint32Array\",\n  \"BigInt64Array\",\n  \"BigUint64Array\",\n  // Keyed collections\n  \"Set\",\n  \"Map\",\n  \"WeakSet\",\n  \"WeakMap\",\n  // Structured data\n  \"ArrayBuffer\",\n  \"SharedArrayBuffer\",\n  \"Atomics\",\n  \"DataView\",\n  \"JSON\",\n  // Control abstraction objects\n  \"Promise\",\n  \"Generator\",\n  \"GeneratorFunction\",\n  \"AsyncFunction\",\n  // Reflection\n  \"Reflect\",\n  \"Proxy\",\n  // Internationalization\n  \"Intl\",\n  // WebAssembly\n  \"WebAssembly\"\n];\n\nconst ERROR_TYPES = [\n  \"Error\",\n  \"EvalError\",\n  \"InternalError\",\n  \"RangeError\",\n  \"ReferenceError\",\n  \"SyntaxError\",\n  \"TypeError\",\n  \"URIError\"\n];\n\nconst BUILT_IN_GLOBALS = [\n  \"setInterval\",\n  \"setTimeout\",\n  \"clearInterval\",\n  \"clearTimeout\",\n\n  \"require\",\n  \"exports\",\n\n  \"eval\",\n  \"isFinite\",\n  \"isNaN\",\n  \"parseFloat\",\n  \"parseInt\",\n  \"decodeURI\",\n  \"decodeURIComponent\",\n  \"encodeURI\",\n  \"encodeURIComponent\",\n  \"escape\",\n  \"unescape\"\n];\n\nconst BUILT_IN_VARIABLES = [\n  \"arguments\",\n  \"this\",\n  \"super\",\n  \"console\",\n  \"window\",\n  \"document\",\n  \"localStorage\",\n  \"sessionStorage\",\n  \"module\",\n  \"global\" // Node.js\n];\n\nconst BUILT_INS = [].concat(\n  BUILT_IN_GLOBALS,\n  TYPES,\n  ERROR_TYPES\n);\n\n/*\nLanguage: JavaScript\nDescription: JavaScript (JS) is a lightweight, interpreted, or just-in-time compiled programming language with first-class functions.\nCategory: common, scripting, web\nWebsite: https://developer.mozilla.org/en-US/docs/Web/JavaScript\n*/\n\n\n/** @type LanguageFn */\nfunction javascript(hljs) {\n  const regex = hljs.regex;\n  /**\n   * Takes a string like \"<Booger\" and checks to see\n   * if we can find a matching \"</Booger\" later in the\n   * content.\n   * @param {RegExpMatchArray} match\n   * @param {{after:number}} param1\n   */\n  const hasClosingTag = (match, { after }) => {\n    const tag = \"</\" + match[0].slice(1);\n    const pos = match.input.indexOf(tag, after);\n    return pos !== -1;\n  };\n\n  const IDENT_RE$1 = IDENT_RE;\n  const FRAGMENT = {\n    begin: '<>',\n    end: '</>'\n  };\n  // to avoid some special cases inside isTrulyOpeningTag\n  const XML_SELF_CLOSING = /<[A-Za-z0-9\\\\._:-]+\\s*\\/>/;\n  const XML_TAG = {\n    begin: /<[A-Za-z0-9\\\\._:-]+/,\n    end: /\\/[A-Za-z0-9\\\\._:-]+>|\\/>/,\n    /**\n     * @param {RegExpMatchArray} match\n     * @param {CallbackResponse} response\n     */\n    isTrulyOpeningTag: (match, response) => {\n      const afterMatchIndex = match[0].length + match.index;\n      const nextChar = match.input[afterMatchIndex];\n      if (\n        // HTML should not include another raw `<` inside a tag\n        // nested type?\n        // `<Array<Array<number>>`, etc.\n        nextChar === \"<\" ||\n        // the , gives away that this is not HTML\n        // `<T, A extends keyof T, V>`\n        nextChar === \",\"\n        ) {\n        response.ignoreMatch();\n        return;\n      }\n\n      // `<something>`\n      // Quite possibly a tag, lets look for a matching closing tag...\n      if (nextChar === \">\") {\n        // if we cannot find a matching closing tag, then we\n        // will ignore it\n        if (!hasClosingTag(match, { after: afterMatchIndex })) {\n          response.ignoreMatch();\n        }\n      }\n\n      // `<blah />` (self-closing)\n      // handled by simpleSelfClosing rule\n\n      let m;\n      const afterMatch = match.input.substring(afterMatchIndex);\n\n      // some more template typing stuff\n      //  <T = any>(key?: string) => Modify<\n      if ((m = afterMatch.match(/^\\s*=/))) {\n        response.ignoreMatch();\n        return;\n      }\n\n      // `<From extends string>`\n      // technically this could be HTML, but it smells like a type\n      // NOTE: This is ugh, but added specifically for https://github.com/highlightjs/highlight.js/issues/3276\n      if ((m = afterMatch.match(/^\\s+extends\\s+/))) {\n        if (m.index === 0) {\n          response.ignoreMatch();\n          // eslint-disable-next-line no-useless-return\n          return;\n        }\n      }\n    }\n  };\n  const KEYWORDS$1 = {\n    $pattern: IDENT_RE,\n    keyword: KEYWORDS,\n    literal: LITERALS,\n    built_in: BUILT_INS,\n    \"variable.language\": BUILT_IN_VARIABLES\n  };\n\n  // https://tc39.es/ecma262/#sec-literals-numeric-literals\n  const decimalDigits = '[0-9](_?[0-9])*';\n  const frac = `\\\\.(${decimalDigits})`;\n  // DecimalIntegerLiteral, including Annex B NonOctalDecimalIntegerLiteral\n  // https://tc39.es/ecma262/#sec-additional-syntax-numeric-literals\n  const decimalInteger = `0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*`;\n  const NUMBER = {\n    className: 'number',\n    variants: [\n      // DecimalLiteral\n      { begin: `(\\\\b(${decimalInteger})((${frac})|\\\\.)?|(${frac}))` +\n        `[eE][+-]?(${decimalDigits})\\\\b` },\n      { begin: `\\\\b(${decimalInteger})\\\\b((${frac})\\\\b|\\\\.)?|(${frac})\\\\b` },\n\n      // DecimalBigIntegerLiteral\n      { begin: `\\\\b(0|[1-9](_?[0-9])*)n\\\\b` },\n\n      // NonDecimalIntegerLiteral\n      { begin: \"\\\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\\\b\" },\n      { begin: \"\\\\b0[bB][0-1](_?[0-1])*n?\\\\b\" },\n      { begin: \"\\\\b0[oO][0-7](_?[0-7])*n?\\\\b\" },\n\n      // LegacyOctalIntegerLiteral (does not include underscore separators)\n      // https://tc39.es/ecma262/#sec-additional-syntax-numeric-literals\n      { begin: \"\\\\b0[0-7]+n?\\\\b\" },\n    ],\n    relevance: 0\n  };\n\n  const SUBST = {\n    className: 'subst',\n    begin: '\\\\$\\\\{',\n    end: '\\\\}',\n    keywords: KEYWORDS$1,\n    contains: [] // defined later\n  };\n  const HTML_TEMPLATE = {\n    begin: '\\.?html`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [\n        hljs.BACKSLASH_ESCAPE,\n        SUBST\n      ],\n      subLanguage: 'xml'\n    }\n  };\n  const CSS_TEMPLATE = {\n    begin: '\\.?css`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [\n        hljs.BACKSLASH_ESCAPE,\n        SUBST\n      ],\n      subLanguage: 'css'\n    }\n  };\n  const GRAPHQL_TEMPLATE = {\n    begin: '\\.?gql`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [\n        hljs.BACKSLASH_ESCAPE,\n        SUBST\n      ],\n      subLanguage: 'graphql'\n    }\n  };\n  const TEMPLATE_STRING = {\n    className: 'string',\n    begin: '`',\n    end: '`',\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      SUBST\n    ]\n  };\n  const JSDOC_COMMENT = hljs.COMMENT(\n    /\\/\\*\\*(?!\\/)/,\n    '\\\\*/',\n    {\n      relevance: 0,\n      contains: [\n        {\n          begin: '(?=@[A-Za-z]+)',\n          relevance: 0,\n          contains: [\n            {\n              className: 'doctag',\n              begin: '@[A-Za-z]+'\n            },\n            {\n              className: 'type',\n              begin: '\\\\{',\n              end: '\\\\}',\n              excludeEnd: true,\n              excludeBegin: true,\n              relevance: 0\n            },\n            {\n              className: 'variable',\n              begin: IDENT_RE$1 + '(?=\\\\s*(-)|$)',\n              endsParent: true,\n              relevance: 0\n            },\n            // eat spaces (not newlines) so we can find\n            // types or variables\n            {\n              begin: /(?=[^\\n])\\s/,\n              relevance: 0\n            }\n          ]\n        }\n      ]\n    }\n  );\n  const COMMENT = {\n    className: \"comment\",\n    variants: [\n      JSDOC_COMMENT,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.C_LINE_COMMENT_MODE\n    ]\n  };\n  const SUBST_INTERNALS = [\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE,\n    HTML_TEMPLATE,\n    CSS_TEMPLATE,\n    GRAPHQL_TEMPLATE,\n    TEMPLATE_STRING,\n    // Skip numbers when they are part of a variable name\n    { match: /\\$\\d+/ },\n    NUMBER,\n    // This is intentional:\n    // See https://github.com/highlightjs/highlight.js/issues/3288\n    // hljs.REGEXP_MODE\n  ];\n  SUBST.contains = SUBST_INTERNALS\n    .concat({\n      // we need to pair up {} inside our subst to prevent\n      // it from ending too early by matching another }\n      begin: /\\{/,\n      end: /\\}/,\n      keywords: KEYWORDS$1,\n      contains: [\n        \"self\"\n      ].concat(SUBST_INTERNALS)\n    });\n  const SUBST_AND_COMMENTS = [].concat(COMMENT, SUBST.contains);\n  const PARAMS_CONTAINS = SUBST_AND_COMMENTS.concat([\n    // eat recursive parens in sub expressions\n    {\n      begin: /(\\s*)\\(/,\n      end: /\\)/,\n      keywords: KEYWORDS$1,\n      contains: [\"self\"].concat(SUBST_AND_COMMENTS)\n    }\n  ]);\n  const PARAMS = {\n    className: 'params',\n    // convert this to negative lookbehind in v12\n    begin: /(\\s*)\\(/, // to match the parms with\n    end: /\\)/,\n    excludeBegin: true,\n    excludeEnd: true,\n    keywords: KEYWORDS$1,\n    contains: PARAMS_CONTAINS\n  };\n\n  // ES6 classes\n  const CLASS_OR_EXTENDS = {\n    variants: [\n      // class Car extends vehicle\n      {\n        match: [\n          /class/,\n          /\\s+/,\n          IDENT_RE$1,\n          /\\s+/,\n          /extends/,\n          /\\s+/,\n          regex.concat(IDENT_RE$1, \"(\", regex.concat(/\\./, IDENT_RE$1), \")*\")\n        ],\n        scope: {\n          1: \"keyword\",\n          3: \"title.class\",\n          5: \"keyword\",\n          7: \"title.class.inherited\"\n        }\n      },\n      // class Car\n      {\n        match: [\n          /class/,\n          /\\s+/,\n          IDENT_RE$1\n        ],\n        scope: {\n          1: \"keyword\",\n          3: \"title.class\"\n        }\n      },\n\n    ]\n  };\n\n  const CLASS_REFERENCE = {\n    relevance: 0,\n    match:\n    regex.either(\n      // Hard coded exceptions\n      /\\bJSON/,\n      // Float32Array, OutT\n      /\\b[A-Z][a-z]+([A-Z][a-z]*|\\d)*/,\n      // CSSFactory, CSSFactoryT\n      /\\b[A-Z]{2,}([A-Z][a-z]+|\\d)+([A-Z][a-z]*)*/,\n      // FPs, FPsT\n      /\\b[A-Z]{2,}[a-z]+([A-Z][a-z]+|\\d)*([A-Z][a-z]*)*/,\n      // P\n      // single letters are not highlighted\n      // BLAH\n      // this will be flagged as a UPPER_CASE_CONSTANT instead\n    ),\n    className: \"title.class\",\n    keywords: {\n      _: [\n        // se we still get relevance credit for JS library classes\n        ...TYPES,\n        ...ERROR_TYPES\n      ]\n    }\n  };\n\n  const USE_STRICT = {\n    label: \"use_strict\",\n    className: 'meta',\n    relevance: 10,\n    begin: /^\\s*['\"]use (strict|asm)['\"]/\n  };\n\n  const FUNCTION_DEFINITION = {\n    variants: [\n      {\n        match: [\n          /function/,\n          /\\s+/,\n          IDENT_RE$1,\n          /(?=\\s*\\()/\n        ]\n      },\n      // anonymous function\n      {\n        match: [\n          /function/,\n          /\\s*(?=\\()/\n        ]\n      }\n    ],\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    label: \"func.def\",\n    contains: [ PARAMS ],\n    illegal: /%/\n  };\n\n  const UPPER_CASE_CONSTANT = {\n    relevance: 0,\n    match: /\\b[A-Z][A-Z_0-9]+\\b/,\n    className: \"variable.constant\"\n  };\n\n  function noneOf(list) {\n    return regex.concat(\"(?!\", list.join(\"|\"), \")\");\n  }\n\n  const FUNCTION_CALL = {\n    match: regex.concat(\n      /\\b/,\n      noneOf([\n        ...BUILT_IN_GLOBALS,\n        \"super\",\n        \"import\"\n      ].map(x => `${x}\\\\s*\\\\(`)),\n      IDENT_RE$1, regex.lookahead(/\\s*\\(/)),\n    className: \"title.function\",\n    relevance: 0\n  };\n\n  const PROPERTY_ACCESS = {\n    begin: regex.concat(/\\./, regex.lookahead(\n      regex.concat(IDENT_RE$1, /(?![0-9A-Za-z$_(])/)\n    )),\n    end: IDENT_RE$1,\n    excludeBegin: true,\n    keywords: \"prototype\",\n    className: \"property\",\n    relevance: 0\n  };\n\n  const GETTER_OR_SETTER = {\n    match: [\n      /get|set/,\n      /\\s+/,\n      IDENT_RE$1,\n      /(?=\\()/\n    ],\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    contains: [\n      { // eat to avoid empty params\n        begin: /\\(\\)/\n      },\n      PARAMS\n    ]\n  };\n\n  const FUNC_LEAD_IN_RE = '(\\\\(' +\n    '[^()]*(\\\\(' +\n    '[^()]*(\\\\(' +\n    '[^()]*' +\n    '\\\\)[^()]*)*' +\n    '\\\\)[^()]*)*' +\n    '\\\\)|' + hljs.UNDERSCORE_IDENT_RE + ')\\\\s*=>';\n\n  const FUNCTION_VARIABLE = {\n    match: [\n      /const|var|let/, /\\s+/,\n      IDENT_RE$1, /\\s*/,\n      /=\\s*/,\n      /(async\\s*)?/, // async is optional\n      regex.lookahead(FUNC_LEAD_IN_RE)\n    ],\n    keywords: \"async\",\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    contains: [\n      PARAMS\n    ]\n  };\n\n  return {\n    name: 'JavaScript',\n    aliases: ['js', 'jsx', 'mjs', 'cjs'],\n    keywords: KEYWORDS$1,\n    // this will be extended by TypeScript\n    exports: { PARAMS_CONTAINS, CLASS_REFERENCE },\n    illegal: /#(?![$_A-z])/,\n    contains: [\n      hljs.SHEBANG({\n        label: \"shebang\",\n        binary: \"node\",\n        relevance: 5\n      }),\n      USE_STRICT,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      HTML_TEMPLATE,\n      CSS_TEMPLATE,\n      GRAPHQL_TEMPLATE,\n      TEMPLATE_STRING,\n      COMMENT,\n      // Skip numbers when they are part of a variable name\n      { match: /\\$\\d+/ },\n      NUMBER,\n      CLASS_REFERENCE,\n      {\n        scope: 'attr',\n        match: IDENT_RE$1 + regex.lookahead(':'),\n        relevance: 0\n      },\n      FUNCTION_VARIABLE,\n      { // \"value\" container\n        begin: '(' + hljs.RE_STARTERS_RE + '|\\\\b(case|return|throw)\\\\b)\\\\s*',\n        keywords: 'return throw case',\n        relevance: 0,\n        contains: [\n          COMMENT,\n          hljs.REGEXP_MODE,\n          {\n            className: 'function',\n            // we have to count the parens to make sure we actually have the\n            // correct bounding ( ) before the =>.  There could be any number of\n            // sub-expressions inside also surrounded by parens.\n            begin: FUNC_LEAD_IN_RE,\n            returnBegin: true,\n            end: '\\\\s*=>',\n            contains: [\n              {\n                className: 'params',\n                variants: [\n                  {\n                    begin: hljs.UNDERSCORE_IDENT_RE,\n                    relevance: 0\n                  },\n                  {\n                    className: null,\n                    begin: /\\(\\s*\\)/,\n                    skip: true\n                  },\n                  {\n                    begin: /(\\s*)\\(/,\n                    end: /\\)/,\n                    excludeBegin: true,\n                    excludeEnd: true,\n                    keywords: KEYWORDS$1,\n                    contains: PARAMS_CONTAINS\n                  }\n                ]\n              }\n            ]\n          },\n          { // could be a comma delimited list of params to a function call\n            begin: /,/,\n            relevance: 0\n          },\n          {\n            match: /\\s+/,\n            relevance: 0\n          },\n          { // JSX\n            variants: [\n              { begin: FRAGMENT.begin, end: FRAGMENT.end },\n              { match: XML_SELF_CLOSING },\n              {\n                begin: XML_TAG.begin,\n                // we carefully check the opening tag to see if it truly\n                // is a tag and not a false positive\n                'on:begin': XML_TAG.isTrulyOpeningTag,\n                end: XML_TAG.end\n              }\n            ],\n            subLanguage: 'xml',\n            contains: [\n              {\n                begin: XML_TAG.begin,\n                end: XML_TAG.end,\n                skip: true,\n                contains: ['self']\n              }\n            ]\n          }\n        ],\n      },\n      FUNCTION_DEFINITION,\n      {\n        // prevent this from getting swallowed up by function\n        // since they appear \"function like\"\n        beginKeywords: \"while if switch catch for\"\n      },\n      {\n        // we have to count the parens to make sure we actually have the correct\n        // bounding ( ).  There could be any number of sub-expressions inside\n        // also surrounded by parens.\n        begin: '\\\\b(?!function)' + hljs.UNDERSCORE_IDENT_RE +\n          '\\\\(' + // first parens\n          '[^()]*(\\\\(' +\n            '[^()]*(\\\\(' +\n              '[^()]*' +\n            '\\\\)[^()]*)*' +\n          '\\\\)[^()]*)*' +\n          '\\\\)\\\\s*\\\\{', // end parens\n        returnBegin:true,\n        label: \"func.def\",\n        contains: [\n          PARAMS,\n          hljs.inherit(hljs.TITLE_MODE, { begin: IDENT_RE$1, className: \"title.function\" })\n        ]\n      },\n      // catch ... so it won't trigger the property rule below\n      {\n        match: /\\.\\.\\./,\n        relevance: 0\n      },\n      PROPERTY_ACCESS,\n      // hack: prevents detection of keywords in some circumstances\n      // .keyword()\n      // $keyword = x\n      {\n        match: '\\\\$' + IDENT_RE$1,\n        relevance: 0\n      },\n      {\n        match: [ /\\bconstructor(?=\\s*\\()/ ],\n        className: { 1: \"title.function\" },\n        contains: [ PARAMS ]\n      },\n      FUNCTION_CALL,\n      UPPER_CASE_CONSTANT,\n      CLASS_OR_EXTENDS,\n      GETTER_OR_SETTER,\n      {\n        match: /\\$[(.]/ // relevance booster for a pattern common to JS libs: `$(something)` and `$.something`\n      }\n    ]\n  };\n}\n\nexport { javascript as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,WAAW;AACjB,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,uCAAuC;IACvC,SAAS;IACT,SAAS;IACT;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,mEAAmE;IACnE;CACD;AACD,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;IACA;CACD;AAED,mFAAmF;AACnF,MAAM,QAAQ;IACZ,sBAAsB;IACtB;IACA;IACA;IACA;IACA,oBAAoB;IACpB;IACA;IACA;IACA;IACA,OAAO;IACP;IACA;IACA,sBAAsB;IACtB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,oBAAoB;IACpB;IACA;IACA;IACA;IACA,kBAAkB;IAClB;IACA;IACA;IACA;IACA;IACA,8BAA8B;IAC9B;IACA;IACA;IACA;IACA,aAAa;IACb;IACA;IACA,uBAAuB;IACvB;IACA,cAAc;IACd;CACD;AAED,MAAM,cAAc;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,mBAAmB;IACvB;IACA;IACA;IACA;IAEA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAAS,UAAU;CACpB;AAED,MAAM,YAAY,EAAE,CAAC,MAAM,CACzB,kBACA,OACA;AAGF;;;;;AAKA,GAGA,qBAAqB,GACrB,SAAS,WAAW,IAAI;IACtB,MAAM,QAAQ,KAAK,KAAK;IACxB;;;;;;GAMC,GACD,MAAM,gBAAgB,CAAC,OAAO,EAAE,KAAK,EAAE;QACrC,MAAM,MAAM,OAAO,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC;QAClC,MAAM,MAAM,MAAM,KAAK,CAAC,OAAO,CAAC,KAAK;QACrC,OAAO,QAAQ,CAAC;IAClB;IAEA,MAAM,aAAa;IACnB,MAAM,WAAW;QACf,OAAO;QACP,KAAK;IACP;IACA,uDAAuD;IACvD,MAAM,mBAAmB;IACzB,MAAM,UAAU;QACd,OAAO;QACP,KAAK;QACL;;;KAGC,GACD,mBAAmB,CAAC,OAAO;YACzB,MAAM,kBAAkB,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG,MAAM,KAAK;YACrD,MAAM,WAAW,MAAM,KAAK,CAAC,gBAAgB;YAC7C,IACE,uDAAuD;YACvD,eAAe;YACf,gCAAgC;YAChC,aAAa,OACb,yCAAyC;YACzC,8BAA8B;YAC9B,aAAa,KACX;gBACF,SAAS,WAAW;gBACpB;YACF;YAEA,gBAAgB;YAChB,gEAAgE;YAChE,IAAI,aAAa,KAAK;gBACpB,oDAAoD;gBACpD,iBAAiB;gBACjB,IAAI,CAAC,cAAc,OAAO;oBAAE,OAAO;gBAAgB,IAAI;oBACrD,SAAS,WAAW;gBACtB;YACF;YAEA,4BAA4B;YAC5B,oCAAoC;YAEpC,IAAI;YACJ,MAAM,aAAa,MAAM,KAAK,CAAC,SAAS,CAAC;YAEzC,kCAAkC;YAClC,sCAAsC;YACtC,IAAK,IAAI,WAAW,KAAK,CAAC,UAAW;gBACnC,SAAS,WAAW;gBACpB;YACF;YAEA,0BAA0B;YAC1B,4DAA4D;YAC5D,wGAAwG;YACxG,IAAK,IAAI,WAAW,KAAK,CAAC,mBAAoB;gBAC5C,IAAI,EAAE,KAAK,KAAK,GAAG;oBACjB,SAAS,WAAW;oBACpB,6CAA6C;oBAC7C;gBACF;YACF;QACF;IACF;IACA,MAAM,aAAa;QACjB,UAAU;QACV,SAAS;QACT,SAAS;QACT,UAAU;QACV,qBAAqB;IACvB;IAEA,yDAAyD;IACzD,MAAM,gBAAgB;IACtB,MAAM,OAAO,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;IACpC,yEAAyE;IACzE,kEAAkE;IAClE,MAAM,iBAAiB,CAAC,mCAAmC,CAAC;IAC5D,MAAM,SAAS;QACb,WAAW;QACX,UAAU;YACR,iBAAiB;YACjB;gBAAE,OAAO,CAAC,KAAK,EAAE,eAAe,GAAG,EAAE,KAAK,SAAS,EAAE,KAAK,EAAE,CAAC,GAC3D,CAAC,UAAU,EAAE,cAAc,IAAI,CAAC;YAAC;YACnC;gBAAE,OAAO,CAAC,IAAI,EAAE,eAAe,MAAM,EAAE,KAAK,YAAY,EAAE,KAAK,IAAI,CAAC;YAAC;YAErE,2BAA2B;YAC3B;gBAAE,OAAO,CAAC,0BAA0B,CAAC;YAAC;YAEtC,2BAA2B;YAC3B;gBAAE,OAAO;YAA2C;YACpD;gBAAE,OAAO;YAA+B;YACxC;gBAAE,OAAO;YAA+B;YAExC,qEAAqE;YACrE,kEAAkE;YAClE;gBAAE,OAAO;YAAkB;SAC5B;QACD,WAAW;IACb;IAEA,MAAM,QAAQ;QACZ,WAAW;QACX,OAAO;QACP,KAAK;QACL,UAAU;QACV,UAAU,EAAE,CAAC,gBAAgB;IAC/B;IACA,MAAM,gBAAgB;QACpB,OAAO;QACP,KAAK;QACL,QAAQ;YACN,KAAK;YACL,WAAW;YACX,UAAU;gBACR,KAAK,gBAAgB;gBACrB;aACD;YACD,aAAa;QACf;IACF;IACA,MAAM,eAAe;QACnB,OAAO;QACP,KAAK;QACL,QAAQ;YACN,KAAK;YACL,WAAW;YACX,UAAU;gBACR,KAAK,gBAAgB;gBACrB;aACD;YACD,aAAa;QACf;IACF;IACA,MAAM,mBAAmB;QACvB,OAAO;QACP,KAAK;QACL,QAAQ;YACN,KAAK;YACL,WAAW;YACX,UAAU;gBACR,KAAK,gBAAgB;gBACrB;aACD;YACD,aAAa;QACf;IACF;IACA,MAAM,kBAAkB;QACtB,WAAW;QACX,OAAO;QACP,KAAK;QACL,UAAU;YACR,KAAK,gBAAgB;YACrB;SACD;IACH;IACA,MAAM,gBAAgB,KAAK,OAAO,CAChC,gBACA,QACA;QACE,WAAW;QACX,UAAU;YACR;gBACE,OAAO;gBACP,WAAW;gBACX,UAAU;oBACR;wBACE,WAAW;wBACX,OAAO;oBACT;oBACA;wBACE,WAAW;wBACX,OAAO;wBACP,KAAK;wBACL,YAAY;wBACZ,cAAc;wBACd,WAAW;oBACb;oBACA;wBACE,WAAW;wBACX,OAAO,aAAa;wBACpB,YAAY;wBACZ,WAAW;oBACb;oBACA,2CAA2C;oBAC3C,qBAAqB;oBACrB;wBACE,OAAO;wBACP,WAAW;oBACb;iBACD;YACH;SACD;IACH;IAEF,MAAM,UAAU;QACd,WAAW;QACX,UAAU;YACR;YACA,KAAK,oBAAoB;YACzB,KAAK,mBAAmB;SACzB;IACH;IACA,MAAM,kBAAkB;QACtB,KAAK,gBAAgB;QACrB,KAAK,iBAAiB;QACtB;QACA;QACA;QACA;QACA,qDAAqD;QACrD;YAAE,OAAO;QAAQ;QACjB;KAID;IACD,MAAM,QAAQ,GAAG,gBACd,MAAM,CAAC;QACN,oDAAoD;QACpD,iDAAiD;QACjD,OAAO;QACP,KAAK;QACL,UAAU;QACV,UAAU;YACR;SACD,CAAC,MAAM,CAAC;IACX;IACF,MAAM,qBAAqB,EAAE,CAAC,MAAM,CAAC,SAAS,MAAM,QAAQ;IAC5D,MAAM,kBAAkB,mBAAmB,MAAM,CAAC;QAChD,0CAA0C;QAC1C;YACE,OAAO;YACP,KAAK;YACL,UAAU;YACV,UAAU;gBAAC;aAAO,CAAC,MAAM,CAAC;QAC5B;KACD;IACD,MAAM,SAAS;QACb,WAAW;QACX,6CAA6C;QAC7C,OAAO;QACP,KAAK;QACL,cAAc;QACd,YAAY;QACZ,UAAU;QACV,UAAU;IACZ;IAEA,cAAc;IACd,MAAM,mBAAmB;QACvB,UAAU;YACR,4BAA4B;YAC5B;gBACE,OAAO;oBACL;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA,MAAM,MAAM,CAAC,YAAY,KAAK,MAAM,MAAM,CAAC,MAAM,aAAa;iBAC/D;gBACD,OAAO;oBACL,GAAG;oBACH,GAAG;oBACH,GAAG;oBACH,GAAG;gBACL;YACF;YACA,YAAY;YACZ;gBACE,OAAO;oBACL;oBACA;oBACA;iBACD;gBACD,OAAO;oBACL,GAAG;oBACH,GAAG;gBACL;YACF;SAED;IACH;IAEA,MAAM,kBAAkB;QACtB,WAAW;QACX,OACA,MAAM,MAAM,CACV,wBAAwB;QACxB,UACA,qBAAqB;QACrB,kCACA,0BAA0B;QAC1B,8CACA,YAAY;QACZ;QAMF,WAAW;QACX,UAAU;YACR,GAAG;gBACD,0DAA0D;mBACvD;mBACA;aACJ;QACH;IACF;IAEA,MAAM,aAAa;QACjB,OAAO;QACP,WAAW;QACX,WAAW;QACX,OAAO;IACT;IAEA,MAAM,sBAAsB;QAC1B,UAAU;YACR;gBACE,OAAO;oBACL;oBACA;oBACA;oBACA;iBACD;YACH;YACA,qBAAqB;YACrB;gBACE,OAAO;oBACL;oBACA;iBACD;YACH;SACD;QACD,WAAW;YACT,GAAG;YACH,GAAG;QACL;QACA,OAAO;QACP,UAAU;YAAE;SAAQ;QACpB,SAAS;IACX;IAEA,MAAM,sBAAsB;QAC1B,WAAW;QACX,OAAO;QACP,WAAW;IACb;IAEA,SAAS,OAAO,IAAI;QAClB,OAAO,MAAM,MAAM,CAAC,OAAO,KAAK,IAAI,CAAC,MAAM;IAC7C;IAEA,MAAM,gBAAgB;QACpB,OAAO,MAAM,MAAM,CACjB,MACA,OAAO;eACF;YACH;YACA;SACD,CAAC,GAAG,CAAC,CAAA,IAAK,GAAG,EAAE,OAAO,CAAC,IACxB,YAAY,MAAM,SAAS,CAAC;QAC9B,WAAW;QACX,WAAW;IACb;IAEA,MAAM,kBAAkB;QACtB,OAAO,MAAM,MAAM,CAAC,MAAM,MAAM,SAAS,CACvC,MAAM,MAAM,CAAC,YAAY;QAE3B,KAAK;QACL,cAAc;QACd,UAAU;QACV,WAAW;QACX,WAAW;IACb;IAEA,MAAM,mBAAmB;QACvB,OAAO;YACL;YACA;YACA;YACA;SACD;QACD,WAAW;YACT,GAAG;YACH,GAAG;QACL;QACA,UAAU;YACR;gBACE,OAAO;YACT;YACA;SACD;IACH;IAEA,MAAM,kBAAkB,SACtB,eACA,eACA,WACA,gBACA,gBACA,SAAS,KAAK,mBAAmB,GAAG;IAEtC,MAAM,oBAAoB;QACxB,OAAO;YACL;YAAiB;YACjB;YAAY;YACZ;YACA;YACA,MAAM,SAAS,CAAC;SACjB;QACD,UAAU;QACV,WAAW;YACT,GAAG;YACH,GAAG;QACL;QACA,UAAU;YACR;SACD;IACH;IAEA,OAAO;QACL,MAAM;QACN,SAAS;YAAC;YAAM;YAAO;YAAO;SAAM;QACpC,UAAU;QACV,sCAAsC;QACtC,SAAS;YAAE;YAAiB;QAAgB;QAC5C,SAAS;QACT,UAAU;YACR,KAAK,OAAO,CAAC;gBACX,OAAO;gBACP,QAAQ;gBACR,WAAW;YACb;YACA;YACA,KAAK,gBAAgB;YACrB,KAAK,iBAAiB;YACtB;YACA;YACA;YACA;YACA;YACA,qDAAqD;YACrD;gBAAE,OAAO;YAAQ;YACjB;YACA;YACA;gBACE,OAAO;gBACP,OAAO,aAAa,MAAM,SAAS,CAAC;gBACpC,WAAW;YACb;YACA;YACA;gBACE,OAAO,MAAM,KAAK,cAAc,GAAG;gBACnC,UAAU;gBACV,WAAW;gBACX,UAAU;oBACR;oBACA,KAAK,WAAW;oBAChB;wBACE,WAAW;wBACX,gEAAgE;wBAChE,oEAAoE;wBACpE,oDAAoD;wBACpD,OAAO;wBACP,aAAa;wBACb,KAAK;wBACL,UAAU;4BACR;gCACE,WAAW;gCACX,UAAU;oCACR;wCACE,OAAO,KAAK,mBAAmB;wCAC/B,WAAW;oCACb;oCACA;wCACE,WAAW;wCACX,OAAO;wCACP,MAAM;oCACR;oCACA;wCACE,OAAO;wCACP,KAAK;wCACL,cAAc;wCACd,YAAY;wCACZ,UAAU;wCACV,UAAU;oCACZ;iCACD;4BACH;yBACD;oBACH;oBACA;wBACE,OAAO;wBACP,WAAW;oBACb;oBACA;wBACE,OAAO;wBACP,WAAW;oBACb;oBACA;wBACE,UAAU;4BACR;gCAAE,OAAO,SAAS,KAAK;gCAAE,KAAK,SAAS,GAAG;4BAAC;4BAC3C;gCAAE,OAAO;4BAAiB;4BAC1B;gCACE,OAAO,QAAQ,KAAK;gCACpB,wDAAwD;gCACxD,oCAAoC;gCACpC,YAAY,QAAQ,iBAAiB;gCACrC,KAAK,QAAQ,GAAG;4BAClB;yBACD;wBACD,aAAa;wBACb,UAAU;4BACR;gCACE,OAAO,QAAQ,KAAK;gCACpB,KAAK,QAAQ,GAAG;gCAChB,MAAM;gCACN,UAAU;oCAAC;iCAAO;4BACpB;yBACD;oBACH;iBACD;YACH;YACA;YACA;gBACE,qDAAqD;gBACrD,oCAAoC;gBACpC,eAAe;YACjB;YACA;gBACE,wEAAwE;gBACxE,qEAAqE;gBACrE,6BAA6B;gBAC7B,OAAO,oBAAoB,KAAK,mBAAmB,GACjD,QAAQ,eAAe;gBACvB,eACE,eACE,WACF,gBACF,gBACA;gBACF,aAAY;gBACZ,OAAO;gBACP,UAAU;oBACR;oBACA,KAAK,OAAO,CAAC,KAAK,UAAU,EAAE;wBAAE,OAAO;wBAAY,WAAW;oBAAiB;iBAChF;YACH;YACA,wDAAwD;YACxD;gBACE,OAAO;gBACP,WAAW;YACb;YACA;YACA,6DAA6D;YAC7D,aAAa;YACb,eAAe;YACf;gBACE,OAAO,QAAQ;gBACf,WAAW;YACb;YACA;gBACE,OAAO;oBAAE;iBAA0B;gBACnC,WAAW;oBAAE,GAAG;gBAAiB;gBACjC,UAAU;oBAAE;iBAAQ;YACtB;YACA;YACA;YACA;YACA;YACA;gBACE,OAAO,SAAS,sFAAsF;YACxG;SACD;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5194, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/json.js"], "sourcesContent": ["/*\nLanguage: JSON\nDescription: <PERSON><PERSON><PERSON> (JavaScript Object Notation) is a lightweight data-interchange format.\nAuthor: <PERSON> <<EMAIL>>\nWebsite: http://www.json.org\nCategory: common, protocols, web\n*/\n\nfunction json(hljs) {\n  const ATTRIBUTE = {\n    className: 'attr',\n    begin: /\"(\\\\.|[^\\\\\"\\r\\n])*\"(?=\\s*:)/,\n    relevance: 1.01\n  };\n  const PUNCTUATION = {\n    match: /[{}[\\],:]/,\n    className: \"punctuation\",\n    relevance: 0\n  };\n  const LITERALS = [\n    \"true\",\n    \"false\",\n    \"null\"\n  ];\n  // NOTE: normally we would rely on `keywords` for this but using a mode here allows us\n  // - to use the very tight `illegal: \\S` rule later to flag any other character\n  // - as illegal indicating that despite looking like JSON we do not truly have\n  // - JSON and thus improve false-positively greatly since JSO<PERSON> will try and claim\n  // - all sorts of JSON looking stuff\n  const LITERALS_MODE = {\n    scope: \"literal\",\n    beginKeywords: LITERALS.join(\" \"),\n  };\n\n  return {\n    name: '<PERSON><PERSON><PERSON>',\n    aliases: ['jsonc'],\n    keywords:{\n      literal: LITERALS,\n    },\n    contains: [\n      ATTRIBUTE,\n      PUNCTUATION,\n      hljs.QUOTE_STRING_MODE,\n      LITERALS_MODE,\n      hljs.C_NUMBER_MODE,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE\n    ],\n    illegal: '\\\\S'\n  };\n}\n\nexport { json as default };\n"], "names": [], "mappings": "AAAA;;;;;;AAMA;;;AAEA,SAAS,KAAK,IAAI;IAChB,MAAM,YAAY;QAChB,WAAW;QACX,OAAO;QACP,WAAW;IACb;IACA,MAAM,cAAc;QAClB,OAAO;QACP,WAAW;QACX,WAAW;IACb;IACA,MAAM,WAAW;QACf;QACA;QACA;KACD;IACD,sFAAsF;IACtF,+EAA+E;IAC/E,8EAA8E;IAC9E,iFAAiF;IACjF,oCAAoC;IACpC,MAAM,gBAAgB;QACpB,OAAO;QACP,eAAe,SAAS,IAAI,CAAC;IAC/B;IAEA,OAAO;QACL,MAAM;QACN,SAAS;YAAC;SAAQ;QAClB,UAAS;YACP,SAAS;QACX;QACA,UAAU;YACR;YACA;YACA,KAAK,iBAAiB;YACtB;YACA,KAAK,aAAa;YAClB,KAAK,mBAAmB;YACxB,KAAK,oBAAoB;SAC1B;QACD,SAAS;IACX;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5255, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/kotlin.js"], "sourcesContent": ["// https://docs.oracle.com/javase/specs/jls/se15/html/jls-3.html#jls-3.10\nvar decimalDigits = '[0-9](_*[0-9])*';\nvar frac = `\\\\.(${decimalDigits})`;\nvar hexDigits = '[0-9a-fA-F](_*[0-9a-fA-F])*';\nvar NUMERIC = {\n  className: 'number',\n  variants: [\n    // DecimalFloatingPointLiteral\n    // including ExponentPart\n    { begin: `(\\\\b(${decimalDigits})((${frac})|\\\\.)?|(${frac}))` +\n      `[eE][+-]?(${decimalDigits})[fFdD]?\\\\b` },\n    // excluding ExponentPart\n    { begin: `\\\\b(${decimalDigits})((${frac})[fFdD]?\\\\b|\\\\.([fFdD]\\\\b)?)` },\n    { begin: `(${frac})[fFdD]?\\\\b` },\n    { begin: `\\\\b(${decimalDigits})[fFdD]\\\\b` },\n\n    // HexadecimalFloatingPointLiteral\n    { begin: `\\\\b0[xX]((${hexDigits})\\\\.?|(${hexDigits})?\\\\.(${hexDigits}))` +\n      `[pP][+-]?(${decimalDigits})[fFdD]?\\\\b` },\n\n    // DecimalIntegerLiteral\n    { begin: '\\\\b(0|[1-9](_*[0-9])*)[lL]?\\\\b' },\n\n    // HexIntegerLiteral\n    { begin: `\\\\b0[xX](${hexDigits})[lL]?\\\\b` },\n\n    // OctalIntegerLiteral\n    { begin: '\\\\b0(_*[0-7])*[lL]?\\\\b' },\n\n    // BinaryIntegerLiteral\n    { begin: '\\\\b0[bB][01](_*[01])*[lL]?\\\\b' },\n  ],\n  relevance: 0\n};\n\n/*\n Language: Kotlin\n Description: Kotlin is an OSS statically typed programming language that targets the JVM, Android, JavaScript and Native.\n Author: Sergey Mashkov <<EMAIL>>\n Website: https://kotlinlang.org\n Category: common\n */\n\n\nfunction kotlin(hljs) {\n  const KEYWORDS = {\n    keyword:\n      'abstract as val var vararg get set class object open private protected public noinline '\n      + 'crossinline dynamic final enum if else do while for when throw try catch finally '\n      + 'import package is in fun override companion reified inline lateinit init '\n      + 'interface annotation data sealed internal infix operator out by constructor super '\n      + 'tailrec where const inner suspend typealias external expect actual',\n    built_in:\n      'Byte Short Char Int Long Boolean Float Double Void Unit Nothing',\n    literal:\n      'true false null'\n  };\n  const KEYWORDS_WITH_LABEL = {\n    className: 'keyword',\n    begin: /\\b(break|continue|return|this)\\b/,\n    starts: { contains: [\n      {\n        className: 'symbol',\n        begin: /@\\w+/\n      }\n    ] }\n  };\n  const LABEL = {\n    className: 'symbol',\n    begin: hljs.UNDERSCORE_IDENT_RE + '@'\n  };\n\n  // for string templates\n  const SUBST = {\n    className: 'subst',\n    begin: /\\$\\{/,\n    end: /\\}/,\n    contains: [ hljs.C_NUMBER_MODE ]\n  };\n  const VARIABLE = {\n    className: 'variable',\n    begin: '\\\\$' + hljs.UNDERSCORE_IDENT_RE\n  };\n  const STRING = {\n    className: 'string',\n    variants: [\n      {\n        begin: '\"\"\"',\n        end: '\"\"\"(?=[^\"])',\n        contains: [\n          VARIABLE,\n          SUBST\n        ]\n      },\n      // Can't use built-in modes easily, as we want to use STRING in the meta\n      // context as 'meta-string' and there's no syntax to remove explicitly set\n      // classNames in built-in modes.\n      {\n        begin: '\\'',\n        end: '\\'',\n        illegal: /\\n/,\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      },\n      {\n        begin: '\"',\n        end: '\"',\n        illegal: /\\n/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          VARIABLE,\n          SUBST\n        ]\n      }\n    ]\n  };\n  SUBST.contains.push(STRING);\n\n  const ANNOTATION_USE_SITE = {\n    className: 'meta',\n    begin: '@(?:file|property|field|get|set|receiver|param|setparam|delegate)\\\\s*:(?:\\\\s*' + hljs.UNDERSCORE_IDENT_RE + ')?'\n  };\n  const ANNOTATION = {\n    className: 'meta',\n    begin: '@' + hljs.UNDERSCORE_IDENT_RE,\n    contains: [\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        contains: [\n          hljs.inherit(STRING, { className: 'string' }),\n          \"self\"\n        ]\n      }\n    ]\n  };\n\n  // https://kotlinlang.org/docs/reference/whatsnew11.html#underscores-in-numeric-literals\n  // According to the doc above, the number mode of kotlin is the same as java 8,\n  // so the code below is copied from java.js\n  const KOTLIN_NUMBER_MODE = NUMERIC;\n  const KOTLIN_NESTED_COMMENT = hljs.COMMENT(\n    '/\\\\*', '\\\\*/',\n    { contains: [ hljs.C_BLOCK_COMMENT_MODE ] }\n  );\n  const KOTLIN_PAREN_TYPE = { variants: [\n    {\n      className: 'type',\n      begin: hljs.UNDERSCORE_IDENT_RE\n    },\n    {\n      begin: /\\(/,\n      end: /\\)/,\n      contains: [] // defined later\n    }\n  ] };\n  const KOTLIN_PAREN_TYPE2 = KOTLIN_PAREN_TYPE;\n  KOTLIN_PAREN_TYPE2.variants[1].contains = [ KOTLIN_PAREN_TYPE ];\n  KOTLIN_PAREN_TYPE.variants[1].contains = [ KOTLIN_PAREN_TYPE2 ];\n\n  return {\n    name: 'Kotlin',\n    aliases: [\n      'kt',\n      'kts'\n    ],\n    keywords: KEYWORDS,\n    contains: [\n      hljs.COMMENT(\n        '/\\\\*\\\\*',\n        '\\\\*/',\n        {\n          relevance: 0,\n          contains: [\n            {\n              className: 'doctag',\n              begin: '@[A-Za-z]+'\n            }\n          ]\n        }\n      ),\n      hljs.C_LINE_COMMENT_MODE,\n      KOTLIN_NESTED_COMMENT,\n      KEYWORDS_WITH_LABEL,\n      LABEL,\n      ANNOTATION_USE_SITE,\n      ANNOTATION,\n      {\n        className: 'function',\n        beginKeywords: 'fun',\n        end: '[(]|$',\n        returnBegin: true,\n        excludeEnd: true,\n        keywords: KEYWORDS,\n        relevance: 5,\n        contains: [\n          {\n            begin: hljs.UNDERSCORE_IDENT_RE + '\\\\s*\\\\(',\n            returnBegin: true,\n            relevance: 0,\n            contains: [ hljs.UNDERSCORE_TITLE_MODE ]\n          },\n          {\n            className: 'type',\n            begin: /</,\n            end: />/,\n            keywords: 'reified',\n            relevance: 0\n          },\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            endsParent: true,\n            keywords: KEYWORDS,\n            relevance: 0,\n            contains: [\n              {\n                begin: /:/,\n                end: /[=,\\/]/,\n                endsWithParent: true,\n                contains: [\n                  KOTLIN_PAREN_TYPE,\n                  hljs.C_LINE_COMMENT_MODE,\n                  KOTLIN_NESTED_COMMENT\n                ],\n                relevance: 0\n              },\n              hljs.C_LINE_COMMENT_MODE,\n              KOTLIN_NESTED_COMMENT,\n              ANNOTATION_USE_SITE,\n              ANNOTATION,\n              STRING,\n              hljs.C_NUMBER_MODE\n            ]\n          },\n          KOTLIN_NESTED_COMMENT\n        ]\n      },\n      {\n        begin: [\n          /class|interface|trait/,\n          /\\s+/,\n          hljs.UNDERSCORE_IDENT_RE\n        ],\n        beginScope: {\n          3: \"title.class\"\n        },\n        keywords: 'class interface trait',\n        end: /[:\\{(]|$/,\n        excludeEnd: true,\n        illegal: 'extends implements',\n        contains: [\n          { beginKeywords: 'public protected internal private constructor' },\n          hljs.UNDERSCORE_TITLE_MODE,\n          {\n            className: 'type',\n            begin: /</,\n            end: />/,\n            excludeBegin: true,\n            excludeEnd: true,\n            relevance: 0\n          },\n          {\n            className: 'type',\n            begin: /[,:]\\s*/,\n            end: /[<\\(,){\\s]|$/,\n            excludeBegin: true,\n            returnEnd: true\n          },\n          ANNOTATION_USE_SITE,\n          ANNOTATION\n        ]\n      },\n      STRING,\n      {\n        className: 'meta',\n        begin: \"^#!/usr/bin/env\",\n        end: '$',\n        illegal: '\\n'\n      },\n      KOTLIN_NUMBER_MODE\n    ]\n  };\n}\n\nexport { kotlin as default };\n"], "names": [], "mappings": "AAAA,yEAAyE;;;;AACzE,IAAI,gBAAgB;AACpB,IAAI,OAAO,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;AAClC,IAAI,YAAY;AAChB,IAAI,UAAU;IACZ,WAAW;IACX,UAAU;QACR,8BAA8B;QAC9B,yBAAyB;QACzB;YAAE,OAAO,CAAC,KAAK,EAAE,cAAc,GAAG,EAAE,KAAK,SAAS,EAAE,KAAK,EAAE,CAAC,GAC1D,CAAC,UAAU,EAAE,cAAc,WAAW,CAAC;QAAC;QAC1C,yBAAyB;QACzB;YAAE,OAAO,CAAC,IAAI,EAAE,cAAc,GAAG,EAAE,KAAK,4BAA4B,CAAC;QAAC;QACtE;YAAE,OAAO,CAAC,CAAC,EAAE,KAAK,WAAW,CAAC;QAAC;QAC/B;YAAE,OAAO,CAAC,IAAI,EAAE,cAAc,UAAU,CAAC;QAAC;QAE1C,kCAAkC;QAClC;YAAE,OAAO,CAAC,UAAU,EAAE,UAAU,OAAO,EAAE,UAAU,MAAM,EAAE,UAAU,EAAE,CAAC,GACtE,CAAC,UAAU,EAAE,cAAc,WAAW,CAAC;QAAC;QAE1C,wBAAwB;QACxB;YAAE,OAAO;QAAiC;QAE1C,oBAAoB;QACpB;YAAE,OAAO,CAAC,SAAS,EAAE,UAAU,SAAS,CAAC;QAAC;QAE1C,sBAAsB;QACtB;YAAE,OAAO;QAAyB;QAElC,uBAAuB;QACvB;YAAE,OAAO;QAAgC;KAC1C;IACD,WAAW;AACb;AAEA;;;;;;CAMC,GAGD,SAAS,OAAO,IAAI;IAClB,MAAM,WAAW;QACf,SACE,4FACE,sFACA,8EACA,uFACA;QACJ,UACE;QACF,SACE;IACJ;IACA,MAAM,sBAAsB;QAC1B,WAAW;QACX,OAAO;QACP,QAAQ;YAAE,UAAU;gBAClB;oBACE,WAAW;oBACX,OAAO;gBACT;aACD;QAAC;IACJ;IACA,MAAM,QAAQ;QACZ,WAAW;QACX,OAAO,KAAK,mBAAmB,GAAG;IACpC;IAEA,uBAAuB;IACvB,MAAM,QAAQ;QACZ,WAAW;QACX,OAAO;QACP,KAAK;QACL,UAAU;YAAE,KAAK,aAAa;SAAE;IAClC;IACA,MAAM,WAAW;QACf,WAAW;QACX,OAAO,QAAQ,KAAK,mBAAmB;IACzC;IACA,MAAM,SAAS;QACb,WAAW;QACX,UAAU;YACR;gBACE,OAAO;gBACP,KAAK;gBACL,UAAU;oBACR;oBACA;iBACD;YACH;YACA,wEAAwE;YACxE,0EAA0E;YAC1E,gCAAgC;YAChC;gBACE,OAAO;gBACP,KAAK;gBACL,SAAS;gBACT,UAAU;oBAAE,KAAK,gBAAgB;iBAAE;YACrC;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,SAAS;gBACT,UAAU;oBACR,KAAK,gBAAgB;oBACrB;oBACA;iBACD;YACH;SACD;IACH;IACA,MAAM,QAAQ,CAAC,IAAI,CAAC;IAEpB,MAAM,sBAAsB;QAC1B,WAAW;QACX,OAAO,kFAAkF,KAAK,mBAAmB,GAAG;IACtH;IACA,MAAM,aAAa;QACjB,WAAW;QACX,OAAO,MAAM,KAAK,mBAAmB;QACrC,UAAU;YACR;gBACE,OAAO;gBACP,KAAK;gBACL,UAAU;oBACR,KAAK,OAAO,CAAC,QAAQ;wBAAE,WAAW;oBAAS;oBAC3C;iBACD;YACH;SACD;IACH;IAEA,wFAAwF;IACxF,+EAA+E;IAC/E,2CAA2C;IAC3C,MAAM,qBAAqB;IAC3B,MAAM,wBAAwB,KAAK,OAAO,CACxC,QAAQ,QACR;QAAE,UAAU;YAAE,KAAK,oBAAoB;SAAE;IAAC;IAE5C,MAAM,oBAAoB;QAAE,UAAU;YACpC;gBACE,WAAW;gBACX,OAAO,KAAK,mBAAmB;YACjC;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,UAAU,EAAE,CAAC,gBAAgB;YAC/B;SACD;IAAC;IACF,MAAM,qBAAqB;IAC3B,mBAAmB,QAAQ,CAAC,EAAE,CAAC,QAAQ,GAAG;QAAE;KAAmB;IAC/D,kBAAkB,QAAQ,CAAC,EAAE,CAAC,QAAQ,GAAG;QAAE;KAAoB;IAE/D,OAAO;QACL,MAAM;QACN,SAAS;YACP;YACA;SACD;QACD,UAAU;QACV,UAAU;YACR,KAAK,OAAO,CACV,WACA,QACA;gBACE,WAAW;gBACX,UAAU;oBACR;wBACE,WAAW;wBACX,OAAO;oBACT;iBACD;YACH;YAEF,KAAK,mBAAmB;YACxB;YACA;YACA;YACA;YACA;YACA;gBACE,WAAW;gBACX,eAAe;gBACf,KAAK;gBACL,aAAa;gBACb,YAAY;gBACZ,UAAU;gBACV,WAAW;gBACX,UAAU;oBACR;wBACE,OAAO,KAAK,mBAAmB,GAAG;wBAClC,aAAa;wBACb,WAAW;wBACX,UAAU;4BAAE,KAAK,qBAAqB;yBAAE;oBAC1C;oBACA;wBACE,WAAW;wBACX,OAAO;wBACP,KAAK;wBACL,UAAU;wBACV,WAAW;oBACb;oBACA;wBACE,WAAW;wBACX,OAAO;wBACP,KAAK;wBACL,YAAY;wBACZ,UAAU;wBACV,WAAW;wBACX,UAAU;4BACR;gCACE,OAAO;gCACP,KAAK;gCACL,gBAAgB;gCAChB,UAAU;oCACR;oCACA,KAAK,mBAAmB;oCACxB;iCACD;gCACD,WAAW;4BACb;4BACA,KAAK,mBAAmB;4BACxB;4BACA;4BACA;4BACA;4BACA,KAAK,aAAa;yBACnB;oBACH;oBACA;iBACD;YACH;YACA;gBACE,OAAO;oBACL;oBACA;oBACA,KAAK,mBAAmB;iBACzB;gBACD,YAAY;oBACV,GAAG;gBACL;gBACA,UAAU;gBACV,KAAK;gBACL,YAAY;gBACZ,SAAS;gBACT,UAAU;oBACR;wBAAE,eAAe;oBAAgD;oBACjE,KAAK,qBAAqB;oBAC1B;wBACE,WAAW;wBACX,OAAO;wBACP,KAAK;wBACL,cAAc;wBACd,YAAY;wBACZ,WAAW;oBACb;oBACA;wBACE,WAAW;wBACX,OAAO;wBACP,KAAK;wBACL,cAAc;wBACd,WAAW;oBACb;oBACA;oBACA;iBACD;YACH;YACA;YACA;gBACE,WAAW;gBACX,OAAO;gBACP,KAAK;gBACL,SAAS;YACX;YACA;SACD;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5560, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/less.js"], "sourcesContent": ["const MODES = (hljs) => {\n  return {\n    IMPORTANT: {\n      scope: 'meta',\n      begin: '!important'\n    },\n    BLOCK_COMMENT: hljs.C_BLOCK_COMMENT_MODE,\n    HEXCOLOR: {\n      scope: 'number',\n      begin: /#(([0-9a-fA-F]{3,4})|(([0-9a-fA-F]{2}){3,4}))\\b/\n    },\n    FUNCTION_DISPATCH: {\n      className: \"built_in\",\n      begin: /[\\w-]+(?=\\()/\n    },\n    ATTRIBUTE_SELECTOR_MODE: {\n      scope: 'selector-attr',\n      begin: /\\[/,\n      end: /\\]/,\n      illegal: '$',\n      contains: [\n        hljs.APOS_STRING_MODE,\n        hljs.QUOTE_STRING_MODE\n      ]\n    },\n    CSS_NUMBER_MODE: {\n      scope: 'number',\n      begin: hljs.NUMBER_RE + '(' +\n        '%|em|ex|ch|rem' +\n        '|vw|vh|vmin|vmax' +\n        '|cm|mm|in|pt|pc|px' +\n        '|deg|grad|rad|turn' +\n        '|s|ms' +\n        '|Hz|kHz' +\n        '|dpi|dpcm|dppx' +\n        ')?',\n      relevance: 0\n    },\n    CSS_VARIABLE: {\n      className: \"attr\",\n      begin: /--[A-Za-z_][A-Za-z0-9_-]*/\n    }\n  };\n};\n\nconst HTML_TAGS = [\n  'a',\n  'abbr',\n  'address',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'blockquote',\n  'body',\n  'button',\n  'canvas',\n  'caption',\n  'cite',\n  'code',\n  'dd',\n  'del',\n  'details',\n  'dfn',\n  'div',\n  'dl',\n  'dt',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'header',\n  'hgroup',\n  'html',\n  'i',\n  'iframe',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'mark',\n  'menu',\n  'nav',\n  'object',\n  'ol',\n  'optgroup',\n  'option',\n  'p',\n  'picture',\n  'q',\n  'quote',\n  'samp',\n  'section',\n  'select',\n  'source',\n  'span',\n  'strong',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'ul',\n  'var',\n  'video'\n];\n\nconst SVG_TAGS = [\n  'defs',\n  'g',\n  'marker',\n  'mask',\n  'pattern',\n  'svg',\n  'switch',\n  'symbol',\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feFlood',\n  'feGaussianBlur',\n  'feImage',\n  'feMerge',\n  'feMorphology',\n  'feOffset',\n  'feSpecularLighting',\n  'feTile',\n  'feTurbulence',\n  'linearGradient',\n  'radialGradient',\n  'stop',\n  'circle',\n  'ellipse',\n  'image',\n  'line',\n  'path',\n  'polygon',\n  'polyline',\n  'rect',\n  'text',\n  'use',\n  'textPath',\n  'tspan',\n  'foreignObject',\n  'clipPath'\n];\n\nconst TAGS = [\n  ...HTML_TAGS,\n  ...SVG_TAGS,\n];\n\n// Sorting, then reversing makes sure longer attributes/elements like\n// `font-weight` are matched fully instead of getting false positives on say `font`\n\nconst MEDIA_FEATURES = [\n  'any-hover',\n  'any-pointer',\n  'aspect-ratio',\n  'color',\n  'color-gamut',\n  'color-index',\n  'device-aspect-ratio',\n  'device-height',\n  'device-width',\n  'display-mode',\n  'forced-colors',\n  'grid',\n  'height',\n  'hover',\n  'inverted-colors',\n  'monochrome',\n  'orientation',\n  'overflow-block',\n  'overflow-inline',\n  'pointer',\n  'prefers-color-scheme',\n  'prefers-contrast',\n  'prefers-reduced-motion',\n  'prefers-reduced-transparency',\n  'resolution',\n  'scan',\n  'scripting',\n  'update',\n  'width',\n  // TODO: find a better solution?\n  'min-width',\n  'max-width',\n  'min-height',\n  'max-height'\n].sort().reverse();\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-classes\nconst PSEUDO_CLASSES = [\n  'active',\n  'any-link',\n  'blank',\n  'checked',\n  'current',\n  'default',\n  'defined',\n  'dir', // dir()\n  'disabled',\n  'drop',\n  'empty',\n  'enabled',\n  'first',\n  'first-child',\n  'first-of-type',\n  'fullscreen',\n  'future',\n  'focus',\n  'focus-visible',\n  'focus-within',\n  'has', // has()\n  'host', // host or host()\n  'host-context', // host-context()\n  'hover',\n  'indeterminate',\n  'in-range',\n  'invalid',\n  'is', // is()\n  'lang', // lang()\n  'last-child',\n  'last-of-type',\n  'left',\n  'link',\n  'local-link',\n  'not', // not()\n  'nth-child', // nth-child()\n  'nth-col', // nth-col()\n  'nth-last-child', // nth-last-child()\n  'nth-last-col', // nth-last-col()\n  'nth-last-of-type', //nth-last-of-type()\n  'nth-of-type', //nth-of-type()\n  'only-child',\n  'only-of-type',\n  'optional',\n  'out-of-range',\n  'past',\n  'placeholder-shown',\n  'read-only',\n  'read-write',\n  'required',\n  'right',\n  'root',\n  'scope',\n  'target',\n  'target-within',\n  'user-invalid',\n  'valid',\n  'visited',\n  'where' // where()\n].sort().reverse();\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-elements\nconst PSEUDO_ELEMENTS = [\n  'after',\n  'backdrop',\n  'before',\n  'cue',\n  'cue-region',\n  'first-letter',\n  'first-line',\n  'grammar-error',\n  'marker',\n  'part',\n  'placeholder',\n  'selection',\n  'slotted',\n  'spelling-error'\n].sort().reverse();\n\nconst ATTRIBUTES = [\n  'accent-color',\n  'align-content',\n  'align-items',\n  'align-self',\n  'alignment-baseline',\n  'all',\n  'anchor-name',\n  'animation',\n  'animation-composition',\n  'animation-delay',\n  'animation-direction',\n  'animation-duration',\n  'animation-fill-mode',\n  'animation-iteration-count',\n  'animation-name',\n  'animation-play-state',\n  'animation-range',\n  'animation-range-end',\n  'animation-range-start',\n  'animation-timeline',\n  'animation-timing-function',\n  'appearance',\n  'aspect-ratio',\n  'backdrop-filter',\n  'backface-visibility',\n  'background',\n  'background-attachment',\n  'background-blend-mode',\n  'background-clip',\n  'background-color',\n  'background-image',\n  'background-origin',\n  'background-position',\n  'background-position-x',\n  'background-position-y',\n  'background-repeat',\n  'background-size',\n  'baseline-shift',\n  'block-size',\n  'border',\n  'border-block',\n  'border-block-color',\n  'border-block-end',\n  'border-block-end-color',\n  'border-block-end-style',\n  'border-block-end-width',\n  'border-block-start',\n  'border-block-start-color',\n  'border-block-start-style',\n  'border-block-start-width',\n  'border-block-style',\n  'border-block-width',\n  'border-bottom',\n  'border-bottom-color',\n  'border-bottom-left-radius',\n  'border-bottom-right-radius',\n  'border-bottom-style',\n  'border-bottom-width',\n  'border-collapse',\n  'border-color',\n  'border-end-end-radius',\n  'border-end-start-radius',\n  'border-image',\n  'border-image-outset',\n  'border-image-repeat',\n  'border-image-slice',\n  'border-image-source',\n  'border-image-width',\n  'border-inline',\n  'border-inline-color',\n  'border-inline-end',\n  'border-inline-end-color',\n  'border-inline-end-style',\n  'border-inline-end-width',\n  'border-inline-start',\n  'border-inline-start-color',\n  'border-inline-start-style',\n  'border-inline-start-width',\n  'border-inline-style',\n  'border-inline-width',\n  'border-left',\n  'border-left-color',\n  'border-left-style',\n  'border-left-width',\n  'border-radius',\n  'border-right',\n  'border-right-color',\n  'border-right-style',\n  'border-right-width',\n  'border-spacing',\n  'border-start-end-radius',\n  'border-start-start-radius',\n  'border-style',\n  'border-top',\n  'border-top-color',\n  'border-top-left-radius',\n  'border-top-right-radius',\n  'border-top-style',\n  'border-top-width',\n  'border-width',\n  'bottom',\n  'box-align',\n  'box-decoration-break',\n  'box-direction',\n  'box-flex',\n  'box-flex-group',\n  'box-lines',\n  'box-ordinal-group',\n  'box-orient',\n  'box-pack',\n  'box-shadow',\n  'box-sizing',\n  'break-after',\n  'break-before',\n  'break-inside',\n  'caption-side',\n  'caret-color',\n  'clear',\n  'clip',\n  'clip-path',\n  'clip-rule',\n  'color',\n  'color-interpolation',\n  'color-interpolation-filters',\n  'color-profile',\n  'color-rendering',\n  'color-scheme',\n  'column-count',\n  'column-fill',\n  'column-gap',\n  'column-rule',\n  'column-rule-color',\n  'column-rule-style',\n  'column-rule-width',\n  'column-span',\n  'column-width',\n  'columns',\n  'contain',\n  'contain-intrinsic-block-size',\n  'contain-intrinsic-height',\n  'contain-intrinsic-inline-size',\n  'contain-intrinsic-size',\n  'contain-intrinsic-width',\n  'container',\n  'container-name',\n  'container-type',\n  'content',\n  'content-visibility',\n  'counter-increment',\n  'counter-reset',\n  'counter-set',\n  'cue',\n  'cue-after',\n  'cue-before',\n  'cursor',\n  'cx',\n  'cy',\n  'direction',\n  'display',\n  'dominant-baseline',\n  'empty-cells',\n  'enable-background',\n  'field-sizing',\n  'fill',\n  'fill-opacity',\n  'fill-rule',\n  'filter',\n  'flex',\n  'flex-basis',\n  'flex-direction',\n  'flex-flow',\n  'flex-grow',\n  'flex-shrink',\n  'flex-wrap',\n  'float',\n  'flood-color',\n  'flood-opacity',\n  'flow',\n  'font',\n  'font-display',\n  'font-family',\n  'font-feature-settings',\n  'font-kerning',\n  'font-language-override',\n  'font-optical-sizing',\n  'font-palette',\n  'font-size',\n  'font-size-adjust',\n  'font-smooth',\n  'font-smoothing',\n  'font-stretch',\n  'font-style',\n  'font-synthesis',\n  'font-synthesis-position',\n  'font-synthesis-small-caps',\n  'font-synthesis-style',\n  'font-synthesis-weight',\n  'font-variant',\n  'font-variant-alternates',\n  'font-variant-caps',\n  'font-variant-east-asian',\n  'font-variant-emoji',\n  'font-variant-ligatures',\n  'font-variant-numeric',\n  'font-variant-position',\n  'font-variation-settings',\n  'font-weight',\n  'forced-color-adjust',\n  'gap',\n  'glyph-orientation-horizontal',\n  'glyph-orientation-vertical',\n  'grid',\n  'grid-area',\n  'grid-auto-columns',\n  'grid-auto-flow',\n  'grid-auto-rows',\n  'grid-column',\n  'grid-column-end',\n  'grid-column-start',\n  'grid-gap',\n  'grid-row',\n  'grid-row-end',\n  'grid-row-start',\n  'grid-template',\n  'grid-template-areas',\n  'grid-template-columns',\n  'grid-template-rows',\n  'hanging-punctuation',\n  'height',\n  'hyphenate-character',\n  'hyphenate-limit-chars',\n  'hyphens',\n  'icon',\n  'image-orientation',\n  'image-rendering',\n  'image-resolution',\n  'ime-mode',\n  'initial-letter',\n  'initial-letter-align',\n  'inline-size',\n  'inset',\n  'inset-area',\n  'inset-block',\n  'inset-block-end',\n  'inset-block-start',\n  'inset-inline',\n  'inset-inline-end',\n  'inset-inline-start',\n  'isolation',\n  'justify-content',\n  'justify-items',\n  'justify-self',\n  'kerning',\n  'left',\n  'letter-spacing',\n  'lighting-color',\n  'line-break',\n  'line-height',\n  'line-height-step',\n  'list-style',\n  'list-style-image',\n  'list-style-position',\n  'list-style-type',\n  'margin',\n  'margin-block',\n  'margin-block-end',\n  'margin-block-start',\n  'margin-bottom',\n  'margin-inline',\n  'margin-inline-end',\n  'margin-inline-start',\n  'margin-left',\n  'margin-right',\n  'margin-top',\n  'margin-trim',\n  'marker',\n  'marker-end',\n  'marker-mid',\n  'marker-start',\n  'marks',\n  'mask',\n  'mask-border',\n  'mask-border-mode',\n  'mask-border-outset',\n  'mask-border-repeat',\n  'mask-border-slice',\n  'mask-border-source',\n  'mask-border-width',\n  'mask-clip',\n  'mask-composite',\n  'mask-image',\n  'mask-mode',\n  'mask-origin',\n  'mask-position',\n  'mask-repeat',\n  'mask-size',\n  'mask-type',\n  'masonry-auto-flow',\n  'math-depth',\n  'math-shift',\n  'math-style',\n  'max-block-size',\n  'max-height',\n  'max-inline-size',\n  'max-width',\n  'min-block-size',\n  'min-height',\n  'min-inline-size',\n  'min-width',\n  'mix-blend-mode',\n  'nav-down',\n  'nav-index',\n  'nav-left',\n  'nav-right',\n  'nav-up',\n  'none',\n  'normal',\n  'object-fit',\n  'object-position',\n  'offset',\n  'offset-anchor',\n  'offset-distance',\n  'offset-path',\n  'offset-position',\n  'offset-rotate',\n  'opacity',\n  'order',\n  'orphans',\n  'outline',\n  'outline-color',\n  'outline-offset',\n  'outline-style',\n  'outline-width',\n  'overflow',\n  'overflow-anchor',\n  'overflow-block',\n  'overflow-clip-margin',\n  'overflow-inline',\n  'overflow-wrap',\n  'overflow-x',\n  'overflow-y',\n  'overlay',\n  'overscroll-behavior',\n  'overscroll-behavior-block',\n  'overscroll-behavior-inline',\n  'overscroll-behavior-x',\n  'overscroll-behavior-y',\n  'padding',\n  'padding-block',\n  'padding-block-end',\n  'padding-block-start',\n  'padding-bottom',\n  'padding-inline',\n  'padding-inline-end',\n  'padding-inline-start',\n  'padding-left',\n  'padding-right',\n  'padding-top',\n  'page',\n  'page-break-after',\n  'page-break-before',\n  'page-break-inside',\n  'paint-order',\n  'pause',\n  'pause-after',\n  'pause-before',\n  'perspective',\n  'perspective-origin',\n  'place-content',\n  'place-items',\n  'place-self',\n  'pointer-events',\n  'position',\n  'position-anchor',\n  'position-visibility',\n  'print-color-adjust',\n  'quotes',\n  'r',\n  'resize',\n  'rest',\n  'rest-after',\n  'rest-before',\n  'right',\n  'rotate',\n  'row-gap',\n  'ruby-align',\n  'ruby-position',\n  'scale',\n  'scroll-behavior',\n  'scroll-margin',\n  'scroll-margin-block',\n  'scroll-margin-block-end',\n  'scroll-margin-block-start',\n  'scroll-margin-bottom',\n  'scroll-margin-inline',\n  'scroll-margin-inline-end',\n  'scroll-margin-inline-start',\n  'scroll-margin-left',\n  'scroll-margin-right',\n  'scroll-margin-top',\n  'scroll-padding',\n  'scroll-padding-block',\n  'scroll-padding-block-end',\n  'scroll-padding-block-start',\n  'scroll-padding-bottom',\n  'scroll-padding-inline',\n  'scroll-padding-inline-end',\n  'scroll-padding-inline-start',\n  'scroll-padding-left',\n  'scroll-padding-right',\n  'scroll-padding-top',\n  'scroll-snap-align',\n  'scroll-snap-stop',\n  'scroll-snap-type',\n  'scroll-timeline',\n  'scroll-timeline-axis',\n  'scroll-timeline-name',\n  'scrollbar-color',\n  'scrollbar-gutter',\n  'scrollbar-width',\n  'shape-image-threshold',\n  'shape-margin',\n  'shape-outside',\n  'shape-rendering',\n  'speak',\n  'speak-as',\n  'src', // @font-face\n  'stop-color',\n  'stop-opacity',\n  'stroke',\n  'stroke-dasharray',\n  'stroke-dashoffset',\n  'stroke-linecap',\n  'stroke-linejoin',\n  'stroke-miterlimit',\n  'stroke-opacity',\n  'stroke-width',\n  'tab-size',\n  'table-layout',\n  'text-align',\n  'text-align-all',\n  'text-align-last',\n  'text-anchor',\n  'text-combine-upright',\n  'text-decoration',\n  'text-decoration-color',\n  'text-decoration-line',\n  'text-decoration-skip',\n  'text-decoration-skip-ink',\n  'text-decoration-style',\n  'text-decoration-thickness',\n  'text-emphasis',\n  'text-emphasis-color',\n  'text-emphasis-position',\n  'text-emphasis-style',\n  'text-indent',\n  'text-justify',\n  'text-orientation',\n  'text-overflow',\n  'text-rendering',\n  'text-shadow',\n  'text-size-adjust',\n  'text-transform',\n  'text-underline-offset',\n  'text-underline-position',\n  'text-wrap',\n  'text-wrap-mode',\n  'text-wrap-style',\n  'timeline-scope',\n  'top',\n  'touch-action',\n  'transform',\n  'transform-box',\n  'transform-origin',\n  'transform-style',\n  'transition',\n  'transition-behavior',\n  'transition-delay',\n  'transition-duration',\n  'transition-property',\n  'transition-timing-function',\n  'translate',\n  'unicode-bidi',\n  'user-modify',\n  'user-select',\n  'vector-effect',\n  'vertical-align',\n  'view-timeline',\n  'view-timeline-axis',\n  'view-timeline-inset',\n  'view-timeline-name',\n  'view-transition-name',\n  'visibility',\n  'voice-balance',\n  'voice-duration',\n  'voice-family',\n  'voice-pitch',\n  'voice-range',\n  'voice-rate',\n  'voice-stress',\n  'voice-volume',\n  'white-space',\n  'white-space-collapse',\n  'widows',\n  'width',\n  'will-change',\n  'word-break',\n  'word-spacing',\n  'word-wrap',\n  'writing-mode',\n  'x',\n  'y',\n  'z-index',\n  'zoom'\n].sort().reverse();\n\n// some grammars use them all as a single group\nconst PSEUDO_SELECTORS = PSEUDO_CLASSES.concat(PSEUDO_ELEMENTS).sort().reverse();\n\n/*\nLanguage: Less\nDescription: It's CSS, with just a little more.\nAuthor:   Max Mikhailov <<EMAIL>>\nWebsite: http://lesscss.org\nCategory: common, css, web\n*/\n\n\n/** @type LanguageFn */\nfunction less(hljs) {\n  const modes = MODES(hljs);\n  const PSEUDO_SELECTORS$1 = PSEUDO_SELECTORS;\n\n  const AT_MODIFIERS = \"and or not only\";\n  const IDENT_RE = '[\\\\w-]+'; // yes, Less identifiers may begin with a digit\n  const INTERP_IDENT_RE = '(' + IDENT_RE + '|@\\\\{' + IDENT_RE + '\\\\})';\n\n  /* Generic Modes */\n\n  const RULES = []; const VALUE_MODES = []; // forward def. for recursive modes\n\n  const STRING_MODE = function(c) {\n    return {\n    // Less strings are not multiline (also include '~' for more consistent coloring of \"escaped\" strings)\n      className: 'string',\n      begin: '~?' + c + '.*?' + c\n    };\n  };\n\n  const IDENT_MODE = function(name, begin, relevance) {\n    return {\n      className: name,\n      begin: begin,\n      relevance: relevance\n    };\n  };\n\n  const AT_KEYWORDS = {\n    $pattern: /[a-z-]+/,\n    keyword: AT_MODIFIERS,\n    attribute: MEDIA_FEATURES.join(\" \")\n  };\n\n  const PARENS_MODE = {\n    // used only to properly balance nested parens inside mixin call, def. arg list\n    begin: '\\\\(',\n    end: '\\\\)',\n    contains: VALUE_MODES,\n    keywords: AT_KEYWORDS,\n    relevance: 0\n  };\n\n  // generic Less highlighter (used almost everywhere except selectors):\n  VALUE_MODES.push(\n    hljs.C_LINE_COMMENT_MODE,\n    hljs.C_BLOCK_COMMENT_MODE,\n    STRING_MODE(\"'\"),\n    STRING_MODE('\"'),\n    modes.CSS_NUMBER_MODE, // fixme: it does not include dot for numbers like .5em :(\n    {\n      begin: '(url|data-uri)\\\\(',\n      starts: {\n        className: 'string',\n        end: '[\\\\)\\\\n]',\n        excludeEnd: true\n      }\n    },\n    modes.HEXCOLOR,\n    PARENS_MODE,\n    IDENT_MODE('variable', '@@?' + IDENT_RE, 10),\n    IDENT_MODE('variable', '@\\\\{' + IDENT_RE + '\\\\}'),\n    IDENT_MODE('built_in', '~?`[^`]*?`'), // inline javascript (or whatever host language) *multiline* string\n    { // @media features (it’s here to not duplicate things in AT_RULE_MODE with extra PARENS_MODE overriding):\n      className: 'attribute',\n      begin: IDENT_RE + '\\\\s*:',\n      end: ':',\n      returnBegin: true,\n      excludeEnd: true\n    },\n    modes.IMPORTANT,\n    { beginKeywords: 'and not' },\n    modes.FUNCTION_DISPATCH\n  );\n\n  const VALUE_WITH_RULESETS = VALUE_MODES.concat({\n    begin: /\\{/,\n    end: /\\}/,\n    contains: RULES\n  });\n\n  const MIXIN_GUARD_MODE = {\n    beginKeywords: 'when',\n    endsWithParent: true,\n    contains: [ { beginKeywords: 'and not' } ].concat(VALUE_MODES) // using this form to override VALUE’s 'function' match\n  };\n\n  /* Rule-Level Modes */\n\n  const RULE_MODE = {\n    begin: INTERP_IDENT_RE + '\\\\s*:',\n    returnBegin: true,\n    end: /[;}]/,\n    relevance: 0,\n    contains: [\n      { begin: /-(webkit|moz|ms|o)-/ },\n      modes.CSS_VARIABLE,\n      {\n        className: 'attribute',\n        begin: '\\\\b(' + ATTRIBUTES.join('|') + ')\\\\b',\n        end: /(?=:)/,\n        starts: {\n          endsWithParent: true,\n          illegal: '[<=$]',\n          relevance: 0,\n          contains: VALUE_MODES\n        }\n      }\n    ]\n  };\n\n  const AT_RULE_MODE = {\n    className: 'keyword',\n    begin: '@(import|media|charset|font-face|(-[a-z]+-)?keyframes|supports|document|namespace|page|viewport|host)\\\\b',\n    starts: {\n      end: '[;{}]',\n      keywords: AT_KEYWORDS,\n      returnEnd: true,\n      contains: VALUE_MODES,\n      relevance: 0\n    }\n  };\n\n  // variable definitions and calls\n  const VAR_RULE_MODE = {\n    className: 'variable',\n    variants: [\n      // using more strict pattern for higher relevance to increase chances of Less detection.\n      // this is *the only* Less specific statement used in most of the sources, so...\n      // (we’ll still often loose to the css-parser unless there's '//' comment,\n      // simply because 1 variable just can't beat 99 properties :)\n      {\n        begin: '@' + IDENT_RE + '\\\\s*:',\n        relevance: 15\n      },\n      { begin: '@' + IDENT_RE }\n    ],\n    starts: {\n      end: '[;}]',\n      returnEnd: true,\n      contains: VALUE_WITH_RULESETS\n    }\n  };\n\n  const SELECTOR_MODE = {\n    // first parse unambiguous selectors (i.e. those not starting with tag)\n    // then fall into the scary lookahead-discriminator variant.\n    // this mode also handles mixin definitions and calls\n    variants: [\n      {\n        begin: '[\\\\.#:&\\\\[>]',\n        end: '[;{}]' // mixin calls end with ';'\n      },\n      {\n        begin: INTERP_IDENT_RE,\n        end: /\\{/\n      }\n    ],\n    returnBegin: true,\n    returnEnd: true,\n    illegal: '[<=\\'$\"]',\n    relevance: 0,\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      MIXIN_GUARD_MODE,\n      IDENT_MODE('keyword', 'all\\\\b'),\n      IDENT_MODE('variable', '@\\\\{' + IDENT_RE + '\\\\}'), // otherwise it’s identified as tag\n      \n      {\n        begin: '\\\\b(' + TAGS.join('|') + ')\\\\b',\n        className: 'selector-tag'\n      },\n      modes.CSS_NUMBER_MODE,\n      IDENT_MODE('selector-tag', INTERP_IDENT_RE, 0),\n      IDENT_MODE('selector-id', '#' + INTERP_IDENT_RE),\n      IDENT_MODE('selector-class', '\\\\.' + INTERP_IDENT_RE, 0),\n      IDENT_MODE('selector-tag', '&', 0),\n      modes.ATTRIBUTE_SELECTOR_MODE,\n      {\n        className: 'selector-pseudo',\n        begin: ':(' + PSEUDO_CLASSES.join('|') + ')'\n      },\n      {\n        className: 'selector-pseudo',\n        begin: ':(:)?(' + PSEUDO_ELEMENTS.join('|') + ')'\n      },\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        relevance: 0,\n        contains: VALUE_WITH_RULESETS\n      }, // argument list of parametric mixins\n      { begin: '!important' }, // eat !important after mixin call or it will be colored as tag\n      modes.FUNCTION_DISPATCH\n    ]\n  };\n\n  const PSEUDO_SELECTOR_MODE = {\n    begin: IDENT_RE + ':(:)?' + `(${PSEUDO_SELECTORS$1.join('|')})`,\n    returnBegin: true,\n    contains: [ SELECTOR_MODE ]\n  };\n\n  RULES.push(\n    hljs.C_LINE_COMMENT_MODE,\n    hljs.C_BLOCK_COMMENT_MODE,\n    AT_RULE_MODE,\n    VAR_RULE_MODE,\n    PSEUDO_SELECTOR_MODE,\n    RULE_MODE,\n    SELECTOR_MODE,\n    MIXIN_GUARD_MODE,\n    modes.FUNCTION_DISPATCH\n  );\n\n  return {\n    name: 'Less',\n    case_insensitive: true,\n    illegal: '[=>\\'/<($\"]',\n    contains: RULES\n  };\n}\n\nexport { less as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,QAAQ,CAAC;IACb,OAAO;QACL,WAAW;YACT,OAAO;YACP,OAAO;QACT;QACA,eAAe,KAAK,oBAAoB;QACxC,UAAU;YACR,OAAO;YACP,OAAO;QACT;QACA,mBAAmB;YACjB,WAAW;YACX,OAAO;QACT;QACA,yBAAyB;YACvB,OAAO;YACP,OAAO;YACP,KAAK;YACL,SAAS;YACT,UAAU;gBACR,KAAK,gBAAgB;gBACrB,KAAK,iBAAiB;aACvB;QACH;QACA,iBAAiB;YACf,OAAO;YACP,OAAO,KAAK,SAAS,GAAG,MACtB,mBACA,qBACA,uBACA,uBACA,UACA,YACA,mBACA;YACF,WAAW;QACb;QACA,cAAc;YACZ,WAAW;YACX,OAAO;QACT;IACF;AACF;AAEA,MAAM,YAAY;IAChB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,OAAO;OACR;OACA;CACJ;AAED,qEAAqE;AACrE,mFAAmF;AAEnF,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,gCAAgC;IAChC;IACA;IACA;IACA;CACD,CAAC,IAAI,GAAG,OAAO;AAEhB,kEAAkE;AAClE,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,QAAQ,UAAU;CACnB,CAAC,IAAI,GAAG,OAAO;AAEhB,mEAAmE;AACnE,MAAM,kBAAkB;IACtB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD,CAAC,IAAI,GAAG,OAAO;AAEhB,MAAM,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD,CAAC,IAAI,GAAG,OAAO;AAEhB,+CAA+C;AAC/C,MAAM,mBAAmB,eAAe,MAAM,CAAC,iBAAiB,IAAI,GAAG,OAAO;AAE9E;;;;;;AAMA,GAGA,qBAAqB,GACrB,SAAS,KAAK,IAAI;IAChB,MAAM,QAAQ,MAAM;IACpB,MAAM,qBAAqB;IAE3B,MAAM,eAAe;IACrB,MAAM,WAAW,WAAW,+CAA+C;IAC3E,MAAM,kBAAkB,MAAM,WAAW,UAAU,WAAW;IAE9D,iBAAiB,GAEjB,MAAM,QAAQ,EAAE;IAAE,MAAM,cAAc,EAAE,EAAE,mCAAmC;IAE7E,MAAM,cAAc,SAAS,CAAC;QAC5B,OAAO;YACP,sGAAsG;YACpG,WAAW;YACX,OAAO,OAAO,IAAI,QAAQ;QAC5B;IACF;IAEA,MAAM,aAAa,SAAS,IAAI,EAAE,KAAK,EAAE,SAAS;QAChD,OAAO;YACL,WAAW;YACX,OAAO;YACP,WAAW;QACb;IACF;IAEA,MAAM,cAAc;QAClB,UAAU;QACV,SAAS;QACT,WAAW,eAAe,IAAI,CAAC;IACjC;IAEA,MAAM,cAAc;QAClB,+EAA+E;QAC/E,OAAO;QACP,KAAK;QACL,UAAU;QACV,UAAU;QACV,WAAW;IACb;IAEA,sEAAsE;IACtE,YAAY,IAAI,CACd,KAAK,mBAAmB,EACxB,KAAK,oBAAoB,EACzB,YAAY,MACZ,YAAY,MACZ,MAAM,eAAe,EACrB;QACE,OAAO;QACP,QAAQ;YACN,WAAW;YACX,KAAK;YACL,YAAY;QACd;IACF,GACA,MAAM,QAAQ,EACd,aACA,WAAW,YAAY,QAAQ,UAAU,KACzC,WAAW,YAAY,SAAS,WAAW,QAC3C,WAAW,YAAY,eACvB;QACE,WAAW;QACX,OAAO,WAAW;QAClB,KAAK;QACL,aAAa;QACb,YAAY;IACd,GACA,MAAM,SAAS,EACf;QAAE,eAAe;IAAU,GAC3B,MAAM,iBAAiB;IAGzB,MAAM,sBAAsB,YAAY,MAAM,CAAC;QAC7C,OAAO;QACP,KAAK;QACL,UAAU;IACZ;IAEA,MAAM,mBAAmB;QACvB,eAAe;QACf,gBAAgB;QAChB,UAAU;YAAE;gBAAE,eAAe;YAAU;SAAG,CAAC,MAAM,CAAC,aAAa,uDAAuD;IACxH;IAEA,oBAAoB,GAEpB,MAAM,YAAY;QAChB,OAAO,kBAAkB;QACzB,aAAa;QACb,KAAK;QACL,WAAW;QACX,UAAU;YACR;gBAAE,OAAO;YAAsB;YAC/B,MAAM,YAAY;YAClB;gBACE,WAAW;gBACX,OAAO,SAAS,WAAW,IAAI,CAAC,OAAO;gBACvC,KAAK;gBACL,QAAQ;oBACN,gBAAgB;oBAChB,SAAS;oBACT,WAAW;oBACX,UAAU;gBACZ;YACF;SACD;IACH;IAEA,MAAM,eAAe;QACnB,WAAW;QACX,OAAO;QACP,QAAQ;YACN,KAAK;YACL,UAAU;YACV,WAAW;YACX,UAAU;YACV,WAAW;QACb;IACF;IAEA,iCAAiC;IACjC,MAAM,gBAAgB;QACpB,WAAW;QACX,UAAU;YACR,wFAAwF;YACxF,gFAAgF;YAChF,0EAA0E;YAC1E,6DAA6D;YAC7D;gBACE,OAAO,MAAM,WAAW;gBACxB,WAAW;YACb;YACA;gBAAE,OAAO,MAAM;YAAS;SACzB;QACD,QAAQ;YACN,KAAK;YACL,WAAW;YACX,UAAU;QACZ;IACF;IAEA,MAAM,gBAAgB;QACpB,uEAAuE;QACvE,4DAA4D;QAC5D,qDAAqD;QACrD,UAAU;YACR;gBACE,OAAO;gBACP,KAAK,QAAQ,2BAA2B;YAC1C;YACA;gBACE,OAAO;gBACP,KAAK;YACP;SACD;QACD,aAAa;QACb,WAAW;QACX,SAAS;QACT,WAAW;QACX,UAAU;YACR,KAAK,mBAAmB;YACxB,KAAK,oBAAoB;YACzB;YACA,WAAW,WAAW;YACtB,WAAW,YAAY,SAAS,WAAW;YAE3C;gBACE,OAAO,SAAS,KAAK,IAAI,CAAC,OAAO;gBACjC,WAAW;YACb;YACA,MAAM,eAAe;YACrB,WAAW,gBAAgB,iBAAiB;YAC5C,WAAW,eAAe,MAAM;YAChC,WAAW,kBAAkB,QAAQ,iBAAiB;YACtD,WAAW,gBAAgB,KAAK;YAChC,MAAM,uBAAuB;YAC7B;gBACE,WAAW;gBACX,OAAO,OAAO,eAAe,IAAI,CAAC,OAAO;YAC3C;YACA;gBACE,WAAW;gBACX,OAAO,WAAW,gBAAgB,IAAI,CAAC,OAAO;YAChD;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,WAAW;gBACX,UAAU;YACZ;YACA;gBAAE,OAAO;YAAa;YACtB,MAAM,iBAAiB;SACxB;IACH;IAEA,MAAM,uBAAuB;QAC3B,OAAO,WAAW,UAAU,CAAC,CAAC,EAAE,mBAAmB,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/D,aAAa;QACb,UAAU;YAAE;SAAe;IAC7B;IAEA,MAAM,IAAI,CACR,KAAK,mBAAmB,EACxB,KAAK,oBAAoB,EACzB,cACA,eACA,sBACA,WACA,eACA,kBACA,MAAM,iBAAiB;IAGzB,OAAO;QACL,MAAM;QACN,kBAAkB;QAClB,SAAS;QACT,UAAU;IACZ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6564, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/lua.js"], "sourcesContent": ["/*\nLanguage: Lua\nDescription: Lua is a powerful, efficient, lightweight, embeddable scripting language.\nAuthor: <PERSON> <<EMAIL>>\nCategory: common, gaming, scripting\nWebsite: https://www.lua.org\n*/\n\nfunction lua(hljs) {\n  const OPENING_LONG_BRACKET = '\\\\[=*\\\\[';\n  const CLOSING_LONG_BRACKET = '\\\\]=*\\\\]';\n  const LONG_BRACKETS = {\n    begin: OPENING_LONG_BRACKET,\n    end: CLOSING_LONG_BRACKET,\n    contains: [ 'self' ]\n  };\n  const COMMENTS = [\n    hljs.COMMENT('--(?!' + OPENING_LONG_BRACKET + ')', '$'),\n    hljs.COMMENT(\n      '--' + OPENING_LONG_BRACKET,\n      CLOSING_LONG_BRACKET,\n      {\n        contains: [ LONG_BRACKETS ],\n        relevance: 10\n      }\n    )\n  ];\n  return {\n    name: '<PERSON><PERSON>',\n    aliases: ['pluto'],\n    keywords: {\n      $pattern: hljs.UNDERSCORE_IDENT_RE,\n      literal: \"true false nil\",\n      keyword: \"and break do else elseif end for goto if in local not or repeat return then until while\",\n      built_in:\n        // Metatags and globals:\n        '_G _ENV _VERSION __index __newindex __mode __call __metatable __tostring __len '\n        + '__gc __add __sub __mul __div __mod __pow __concat __unm __eq __lt __le assert '\n        // Standard methods and properties:\n        + 'collectgarbage dofile error getfenv getmetatable ipairs load loadfile loadstring '\n        + 'module next pairs pcall print rawequal rawget rawset require select setfenv '\n        + 'setmetatable tonumber tostring type unpack xpcall arg self '\n        // Library methods and properties (one line per library):\n        + 'coroutine resume yield status wrap create running debug getupvalue '\n        + 'debug sethook getmetatable gethook setmetatable setlocal traceback setfenv getinfo setupvalue getlocal getregistry getfenv '\n        + 'io lines write close flush open output type read stderr stdin input stdout popen tmpfile '\n        + 'math log max acos huge ldexp pi cos tanh pow deg tan cosh sinh random randomseed frexp ceil floor rad abs sqrt modf asin min mod fmod log10 atan2 exp sin atan '\n        + 'os exit setlocale date getenv difftime remove time clock tmpname rename execute package preload loadlib loaded loaders cpath config path seeall '\n        + 'string sub upper len gfind rep find match char dump gmatch reverse byte format gsub lower '\n        + 'table setn insert getn foreachi maxn foreach concat sort remove'\n    },\n    contains: COMMENTS.concat([\n      {\n        className: 'function',\n        beginKeywords: 'function',\n        end: '\\\\)',\n        contains: [\n          hljs.inherit(hljs.TITLE_MODE, { begin: '([_a-zA-Z]\\\\w*\\\\.)*([_a-zA-Z]\\\\w*:)?[_a-zA-Z]\\\\w*' }),\n          {\n            className: 'params',\n            begin: '\\\\(',\n            endsWithParent: true,\n            contains: COMMENTS\n          }\n        ].concat(COMMENTS)\n      },\n      hljs.C_NUMBER_MODE,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      {\n        className: 'string',\n        begin: OPENING_LONG_BRACKET,\n        end: CLOSING_LONG_BRACKET,\n        contains: [ LONG_BRACKETS ],\n        relevance: 5\n      }\n    ])\n  };\n}\n\nexport { lua as default };\n"], "names": [], "mappings": "AAAA;;;;;;AAMA;;;AAEA,SAAS,IAAI,IAAI;IACf,MAAM,uBAAuB;IAC7B,MAAM,uBAAuB;IAC7B,MAAM,gBAAgB;QACpB,OAAO;QACP,KAAK;QACL,UAAU;YAAE;SAAQ;IACtB;IACA,MAAM,WAAW;QACf,KAAK,OAAO,CAAC,UAAU,uBAAuB,KAAK;QACnD,KAAK,OAAO,CACV,OAAO,sBACP,sBACA;YACE,UAAU;gBAAE;aAAe;YAC3B,WAAW;QACb;KAEH;IACD,OAAO;QACL,MAAM;QACN,SAAS;YAAC;SAAQ;QAClB,UAAU;YACR,UAAU,KAAK,mBAAmB;YAClC,SAAS;YACT,SAAS;YACT,UACE,wBAAwB;YACxB,oFACE,mFAEA,sFACA,iFACA,gEAEA,wEACA,gIACA,8FACA,oKACA,qJACA,+FACA;QACN;QACA,UAAU,SAAS,MAAM,CAAC;YACxB;gBACE,WAAW;gBACX,eAAe;gBACf,KAAK;gBACL,UAAU;oBACR,KAAK,OAAO,CAAC,KAAK,UAAU,EAAE;wBAAE,OAAO;oBAAoD;oBAC3F;wBACE,WAAW;wBACX,OAAO;wBACP,gBAAgB;wBAChB,UAAU;oBACZ;iBACD,CAAC,MAAM,CAAC;YACX;YACA,KAAK,aAAa;YAClB,KAAK,gBAAgB;YACrB,KAAK,iBAAiB;YACtB;gBACE,WAAW;gBACX,OAAO;gBACP,KAAK;gBACL,UAAU;oBAAE;iBAAe;gBAC3B,WAAW;YACb;SACD;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6643, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/makefile.js"], "sourcesContent": ["/*\nLanguage: Makefile\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.gnu.org/software/make/manual/html_node/Introduction.html\nCategory: common, build-system\n*/\n\nfunction makefile(hljs) {\n  /* Variables: simple (eg $(var)) and special (eg $@) */\n  const VARIABLE = {\n    className: 'variable',\n    variants: [\n      {\n        begin: '\\\\$\\\\(' + hljs.UNDERSCORE_IDENT_RE + '\\\\)',\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      },\n      { begin: /\\$[@%<?\\^\\+\\*]/ }\n    ]\n  };\n  /* Quoted string with variables inside */\n  const QUOTE_STRING = {\n    className: 'string',\n    begin: /\"/,\n    end: /\"/,\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      VARIABLE\n    ]\n  };\n  /* Function: $(func arg,...) */\n  const FUNC = {\n    className: 'variable',\n    begin: /\\$\\([\\w-]+\\s/,\n    end: /\\)/,\n    keywords: { built_in:\n        'subst patsubst strip findstring filter filter-out sort '\n        + 'word wordlist firstword lastword dir notdir suffix basename '\n        + 'addsuffix addprefix join wildcard realpath abspath error warning '\n        + 'shell origin flavor foreach if or and call eval file value' },\n    contains: [ \n      VARIABLE,\n      QUOTE_STRING // Added QUOTE_STRING as they can be a part of functions\n    ]\n  };\n  /* Variable assignment */\n  const ASSIGNMENT = { begin: '^' + hljs.UNDERSCORE_IDENT_RE + '\\\\s*(?=[:+?]?=)' };\n  /* Meta targets (.PHONY) */\n  const META = {\n    className: 'meta',\n    begin: /^\\.PHONY:/,\n    end: /$/,\n    keywords: {\n      $pattern: /[\\.\\w]+/,\n      keyword: '.PHONY'\n    }\n  };\n  /* Targets */\n  const TARGET = {\n    className: 'section',\n    begin: /^[^\\s]+:/,\n    end: /$/,\n    contains: [ VARIABLE ]\n  };\n  return {\n    name: 'Makefile',\n    aliases: [\n      'mk',\n      'mak',\n      'make',\n    ],\n    keywords: {\n      $pattern: /[\\w-]+/,\n      keyword: 'define endef undefine ifdef ifndef ifeq ifneq else endif '\n      + 'include -include sinclude override export unexport private vpath'\n    },\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      VARIABLE,\n      QUOTE_STRING,\n      FUNC,\n      ASSIGNMENT,\n      META,\n      TARGET\n    ]\n  };\n}\n\nexport { makefile as default };\n"], "names": [], "mappings": "AAAA;;;;;;AAMA;;;AAEA,SAAS,SAAS,IAAI;IACpB,qDAAqD,GACrD,MAAM,WAAW;QACf,WAAW;QACX,UAAU;YACR;gBACE,OAAO,WAAW,KAAK,mBAAmB,GAAG;gBAC7C,UAAU;oBAAE,KAAK,gBAAgB;iBAAE;YACrC;YACA;gBAAE,OAAO;YAAiB;SAC3B;IACH;IACA,uCAAuC,GACvC,MAAM,eAAe;QACnB,WAAW;QACX,OAAO;QACP,KAAK;QACL,UAAU;YACR,KAAK,gBAAgB;YACrB;SACD;IACH;IACA,6BAA6B,GAC7B,MAAM,OAAO;QACX,WAAW;QACX,OAAO;QACP,KAAK;QACL,UAAU;YAAE,UACR,4DACE,iEACA,sEACA;QAA6D;QACnE,UAAU;YACR;YACA,aAAa,wDAAwD;SACtE;IACH;IACA,uBAAuB,GACvB,MAAM,aAAa;QAAE,OAAO,MAAM,KAAK,mBAAmB,GAAG;IAAkB;IAC/E,yBAAyB,GACzB,MAAM,OAAO;QACX,WAAW;QACX,OAAO;QACP,KAAK;QACL,UAAU;YACR,UAAU;YACV,SAAS;QACX;IACF;IACA,WAAW,GACX,MAAM,SAAS;QACb,WAAW;QACX,OAAO;QACP,KAAK;QACL,UAAU;YAAE;SAAU;IACxB;IACA,OAAO;QACL,MAAM;QACN,SAAS;YACP;YACA;YACA;SACD;QACD,UAAU;YACR,UAAU;YACV,SAAS,8DACP;QACJ;QACA,UAAU;YACR,KAAK,iBAAiB;YACtB;YACA;YACA;YACA;YACA;YACA;SACD;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6737, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/markdown.js"], "sourcesContent": ["/*\nLanguage: Markdown\nRequires: xml.js\nAuthor: <PERSON> <john.cre<PERSON><PERSON>@gmail.com>\nWebsite: https://daringfireball.net/projects/markdown/\nCategory: common, markup\n*/\n\nfunction markdown(hljs) {\n  const regex = hljs.regex;\n  const INLINE_HTML = {\n    begin: /<\\/?[A-Za-z_]/,\n    end: '>',\n    subLanguage: 'xml',\n    relevance: 0\n  };\n  const HORIZONTAL_RULE = {\n    begin: '^[-\\\\*]{3,}',\n    end: '$'\n  };\n  const CODE = {\n    className: 'code',\n    variants: [\n      // TODO: fix to allow these to work with sublanguage also\n      { begin: '(`{3,})[^`](.|\\\\n)*?\\\\1`*[ ]*' },\n      { begin: '(~{3,})[^~](.|\\\\n)*?\\\\1~*[ ]*' },\n      // needed to allow markdown as a sublanguage to work\n      {\n        begin: '```',\n        end: '```+[ ]*$'\n      },\n      {\n        begin: '~~~',\n        end: '~~~+[ ]*$'\n      },\n      { begin: '`.+?`' },\n      {\n        begin: '(?=^( {4}|\\\\t))',\n        // use contains to gobble up multiple lines to allow the block to be whatever size\n        // but only have a single open/close tag vs one per line\n        contains: [\n          {\n            begin: '^( {4}|\\\\t)',\n            end: '(\\\\n)$'\n          }\n        ],\n        relevance: 0\n      }\n    ]\n  };\n  const LIST = {\n    className: 'bullet',\n    begin: '^[ \\t]*([*+-]|(\\\\d+\\\\.))(?=\\\\s+)',\n    end: '\\\\s+',\n    excludeEnd: true\n  };\n  const LINK_REFERENCE = {\n    begin: /^\\[[^\\n]+\\]:/,\n    returnBegin: true,\n    contains: [\n      {\n        className: 'symbol',\n        begin: /\\[/,\n        end: /\\]/,\n        excludeBegin: true,\n        excludeEnd: true\n      },\n      {\n        className: 'link',\n        begin: /:\\s*/,\n        end: /$/,\n        excludeBegin: true\n      }\n    ]\n  };\n  const URL_SCHEME = /[A-Za-z][A-Za-z0-9+.-]*/;\n  const LINK = {\n    variants: [\n      // too much like nested array access in so many languages\n      // to have any real relevance\n      {\n        begin: /\\[.+?\\]\\[.*?\\]/,\n        relevance: 0\n      },\n      // popular internet URLs\n      {\n        begin: /\\[.+?\\]\\(((data|javascript|mailto):|(?:http|ftp)s?:\\/\\/).*?\\)/,\n        relevance: 2\n      },\n      {\n        begin: regex.concat(/\\[.+?\\]\\(/, URL_SCHEME, /:\\/\\/.*?\\)/),\n        relevance: 2\n      },\n      // relative urls\n      {\n        begin: /\\[.+?\\]\\([./?&#].*?\\)/,\n        relevance: 1\n      },\n      // whatever else, lower relevance (might not be a link at all)\n      {\n        begin: /\\[.*?\\]\\(.*?\\)/,\n        relevance: 0\n      }\n    ],\n    returnBegin: true,\n    contains: [\n      {\n        // empty strings for alt or link text\n        match: /\\[(?=\\])/ },\n      {\n        className: 'string',\n        relevance: 0,\n        begin: '\\\\[',\n        end: '\\\\]',\n        excludeBegin: true,\n        returnEnd: true\n      },\n      {\n        className: 'link',\n        relevance: 0,\n        begin: '\\\\]\\\\(',\n        end: '\\\\)',\n        excludeBegin: true,\n        excludeEnd: true\n      },\n      {\n        className: 'symbol',\n        relevance: 0,\n        begin: '\\\\]\\\\[',\n        end: '\\\\]',\n        excludeBegin: true,\n        excludeEnd: true\n      }\n    ]\n  };\n  const BOLD = {\n    className: 'strong',\n    contains: [], // defined later\n    variants: [\n      {\n        begin: /_{2}(?!\\s)/,\n        end: /_{2}/\n      },\n      {\n        begin: /\\*{2}(?!\\s)/,\n        end: /\\*{2}/\n      }\n    ]\n  };\n  const ITALIC = {\n    className: 'emphasis',\n    contains: [], // defined later\n    variants: [\n      {\n        begin: /\\*(?![*\\s])/,\n        end: /\\*/\n      },\n      {\n        begin: /_(?![_\\s])/,\n        end: /_/,\n        relevance: 0\n      }\n    ]\n  };\n\n  // 3 level deep nesting is not allowed because it would create confusion\n  // in cases like `***testing***` because where we don't know if the last\n  // `***` is starting a new bold/italic or finishing the last one\n  const BOLD_WITHOUT_ITALIC = hljs.inherit(BOLD, { contains: [] });\n  const ITALIC_WITHOUT_BOLD = hljs.inherit(ITALIC, { contains: [] });\n  BOLD.contains.push(ITALIC_WITHOUT_BOLD);\n  ITALIC.contains.push(BOLD_WITHOUT_ITALIC);\n\n  let CONTAINABLE = [\n    INLINE_HTML,\n    LINK\n  ];\n\n  [\n    BOLD,\n    ITALIC,\n    BOLD_WITHOUT_ITALIC,\n    ITALIC_WITHOUT_BOLD\n  ].forEach(m => {\n    m.contains = m.contains.concat(CONTAINABLE);\n  });\n\n  CONTAINABLE = CONTAINABLE.concat(BOLD, ITALIC);\n\n  const HEADER = {\n    className: 'section',\n    variants: [\n      {\n        begin: '^#{1,6}',\n        end: '$',\n        contains: CONTAINABLE\n      },\n      {\n        begin: '(?=^.+?\\\\n[=-]{2,}$)',\n        contains: [\n          { begin: '^[=-]*$' },\n          {\n            begin: '^',\n            end: \"\\\\n\",\n            contains: CONTAINABLE\n          }\n        ]\n      }\n    ]\n  };\n\n  const BLOCKQUOTE = {\n    className: 'quote',\n    begin: '^>\\\\s+',\n    contains: CONTAINABLE,\n    end: '$'\n  };\n\n  const ENTITY = {\n    //https://spec.commonmark.org/0.31.2/#entity-references\n    scope: 'literal',\n    match: /&([a-zA-Z0-9]+|#[0-9]{1,7}|#[Xx][0-9a-fA-F]{1,6});/\n  };\n\n  return {\n    name: 'Markdown',\n    aliases: [\n      'md',\n      'mkdown',\n      'mkd'\n    ],\n    contains: [\n      HEADER,\n      INLINE_HTML,\n      LIST,\n      BOLD,\n      ITALIC,\n      BLOCKQUOTE,\n      CODE,\n      HORIZONTAL_RULE,\n      LINK,\n      LINK_REFERENCE,\n      ENTITY\n    ]\n  };\n}\n\nexport { markdown as default };\n"], "names": [], "mappings": "AAAA;;;;;;AAMA;;;AAEA,SAAS,SAAS,IAAI;IACpB,MAAM,QAAQ,KAAK,KAAK;IACxB,MAAM,cAAc;QAClB,OAAO;QACP,KAAK;QACL,aAAa;QACb,WAAW;IACb;IACA,MAAM,kBAAkB;QACtB,OAAO;QACP,KAAK;IACP;IACA,MAAM,OAAO;QACX,WAAW;QACX,UAAU;YACR,yDAAyD;YACzD;gBAAE,OAAO;YAAgC;YACzC;gBAAE,OAAO;YAAgC;YACzC,oDAAoD;YACpD;gBACE,OAAO;gBACP,KAAK;YACP;YACA;gBACE,OAAO;gBACP,KAAK;YACP;YACA;gBAAE,OAAO;YAAQ;YACjB;gBACE,OAAO;gBACP,kFAAkF;gBAClF,wDAAwD;gBACxD,UAAU;oBACR;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;gBACD,WAAW;YACb;SACD;IACH;IACA,MAAM,OAAO;QACX,WAAW;QACX,OAAO;QACP,KAAK;QACL,YAAY;IACd;IACA,MAAM,iBAAiB;QACrB,OAAO;QACP,aAAa;QACb,UAAU;YACR;gBACE,WAAW;gBACX,OAAO;gBACP,KAAK;gBACL,cAAc;gBACd,YAAY;YACd;YACA;gBACE,WAAW;gBACX,OAAO;gBACP,KAAK;gBACL,cAAc;YAChB;SACD;IACH;IACA,MAAM,aAAa;IACnB,MAAM,OAAO;QACX,UAAU;YACR,yDAAyD;YACzD,6BAA6B;YAC7B;gBACE,OAAO;gBACP,WAAW;YACb;YACA,wBAAwB;YACxB;gBACE,OAAO;gBACP,WAAW;YACb;YACA;gBACE,OAAO,MAAM,MAAM,CAAC,aAAa,YAAY;gBAC7C,WAAW;YACb;YACA,gBAAgB;YAChB;gBACE,OAAO;gBACP,WAAW;YACb;YACA,8DAA8D;YAC9D;gBACE,OAAO;gBACP,WAAW;YACb;SACD;QACD,aAAa;QACb,UAAU;YACR;gBACE,qCAAqC;gBACrC,OAAO;YAAW;YACpB;gBACE,WAAW;gBACX,WAAW;gBACX,OAAO;gBACP,KAAK;gBACL,cAAc;gBACd,WAAW;YACb;YACA;gBACE,WAAW;gBACX,WAAW;gBACX,OAAO;gBACP,KAAK;gBACL,cAAc;gBACd,YAAY;YACd;YACA;gBACE,WAAW;gBACX,WAAW;gBACX,OAAO;gBACP,KAAK;gBACL,cAAc;gBACd,YAAY;YACd;SACD;IACH;IACA,MAAM,OAAO;QACX,WAAW;QACX,UAAU,EAAE;QACZ,UAAU;YACR;gBACE,OAAO;gBACP,KAAK;YACP;YACA;gBACE,OAAO;gBACP,KAAK;YACP;SACD;IACH;IACA,MAAM,SAAS;QACb,WAAW;QACX,UAAU,EAAE;QACZ,UAAU;YACR;gBACE,OAAO;gBACP,KAAK;YACP;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,WAAW;YACb;SACD;IACH;IAEA,wEAAwE;IACxE,wEAAwE;IACxE,gEAAgE;IAChE,MAAM,sBAAsB,KAAK,OAAO,CAAC,MAAM;QAAE,UAAU,EAAE;IAAC;IAC9D,MAAM,sBAAsB,KAAK,OAAO,CAAC,QAAQ;QAAE,UAAU,EAAE;IAAC;IAChE,KAAK,QAAQ,CAAC,IAAI,CAAC;IACnB,OAAO,QAAQ,CAAC,IAAI,CAAC;IAErB,IAAI,cAAc;QAChB;QACA;KACD;IAED;QACE;QACA;QACA;QACA;KACD,CAAC,OAAO,CAAC,CAAA;QACR,EAAE,QAAQ,GAAG,EAAE,QAAQ,CAAC,MAAM,CAAC;IACjC;IAEA,cAAc,YAAY,MAAM,CAAC,MAAM;IAEvC,MAAM,SAAS;QACb,WAAW;QACX,UAAU;YACR;gBACE,OAAO;gBACP,KAAK;gBACL,UAAU;YACZ;YACA;gBACE,OAAO;gBACP,UAAU;oBACR;wBAAE,OAAO;oBAAU;oBACnB;wBACE,OAAO;wBACP,KAAK;wBACL,UAAU;oBACZ;iBACD;YACH;SACD;IACH;IAEA,MAAM,aAAa;QACjB,WAAW;QACX,OAAO;QACP,UAAU;QACV,KAAK;IACP;IAEA,MAAM,SAAS;QACb,uDAAuD;QACvD,OAAO;QACP,OAAO;IACT;IAEA,OAAO;QACL,MAAM;QACN,SAAS;YACP;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6996, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/objectivec.js"], "sourcesContent": ["/*\nLanguage: Objective-C\nAuthor: <PERSON><PERSON><PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON> <ahfar<PERSON>@gmail.com>, <PERSON> <<EMAIL>>\nWebsite: https://developer.apple.com/documentation/objectivec\nCategory: common\n*/\n\nfunction objectivec(hljs) {\n  const API_CLASS = {\n    className: 'built_in',\n    begin: '\\\\b(AV|CA|CF|CG|CI|CL|CM|CN|CT|MK|MP|MTK|MTL|NS|SCN|SK|UI|WK|XC)\\\\w+'\n  };\n  const IDENTIFIER_RE = /[a-zA-Z@][a-zA-Z0-9_]*/;\n  const TYPES = [\n    \"int\",\n    \"float\",\n    \"char\",\n    \"unsigned\",\n    \"signed\",\n    \"short\",\n    \"long\",\n    \"double\",\n    \"wchar_t\",\n    \"unichar\",\n    \"void\",\n    \"bool\",\n    \"BOOL\",\n    \"id|0\",\n    \"_Bool\"\n  ];\n  const KWS = [\n    \"while\",\n    \"export\",\n    \"sizeof\",\n    \"typedef\",\n    \"const\",\n    \"struct\",\n    \"for\",\n    \"union\",\n    \"volatile\",\n    \"static\",\n    \"mutable\",\n    \"if\",\n    \"do\",\n    \"return\",\n    \"goto\",\n    \"enum\",\n    \"else\",\n    \"break\",\n    \"extern\",\n    \"asm\",\n    \"case\",\n    \"default\",\n    \"register\",\n    \"explicit\",\n    \"typename\",\n    \"switch\",\n    \"continue\",\n    \"inline\",\n    \"readonly\",\n    \"assign\",\n    \"readwrite\",\n    \"self\",\n    \"@synchronized\",\n    \"id\",\n    \"typeof\",\n    \"nonatomic\",\n    \"IBOutlet\",\n    \"IBAction\",\n    \"strong\",\n    \"weak\",\n    \"copy\",\n    \"in\",\n    \"out\",\n    \"inout\",\n    \"bycopy\",\n    \"byref\",\n    \"oneway\",\n    \"__strong\",\n    \"__weak\",\n    \"__block\",\n    \"__autoreleasing\",\n    \"@private\",\n    \"@protected\",\n    \"@public\",\n    \"@try\",\n    \"@property\",\n    \"@end\",\n    \"@throw\",\n    \"@catch\",\n    \"@finally\",\n    \"@autoreleasepool\",\n    \"@synthesize\",\n    \"@dynamic\",\n    \"@selector\",\n    \"@optional\",\n    \"@required\",\n    \"@encode\",\n    \"@package\",\n    \"@import\",\n    \"@defs\",\n    \"@compatibility_alias\",\n    \"__bridge\",\n    \"__bridge_transfer\",\n    \"__bridge_retained\",\n    \"__bridge_retain\",\n    \"__covariant\",\n    \"__contravariant\",\n    \"__kindof\",\n    \"_Nonnull\",\n    \"_Nullable\",\n    \"_Null_unspecified\",\n    \"__FUNCTION__\",\n    \"__PRETTY_FUNCTION__\",\n    \"__attribute__\",\n    \"getter\",\n    \"setter\",\n    \"retain\",\n    \"unsafe_unretained\",\n    \"nonnull\",\n    \"nullable\",\n    \"null_unspecified\",\n    \"null_resettable\",\n    \"class\",\n    \"instancetype\",\n    \"NS_DESIGNATED_INITIALIZER\",\n    \"NS_UNAVAILABLE\",\n    \"NS_REQUIRES_SUPER\",\n    \"NS_RETURNS_INNER_POINTER\",\n    \"NS_INLINE\",\n    \"NS_AVAILABLE\",\n    \"NS_DEPRECATED\",\n    \"NS_ENUM\",\n    \"NS_OPTIONS\",\n    \"NS_SWIFT_UNAVAILABLE\",\n    \"NS_ASSUME_NONNULL_BEGIN\",\n    \"NS_ASSUME_NONNULL_END\",\n    \"NS_REFINED_FOR_SWIFT\",\n    \"NS_SWIFT_NAME\",\n    \"NS_SWIFT_NOTHROW\",\n    \"NS_DURING\",\n    \"NS_HANDLER\",\n    \"NS_ENDHANDLER\",\n    \"NS_VALUERETURN\",\n    \"NS_VOIDRETURN\"\n  ];\n  const LITERALS = [\n    \"false\",\n    \"true\",\n    \"FALSE\",\n    \"TRUE\",\n    \"nil\",\n    \"YES\",\n    \"NO\",\n    \"NULL\"\n  ];\n  const BUILT_INS = [\n    \"dispatch_once_t\",\n    \"dispatch_queue_t\",\n    \"dispatch_sync\",\n    \"dispatch_async\",\n    \"dispatch_once\"\n  ];\n  const KEYWORDS = {\n    \"variable.language\": [\n      \"this\",\n      \"super\"\n    ],\n    $pattern: IDENTIFIER_RE,\n    keyword: KWS,\n    literal: LITERALS,\n    built_in: BUILT_INS,\n    type: TYPES\n  };\n  const CLASS_KEYWORDS = {\n    $pattern: IDENTIFIER_RE,\n    keyword: [\n      \"@interface\",\n      \"@class\",\n      \"@protocol\",\n      \"@implementation\"\n    ]\n  };\n  return {\n    name: 'Objective-C',\n    aliases: [\n      'mm',\n      'objc',\n      'obj-c',\n      'obj-c++',\n      'objective-c++'\n    ],\n    keywords: KEYWORDS,\n    illegal: '</',\n    contains: [\n      API_CLASS,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.C_NUMBER_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.APOS_STRING_MODE,\n      {\n        className: 'string',\n        variants: [\n          {\n            begin: '@\"',\n            end: '\"',\n            illegal: '\\\\n',\n            contains: [ hljs.BACKSLASH_ESCAPE ]\n          }\n        ]\n      },\n      {\n        className: 'meta',\n        begin: /#\\s*[a-z]+\\b/,\n        end: /$/,\n        keywords: { keyword:\n            'if else elif endif define undef warning error line '\n            + 'pragma ifdef ifndef include' },\n        contains: [\n          {\n            begin: /\\\\\\n/,\n            relevance: 0\n          },\n          hljs.inherit(hljs.QUOTE_STRING_MODE, { className: 'string' }),\n          {\n            className: 'string',\n            begin: /<.*?>/,\n            end: /$/,\n            illegal: '\\\\n'\n          },\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        className: 'class',\n        begin: '(' + CLASS_KEYWORDS.keyword.join('|') + ')\\\\b',\n        end: /(\\{|$)/,\n        excludeEnd: true,\n        keywords: CLASS_KEYWORDS,\n        contains: [ hljs.UNDERSCORE_TITLE_MODE ]\n      },\n      {\n        begin: '\\\\.' + hljs.UNDERSCORE_IDENT_RE,\n        relevance: 0\n      }\n    ]\n  };\n}\n\nexport { objectivec as default };\n"], "names": [], "mappings": "AAAA;;;;;;AAMA;;;AAEA,SAAS,WAAW,IAAI;IACtB,MAAM,YAAY;QAChB,WAAW;QACX,OAAO;IACT;IACA,MAAM,gBAAgB;IACtB,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,MAAM;QACV;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,YAAY;QAChB;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,WAAW;QACf,qBAAqB;YACnB;YACA;SACD;QACD,UAAU;QACV,SAAS;QACT,SAAS;QACT,UAAU;QACV,MAAM;IACR;IACA,MAAM,iBAAiB;QACrB,UAAU;QACV,SAAS;YACP;YACA;YACA;YACA;SACD;IACH;IACA,OAAO;QACL,MAAM;QACN,SAAS;YACP;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,SAAS;QACT,UAAU;YACR;YACA,KAAK,mBAAmB;YACxB,KAAK,oBAAoB;YACzB,KAAK,aAAa;YAClB,KAAK,iBAAiB;YACtB,KAAK,gBAAgB;YACrB;gBACE,WAAW;gBACX,UAAU;oBACR;wBACE,OAAO;wBACP,KAAK;wBACL,SAAS;wBACT,UAAU;4BAAE,KAAK,gBAAgB;yBAAE;oBACrC;iBACD;YACH;YACA;gBACE,WAAW;gBACX,OAAO;gBACP,KAAK;gBACL,UAAU;oBAAE,SACR,wDACE;gBAA8B;gBACpC,UAAU;oBACR;wBACE,OAAO;wBACP,WAAW;oBACb;oBACA,KAAK,OAAO,CAAC,KAAK,iBAAiB,EAAE;wBAAE,WAAW;oBAAS;oBAC3D;wBACE,WAAW;wBACX,OAAO;wBACP,KAAK;wBACL,SAAS;oBACX;oBACA,KAAK,mBAAmB;oBACxB,KAAK,oBAAoB;iBAC1B;YACH;YACA;gBACE,WAAW;gBACX,OAAO,MAAM,eAAe,OAAO,CAAC,IAAI,CAAC,OAAO;gBAChD,KAAK;gBACL,YAAY;gBACZ,UAAU;gBACV,UAAU;oBAAE,KAAK,qBAAqB;iBAAE;YAC1C;YACA;gBACE,OAAO,QAAQ,KAAK,mBAAmB;gBACvC,WAAW;YACb;SACD;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7261, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/perl.js"], "sourcesContent": ["/*\nLanguage: Perl\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://www.perl.org\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction perl(hljs) {\n  const regex = hljs.regex;\n  const KEYWORDS = [\n    'abs',\n    'accept',\n    'alarm',\n    'and',\n    'atan2',\n    'bind',\n    'binmode',\n    'bless',\n    'break',\n    'caller',\n    'chdir',\n    'chmod',\n    'chomp',\n    'chop',\n    'chown',\n    'chr',\n    'chroot',\n    'class',\n    'close',\n    'closedir',\n    'connect',\n    'continue',\n    'cos',\n    'crypt',\n    'dbmclose',\n    'dbmopen',\n    'defined',\n    'delete',\n    'die',\n    'do',\n    'dump',\n    'each',\n    'else',\n    'elsif',\n    'endgrent',\n    'endhostent',\n    'endnetent',\n    'endprotoent',\n    'endpwent',\n    'endservent',\n    'eof',\n    'eval',\n    'exec',\n    'exists',\n    'exit',\n    'exp',\n    'fcntl',\n    'field',\n    'fileno',\n    'flock',\n    'for',\n    'foreach',\n    'fork',\n    'format',\n    'formline',\n    'getc',\n    'getgrent',\n    'getgrgid',\n    'getgrnam',\n    'gethostbyaddr',\n    'gethostbyname',\n    'gethostent',\n    'getlogin',\n    'getnetbyaddr',\n    'getnetbyname',\n    'getnetent',\n    'getpeername',\n    'getpgrp',\n    'getpriority',\n    'getprotobyname',\n    'getprotobynumber',\n    'getprotoent',\n    'getpwent',\n    'getpwnam',\n    'getpwuid',\n    'getservbyname',\n    'getservbyport',\n    'getservent',\n    'getsockname',\n    'getsockopt',\n    'given',\n    'glob',\n    'gmtime',\n    'goto',\n    'grep',\n    'gt',\n    'hex',\n    'if',\n    'index',\n    'int',\n    'ioctl',\n    'join',\n    'keys',\n    'kill',\n    'last',\n    'lc',\n    'lcfirst',\n    'length',\n    'link',\n    'listen',\n    'local',\n    'localtime',\n    'log',\n    'lstat',\n    'lt',\n    'ma',\n    'map',\n    'method',\n    'mkdir',\n    'msgctl',\n    'msgget',\n    'msgrcv',\n    'msgsnd',\n    'my',\n    'ne',\n    'next',\n    'no',\n    'not',\n    'oct',\n    'open',\n    'opendir',\n    'or',\n    'ord',\n    'our',\n    'pack',\n    'package',\n    'pipe',\n    'pop',\n    'pos',\n    'print',\n    'printf',\n    'prototype',\n    'push',\n    'q|0',\n    'qq',\n    'quotemeta',\n    'qw',\n    'qx',\n    'rand',\n    'read',\n    'readdir',\n    'readline',\n    'readlink',\n    'readpipe',\n    'recv',\n    'redo',\n    'ref',\n    'rename',\n    'require',\n    'reset',\n    'return',\n    'reverse',\n    'rewinddir',\n    'rindex',\n    'rmdir',\n    'say',\n    'scalar',\n    'seek',\n    'seekdir',\n    'select',\n    'semctl',\n    'semget',\n    'semop',\n    'send',\n    'setgrent',\n    'sethostent',\n    'setnetent',\n    'setpgrp',\n    'setpriority',\n    'setprotoent',\n    'setpwent',\n    'setservent',\n    'setsockopt',\n    'shift',\n    'shmctl',\n    'shmget',\n    'shmread',\n    'shmwrite',\n    'shutdown',\n    'sin',\n    'sleep',\n    'socket',\n    'socketpair',\n    'sort',\n    'splice',\n    'split',\n    'sprintf',\n    'sqrt',\n    'srand',\n    'stat',\n    'state',\n    'study',\n    'sub',\n    'substr',\n    'symlink',\n    'syscall',\n    'sysopen',\n    'sysread',\n    'sysseek',\n    'system',\n    'syswrite',\n    'tell',\n    'telldir',\n    'tie',\n    'tied',\n    'time',\n    'times',\n    'tr',\n    'truncate',\n    'uc',\n    'ucfirst',\n    'umask',\n    'undef',\n    'unless',\n    'unlink',\n    'unpack',\n    'unshift',\n    'untie',\n    'until',\n    'use',\n    'utime',\n    'values',\n    'vec',\n    'wait',\n    'waitpid',\n    'wantarray',\n    'warn',\n    'when',\n    'while',\n    'write',\n    'x|0',\n    'xor',\n    'y|0'\n  ];\n\n  // https://perldoc.perl.org/perlre#Modifiers\n  const REGEX_MODIFIERS = /[dualxmsipngr]{0,12}/; // aa and xx are valid, making max length 12\n  const PERL_KEYWORDS = {\n    $pattern: /[\\w.]+/,\n    keyword: KEYWORDS.join(\" \")\n  };\n  const SUBST = {\n    className: 'subst',\n    begin: '[$@]\\\\{',\n    end: '\\\\}',\n    keywords: PERL_KEYWORDS\n  };\n  const METHOD = {\n    begin: /->\\{/,\n    end: /\\}/\n    // contains defined later\n  };\n  const ATTR = {\n    scope: 'attr',\n    match: /\\s+:\\s*\\w+(\\s*\\(.*?\\))?/,\n  };\n  const VAR = {\n    scope: 'variable',\n    variants: [\n      { begin: /\\$\\d/ },\n      { begin: regex.concat(\n        /[$%@](?!\")(\\^\\w\\b|#\\w+(::\\w+)*|\\{\\w+\\}|\\w+(::\\w*)*)/,\n        // negative look-ahead tries to avoid matching patterns that are not\n        // Perl at all like $ident$, @ident@, etc.\n        `(?![A-Za-z])(?![@$%])`\n        )\n      },\n      {\n        // Only $= is a special Perl variable and one can't declare @= or %=.\n        begin: /[$%@](?!\")[^\\s\\w{=]|\\$=/,\n        relevance: 0\n      }\n    ],\n    contains: [ ATTR ],\n  };\n  const NUMBER = {\n    className: 'number',\n    variants: [\n      // decimal numbers:\n      // include the case where a number starts with a dot (eg. .9), and\n      // the leading 0? avoids mixing the first and second match on 0.x cases\n      { match: /0?\\.[0-9][0-9_]+\\b/ },\n      // include the special versioned number (eg. v5.38)\n      { match: /\\bv?(0|[1-9][0-9_]*(\\.[0-9_]+)?|[1-9][0-9_]*)\\b/ },\n      // non-decimal numbers:\n      { match: /\\b0[0-7][0-7_]*\\b/ },\n      { match: /\\b0x[0-9a-fA-F][0-9a-fA-F_]*\\b/ },\n      { match: /\\b0b[0-1][0-1_]*\\b/ },\n    ],\n    relevance: 0\n  };\n  const STRING_CONTAINS = [\n    hljs.BACKSLASH_ESCAPE,\n    SUBST,\n    VAR\n  ];\n  const REGEX_DELIMS = [\n    /!/,\n    /\\//,\n    /\\|/,\n    /\\?/,\n    /'/,\n    /\"/, // valid but infrequent and weird\n    /#/ // valid but infrequent and weird\n  ];\n  /**\n   * @param {string|RegExp} prefix\n   * @param {string|RegExp} open\n   * @param {string|RegExp} close\n   */\n  const PAIRED_DOUBLE_RE = (prefix, open, close = '\\\\1') => {\n    const middle = (close === '\\\\1')\n      ? close\n      : regex.concat(close, open);\n    return regex.concat(\n      regex.concat(\"(?:\", prefix, \")\"),\n      open,\n      /(?:\\\\.|[^\\\\\\/])*?/,\n      middle,\n      /(?:\\\\.|[^\\\\\\/])*?/,\n      close,\n      REGEX_MODIFIERS\n    );\n  };\n  /**\n   * @param {string|RegExp} prefix\n   * @param {string|RegExp} open\n   * @param {string|RegExp} close\n   */\n  const PAIRED_RE = (prefix, open, close) => {\n    return regex.concat(\n      regex.concat(\"(?:\", prefix, \")\"),\n      open,\n      /(?:\\\\.|[^\\\\\\/])*?/,\n      close,\n      REGEX_MODIFIERS\n    );\n  };\n  const PERL_DEFAULT_CONTAINS = [\n    VAR,\n    hljs.HASH_COMMENT_MODE,\n    hljs.COMMENT(\n      /^=\\w/,\n      /=cut/,\n      { endsWithParent: true }\n    ),\n    METHOD,\n    {\n      className: 'string',\n      contains: STRING_CONTAINS,\n      variants: [\n        {\n          begin: 'q[qwxr]?\\\\s*\\\\(',\n          end: '\\\\)',\n          relevance: 5\n        },\n        {\n          begin: 'q[qwxr]?\\\\s*\\\\[',\n          end: '\\\\]',\n          relevance: 5\n        },\n        {\n          begin: 'q[qwxr]?\\\\s*\\\\{',\n          end: '\\\\}',\n          relevance: 5\n        },\n        {\n          begin: 'q[qwxr]?\\\\s*\\\\|',\n          end: '\\\\|',\n          relevance: 5\n        },\n        {\n          begin: 'q[qwxr]?\\\\s*<',\n          end: '>',\n          relevance: 5\n        },\n        {\n          begin: 'qw\\\\s+q',\n          end: 'q',\n          relevance: 5\n        },\n        {\n          begin: '\\'',\n          end: '\\'',\n          contains: [ hljs.BACKSLASH_ESCAPE ]\n        },\n        {\n          begin: '\"',\n          end: '\"'\n        },\n        {\n          begin: '`',\n          end: '`',\n          contains: [ hljs.BACKSLASH_ESCAPE ]\n        },\n        {\n          begin: /\\{\\w+\\}/,\n          relevance: 0\n        },\n        {\n          begin: '-?\\\\w+\\\\s*=>',\n          relevance: 0\n        }\n      ]\n    },\n    NUMBER,\n    { // regexp container\n      begin: '(\\\\/\\\\/|' + hljs.RE_STARTERS_RE + '|\\\\b(split|return|print|reverse|grep)\\\\b)\\\\s*',\n      keywords: 'split return print reverse grep',\n      relevance: 0,\n      contains: [\n        hljs.HASH_COMMENT_MODE,\n        {\n          className: 'regexp',\n          variants: [\n            // allow matching common delimiters\n            { begin: PAIRED_DOUBLE_RE(\"s|tr|y\", regex.either(...REGEX_DELIMS, { capture: true })) },\n            // and then paired delmis\n            { begin: PAIRED_DOUBLE_RE(\"s|tr|y\", \"\\\\(\", \"\\\\)\") },\n            { begin: PAIRED_DOUBLE_RE(\"s|tr|y\", \"\\\\[\", \"\\\\]\") },\n            { begin: PAIRED_DOUBLE_RE(\"s|tr|y\", \"\\\\{\", \"\\\\}\") }\n          ],\n          relevance: 2\n        },\n        {\n          className: 'regexp',\n          variants: [\n            {\n              // could be a comment in many languages so do not count\n              // as relevant\n              begin: /(m|qr)\\/\\//,\n              relevance: 0\n            },\n            // prefix is optional with /regex/\n            { begin: PAIRED_RE(\"(?:m|qr)?\", /\\//, /\\//) },\n            // allow matching common delimiters\n            { begin: PAIRED_RE(\"m|qr\", regex.either(...REGEX_DELIMS, { capture: true }), /\\1/) },\n            // allow common paired delmins\n            { begin: PAIRED_RE(\"m|qr\", /\\(/, /\\)/) },\n            { begin: PAIRED_RE(\"m|qr\", /\\[/, /\\]/) },\n            { begin: PAIRED_RE(\"m|qr\", /\\{/, /\\}/) }\n          ]\n        }\n      ]\n    },\n    {\n      className: 'function',\n      beginKeywords: 'sub method',\n      end: '(\\\\s*\\\\(.*?\\\\))?[;{]',\n      excludeEnd: true,\n      relevance: 5,\n      contains: [ hljs.TITLE_MODE, ATTR ]\n    },\n    {\n      className: 'class',\n      beginKeywords: 'class',\n      end: '[;{]',\n      excludeEnd: true,\n      relevance: 5,\n      contains: [ hljs.TITLE_MODE, ATTR, NUMBER ]\n    },\n    {\n      begin: '-\\\\w\\\\b',\n      relevance: 0\n    },\n    {\n      begin: \"^__DATA__$\",\n      end: \"^__END__$\",\n      subLanguage: 'mojolicious',\n      contains: [\n        {\n          begin: \"^@@.*\",\n          end: \"$\",\n          className: \"comment\"\n        }\n      ]\n    }\n  ];\n  SUBST.contains = PERL_DEFAULT_CONTAINS;\n  METHOD.contains = PERL_DEFAULT_CONTAINS;\n\n  return {\n    name: 'Perl',\n    aliases: [\n      'pl',\n      'pm'\n    ],\n    keywords: PERL_KEYWORDS,\n    contains: PERL_DEFAULT_CONTAINS\n  };\n}\n\nexport { perl as default };\n"], "names": [], "mappings": "AAAA;;;;;AAKA,GAEA,qBAAqB;;;AACrB,SAAS,KAAK,IAAI;IAChB,MAAM,QAAQ,KAAK,KAAK;IACxB,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,4CAA4C;IAC5C,MAAM,kBAAkB,wBAAwB,4CAA4C;IAC5F,MAAM,gBAAgB;QACpB,UAAU;QACV,SAAS,SAAS,IAAI,CAAC;IACzB;IACA,MAAM,QAAQ;QACZ,WAAW;QACX,OAAO;QACP,KAAK;QACL,UAAU;IACZ;IACA,MAAM,SAAS;QACb,OAAO;QACP,KAAK;IAEP;IACA,MAAM,OAAO;QACX,OAAO;QACP,OAAO;IACT;IACA,MAAM,MAAM;QACV,OAAO;QACP,UAAU;YACR;gBAAE,OAAO;YAAO;YAChB;gBAAE,OAAO,MAAM,MAAM,CACnB,uDACA,oEAAoE;gBACpE,0CAA0C;gBAC1C,CAAC,qBAAqB,CAAC;YAEzB;YACA;gBACE,qEAAqE;gBACrE,OAAO;gBACP,WAAW;YACb;SACD;QACD,UAAU;YAAE;SAAM;IACpB;IACA,MAAM,SAAS;QACb,WAAW;QACX,UAAU;YACR,mBAAmB;YACnB,kEAAkE;YAClE,uEAAuE;YACvE;gBAAE,OAAO;YAAqB;YAC9B,mDAAmD;YACnD;gBAAE,OAAO;YAAkD;YAC3D,uBAAuB;YACvB;gBAAE,OAAO;YAAoB;YAC7B;gBAAE,OAAO;YAAiC;YAC1C;gBAAE,OAAO;YAAqB;SAC/B;QACD,WAAW;IACb;IACA,MAAM,kBAAkB;QACtB,KAAK,gBAAgB;QACrB;QACA;KACD;IACD,MAAM,eAAe;QACnB;QACA;QACA;QACA;QACA;QACA;QACA,IAAI,iCAAiC;KACtC;IACD;;;;GAIC,GACD,MAAM,mBAAmB,CAAC,QAAQ,MAAM,QAAQ,KAAK;QACnD,MAAM,SAAS,AAAC,UAAU,QACtB,QACA,MAAM,MAAM,CAAC,OAAO;QACxB,OAAO,MAAM,MAAM,CACjB,MAAM,MAAM,CAAC,OAAO,QAAQ,MAC5B,MACA,qBACA,QACA,qBACA,OACA;IAEJ;IACA;;;;GAIC,GACD,MAAM,YAAY,CAAC,QAAQ,MAAM;QAC/B,OAAO,MAAM,MAAM,CACjB,MAAM,MAAM,CAAC,OAAO,QAAQ,MAC5B,MACA,qBACA,OACA;IAEJ;IACA,MAAM,wBAAwB;QAC5B;QACA,KAAK,iBAAiB;QACtB,KAAK,OAAO,CACV,QACA,QACA;YAAE,gBAAgB;QAAK;QAEzB;QACA;YACE,WAAW;YACX,UAAU;YACV,UAAU;gBACR;oBACE,OAAO;oBACP,KAAK;oBACL,WAAW;gBACb;gBACA;oBACE,OAAO;oBACP,KAAK;oBACL,WAAW;gBACb;gBACA;oBACE,OAAO;oBACP,KAAK;oBACL,WAAW;gBACb;gBACA;oBACE,OAAO;oBACP,KAAK;oBACL,WAAW;gBACb;gBACA;oBACE,OAAO;oBACP,KAAK;oBACL,WAAW;gBACb;gBACA;oBACE,OAAO;oBACP,KAAK;oBACL,WAAW;gBACb;gBACA;oBACE,OAAO;oBACP,KAAK;oBACL,UAAU;wBAAE,KAAK,gBAAgB;qBAAE;gBACrC;gBACA;oBACE,OAAO;oBACP,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,KAAK;oBACL,UAAU;wBAAE,KAAK,gBAAgB;qBAAE;gBACrC;gBACA;oBACE,OAAO;oBACP,WAAW;gBACb;gBACA;oBACE,OAAO;oBACP,WAAW;gBACb;aACD;QACH;QACA;QACA;YACE,OAAO,aAAa,KAAK,cAAc,GAAG;YAC1C,UAAU;YACV,WAAW;YACX,UAAU;gBACR,KAAK,iBAAiB;gBACtB;oBACE,WAAW;oBACX,UAAU;wBACR,mCAAmC;wBACnC;4BAAE,OAAO,iBAAiB,UAAU,MAAM,MAAM,IAAI,cAAc;gCAAE,SAAS;4BAAK;wBAAI;wBACtF,yBAAyB;wBACzB;4BAAE,OAAO,iBAAiB,UAAU,OAAO;wBAAO;wBAClD;4BAAE,OAAO,iBAAiB,UAAU,OAAO;wBAAO;wBAClD;4BAAE,OAAO,iBAAiB,UAAU,OAAO;wBAAO;qBACnD;oBACD,WAAW;gBACb;gBACA;oBACE,WAAW;oBACX,UAAU;wBACR;4BACE,uDAAuD;4BACvD,cAAc;4BACd,OAAO;4BACP,WAAW;wBACb;wBACA,kCAAkC;wBAClC;4BAAE,OAAO,UAAU,aAAa,MAAM;wBAAM;wBAC5C,mCAAmC;wBACnC;4BAAE,OAAO,UAAU,QAAQ,MAAM,MAAM,IAAI,cAAc;gCAAE,SAAS;4BAAK,IAAI;wBAAM;wBACnF,8BAA8B;wBAC9B;4BAAE,OAAO,UAAU,QAAQ,MAAM;wBAAM;wBACvC;4BAAE,OAAO,UAAU,QAAQ,MAAM;wBAAM;wBACvC;4BAAE,OAAO,UAAU,QAAQ,MAAM;wBAAM;qBACxC;gBACH;aACD;QACH;QACA;YACE,WAAW;YACX,eAAe;YACf,KAAK;YACL,YAAY;YACZ,WAAW;YACX,UAAU;gBAAE,KAAK,UAAU;gBAAE;aAAM;QACrC;QACA;YACE,WAAW;YACX,eAAe;YACf,KAAK;YACL,YAAY;YACZ,WAAW;YACX,UAAU;gBAAE,KAAK,UAAU;gBAAE;gBAAM;aAAQ;QAC7C;QACA;YACE,OAAO;YACP,WAAW;QACb;QACA;YACE,OAAO;YACP,KAAK;YACL,aAAa;YACb,UAAU;gBACR;oBACE,OAAO;oBACP,KAAK;oBACL,WAAW;gBACb;aACD;QACH;KACD;IACD,MAAM,QAAQ,GAAG;IACjB,OAAO,QAAQ,GAAG;IAElB,OAAO;QACL,MAAM;QACN,SAAS;YACP;YACA;SACD;QACD,UAAU;QACV,UAAU;IACZ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7792, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/php.js"], "sourcesContent": ["/*\nLanguage: PHP\nAuthor: <PERSON> <<PERSON>.<EMAIL>>\nContributors: <PERSON><PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>\nWebsite: https://www.php.net\nCategory: common\n*/\n\n/**\n * @param {H<PERSON><PERSON><PERSON><PERSON>} hljs\n * @returns {LanguageDetail}\n * */\nfunction php(hljs) {\n  const regex = hljs.regex;\n  // negative look-ahead tries to avoid matching patterns that are not\n  // Perl at all like $ident$, @ident@, etc.\n  const NOT_PERL_ETC = /(?![A-Za-z0-9])(?![$])/;\n  const IDENT_RE = regex.concat(\n    /[a-zA-Z_\\x7f-\\xff][a-zA-Z0-9_\\x7f-\\xff]*/,\n    NOT_PERL_ETC);\n  // Will not detect camelCase classes\n  const PASCAL_CASE_CLASS_NAME_RE = regex.concat(\n    /(\\\\?[A-Z][a-z0-9_\\x7f-\\xff]+|\\\\?[A-Z]+(?=[A-Z][a-z0-9_\\x7f-\\xff])){1,}/,\n    NOT_PERL_ETC);\n  const UPCASE_NAME_RE = regex.concat(\n    /[A-Z]+/,\n    NOT_PERL_ETC);\n  const VARIABLE = {\n    scope: 'variable',\n    match: '\\\\$+' + IDENT_RE,\n  };\n  const PREPROCESSOR = {\n    scope: \"meta\",\n    variants: [\n      { begin: /<\\?php/, relevance: 10 }, // boost for obvious PHP\n      { begin: /<\\?=/ },\n      // less relevant per PSR-1 which says not to use short-tags\n      { begin: /<\\?/, relevance: 0.1 },\n      { begin: /\\?>/ } // end php tag\n    ]\n  };\n  const SUBST = {\n    scope: 'subst',\n    variants: [\n      { begin: /\\$\\w+/ },\n      {\n        begin: /\\{\\$/,\n        end: /\\}/\n      }\n    ]\n  };\n  const SINGLE_QUOTED = hljs.inherit(hljs.APOS_STRING_MODE, { illegal: null, });\n  const DOUBLE_QUOTED = hljs.inherit(hljs.QUOTE_STRING_MODE, {\n    illegal: null,\n    contains: hljs.QUOTE_STRING_MODE.contains.concat(SUBST),\n  });\n\n  const HEREDOC = {\n    begin: /<<<[ \\t]*(?:(\\w+)|\"(\\w+)\")\\n/,\n    end: /[ \\t]*(\\w+)\\b/,\n    contains: hljs.QUOTE_STRING_MODE.contains.concat(SUBST),\n    'on:begin': (m, resp) => { resp.data._beginMatch = m[1] || m[2]; },\n    'on:end': (m, resp) => { if (resp.data._beginMatch !== m[1]) resp.ignoreMatch(); },\n  };\n\n  const NOWDOC = hljs.END_SAME_AS_BEGIN({\n    begin: /<<<[ \\t]*'(\\w+)'\\n/,\n    end: /[ \\t]*(\\w+)\\b/,\n  });\n  // list of valid whitespaces because non-breaking space might be part of a IDENT_RE\n  const WHITESPACE = '[ \\t\\n]';\n  const STRING = {\n    scope: 'string',\n    variants: [\n      DOUBLE_QUOTED,\n      SINGLE_QUOTED,\n      HEREDOC,\n      NOWDOC\n    ]\n  };\n  const NUMBER = {\n    scope: 'number',\n    variants: [\n      { begin: `\\\\b0[bB][01]+(?:_[01]+)*\\\\b` }, // Binary w/ underscore support\n      { begin: `\\\\b0[oO][0-7]+(?:_[0-7]+)*\\\\b` }, // Octals w/ underscore support\n      { begin: `\\\\b0[xX][\\\\da-fA-F]+(?:_[\\\\da-fA-F]+)*\\\\b` }, // Hex w/ underscore support\n      // Decimals w/ underscore support, with optional fragments and scientific exponent (e) suffix.\n      { begin: `(?:\\\\b\\\\d+(?:_\\\\d+)*(\\\\.(?:\\\\d+(?:_\\\\d+)*))?|\\\\B\\\\.\\\\d+)(?:[eE][+-]?\\\\d+)?` }\n    ],\n    relevance: 0\n  };\n  const LITERALS = [\n    \"false\",\n    \"null\",\n    \"true\"\n  ];\n  const KWS = [\n    // Magic constants:\n    // <https://www.php.net/manual/en/language.constants.predefined.php>\n    \"__CLASS__\",\n    \"__DIR__\",\n    \"__FILE__\",\n    \"__FUNCTION__\",\n    \"__COMPILER_HALT_OFFSET__\",\n    \"__LINE__\",\n    \"__METHOD__\",\n    \"__NAMESPACE__\",\n    \"__TRAIT__\",\n    // Function that look like language construct or language construct that look like function:\n    // List of keywords that may not require parenthesis\n    \"die\",\n    \"echo\",\n    \"exit\",\n    \"include\",\n    \"include_once\",\n    \"print\",\n    \"require\",\n    \"require_once\",\n    // These are not language construct (function) but operate on the currently-executing function and can access the current symbol table\n    // 'compact extract func_get_arg func_get_args func_num_args get_called_class get_parent_class ' +\n    // Other keywords:\n    // <https://www.php.net/manual/en/reserved.php>\n    // <https://www.php.net/manual/en/language.types.type-juggling.php>\n    \"array\",\n    \"abstract\",\n    \"and\",\n    \"as\",\n    \"binary\",\n    \"bool\",\n    \"boolean\",\n    \"break\",\n    \"callable\",\n    \"case\",\n    \"catch\",\n    \"class\",\n    \"clone\",\n    \"const\",\n    \"continue\",\n    \"declare\",\n    \"default\",\n    \"do\",\n    \"double\",\n    \"else\",\n    \"elseif\",\n    \"empty\",\n    \"enddeclare\",\n    \"endfor\",\n    \"endforeach\",\n    \"endif\",\n    \"endswitch\",\n    \"endwhile\",\n    \"enum\",\n    \"eval\",\n    \"extends\",\n    \"final\",\n    \"finally\",\n    \"float\",\n    \"for\",\n    \"foreach\",\n    \"from\",\n    \"global\",\n    \"goto\",\n    \"if\",\n    \"implements\",\n    \"instanceof\",\n    \"insteadof\",\n    \"int\",\n    \"integer\",\n    \"interface\",\n    \"isset\",\n    \"iterable\",\n    \"list\",\n    \"match|0\",\n    \"mixed\",\n    \"new\",\n    \"never\",\n    \"object\",\n    \"or\",\n    \"private\",\n    \"protected\",\n    \"public\",\n    \"readonly\",\n    \"real\",\n    \"return\",\n    \"string\",\n    \"switch\",\n    \"throw\",\n    \"trait\",\n    \"try\",\n    \"unset\",\n    \"use\",\n    \"var\",\n    \"void\",\n    \"while\",\n    \"xor\",\n    \"yield\"\n  ];\n\n  const BUILT_INS = [\n    // Standard PHP library:\n    // <https://www.php.net/manual/en/book.spl.php>\n    \"Error|0\",\n    \"AppendIterator\",\n    \"ArgumentCountError\",\n    \"ArithmeticError\",\n    \"ArrayIterator\",\n    \"ArrayObject\",\n    \"AssertionError\",\n    \"BadFunctionCallException\",\n    \"BadMethodCallException\",\n    \"CachingIterator\",\n    \"CallbackFilterIterator\",\n    \"CompileError\",\n    \"Countable\",\n    \"DirectoryIterator\",\n    \"DivisionByZeroError\",\n    \"DomainException\",\n    \"EmptyIterator\",\n    \"ErrorException\",\n    \"Exception\",\n    \"FilesystemIterator\",\n    \"FilterIterator\",\n    \"GlobIterator\",\n    \"InfiniteIterator\",\n    \"InvalidArgumentException\",\n    \"IteratorIterator\",\n    \"LengthException\",\n    \"LimitIterator\",\n    \"LogicException\",\n    \"MultipleIterator\",\n    \"NoRewindIterator\",\n    \"OutOfBoundsException\",\n    \"OutOfRangeException\",\n    \"OuterIterator\",\n    \"OverflowException\",\n    \"ParentIterator\",\n    \"ParseError\",\n    \"RangeException\",\n    \"RecursiveArrayIterator\",\n    \"RecursiveCachingIterator\",\n    \"RecursiveCallbackFilterIterator\",\n    \"RecursiveDirectoryIterator\",\n    \"RecursiveFilterIterator\",\n    \"RecursiveIterator\",\n    \"RecursiveIteratorIterator\",\n    \"RecursiveRegexIterator\",\n    \"RecursiveTreeIterator\",\n    \"RegexIterator\",\n    \"RuntimeException\",\n    \"SeekableIterator\",\n    \"SplDoublyLinkedList\",\n    \"SplFileInfo\",\n    \"SplFileObject\",\n    \"SplFixedArray\",\n    \"SplHeap\",\n    \"SplMaxHeap\",\n    \"SplMinHeap\",\n    \"SplObjectStorage\",\n    \"SplObserver\",\n    \"SplPriorityQueue\",\n    \"SplQueue\",\n    \"SplStack\",\n    \"SplSubject\",\n    \"SplTempFileObject\",\n    \"TypeError\",\n    \"UnderflowException\",\n    \"UnexpectedValueException\",\n    \"UnhandledMatchError\",\n    // Reserved interfaces:\n    // <https://www.php.net/manual/en/reserved.interfaces.php>\n    \"ArrayAccess\",\n    \"BackedEnum\",\n    \"Closure\",\n    \"Fiber\",\n    \"Generator\",\n    \"Iterator\",\n    \"IteratorAggregate\",\n    \"Serializable\",\n    \"Stringable\",\n    \"Throwable\",\n    \"Traversable\",\n    \"UnitEnum\",\n    \"WeakReference\",\n    \"WeakMap\",\n    // Reserved classes:\n    // <https://www.php.net/manual/en/reserved.classes.php>\n    \"Directory\",\n    \"__PHP_Incomplete_Class\",\n    \"parent\",\n    \"php_user_filter\",\n    \"self\",\n    \"static\",\n    \"stdClass\"\n  ];\n\n  /** Dual-case keywords\n   *\n   * [\"then\",\"FILE\"] =>\n   *     [\"then\", \"THEN\", \"FILE\", \"file\"]\n   *\n   * @param {string[]} items */\n  const dualCase = (items) => {\n    /** @type string[] */\n    const result = [];\n    items.forEach(item => {\n      result.push(item);\n      if (item.toLowerCase() === item) {\n        result.push(item.toUpperCase());\n      } else {\n        result.push(item.toLowerCase());\n      }\n    });\n    return result;\n  };\n\n  const KEYWORDS = {\n    keyword: KWS,\n    literal: dualCase(LITERALS),\n    built_in: BUILT_INS,\n  };\n\n  /**\n   * @param {string[]} items */\n  const normalizeKeywords = (items) => {\n    return items.map(item => {\n      return item.replace(/\\|\\d+$/, \"\");\n    });\n  };\n\n  const CONSTRUCTOR_CALL = { variants: [\n    {\n      match: [\n        /new/,\n        regex.concat(WHITESPACE, \"+\"),\n        // to prevent built ins from being confused as the class constructor call\n        regex.concat(\"(?!\", normalizeKeywords(BUILT_INS).join(\"\\\\b|\"), \"\\\\b)\"),\n        PASCAL_CASE_CLASS_NAME_RE,\n      ],\n      scope: {\n        1: \"keyword\",\n        4: \"title.class\",\n      },\n    }\n  ] };\n\n  const CONSTANT_REFERENCE = regex.concat(IDENT_RE, \"\\\\b(?!\\\\()\");\n\n  const LEFT_AND_RIGHT_SIDE_OF_DOUBLE_COLON = { variants: [\n    {\n      match: [\n        regex.concat(\n          /::/,\n          regex.lookahead(/(?!class\\b)/)\n        ),\n        CONSTANT_REFERENCE,\n      ],\n      scope: { 2: \"variable.constant\", },\n    },\n    {\n      match: [\n        /::/,\n        /class/,\n      ],\n      scope: { 2: \"variable.language\", },\n    },\n    {\n      match: [\n        PASCAL_CASE_CLASS_NAME_RE,\n        regex.concat(\n          /::/,\n          regex.lookahead(/(?!class\\b)/)\n        ),\n        CONSTANT_REFERENCE,\n      ],\n      scope: {\n        1: \"title.class\",\n        3: \"variable.constant\",\n      },\n    },\n    {\n      match: [\n        PASCAL_CASE_CLASS_NAME_RE,\n        regex.concat(\n          \"::\",\n          regex.lookahead(/(?!class\\b)/)\n        ),\n      ],\n      scope: { 1: \"title.class\", },\n    },\n    {\n      match: [\n        PASCAL_CASE_CLASS_NAME_RE,\n        /::/,\n        /class/,\n      ],\n      scope: {\n        1: \"title.class\",\n        3: \"variable.language\",\n      },\n    }\n  ] };\n\n  const NAMED_ARGUMENT = {\n    scope: 'attr',\n    match: regex.concat(IDENT_RE, regex.lookahead(':'), regex.lookahead(/(?!::)/)),\n  };\n  const PARAMS_MODE = {\n    relevance: 0,\n    begin: /\\(/,\n    end: /\\)/,\n    keywords: KEYWORDS,\n    contains: [\n      NAMED_ARGUMENT,\n      VARIABLE,\n      LEFT_AND_RIGHT_SIDE_OF_DOUBLE_COLON,\n      hljs.C_BLOCK_COMMENT_MODE,\n      STRING,\n      NUMBER,\n      CONSTRUCTOR_CALL,\n    ],\n  };\n  const FUNCTION_INVOKE = {\n    relevance: 0,\n    match: [\n      /\\b/,\n      // to prevent keywords from being confused as the function title\n      regex.concat(\"(?!fn\\\\b|function\\\\b|\", normalizeKeywords(KWS).join(\"\\\\b|\"), \"|\", normalizeKeywords(BUILT_INS).join(\"\\\\b|\"), \"\\\\b)\"),\n      IDENT_RE,\n      regex.concat(WHITESPACE, \"*\"),\n      regex.lookahead(/(?=\\()/)\n    ],\n    scope: { 3: \"title.function.invoke\", },\n    contains: [ PARAMS_MODE ]\n  };\n  PARAMS_MODE.contains.push(FUNCTION_INVOKE);\n\n  const ATTRIBUTE_CONTAINS = [\n    NAMED_ARGUMENT,\n    LEFT_AND_RIGHT_SIDE_OF_DOUBLE_COLON,\n    hljs.C_BLOCK_COMMENT_MODE,\n    STRING,\n    NUMBER,\n    CONSTRUCTOR_CALL,\n  ];\n\n  const ATTRIBUTES = {\n    begin: regex.concat(/#\\[\\s*\\\\?/,\n      regex.either(\n        PASCAL_CASE_CLASS_NAME_RE,\n        UPCASE_NAME_RE\n      )\n    ),\n    beginScope: \"meta\",\n    end: /]/,\n    endScope: \"meta\",\n    keywords: {\n      literal: LITERALS,\n      keyword: [\n        'new',\n        'array',\n      ]\n    },\n    contains: [\n      {\n        begin: /\\[/,\n        end: /]/,\n        keywords: {\n          literal: LITERALS,\n          keyword: [\n            'new',\n            'array',\n          ]\n        },\n        contains: [\n          'self',\n          ...ATTRIBUTE_CONTAINS,\n        ]\n      },\n      ...ATTRIBUTE_CONTAINS,\n      {\n        scope: 'meta',\n        variants: [\n          { match: PASCAL_CASE_CLASS_NAME_RE },\n          { match: UPCASE_NAME_RE }\n        ]\n      }\n    ]\n  };\n\n  return {\n    case_insensitive: false,\n    keywords: KEYWORDS,\n    contains: [\n      ATTRIBUTES,\n      hljs.HASH_COMMENT_MODE,\n      hljs.COMMENT('//', '$'),\n      hljs.COMMENT(\n        '/\\\\*',\n        '\\\\*/',\n        { contains: [\n          {\n            scope: 'doctag',\n            match: '@[A-Za-z]+'\n          }\n        ] }\n      ),\n      {\n        match: /__halt_compiler\\(\\);/,\n        keywords: '__halt_compiler',\n        starts: {\n          scope: \"comment\",\n          end: hljs.MATCH_NOTHING_RE,\n          contains: [\n            {\n              match: /\\?>/,\n              scope: \"meta\",\n              endsParent: true\n            }\n          ]\n        }\n      },\n      PREPROCESSOR,\n      {\n        scope: 'variable.language',\n        match: /\\$this\\b/\n      },\n      VARIABLE,\n      FUNCTION_INVOKE,\n      LEFT_AND_RIGHT_SIDE_OF_DOUBLE_COLON,\n      {\n        match: [\n          /const/,\n          /\\s/,\n          IDENT_RE,\n        ],\n        scope: {\n          1: \"keyword\",\n          3: \"variable.constant\",\n        },\n      },\n      CONSTRUCTOR_CALL,\n      {\n        scope: 'function',\n        relevance: 0,\n        beginKeywords: 'fn function',\n        end: /[;{]/,\n        excludeEnd: true,\n        illegal: '[$%\\\\[]',\n        contains: [\n          { beginKeywords: 'use', },\n          hljs.UNDERSCORE_TITLE_MODE,\n          {\n            begin: '=>', // No markup, just a relevance booster\n            endsParent: true\n          },\n          {\n            scope: 'params',\n            begin: '\\\\(',\n            end: '\\\\)',\n            excludeBegin: true,\n            excludeEnd: true,\n            keywords: KEYWORDS,\n            contains: [\n              'self',\n              ATTRIBUTES,\n              VARIABLE,\n              LEFT_AND_RIGHT_SIDE_OF_DOUBLE_COLON,\n              hljs.C_BLOCK_COMMENT_MODE,\n              STRING,\n              NUMBER\n            ]\n          },\n        ]\n      },\n      {\n        scope: 'class',\n        variants: [\n          {\n            beginKeywords: \"enum\",\n            illegal: /[($\"]/\n          },\n          {\n            beginKeywords: \"class interface trait\",\n            illegal: /[:($\"]/\n          }\n        ],\n        relevance: 0,\n        end: /\\{/,\n        excludeEnd: true,\n        contains: [\n          { beginKeywords: 'extends implements' },\n          hljs.UNDERSCORE_TITLE_MODE\n        ]\n      },\n      // both use and namespace still use \"old style\" rules (vs multi-match)\n      // because the namespace name can include `\\` and we still want each\n      // element to be treated as its own *individual* title\n      {\n        beginKeywords: 'namespace',\n        relevance: 0,\n        end: ';',\n        illegal: /[.']/,\n        contains: [ hljs.inherit(hljs.UNDERSCORE_TITLE_MODE, { scope: \"title.class\" }) ]\n      },\n      {\n        beginKeywords: 'use',\n        relevance: 0,\n        end: ';',\n        contains: [\n          // TODO: title.function vs title.class\n          {\n            match: /\\b(as|const|function)\\b/,\n            scope: \"keyword\"\n          },\n          // TODO: could be title.class or title.function\n          hljs.UNDERSCORE_TITLE_MODE\n        ]\n      },\n      STRING,\n      NUMBER,\n    ]\n  };\n}\n\nexport { php as default };\n"], "names": [], "mappings": "AAAA;;;;;;AAMA,GAEA;;;GAGG;;;AACH,SAAS,IAAI,IAAI;IACf,MAAM,QAAQ,KAAK,KAAK;IACxB,oEAAoE;IACpE,0CAA0C;IAC1C,MAAM,eAAe;IACrB,MAAM,WAAW,MAAM,MAAM,CAC3B,4CACA;IACF,oCAAoC;IACpC,MAAM,4BAA4B,MAAM,MAAM,CAC5C,0EACA;IACF,MAAM,iBAAiB,MAAM,MAAM,CACjC,UACA;IACF,MAAM,WAAW;QACf,OAAO;QACP,OAAO,SAAS;IAClB;IACA,MAAM,eAAe;QACnB,OAAO;QACP,UAAU;YACR;gBAAE,OAAO;gBAAU,WAAW;YAAG;YACjC;gBAAE,OAAO;YAAO;YAChB,2DAA2D;YAC3D;gBAAE,OAAO;gBAAO,WAAW;YAAI;YAC/B;gBAAE,OAAO;YAAM,EAAE,cAAc;SAChC;IACH;IACA,MAAM,QAAQ;QACZ,OAAO;QACP,UAAU;YACR;gBAAE,OAAO;YAAQ;YACjB;gBACE,OAAO;gBACP,KAAK;YACP;SACD;IACH;IACA,MAAM,gBAAgB,KAAK,OAAO,CAAC,KAAK,gBAAgB,EAAE;QAAE,SAAS;IAAM;IAC3E,MAAM,gBAAgB,KAAK,OAAO,CAAC,KAAK,iBAAiB,EAAE;QACzD,SAAS;QACT,UAAU,KAAK,iBAAiB,CAAC,QAAQ,CAAC,MAAM,CAAC;IACnD;IAEA,MAAM,UAAU;QACd,OAAO;QACP,KAAK;QACL,UAAU,KAAK,iBAAiB,CAAC,QAAQ,CAAC,MAAM,CAAC;QACjD,YAAY,CAAC,GAAG;YAAW,KAAK,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE;QAAE;QACjE,UAAU,CAAC,GAAG;YAAW,IAAI,KAAK,IAAI,CAAC,WAAW,KAAK,CAAC,CAAC,EAAE,EAAE,KAAK,WAAW;QAAI;IACnF;IAEA,MAAM,SAAS,KAAK,iBAAiB,CAAC;QACpC,OAAO;QACP,KAAK;IACP;IACA,mFAAmF;IACnF,MAAM,aAAa;IACnB,MAAM,SAAS;QACb,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA,MAAM,SAAS;QACb,OAAO;QACP,UAAU;YACR;gBAAE,OAAO,CAAC,2BAA2B,CAAC;YAAC;YACvC;gBAAE,OAAO,CAAC,6BAA6B,CAAC;YAAC;YACzC;gBAAE,OAAO,CAAC,yCAAyC,CAAC;YAAC;YACrD,8FAA8F;YAC9F;gBAAE,OAAO,CAAC,0EAA0E,CAAC;YAAC;SACvF;QACD,WAAW;IACb;IACA,MAAM,WAAW;QACf;QACA;QACA;KACD;IACD,MAAM,MAAM;QACV,mBAAmB;QACnB,oEAAoE;QACpE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,4FAA4F;QAC5F,oDAAoD;QACpD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,sIAAsI;QACtI,kGAAkG;QAClG,kBAAkB;QAClB,+CAA+C;QAC/C,mEAAmE;QACnE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,YAAY;QAChB,wBAAwB;QACxB,+CAA+C;QAC/C;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,uBAAuB;QACvB,0DAA0D;QAC1D;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,oBAAoB;QACpB,uDAAuD;QACvD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED;;;;;6BAK2B,GAC3B,MAAM,WAAW,CAAC;QAChB,mBAAmB,GACnB,MAAM,SAAS,EAAE;QACjB,MAAM,OAAO,CAAC,CAAA;YACZ,OAAO,IAAI,CAAC;YACZ,IAAI,KAAK,WAAW,OAAO,MAAM;gBAC/B,OAAO,IAAI,CAAC,KAAK,WAAW;YAC9B,OAAO;gBACL,OAAO,IAAI,CAAC,KAAK,WAAW;YAC9B;QACF;QACA,OAAO;IACT;IAEA,MAAM,WAAW;QACf,SAAS;QACT,SAAS,SAAS;QAClB,UAAU;IACZ;IAEA;6BAC2B,GAC3B,MAAM,oBAAoB,CAAC;QACzB,OAAO,MAAM,GAAG,CAAC,CAAA;YACf,OAAO,KAAK,OAAO,CAAC,UAAU;QAChC;IACF;IAEA,MAAM,mBAAmB;QAAE,UAAU;YACnC;gBACE,OAAO;oBACL;oBACA,MAAM,MAAM,CAAC,YAAY;oBACzB,yEAAyE;oBACzE,MAAM,MAAM,CAAC,OAAO,kBAAkB,WAAW,IAAI,CAAC,SAAS;oBAC/D;iBACD;gBACD,OAAO;oBACL,GAAG;oBACH,GAAG;gBACL;YACF;SACD;IAAC;IAEF,MAAM,qBAAqB,MAAM,MAAM,CAAC,UAAU;IAElD,MAAM,sCAAsC;QAAE,UAAU;YACtD;gBACE,OAAO;oBACL,MAAM,MAAM,CACV,MACA,MAAM,SAAS,CAAC;oBAElB;iBACD;gBACD,OAAO;oBAAE,GAAG;gBAAqB;YACnC;YACA;gBACE,OAAO;oBACL;oBACA;iBACD;gBACD,OAAO;oBAAE,GAAG;gBAAqB;YACnC;YACA;gBACE,OAAO;oBACL;oBACA,MAAM,MAAM,CACV,MACA,MAAM,SAAS,CAAC;oBAElB;iBACD;gBACD,OAAO;oBACL,GAAG;oBACH,GAAG;gBACL;YACF;YACA;gBACE,OAAO;oBACL;oBACA,MAAM,MAAM,CACV,MACA,MAAM,SAAS,CAAC;iBAEnB;gBACD,OAAO;oBAAE,GAAG;gBAAe;YAC7B;YACA;gBACE,OAAO;oBACL;oBACA;oBACA;iBACD;gBACD,OAAO;oBACL,GAAG;oBACH,GAAG;gBACL;YACF;SACD;IAAC;IAEF,MAAM,iBAAiB;QACrB,OAAO;QACP,OAAO,MAAM,MAAM,CAAC,UAAU,MAAM,SAAS,CAAC,MAAM,MAAM,SAAS,CAAC;IACtE;IACA,MAAM,cAAc;QAClB,WAAW;QACX,OAAO;QACP,KAAK;QACL,UAAU;QACV,UAAU;YACR;YACA;YACA;YACA,KAAK,oBAAoB;YACzB;YACA;YACA;SACD;IACH;IACA,MAAM,kBAAkB;QACtB,WAAW;QACX,OAAO;YACL;YACA,gEAAgE;YAChE,MAAM,MAAM,CAAC,yBAAyB,kBAAkB,KAAK,IAAI,CAAC,SAAS,KAAK,kBAAkB,WAAW,IAAI,CAAC,SAAS;YAC3H;YACA,MAAM,MAAM,CAAC,YAAY;YACzB,MAAM,SAAS,CAAC;SACjB;QACD,OAAO;YAAE,GAAG;QAAyB;QACrC,UAAU;YAAE;SAAa;IAC3B;IACA,YAAY,QAAQ,CAAC,IAAI,CAAC;IAE1B,MAAM,qBAAqB;QACzB;QACA;QACA,KAAK,oBAAoB;QACzB;QACA;QACA;KACD;IAED,MAAM,aAAa;QACjB,OAAO,MAAM,MAAM,CAAC,aAClB,MAAM,MAAM,CACV,2BACA;QAGJ,YAAY;QACZ,KAAK;QACL,UAAU;QACV,UAAU;YACR,SAAS;YACT,SAAS;gBACP;gBACA;aACD;QACH;QACA,UAAU;YACR;gBACE,OAAO;gBACP,KAAK;gBACL,UAAU;oBACR,SAAS;oBACT,SAAS;wBACP;wBACA;qBACD;gBACH;gBACA,UAAU;oBACR;uBACG;iBACJ;YACH;eACG;YACH;gBACE,OAAO;gBACP,UAAU;oBACR;wBAAE,OAAO;oBAA0B;oBACnC;wBAAE,OAAO;oBAAe;iBACzB;YACH;SACD;IACH;IAEA,OAAO;QACL,kBAAkB;QAClB,UAAU;QACV,UAAU;YACR;YACA,KAAK,iBAAiB;YACtB,KAAK,OAAO,CAAC,MAAM;YACnB,KAAK,OAAO,CACV,QACA,QACA;gBAAE,UAAU;oBACV;wBACE,OAAO;wBACP,OAAO;oBACT;iBACD;YAAC;YAEJ;gBACE,OAAO;gBACP,UAAU;gBACV,QAAQ;oBACN,OAAO;oBACP,KAAK,KAAK,gBAAgB;oBAC1B,UAAU;wBACR;4BACE,OAAO;4BACP,OAAO;4BACP,YAAY;wBACd;qBACD;gBACH;YACF;YACA;YACA;gBACE,OAAO;gBACP,OAAO;YACT;YACA;YACA;YACA;YACA;gBACE,OAAO;oBACL;oBACA;oBACA;iBACD;gBACD,OAAO;oBACL,GAAG;oBACH,GAAG;gBACL;YACF;YACA;YACA;gBACE,OAAO;gBACP,WAAW;gBACX,eAAe;gBACf,KAAK;gBACL,YAAY;gBACZ,SAAS;gBACT,UAAU;oBACR;wBAAE,eAAe;oBAAO;oBACxB,KAAK,qBAAqB;oBAC1B;wBACE,OAAO;wBACP,YAAY;oBACd;oBACA;wBACE,OAAO;wBACP,OAAO;wBACP,KAAK;wBACL,cAAc;wBACd,YAAY;wBACZ,UAAU;wBACV,UAAU;4BACR;4BACA;4BACA;4BACA;4BACA,KAAK,oBAAoB;4BACzB;4BACA;yBACD;oBACH;iBACD;YACH;YACA;gBACE,OAAO;gBACP,UAAU;oBACR;wBACE,eAAe;wBACf,SAAS;oBACX;oBACA;wBACE,eAAe;wBACf,SAAS;oBACX;iBACD;gBACD,WAAW;gBACX,KAAK;gBACL,YAAY;gBACZ,UAAU;oBACR;wBAAE,eAAe;oBAAqB;oBACtC,KAAK,qBAAqB;iBAC3B;YACH;YACA,sEAAsE;YACtE,oEAAoE;YACpE,sDAAsD;YACtD;gBACE,eAAe;gBACf,WAAW;gBACX,KAAK;gBACL,SAAS;gBACT,UAAU;oBAAE,KAAK,OAAO,CAAC,KAAK,qBAAqB,EAAE;wBAAE,OAAO;oBAAc;iBAAI;YAClF;YACA;gBACE,eAAe;gBACf,WAAW;gBACX,KAAK;gBACL,UAAU;oBACR,sCAAsC;oBACtC;wBACE,OAAO;wBACP,OAAO;oBACT;oBACA,+CAA+C;oBAC/C,KAAK,qBAAqB;iBAC3B;YACH;YACA;YACA;SACD;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8436, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/php-template.js"], "sourcesContent": ["/*\nLanguage: PHP Template\nRequires: xml.js, php.js\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://www.php.net\nCategory: common\n*/\n\nfunction phpTemplate(hljs) {\n  return {\n    name: \"PHP template\",\n    subLanguage: 'xml',\n    contains: [\n      {\n        begin: /<\\?(php|=)?/,\n        end: /\\?>/,\n        subLanguage: 'php',\n        contains: [\n          // We don't want the php closing tag ?> to close the PHP block when\n          // inside any of the following blocks:\n          {\n            begin: '/\\\\*',\n            end: '\\\\*/',\n            skip: true\n          },\n          {\n            begin: 'b\"',\n            end: '\"',\n            skip: true\n          },\n          {\n            begin: 'b\\'',\n            end: '\\'',\n            skip: true\n          },\n          hljs.inherit(hljs.APOS_STRING_MODE, {\n            illegal: null,\n            className: null,\n            contains: null,\n            skip: true\n          }),\n          hljs.inherit(hljs.QUOTE_STRING_MODE, {\n            illegal: null,\n            className: null,\n            contains: null,\n            skip: true\n          })\n        ]\n      }\n    ]\n  };\n}\n\nexport { phpTemplate as default };\n"], "names": [], "mappings": "AAAA;;;;;;AAMA;;;AAEA,SAAS,YAAY,IAAI;IACvB,OAAO;QACL,MAAM;QACN,aAAa;QACb,UAAU;YACR;gBACE,OAAO;gBACP,KAAK;gBACL,aAAa;gBACb,UAAU;oBACR,mEAAmE;oBACnE,sCAAsC;oBACtC;wBACE,OAAO;wBACP,KAAK;wBACL,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,KAAK;wBACL,MAAM;oBACR;oBACA;wBACE,OAAO;wBACP,KAAK;wBACL,MAAM;oBACR;oBACA,KAAK,OAAO,CAAC,KAAK,gBAAgB,EAAE;wBAClC,SAAS;wBACT,WAAW;wBACX,UAAU;wBACV,MAAM;oBACR;oBACA,KAAK,OAAO,CAAC,KAAK,iBAAiB,EAAE;wBACnC,SAAS;wBACT,WAAW;wBACX,UAAU;wBACV,MAAM;oBACR;iBACD;YACH;SACD;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8496, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/plaintext.js"], "sourcesContent": ["/*\nLanguage: Plain text\nAuthor: <PERSON><PERSON> (<EMAIL>)\nDescription: Plain text without any highlighting.\nCategory: common\n*/\n\nfunction plaintext(hljs) {\n  return {\n    name: 'Plain text',\n    aliases: [\n      'text',\n      'txt'\n    ],\n    disableAutodetect: true\n  };\n}\n\nexport { plaintext as default };\n"], "names": [], "mappings": "AAAA;;;;;AAKA;;;AAEA,SAAS,UAAU,IAAI;IACrB,OAAO;QACL,MAAM;QACN,SAAS;YACP;YACA;SACD;QACD,mBAAmB;IACrB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8521, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/python.js"], "sourcesContent": ["/*\nLanguage: Python\nDescription: Python is an interpreted, object-oriented, high-level programming language with dynamic semantics.\nWebsite: https://www.python.org\nCategory: common\n*/\n\nfunction python(hljs) {\n  const regex = hljs.regex;\n  const IDENT_RE = /[\\p{XID_Start}_]\\p{XID_Continue}*/u;\n  const RESERVED_WORDS = [\n    'and',\n    'as',\n    'assert',\n    'async',\n    'await',\n    'break',\n    'case',\n    'class',\n    'continue',\n    'def',\n    'del',\n    'elif',\n    'else',\n    'except',\n    'finally',\n    'for',\n    'from',\n    'global',\n    'if',\n    'import',\n    'in',\n    'is',\n    'lambda',\n    'match',\n    'nonlocal|10',\n    'not',\n    'or',\n    'pass',\n    'raise',\n    'return',\n    'try',\n    'while',\n    'with',\n    'yield'\n  ];\n\n  const BUILT_INS = [\n    '__import__',\n    'abs',\n    'all',\n    'any',\n    'ascii',\n    'bin',\n    'bool',\n    'breakpoint',\n    'bytearray',\n    'bytes',\n    'callable',\n    'chr',\n    'classmethod',\n    'compile',\n    'complex',\n    'delattr',\n    'dict',\n    'dir',\n    'divmod',\n    'enumerate',\n    'eval',\n    'exec',\n    'filter',\n    'float',\n    'format',\n    'frozenset',\n    'getattr',\n    'globals',\n    'hasattr',\n    'hash',\n    'help',\n    'hex',\n    'id',\n    'input',\n    'int',\n    'isinstance',\n    'issubclass',\n    'iter',\n    'len',\n    'list',\n    'locals',\n    'map',\n    'max',\n    'memoryview',\n    'min',\n    'next',\n    'object',\n    'oct',\n    'open',\n    'ord',\n    'pow',\n    'print',\n    'property',\n    'range',\n    'repr',\n    'reversed',\n    'round',\n    'set',\n    'setattr',\n    'slice',\n    'sorted',\n    'staticmethod',\n    'str',\n    'sum',\n    'super',\n    'tuple',\n    'type',\n    'vars',\n    'zip'\n  ];\n\n  const LITERALS = [\n    '__debug__',\n    'Ellipsis',\n    'False',\n    'None',\n    'NotImplemented',\n    'True'\n  ];\n\n  // https://docs.python.org/3/library/typing.html\n  // TODO: Could these be supplemented by a CamelCase matcher in certain\n  // contexts, leaving these remaining only for relevance hinting?\n  const TYPES = [\n    \"Any\",\n    \"Callable\",\n    \"Coroutine\",\n    \"Dict\",\n    \"List\",\n    \"Literal\",\n    \"Generic\",\n    \"Optional\",\n    \"Sequence\",\n    \"Set\",\n    \"Tuple\",\n    \"Type\",\n    \"Union\"\n  ];\n\n  const KEYWORDS = {\n    $pattern: /[A-Za-z]\\w+|__\\w+__/,\n    keyword: RESERVED_WORDS,\n    built_in: BUILT_INS,\n    literal: LITERALS,\n    type: TYPES\n  };\n\n  const PROMPT = {\n    className: 'meta',\n    begin: /^(>>>|\\.\\.\\.) /\n  };\n\n  const SUBST = {\n    className: 'subst',\n    begin: /\\{/,\n    end: /\\}/,\n    keywords: KEYWORDS,\n    illegal: /#/\n  };\n\n  const LITERAL_BRACKET = {\n    begin: /\\{\\{/,\n    relevance: 0\n  };\n\n  const STRING = {\n    className: 'string',\n    contains: [ hljs.BACKSLASH_ESCAPE ],\n    variants: [\n      {\n        begin: /([uU]|[bB]|[rR]|[bB][rR]|[rR][bB])?'''/,\n        end: /'''/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          PROMPT\n        ],\n        relevance: 10\n      },\n      {\n        begin: /([uU]|[bB]|[rR]|[bB][rR]|[rR][bB])?\"\"\"/,\n        end: /\"\"\"/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          PROMPT\n        ],\n        relevance: 10\n      },\n      {\n        begin: /([fF][rR]|[rR][fF]|[fF])'''/,\n        end: /'''/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          PROMPT,\n          LITERAL_BRACKET,\n          SUBST\n        ]\n      },\n      {\n        begin: /([fF][rR]|[rR][fF]|[fF])\"\"\"/,\n        end: /\"\"\"/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          PROMPT,\n          LITERAL_BRACKET,\n          SUBST\n        ]\n      },\n      {\n        begin: /([uU]|[rR])'/,\n        end: /'/,\n        relevance: 10\n      },\n      {\n        begin: /([uU]|[rR])\"/,\n        end: /\"/,\n        relevance: 10\n      },\n      {\n        begin: /([bB]|[bB][rR]|[rR][bB])'/,\n        end: /'/\n      },\n      {\n        begin: /([bB]|[bB][rR]|[rR][bB])\"/,\n        end: /\"/\n      },\n      {\n        begin: /([fF][rR]|[rR][fF]|[fF])'/,\n        end: /'/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          LITERAL_BRACKET,\n          SUBST\n        ]\n      },\n      {\n        begin: /([fF][rR]|[rR][fF]|[fF])\"/,\n        end: /\"/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          LITERAL_BRACKET,\n          SUBST\n        ]\n      },\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE\n    ]\n  };\n\n  // https://docs.python.org/3.9/reference/lexical_analysis.html#numeric-literals\n  const digitpart = '[0-9](_?[0-9])*';\n  const pointfloat = `(\\\\b(${digitpart}))?\\\\.(${digitpart})|\\\\b(${digitpart})\\\\.`;\n  // Whitespace after a number (or any lexical token) is needed only if its absence\n  // would change the tokenization\n  // https://docs.python.org/3.9/reference/lexical_analysis.html#whitespace-between-tokens\n  // We deviate slightly, requiring a word boundary or a keyword\n  // to avoid accidentally recognizing *prefixes* (e.g., `0` in `0x41` or `08` or `0__1`)\n  const lookahead = `\\\\b|${RESERVED_WORDS.join('|')}`;\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    variants: [\n      // exponentfloat, pointfloat\n      // https://docs.python.org/3.9/reference/lexical_analysis.html#floating-point-literals\n      // optionally imaginary\n      // https://docs.python.org/3.9/reference/lexical_analysis.html#imaginary-literals\n      // Note: no leading \\b because floats can start with a decimal point\n      // and we don't want to mishandle e.g. `fn(.5)`,\n      // no trailing \\b for pointfloat because it can end with a decimal point\n      // and we don't want to mishandle e.g. `0..hex()`; this should be safe\n      // because both MUST contain a decimal point and so cannot be confused with\n      // the interior part of an identifier\n      {\n        begin: `(\\\\b(${digitpart})|(${pointfloat}))[eE][+-]?(${digitpart})[jJ]?(?=${lookahead})`\n      },\n      {\n        begin: `(${pointfloat})[jJ]?`\n      },\n\n      // decinteger, bininteger, octinteger, hexinteger\n      // https://docs.python.org/3.9/reference/lexical_analysis.html#integer-literals\n      // optionally \"long\" in Python 2\n      // https://docs.python.org/2.7/reference/lexical_analysis.html#integer-and-long-integer-literals\n      // decinteger is optionally imaginary\n      // https://docs.python.org/3.9/reference/lexical_analysis.html#imaginary-literals\n      {\n        begin: `\\\\b([1-9](_?[0-9])*|0+(_?0)*)[lLjJ]?(?=${lookahead})`\n      },\n      {\n        begin: `\\\\b0[bB](_?[01])+[lL]?(?=${lookahead})`\n      },\n      {\n        begin: `\\\\b0[oO](_?[0-7])+[lL]?(?=${lookahead})`\n      },\n      {\n        begin: `\\\\b0[xX](_?[0-9a-fA-F])+[lL]?(?=${lookahead})`\n      },\n\n      // imagnumber (digitpart-based)\n      // https://docs.python.org/3.9/reference/lexical_analysis.html#imaginary-literals\n      {\n        begin: `\\\\b(${digitpart})[jJ](?=${lookahead})`\n      }\n    ]\n  };\n  const COMMENT_TYPE = {\n    className: \"comment\",\n    begin: regex.lookahead(/# type:/),\n    end: /$/,\n    keywords: KEYWORDS,\n    contains: [\n      { // prevent keywords from coloring `type`\n        begin: /# type:/\n      },\n      // comment within a datatype comment includes no keywords\n      {\n        begin: /#/,\n        end: /\\b\\B/,\n        endsWithParent: true\n      }\n    ]\n  };\n  const PARAMS = {\n    className: 'params',\n    variants: [\n      // Exclude params in functions without params\n      {\n        className: \"\",\n        begin: /\\(\\s*\\)/,\n        skip: true\n      },\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        excludeBegin: true,\n        excludeEnd: true,\n        keywords: KEYWORDS,\n        contains: [\n          'self',\n          PROMPT,\n          NUMBER,\n          STRING,\n          hljs.HASH_COMMENT_MODE\n        ]\n      }\n    ]\n  };\n  SUBST.contains = [\n    STRING,\n    NUMBER,\n    PROMPT\n  ];\n\n  return {\n    name: 'Python',\n    aliases: [\n      'py',\n      'gyp',\n      'ipython'\n    ],\n    unicodeRegex: true,\n    keywords: KEYWORDS,\n    illegal: /(<\\/|\\?)|=>/,\n    contains: [\n      PROMPT,\n      NUMBER,\n      {\n        // very common convention\n        scope: 'variable.language',\n        match: /\\bself\\b/\n      },\n      {\n        // eat \"if\" prior to string so that it won't accidentally be\n        // labeled as an f-string\n        beginKeywords: \"if\",\n        relevance: 0\n      },\n      { match: /\\bor\\b/, scope: \"keyword\" },\n      STRING,\n      COMMENT_TYPE,\n      hljs.HASH_COMMENT_MODE,\n      {\n        match: [\n          /\\bdef/, /\\s+/,\n          IDENT_RE,\n        ],\n        scope: {\n          1: \"keyword\",\n          3: \"title.function\"\n        },\n        contains: [ PARAMS ]\n      },\n      {\n        variants: [\n          {\n            match: [\n              /\\bclass/, /\\s+/,\n              IDENT_RE, /\\s*/,\n              /\\(\\s*/, IDENT_RE,/\\s*\\)/\n            ],\n          },\n          {\n            match: [\n              /\\bclass/, /\\s+/,\n              IDENT_RE\n            ],\n          }\n        ],\n        scope: {\n          1: \"keyword\",\n          3: \"title.class\",\n          6: \"title.class.inherited\",\n        }\n      },\n      {\n        className: 'meta',\n        begin: /^[\\t ]*@/,\n        end: /(?=#)|$/,\n        contains: [\n          NUMBER,\n          PARAMS,\n          STRING\n        ]\n      }\n    ]\n  };\n}\n\nexport { python as default };\n"], "names": [], "mappings": "AAAA;;;;;AAKA;;;AAEA,SAAS,OAAO,IAAI;IAClB,MAAM,QAAQ,KAAK,KAAK;IACxB,MAAM,WAAW;IACjB,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,YAAY;QAChB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;QACA;KACD;IAED,gDAAgD;IAChD,sEAAsE;IACtE,gEAAgE;IAChE,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,WAAW;QACf,UAAU;QACV,SAAS;QACT,UAAU;QACV,SAAS;QACT,MAAM;IACR;IAEA,MAAM,SAAS;QACb,WAAW;QACX,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,WAAW;QACX,OAAO;QACP,KAAK;QACL,UAAU;QACV,SAAS;IACX;IAEA,MAAM,kBAAkB;QACtB,OAAO;QACP,WAAW;IACb;IAEA,MAAM,SAAS;QACb,WAAW;QACX,UAAU;YAAE,KAAK,gBAAgB;SAAE;QACnC,UAAU;YACR;gBACE,OAAO;gBACP,KAAK;gBACL,UAAU;oBACR,KAAK,gBAAgB;oBACrB;iBACD;gBACD,WAAW;YACb;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,UAAU;oBACR,KAAK,gBAAgB;oBACrB;iBACD;gBACD,WAAW;YACb;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,UAAU;oBACR,KAAK,gBAAgB;oBACrB;oBACA;oBACA;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,UAAU;oBACR,KAAK,gBAAgB;oBACrB;oBACA;oBACA;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,WAAW;YACb;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,WAAW;YACb;YACA;gBACE,OAAO;gBACP,KAAK;YACP;YACA;gBACE,OAAO;gBACP,KAAK;YACP;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,UAAU;oBACR,KAAK,gBAAgB;oBACrB;oBACA;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,UAAU;oBACR,KAAK,gBAAgB;oBACrB;oBACA;iBACD;YACH;YACA,KAAK,gBAAgB;YACrB,KAAK,iBAAiB;SACvB;IACH;IAEA,+EAA+E;IAC/E,MAAM,YAAY;IAClB,MAAM,aAAa,CAAC,KAAK,EAAE,UAAU,OAAO,EAAE,UAAU,MAAM,EAAE,UAAU,IAAI,CAAC;IAC/E,iFAAiF;IACjF,gCAAgC;IAChC,wFAAwF;IACxF,8DAA8D;IAC9D,uFAAuF;IACvF,MAAM,YAAY,CAAC,IAAI,EAAE,eAAe,IAAI,CAAC,MAAM;IACnD,MAAM,SAAS;QACb,WAAW;QACX,WAAW;QACX,UAAU;YACR,4BAA4B;YAC5B,sFAAsF;YACtF,uBAAuB;YACvB,iFAAiF;YACjF,oEAAoE;YACpE,gDAAgD;YAChD,wEAAwE;YACxE,sEAAsE;YACtE,2EAA2E;YAC3E,qCAAqC;YACrC;gBACE,OAAO,CAAC,KAAK,EAAE,UAAU,GAAG,EAAE,WAAW,YAAY,EAAE,UAAU,SAAS,EAAE,UAAU,CAAC,CAAC;YAC1F;YACA;gBACE,OAAO,CAAC,CAAC,EAAE,WAAW,MAAM,CAAC;YAC/B;YAEA,iDAAiD;YACjD,+EAA+E;YAC/E,gCAAgC;YAChC,gGAAgG;YAChG,qCAAqC;YACrC,iFAAiF;YACjF;gBACE,OAAO,CAAC,uCAAuC,EAAE,UAAU,CAAC,CAAC;YAC/D;YACA;gBACE,OAAO,CAAC,yBAAyB,EAAE,UAAU,CAAC,CAAC;YACjD;YACA;gBACE,OAAO,CAAC,0BAA0B,EAAE,UAAU,CAAC,CAAC;YAClD;YACA;gBACE,OAAO,CAAC,gCAAgC,EAAE,UAAU,CAAC,CAAC;YACxD;YAEA,+BAA+B;YAC/B,iFAAiF;YACjF;gBACE,OAAO,CAAC,IAAI,EAAE,UAAU,QAAQ,EAAE,UAAU,CAAC,CAAC;YAChD;SACD;IACH;IACA,MAAM,eAAe;QACnB,WAAW;QACX,OAAO,MAAM,SAAS,CAAC;QACvB,KAAK;QACL,UAAU;QACV,UAAU;YACR;gBACE,OAAO;YACT;YACA,yDAAyD;YACzD;gBACE,OAAO;gBACP,KAAK;gBACL,gBAAgB;YAClB;SACD;IACH;IACA,MAAM,SAAS;QACb,WAAW;QACX,UAAU;YACR,6CAA6C;YAC7C;gBACE,WAAW;gBACX,OAAO;gBACP,MAAM;YACR;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,cAAc;gBACd,YAAY;gBACZ,UAAU;gBACV,UAAU;oBACR;oBACA;oBACA;oBACA;oBACA,KAAK,iBAAiB;iBACvB;YACH;SACD;IACH;IACA,MAAM,QAAQ,GAAG;QACf;QACA;QACA;KACD;IAED,OAAO;QACL,MAAM;QACN,SAAS;YACP;YACA;YACA;SACD;QACD,cAAc;QACd,UAAU;QACV,SAAS;QACT,UAAU;YACR;YACA;YACA;gBACE,yBAAyB;gBACzB,OAAO;gBACP,OAAO;YACT;YACA;gBACE,4DAA4D;gBAC5D,yBAAyB;gBACzB,eAAe;gBACf,WAAW;YACb;YACA;gBAAE,OAAO;gBAAU,OAAO;YAAU;YACpC;YACA;YACA,KAAK,iBAAiB;YACtB;gBACE,OAAO;oBACL;oBAAS;oBACT;iBACD;gBACD,OAAO;oBACL,GAAG;oBACH,GAAG;gBACL;gBACA,UAAU;oBAAE;iBAAQ;YACtB;YACA;gBACE,UAAU;oBACR;wBACE,OAAO;4BACL;4BAAW;4BACX;4BAAU;4BACV;4BAAS;4BAAS;yBACnB;oBACH;oBACA;wBACE,OAAO;4BACL;4BAAW;4BACX;yBACD;oBACH;iBACD;gBACD,OAAO;oBACL,GAAG;oBACH,GAAG;oBACH,GAAG;gBACL;YACF;YACA;gBACE,WAAW;gBACX,OAAO;gBACP,KAAK;gBACL,UAAU;oBACR;oBACA;oBACA;iBACD;YACH;SACD;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 8964, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/python-repl.js"], "sourcesContent": ["/*\nLanguage: Python REPL\nRequires: python.js\nAuthor: <PERSON> <<EMAIL>>\nCategory: common\n*/\n\nfunction pythonRepl(hljs) {\n  return {\n    aliases: [ 'pycon' ],\n    contains: [\n      {\n        className: 'meta.prompt',\n        starts: {\n          // a space separates the REPL prefix from the actual code\n          // this is purely for cleaner HTML output\n          end: / |$/,\n          starts: {\n            end: '$',\n            subLanguage: 'python'\n          }\n        },\n        variants: [\n          { begin: /^>>>(?=[ ]|$)/ },\n          { begin: /^\\.\\.\\.(?=[ ]|$)/ }\n        ]\n      }\n    ]\n  };\n}\n\nexport { pythonRepl as default };\n"], "names": [], "mappings": "AAAA;;;;;AAKA;;;AAEA,SAAS,WAAW,IAAI;IACtB,OAAO;QACL,SAAS;YAAE;SAAS;QACpB,UAAU;YACR;gBACE,WAAW;gBACX,QAAQ;oBACN,yDAAyD;oBACzD,yCAAyC;oBACzC,KAAK;oBACL,QAAQ;wBACN,KAAK;wBACL,aAAa;oBACf;gBACF;gBACA,UAAU;oBACR;wBAAE,OAAO;oBAAgB;oBACzB;wBAAE,OAAO;oBAAmB;iBAC7B;YACH;SACD;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 9008, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/r.js"], "sourcesContent": ["/*\nLanguage: R\nDescription: R is a free software environment for statistical computing and graphics.\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nWebsite: https://www.r-project.org\nCategory: common,scientific\n*/\n\n/** @type LanguageFn */\nfunction r(hljs) {\n  const regex = hljs.regex;\n  // Identifiers in R cannot start with `_`, but they can start with `.` if it\n  // is not immediately followed by a digit.\n  // R also supports quoted identifiers, which are near-arbitrary sequences\n  // delimited by backticks (`…`), which may contain escape sequences. These are\n  // handled in a separate mode. See `test/markup/r/names.txt` for examples.\n  // FIXME: Support Unicode identifiers.\n  const IDENT_RE = /(?:(?:[a-zA-Z]|\\.[._a-zA-Z])[._a-zA-Z0-9]*)|\\.(?!\\d)/;\n  const NUMBER_TYPES_RE = regex.either(\n    // Special case: only hexadecimal binary powers can contain fractions\n    /0[xX][0-9a-fA-F]+\\.[0-9a-fA-F]*[pP][+-]?\\d+i?/,\n    // Hexadecimal numbers without fraction and optional binary power\n    /0[xX][0-9a-fA-F]+(?:[pP][+-]?\\d+)?[Li]?/,\n    // Decimal numbers\n    /(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:[eE][+-]?\\d+)?[Li]?/\n  );\n  const OPERATORS_RE = /[=!<>:]=|\\|\\||&&|:::?|<-|<<-|->>|->|\\|>|[-+*\\/?!$&|:<=>@^~]|\\*\\*/;\n  const PUNCTUATION_RE = regex.either(\n    /[()]/,\n    /[{}]/,\n    /\\[\\[/,\n    /[[\\]]/,\n    /\\\\/,\n    /,/\n  );\n\n  return {\n    name: 'R',\n\n    keywords: {\n      $pattern: IDENT_RE,\n      keyword:\n        'function if in break next repeat else for while',\n      literal:\n        'NULL NA TRUE FALSE Inf NaN NA_integer_|10 NA_real_|10 '\n        + 'NA_character_|10 NA_complex_|10',\n      built_in:\n        // Builtin constants\n        'LETTERS letters month.abb month.name pi T F '\n        // Primitive functions\n        // These are all the functions in `base` that are implemented as a\n        // `.Primitive`, minus those functions that are also keywords.\n        + 'abs acos acosh all any anyNA Arg as.call as.character '\n        + 'as.complex as.double as.environment as.integer as.logical '\n        + 'as.null.default as.numeric as.raw asin asinh atan atanh attr '\n        + 'attributes baseenv browser c call ceiling class Conj cos cosh '\n        + 'cospi cummax cummin cumprod cumsum digamma dim dimnames '\n        + 'emptyenv exp expression floor forceAndCall gamma gc.time '\n        + 'globalenv Im interactive invisible is.array is.atomic is.call '\n        + 'is.character is.complex is.double is.environment is.expression '\n        + 'is.finite is.function is.infinite is.integer is.language '\n        + 'is.list is.logical is.matrix is.na is.name is.nan is.null '\n        + 'is.numeric is.object is.pairlist is.raw is.recursive is.single '\n        + 'is.symbol lazyLoadDBfetch length lgamma list log max min '\n        + 'missing Mod names nargs nzchar oldClass on.exit pos.to.env '\n        + 'proc.time prod quote range Re rep retracemem return round '\n        + 'seq_along seq_len seq.int sign signif sin sinh sinpi sqrt '\n        + 'standardGeneric substitute sum switch tan tanh tanpi tracemem '\n        + 'trigamma trunc unclass untracemem UseMethod xtfrm',\n    },\n\n    contains: [\n      // Roxygen comments\n      hljs.COMMENT(\n        /#'/,\n        /$/,\n        { contains: [\n          {\n            // Handle `@examples` separately to cause all subsequent code\n            // until the next `@`-tag on its own line to be kept as-is,\n            // preventing highlighting. This code is example R code, so nested\n            // doctags shouldn’t be treated as such. See\n            // `test/markup/r/roxygen.txt` for an example.\n            scope: 'doctag',\n            match: /@examples/,\n            starts: {\n              end: regex.lookahead(regex.either(\n                // end if another doc comment\n                /\\n^#'\\s*(?=@[a-zA-Z]+)/,\n                // or a line with no comment\n                /\\n^(?!#')/\n              )),\n              endsParent: true\n            }\n          },\n          {\n            // Handle `@param` to highlight the parameter name following\n            // after.\n            scope: 'doctag',\n            begin: '@param',\n            end: /$/,\n            contains: [\n              {\n                scope: 'variable',\n                variants: [\n                  { match: IDENT_RE },\n                  { match: /`(?:\\\\.|[^`\\\\])+`/ }\n                ],\n                endsParent: true\n              }\n            ]\n          },\n          {\n            scope: 'doctag',\n            match: /@[a-zA-Z]+/\n          },\n          {\n            scope: 'keyword',\n            match: /\\\\[a-zA-Z]+/\n          }\n        ] }\n      ),\n\n      hljs.HASH_COMMENT_MODE,\n\n      {\n        scope: 'string',\n        contains: [ hljs.BACKSLASH_ESCAPE ],\n        variants: [\n          hljs.END_SAME_AS_BEGIN({\n            begin: /[rR]\"(-*)\\(/,\n            end: /\\)(-*)\"/\n          }),\n          hljs.END_SAME_AS_BEGIN({\n            begin: /[rR]\"(-*)\\{/,\n            end: /\\}(-*)\"/\n          }),\n          hljs.END_SAME_AS_BEGIN({\n            begin: /[rR]\"(-*)\\[/,\n            end: /\\](-*)\"/\n          }),\n          hljs.END_SAME_AS_BEGIN({\n            begin: /[rR]'(-*)\\(/,\n            end: /\\)(-*)'/\n          }),\n          hljs.END_SAME_AS_BEGIN({\n            begin: /[rR]'(-*)\\{/,\n            end: /\\}(-*)'/\n          }),\n          hljs.END_SAME_AS_BEGIN({\n            begin: /[rR]'(-*)\\[/,\n            end: /\\](-*)'/\n          }),\n          {\n            begin: '\"',\n            end: '\"',\n            relevance: 0\n          },\n          {\n            begin: \"'\",\n            end: \"'\",\n            relevance: 0\n          }\n        ],\n      },\n\n      // Matching numbers immediately following punctuation and operators is\n      // tricky since we need to look at the character ahead of a number to\n      // ensure the number is not part of an identifier, and we cannot use\n      // negative look-behind assertions. So instead we explicitly handle all\n      // possible combinations of (operator|punctuation), number.\n      // TODO: replace with negative look-behind when available\n      // { begin: /(?<![a-zA-Z0-9._])0[xX][0-9a-fA-F]+\\.[0-9a-fA-F]*[pP][+-]?\\d+i?/ },\n      // { begin: /(?<![a-zA-Z0-9._])0[xX][0-9a-fA-F]+([pP][+-]?\\d+)?[Li]?/ },\n      // { begin: /(?<![a-zA-Z0-9._])(\\d+(\\.\\d*)?|\\.\\d+)([eE][+-]?\\d+)?[Li]?/ }\n      {\n        relevance: 0,\n        variants: [\n          {\n            scope: {\n              1: 'operator',\n              2: 'number'\n            },\n            match: [\n              OPERATORS_RE,\n              NUMBER_TYPES_RE\n            ]\n          },\n          {\n            scope: {\n              1: 'operator',\n              2: 'number'\n            },\n            match: [\n              /%[^%]*%/,\n              NUMBER_TYPES_RE\n            ]\n          },\n          {\n            scope: {\n              1: 'punctuation',\n              2: 'number'\n            },\n            match: [\n              PUNCTUATION_RE,\n              NUMBER_TYPES_RE\n            ]\n          },\n          {\n            scope: { 2: 'number' },\n            match: [\n              /[^a-zA-Z0-9._]|^/, // not part of an identifier, or start of document\n              NUMBER_TYPES_RE\n            ]\n          }\n        ]\n      },\n\n      // Operators/punctuation when they're not directly followed by numbers\n      {\n        // Relevance boost for the most common assignment form.\n        scope: { 3: 'operator' },\n        match: [\n          IDENT_RE,\n          /\\s+/,\n          /<-/,\n          /\\s+/\n        ]\n      },\n\n      {\n        scope: 'operator',\n        relevance: 0,\n        variants: [\n          { match: OPERATORS_RE },\n          { match: /%[^%]*%/ }\n        ]\n      },\n\n      {\n        scope: 'punctuation',\n        relevance: 0,\n        match: PUNCTUATION_RE\n      },\n\n      {\n        // Escaped identifier\n        begin: '`',\n        end: '`',\n        contains: [ { begin: /\\\\./ } ]\n      }\n    ]\n  };\n}\n\nexport { r as default };\n"], "names": [], "mappings": "AAAA;;;;;;;AAOA,GAEA,qBAAqB;;;AACrB,SAAS,EAAE,IAAI;IACb,MAAM,QAAQ,KAAK,KAAK;IACxB,4EAA4E;IAC5E,0CAA0C;IAC1C,yEAAyE;IACzE,8EAA8E;IAC9E,0EAA0E;IAC1E,sCAAsC;IACtC,MAAM,WAAW;IACjB,MAAM,kBAAkB,MAAM,MAAM,CAClC,qEAAqE;IACrE,iDACA,iEAAiE;IACjE,2CACA,kBAAkB;IAClB;IAEF,MAAM,eAAe;IACrB,MAAM,iBAAiB,MAAM,MAAM,CACjC,QACA,QACA,QACA,SACA,MACA;IAGF,OAAO;QACL,MAAM;QAEN,UAAU;YACR,UAAU;YACV,SACE;YACF,SACE,2DACE;YACJ,UACE,oBAAoB;YACpB,iDAIE,2DACA,+DACA,kEACA,mEACA,6DACA,8DACA,mEACA,oEACA,8DACA,+DACA,oEACA,8DACA,gEACA,+DACA,+DACA,mEACA;QACN;QAEA,UAAU;YACR,mBAAmB;YACnB,KAAK,OAAO,CACV,MACA,KACA;gBAAE,UAAU;oBACV;wBACE,6DAA6D;wBAC7D,2DAA2D;wBAC3D,kEAAkE;wBAClE,4CAA4C;wBAC5C,8CAA8C;wBAC9C,OAAO;wBACP,OAAO;wBACP,QAAQ;4BACN,KAAK,MAAM,SAAS,CAAC,MAAM,MAAM,CAC/B,6BAA6B;4BAC7B,0BACA,4BAA4B;4BAC5B;4BAEF,YAAY;wBACd;oBACF;oBACA;wBACE,4DAA4D;wBAC5D,SAAS;wBACT,OAAO;wBACP,OAAO;wBACP,KAAK;wBACL,UAAU;4BACR;gCACE,OAAO;gCACP,UAAU;oCACR;wCAAE,OAAO;oCAAS;oCAClB;wCAAE,OAAO;oCAAoB;iCAC9B;gCACD,YAAY;4BACd;yBACD;oBACH;oBACA;wBACE,OAAO;wBACP,OAAO;oBACT;oBACA;wBACE,OAAO;wBACP,OAAO;oBACT;iBACD;YAAC;YAGJ,KAAK,iBAAiB;YAEtB;gBACE,OAAO;gBACP,UAAU;oBAAE,KAAK,gBAAgB;iBAAE;gBACnC,UAAU;oBACR,KAAK,iBAAiB,CAAC;wBACrB,OAAO;wBACP,KAAK;oBACP;oBACA,KAAK,iBAAiB,CAAC;wBACrB,OAAO;wBACP,KAAK;oBACP;oBACA,KAAK,iBAAiB,CAAC;wBACrB,OAAO;wBACP,KAAK;oBACP;oBACA,KAAK,iBAAiB,CAAC;wBACrB,OAAO;wBACP,KAAK;oBACP;oBACA,KAAK,iBAAiB,CAAC;wBACrB,OAAO;wBACP,KAAK;oBACP;oBACA,KAAK,iBAAiB,CAAC;wBACrB,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;wBACL,WAAW;oBACb;oBACA;wBACE,OAAO;wBACP,KAAK;wBACL,WAAW;oBACb;iBACD;YACH;YAEA,sEAAsE;YACtE,qEAAqE;YACrE,oEAAoE;YACpE,uEAAuE;YACvE,2DAA2D;YAC3D,yDAAyD;YACzD,gFAAgF;YAChF,wEAAwE;YACxE,yEAAyE;YACzE;gBACE,WAAW;gBACX,UAAU;oBACR;wBACE,OAAO;4BACL,GAAG;4BACH,GAAG;wBACL;wBACA,OAAO;4BACL;4BACA;yBACD;oBACH;oBACA;wBACE,OAAO;4BACL,GAAG;4BACH,GAAG;wBACL;wBACA,OAAO;4BACL;4BACA;yBACD;oBACH;oBACA;wBACE,OAAO;4BACL,GAAG;4BACH,GAAG;wBACL;wBACA,OAAO;4BACL;4BACA;yBACD;oBACH;oBACA;wBACE,OAAO;4BAAE,GAAG;wBAAS;wBACrB,OAAO;4BACL;4BACA;yBACD;oBACH;iBACD;YACH;YAEA,sEAAsE;YACtE;gBACE,uDAAuD;gBACvD,OAAO;oBAAE,GAAG;gBAAW;gBACvB,OAAO;oBACL;oBACA;oBACA;oBACA;iBACD;YACH;YAEA;gBACE,OAAO;gBACP,WAAW;gBACX,UAAU;oBACR;wBAAE,OAAO;oBAAa;oBACtB;wBAAE,OAAO;oBAAU;iBACpB;YACH;YAEA;gBACE,OAAO;gBACP,WAAW;gBACX,OAAO;YACT;YAEA;gBACE,qBAAqB;gBACrB,OAAO;gBACP,KAAK;gBACL,UAAU;oBAAE;wBAAE,OAAO;oBAAM;iBAAG;YAChC;SACD;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 9238, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/ruby.js"], "sourcesContent": ["/*\nLanguage: Ruby\nDescription: Ruby is a dynamic, open source programming language with a focus on simplicity and productivity.\nWebsite: https://www.ruby-lang.org/\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>\nCategory: common, scripting\n*/\n\nfunction ruby(hljs) {\n  const regex = hljs.regex;\n  const RUBY_METHOD_RE = '([a-zA-Z_]\\\\w*[!?=]?|[-+~]@|<<|>>|=~|===?|<=>|[<>]=?|\\\\*\\\\*|[-/+%^&*~`|]|\\\\[\\\\]=?)';\n  // TODO: move concepts like CAMEL_CASE into `modes.js`\n  const CLASS_NAME_RE = regex.either(\n    /\\b([A-Z]+[a-z0-9]+)+/,\n    // ends in caps\n    /\\b([A-Z]+[a-z0-9]+)+[A-Z]+/,\n  )\n  ;\n  const CLASS_NAME_WITH_NAMESPACE_RE = regex.concat(CLASS_NAME_RE, /(::\\w+)*/);\n  // very popular ruby built-ins that one might even assume\n  // are actual keywords (despite that not being the case)\n  const PSEUDO_KWS = [\n    \"include\",\n    \"extend\",\n    \"prepend\",\n    \"public\",\n    \"private\",\n    \"protected\",\n    \"raise\",\n    \"throw\"\n  ];\n  const RUBY_KEYWORDS = {\n    \"variable.constant\": [\n      \"__FILE__\",\n      \"__LINE__\",\n      \"__ENCODING__\"\n    ],\n    \"variable.language\": [\n      \"self\",\n      \"super\",\n    ],\n    keyword: [\n      \"alias\",\n      \"and\",\n      \"begin\",\n      \"BEGIN\",\n      \"break\",\n      \"case\",\n      \"class\",\n      \"defined\",\n      \"do\",\n      \"else\",\n      \"elsif\",\n      \"end\",\n      \"END\",\n      \"ensure\",\n      \"for\",\n      \"if\",\n      \"in\",\n      \"module\",\n      \"next\",\n      \"not\",\n      \"or\",\n      \"redo\",\n      \"require\",\n      \"rescue\",\n      \"retry\",\n      \"return\",\n      \"then\",\n      \"undef\",\n      \"unless\",\n      \"until\",\n      \"when\",\n      \"while\",\n      \"yield\",\n      ...PSEUDO_KWS\n    ],\n    built_in: [\n      \"proc\",\n      \"lambda\",\n      \"attr_accessor\",\n      \"attr_reader\",\n      \"attr_writer\",\n      \"define_method\",\n      \"private_constant\",\n      \"module_function\"\n    ],\n    literal: [\n      \"true\",\n      \"false\",\n      \"nil\"\n    ]\n  };\n  const YARDOCTAG = {\n    className: 'doctag',\n    begin: '@[A-Za-z]+'\n  };\n  const IRB_OBJECT = {\n    begin: '#<',\n    end: '>'\n  };\n  const COMMENT_MODES = [\n    hljs.COMMENT(\n      '#',\n      '$',\n      { contains: [ YARDOCTAG ] }\n    ),\n    hljs.COMMENT(\n      '^=begin',\n      '^=end',\n      {\n        contains: [ YARDOCTAG ],\n        relevance: 10\n      }\n    ),\n    hljs.COMMENT('^__END__', hljs.MATCH_NOTHING_RE)\n  ];\n  const SUBST = {\n    className: 'subst',\n    begin: /#\\{/,\n    end: /\\}/,\n    keywords: RUBY_KEYWORDS\n  };\n  const STRING = {\n    className: 'string',\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      SUBST\n    ],\n    variants: [\n      {\n        begin: /'/,\n        end: /'/\n      },\n      {\n        begin: /\"/,\n        end: /\"/\n      },\n      {\n        begin: /`/,\n        end: /`/\n      },\n      {\n        begin: /%[qQwWx]?\\(/,\n        end: /\\)/\n      },\n      {\n        begin: /%[qQwWx]?\\[/,\n        end: /\\]/\n      },\n      {\n        begin: /%[qQwWx]?\\{/,\n        end: /\\}/\n      },\n      {\n        begin: /%[qQwWx]?</,\n        end: />/\n      },\n      {\n        begin: /%[qQwWx]?\\//,\n        end: /\\//\n      },\n      {\n        begin: /%[qQwWx]?%/,\n        end: /%/\n      },\n      {\n        begin: /%[qQwWx]?-/,\n        end: /-/\n      },\n      {\n        begin: /%[qQwWx]?\\|/,\n        end: /\\|/\n      },\n      // in the following expressions, \\B in the beginning suppresses recognition of ?-sequences\n      // where ? is the last character of a preceding identifier, as in: `func?4`\n      { begin: /\\B\\?(\\\\\\d{1,3})/ },\n      { begin: /\\B\\?(\\\\x[A-Fa-f0-9]{1,2})/ },\n      { begin: /\\B\\?(\\\\u\\{?[A-Fa-f0-9]{1,6}\\}?)/ },\n      { begin: /\\B\\?(\\\\M-\\\\C-|\\\\M-\\\\c|\\\\c\\\\M-|\\\\M-|\\\\C-\\\\M-)[\\x20-\\x7e]/ },\n      { begin: /\\B\\?\\\\(c|C-)[\\x20-\\x7e]/ },\n      { begin: /\\B\\?\\\\?\\S/ },\n      // heredocs\n      {\n        // this guard makes sure that we have an entire heredoc and not a false\n        // positive (auto-detect, etc.)\n        begin: regex.concat(\n          /<<[-~]?'?/,\n          regex.lookahead(/(\\w+)(?=\\W)[^\\n]*\\n(?:[^\\n]*\\n)*?\\s*\\1\\b/)\n        ),\n        contains: [\n          hljs.END_SAME_AS_BEGIN({\n            begin: /(\\w+)/,\n            end: /(\\w+)/,\n            contains: [\n              hljs.BACKSLASH_ESCAPE,\n              SUBST\n            ]\n          })\n        ]\n      }\n    ]\n  };\n\n  // Ruby syntax is underdocumented, but this grammar seems to be accurate\n  // as of version 2.7.2 (confirmed with (irb and `Ripper.sexp(...)`)\n  // https://docs.ruby-lang.org/en/2.7.0/doc/syntax/literals_rdoc.html#label-Numbers\n  const decimal = '[1-9](_?[0-9])*|0';\n  const digits = '[0-9](_?[0-9])*';\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    variants: [\n      // decimal integer/float, optionally exponential or rational, optionally imaginary\n      { begin: `\\\\b(${decimal})(\\\\.(${digits}))?([eE][+-]?(${digits})|r)?i?\\\\b` },\n\n      // explicit decimal/binary/octal/hexadecimal integer,\n      // optionally rational and/or imaginary\n      { begin: \"\\\\b0[dD][0-9](_?[0-9])*r?i?\\\\b\" },\n      { begin: \"\\\\b0[bB][0-1](_?[0-1])*r?i?\\\\b\" },\n      { begin: \"\\\\b0[oO][0-7](_?[0-7])*r?i?\\\\b\" },\n      { begin: \"\\\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*r?i?\\\\b\" },\n\n      // 0-prefixed implicit octal integer, optionally rational and/or imaginary\n      { begin: \"\\\\b0(_?[0-7])+r?i?\\\\b\" }\n    ]\n  };\n\n  const PARAMS = {\n    variants: [\n      {\n        match: /\\(\\)/,\n      },\n      {\n        className: 'params',\n        begin: /\\(/,\n        end: /(?=\\))/,\n        excludeBegin: true,\n        endsParent: true,\n        keywords: RUBY_KEYWORDS,\n      }\n    ]\n  };\n\n  const INCLUDE_EXTEND = {\n    match: [\n      /(include|extend)\\s+/,\n      CLASS_NAME_WITH_NAMESPACE_RE\n    ],\n    scope: {\n      2: \"title.class\"\n    },\n    keywords: RUBY_KEYWORDS\n  };\n\n  const CLASS_DEFINITION = {\n    variants: [\n      {\n        match: [\n          /class\\s+/,\n          CLASS_NAME_WITH_NAMESPACE_RE,\n          /\\s+<\\s+/,\n          CLASS_NAME_WITH_NAMESPACE_RE\n        ]\n      },\n      {\n        match: [\n          /\\b(class|module)\\s+/,\n          CLASS_NAME_WITH_NAMESPACE_RE\n        ]\n      }\n    ],\n    scope: {\n      2: \"title.class\",\n      4: \"title.class.inherited\"\n    },\n    keywords: RUBY_KEYWORDS\n  };\n\n  const UPPER_CASE_CONSTANT = {\n    relevance: 0,\n    match: /\\b[A-Z][A-Z_0-9]+\\b/,\n    className: \"variable.constant\"\n  };\n\n  const METHOD_DEFINITION = {\n    match: [\n      /def/, /\\s+/,\n      RUBY_METHOD_RE\n    ],\n    scope: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    contains: [\n      PARAMS\n    ]\n  };\n\n  const OBJECT_CREATION = {\n    relevance: 0,\n    match: [\n      CLASS_NAME_WITH_NAMESPACE_RE,\n      /\\.new[. (]/\n    ],\n    scope: {\n      1: \"title.class\"\n    }\n  };\n\n  // CamelCase\n  const CLASS_REFERENCE = {\n    relevance: 0,\n    match: CLASS_NAME_RE,\n    scope: \"title.class\"\n  };\n\n  const RUBY_DEFAULT_CONTAINS = [\n    STRING,\n    CLASS_DEFINITION,\n    INCLUDE_EXTEND,\n    OBJECT_CREATION,\n    UPPER_CASE_CONSTANT,\n    CLASS_REFERENCE,\n    METHOD_DEFINITION,\n    {\n      // swallow namespace qualifiers before symbols\n      begin: hljs.IDENT_RE + '::' },\n    {\n      className: 'symbol',\n      begin: hljs.UNDERSCORE_IDENT_RE + '(!|\\\\?)?:',\n      relevance: 0\n    },\n    {\n      className: 'symbol',\n      begin: ':(?!\\\\s)',\n      contains: [\n        STRING,\n        { begin: RUBY_METHOD_RE }\n      ],\n      relevance: 0\n    },\n    NUMBER,\n    {\n      // negative-look forward attempts to prevent false matches like:\n      // @ident@ or $ident$ that might indicate this is not ruby at all\n      className: \"variable\",\n      begin: '(\\\\$\\\\W)|((\\\\$|@@?)(\\\\w+))(?=[^@$?])' + `(?![A-Za-z])(?![@$?'])`\n    },\n    {\n      className: 'params',\n      begin: /\\|(?!=)/,\n      end: /\\|/,\n      excludeBegin: true,\n      excludeEnd: true,\n      relevance: 0, // this could be a lot of things (in other languages) other than params\n      keywords: RUBY_KEYWORDS\n    },\n    { // regexp container\n      begin: '(' + hljs.RE_STARTERS_RE + '|unless)\\\\s*',\n      keywords: 'unless',\n      contains: [\n        {\n          className: 'regexp',\n          contains: [\n            hljs.BACKSLASH_ESCAPE,\n            SUBST\n          ],\n          illegal: /\\n/,\n          variants: [\n            {\n              begin: '/',\n              end: '/[a-z]*'\n            },\n            {\n              begin: /%r\\{/,\n              end: /\\}[a-z]*/\n            },\n            {\n              begin: '%r\\\\(',\n              end: '\\\\)[a-z]*'\n            },\n            {\n              begin: '%r!',\n              end: '![a-z]*'\n            },\n            {\n              begin: '%r\\\\[',\n              end: '\\\\][a-z]*'\n            }\n          ]\n        }\n      ].concat(IRB_OBJECT, COMMENT_MODES),\n      relevance: 0\n    }\n  ].concat(IRB_OBJECT, COMMENT_MODES);\n\n  SUBST.contains = RUBY_DEFAULT_CONTAINS;\n  PARAMS.contains = RUBY_DEFAULT_CONTAINS;\n\n  // >>\n  // ?>\n  const SIMPLE_PROMPT = \"[>?]>\";\n  // irb(main):001:0>\n  const DEFAULT_PROMPT = \"[\\\\w#]+\\\\(\\\\w+\\\\):\\\\d+:\\\\d+[>*]\";\n  const RVM_PROMPT = \"(\\\\w+-)?\\\\d+\\\\.\\\\d+\\\\.\\\\d+(p\\\\d+)?[^\\\\d][^>]+>\";\n\n  const IRB_DEFAULT = [\n    {\n      begin: /^\\s*=>/,\n      starts: {\n        end: '$',\n        contains: RUBY_DEFAULT_CONTAINS\n      }\n    },\n    {\n      className: 'meta.prompt',\n      begin: '^(' + SIMPLE_PROMPT + \"|\" + DEFAULT_PROMPT + '|' + RVM_PROMPT + ')(?=[ ])',\n      starts: {\n        end: '$',\n        keywords: RUBY_KEYWORDS,\n        contains: RUBY_DEFAULT_CONTAINS\n      }\n    }\n  ];\n\n  COMMENT_MODES.unshift(IRB_OBJECT);\n\n  return {\n    name: 'Ruby',\n    aliases: [\n      'rb',\n      'gemspec',\n      'podspec',\n      'thor',\n      'irb'\n    ],\n    keywords: RUBY_KEYWORDS,\n    illegal: /\\/\\*/,\n    contains: [ hljs.SHEBANG({ binary: \"ruby\" }) ]\n      .concat(IRB_DEFAULT)\n      .concat(COMMENT_MODES)\n      .concat(RUBY_DEFAULT_CONTAINS)\n  };\n}\n\nexport { ruby as default };\n"], "names": [], "mappings": "AAAA;;;;;;;AAOA;;;AAEA,SAAS,KAAK,IAAI;IAChB,MAAM,QAAQ,KAAK,KAAK;IACxB,MAAM,iBAAiB;IACvB,sDAAsD;IACtD,MAAM,gBAAgB,MAAM,MAAM,CAChC,wBACA,eAAe;IACf;IAGF,MAAM,+BAA+B,MAAM,MAAM,CAAC,eAAe;IACjE,yDAAyD;IACzD,wDAAwD;IACxD,MAAM,aAAa;QACjB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,gBAAgB;QACpB,qBAAqB;YACnB;YACA;YACA;SACD;QACD,qBAAqB;YACnB;YACA;SACD;QACD,SAAS;YACP;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;eACG;SACJ;QACD,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,SAAS;YACP;YACA;YACA;SACD;IACH;IACA,MAAM,YAAY;QAChB,WAAW;QACX,OAAO;IACT;IACA,MAAM,aAAa;QACjB,OAAO;QACP,KAAK;IACP;IACA,MAAM,gBAAgB;QACpB,KAAK,OAAO,CACV,KACA,KACA;YAAE,UAAU;gBAAE;aAAW;QAAC;QAE5B,KAAK,OAAO,CACV,WACA,SACA;YACE,UAAU;gBAAE;aAAW;YACvB,WAAW;QACb;QAEF,KAAK,OAAO,CAAC,YAAY,KAAK,gBAAgB;KAC/C;IACD,MAAM,QAAQ;QACZ,WAAW;QACX,OAAO;QACP,KAAK;QACL,UAAU;IACZ;IACA,MAAM,SAAS;QACb,WAAW;QACX,UAAU;YACR,KAAK,gBAAgB;YACrB;SACD;QACD,UAAU;YACR;gBACE,OAAO;gBACP,KAAK;YACP;YACA;gBACE,OAAO;gBACP,KAAK;YACP;YACA;gBACE,OAAO;gBACP,KAAK;YACP;YACA;gBACE,OAAO;gBACP,KAAK;YACP;YACA;gBACE,OAAO;gBACP,KAAK;YACP;YACA;gBACE,OAAO;gBACP,KAAK;YACP;YACA;gBACE,OAAO;gBACP,KAAK;YACP;YACA;gBACE,OAAO;gBACP,KAAK;YACP;YACA;gBACE,OAAO;gBACP,KAAK;YACP;YACA;gBACE,OAAO;gBACP,KAAK;YACP;YACA;gBACE,OAAO;gBACP,KAAK;YACP;YACA,0FAA0F;YAC1F,2EAA2E;YAC3E;gBAAE,OAAO;YAAkB;YAC3B;gBAAE,OAAO;YAA4B;YACrC;gBAAE,OAAO;YAAkC;YAC3C;gBAAE,OAAO;YAA0D;YACnE;gBAAE,OAAO;YAA0B;YACnC;gBAAE,OAAO;YAAY;YACrB,WAAW;YACX;gBACE,uEAAuE;gBACvE,+BAA+B;gBAC/B,OAAO,MAAM,MAAM,CACjB,aACA,MAAM,SAAS,CAAC;gBAElB,UAAU;oBACR,KAAK,iBAAiB,CAAC;wBACrB,OAAO;wBACP,KAAK;wBACL,UAAU;4BACR,KAAK,gBAAgB;4BACrB;yBACD;oBACH;iBACD;YACH;SACD;IACH;IAEA,wEAAwE;IACxE,mEAAmE;IACnE,kFAAkF;IAClF,MAAM,UAAU;IAChB,MAAM,SAAS;IACf,MAAM,SAAS;QACb,WAAW;QACX,WAAW;QACX,UAAU;YACR,kFAAkF;YAClF;gBAAE,OAAO,CAAC,IAAI,EAAE,QAAQ,MAAM,EAAE,OAAO,cAAc,EAAE,OAAO,UAAU,CAAC;YAAC;YAE1E,qDAAqD;YACrD,uCAAuC;YACvC;gBAAE,OAAO;YAAiC;YAC1C;gBAAE,OAAO;YAAiC;YAC1C;gBAAE,OAAO;YAAiC;YAC1C;gBAAE,OAAO;YAA6C;YAEtD,0EAA0E;YAC1E;gBAAE,OAAO;YAAwB;SAClC;IACH;IAEA,MAAM,SAAS;QACb,UAAU;YACR;gBACE,OAAO;YACT;YACA;gBACE,WAAW;gBACX,OAAO;gBACP,KAAK;gBACL,cAAc;gBACd,YAAY;gBACZ,UAAU;YACZ;SACD;IACH;IAEA,MAAM,iBAAiB;QACrB,OAAO;YACL;YACA;SACD;QACD,OAAO;YACL,GAAG;QACL;QACA,UAAU;IACZ;IAEA,MAAM,mBAAmB;QACvB,UAAU;YACR;gBACE,OAAO;oBACL;oBACA;oBACA;oBACA;iBACD;YACH;YACA;gBACE,OAAO;oBACL;oBACA;iBACD;YACH;SACD;QACD,OAAO;YACL,GAAG;YACH,GAAG;QACL;QACA,UAAU;IACZ;IAEA,MAAM,sBAAsB;QAC1B,WAAW;QACX,OAAO;QACP,WAAW;IACb;IAEA,MAAM,oBAAoB;QACxB,OAAO;YACL;YAAO;YACP;SACD;QACD,OAAO;YACL,GAAG;YACH,GAAG;QACL;QACA,UAAU;YACR;SACD;IACH;IAEA,MAAM,kBAAkB;QACtB,WAAW;QACX,OAAO;YACL;YACA;SACD;QACD,OAAO;YACL,GAAG;QACL;IACF;IAEA,YAAY;IACZ,MAAM,kBAAkB;QACtB,WAAW;QACX,OAAO;QACP,OAAO;IACT;IAEA,MAAM,wBAAwB;QAC5B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;YACE,8CAA8C;YAC9C,OAAO,KAAK,QAAQ,GAAG;QAAK;QAC9B;YACE,WAAW;YACX,OAAO,KAAK,mBAAmB,GAAG;YAClC,WAAW;QACb;QACA;YACE,WAAW;YACX,OAAO;YACP,UAAU;gBACR;gBACA;oBAAE,OAAO;gBAAe;aACzB;YACD,WAAW;QACb;QACA;QACA;YACE,gEAAgE;YAChE,iEAAiE;YACjE,WAAW;YACX,OAAO,yCAAyC,CAAC,sBAAsB,CAAC;QAC1E;QACA;YACE,WAAW;YACX,OAAO;YACP,KAAK;YACL,cAAc;YACd,YAAY;YACZ,WAAW;YACX,UAAU;QACZ;QACA;YACE,OAAO,MAAM,KAAK,cAAc,GAAG;YACnC,UAAU;YACV,UAAU;gBACR;oBACE,WAAW;oBACX,UAAU;wBACR,KAAK,gBAAgB;wBACrB;qBACD;oBACD,SAAS;oBACT,UAAU;wBACR;4BACE,OAAO;4BACP,KAAK;wBACP;wBACA;4BACE,OAAO;4BACP,KAAK;wBACP;wBACA;4BACE,OAAO;4BACP,KAAK;wBACP;wBACA;4BACE,OAAO;4BACP,KAAK;wBACP;wBACA;4BACE,OAAO;4BACP,KAAK;wBACP;qBACD;gBACH;aACD,CAAC,MAAM,CAAC,YAAY;YACrB,WAAW;QACb;KACD,CAAC,MAAM,CAAC,YAAY;IAErB,MAAM,QAAQ,GAAG;IACjB,OAAO,QAAQ,GAAG;IAElB,KAAK;IACL,KAAK;IACL,MAAM,gBAAgB;IACtB,mBAAmB;IACnB,MAAM,iBAAiB;IACvB,MAAM,aAAa;IAEnB,MAAM,cAAc;QAClB;YACE,OAAO;YACP,QAAQ;gBACN,KAAK;gBACL,UAAU;YACZ;QACF;QACA;YACE,WAAW;YACX,OAAO,OAAO,gBAAgB,MAAM,iBAAiB,MAAM,aAAa;YACxE,QAAQ;gBACN,KAAK;gBACL,UAAU;gBACV,UAAU;YACZ;QACF;KACD;IAED,cAAc,OAAO,CAAC;IAEtB,OAAO;QACL,MAAM;QACN,SAAS;YACP;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,SAAS;QACT,UAAU;YAAE,KAAK,OAAO,CAAC;gBAAE,QAAQ;YAAO;SAAI,CAC3C,MAAM,CAAC,aACP,MAAM,CAAC,eACP,MAAM,CAAC;IACZ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 9696, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/rust.js"], "sourcesContent": ["/*\nLanguage: Rust\nAuthor: <PERSON><PERSON> <andrey.v<PERSON><PERSON>@gmail.com>\nContributors: <PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.rust-lang.org\nCategory: common, system\n*/\n\n/** @type LanguageFn */\n\nfunction rust(hljs) {\n  const regex = hljs.regex;\n  // ============================================\n  // Added to support the r# keyword, which is a raw identifier in Rust.\n  const RAW_IDENTIFIER = /(r#)?/;\n  const UNDERSCORE_IDENT_RE = regex.concat(RAW_IDENTIFIER, hljs.UNDERSCORE_IDENT_RE);\n  const IDENT_RE = regex.concat(RAW_IDENTIFIER, hljs.IDENT_RE);\n  // ============================================\n  const FUNCTION_INVOKE = {\n    className: \"title.function.invoke\",\n    relevance: 0,\n    begin: regex.concat(\n      /\\b/,\n      /(?!let|for|while|if|else|match\\b)/,\n      IDENT_RE,\n      regex.lookahead(/\\s*\\(/))\n  };\n  const NUMBER_SUFFIX = '([ui](8|16|32|64|128|size)|f(32|64))\\?';\n  const KEYWORDS = [\n    \"abstract\",\n    \"as\",\n    \"async\",\n    \"await\",\n    \"become\",\n    \"box\",\n    \"break\",\n    \"const\",\n    \"continue\",\n    \"crate\",\n    \"do\",\n    \"dyn\",\n    \"else\",\n    \"enum\",\n    \"extern\",\n    \"false\",\n    \"final\",\n    \"fn\",\n    \"for\",\n    \"if\",\n    \"impl\",\n    \"in\",\n    \"let\",\n    \"loop\",\n    \"macro\",\n    \"match\",\n    \"mod\",\n    \"move\",\n    \"mut\",\n    \"override\",\n    \"priv\",\n    \"pub\",\n    \"ref\",\n    \"return\",\n    \"self\",\n    \"Self\",\n    \"static\",\n    \"struct\",\n    \"super\",\n    \"trait\",\n    \"true\",\n    \"try\",\n    \"type\",\n    \"typeof\",\n    \"union\",\n    \"unsafe\",\n    \"unsized\",\n    \"use\",\n    \"virtual\",\n    \"where\",\n    \"while\",\n    \"yield\"\n  ];\n  const LITERALS = [\n    \"true\",\n    \"false\",\n    \"Some\",\n    \"None\",\n    \"Ok\",\n    \"Err\"\n  ];\n  const BUILTINS = [\n    // functions\n    'drop ',\n    // traits\n    \"Copy\",\n    \"Send\",\n    \"Sized\",\n    \"Sync\",\n    \"Drop\",\n    \"Fn\",\n    \"FnMut\",\n    \"FnOnce\",\n    \"ToOwned\",\n    \"Clone\",\n    \"Debug\",\n    \"PartialEq\",\n    \"PartialOrd\",\n    \"Eq\",\n    \"Ord\",\n    \"AsRef\",\n    \"AsMut\",\n    \"Into\",\n    \"From\",\n    \"Default\",\n    \"Iterator\",\n    \"Extend\",\n    \"IntoIterator\",\n    \"DoubleEndedIterator\",\n    \"ExactSizeIterator\",\n    \"SliceConcatExt\",\n    \"ToString\",\n    // macros\n    \"assert!\",\n    \"assert_eq!\",\n    \"bitflags!\",\n    \"bytes!\",\n    \"cfg!\",\n    \"col!\",\n    \"concat!\",\n    \"concat_idents!\",\n    \"debug_assert!\",\n    \"debug_assert_eq!\",\n    \"env!\",\n    \"eprintln!\",\n    \"panic!\",\n    \"file!\",\n    \"format!\",\n    \"format_args!\",\n    \"include_bytes!\",\n    \"include_str!\",\n    \"line!\",\n    \"local_data_key!\",\n    \"module_path!\",\n    \"option_env!\",\n    \"print!\",\n    \"println!\",\n    \"select!\",\n    \"stringify!\",\n    \"try!\",\n    \"unimplemented!\",\n    \"unreachable!\",\n    \"vec!\",\n    \"write!\",\n    \"writeln!\",\n    \"macro_rules!\",\n    \"assert_ne!\",\n    \"debug_assert_ne!\"\n  ];\n  const TYPES = [\n    \"i8\",\n    \"i16\",\n    \"i32\",\n    \"i64\",\n    \"i128\",\n    \"isize\",\n    \"u8\",\n    \"u16\",\n    \"u32\",\n    \"u64\",\n    \"u128\",\n    \"usize\",\n    \"f32\",\n    \"f64\",\n    \"str\",\n    \"char\",\n    \"bool\",\n    \"Box\",\n    \"Option\",\n    \"Result\",\n    \"String\",\n    \"Vec\"\n  ];\n  return {\n    name: 'Rust',\n    aliases: [ 'rs' ],\n    keywords: {\n      $pattern: hljs.IDENT_RE + '!?',\n      type: TYPES,\n      keyword: KEYWORDS,\n      literal: LITERALS,\n      built_in: BUILTINS\n    },\n    illegal: '</',\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.COMMENT('/\\\\*', '\\\\*/', { contains: [ 'self' ] }),\n      hljs.inherit(hljs.QUOTE_STRING_MODE, {\n        begin: /b?\"/,\n        illegal: null\n      }),\n      {\n        className: 'symbol',\n        // negative lookahead to avoid matching `'`\n        begin: /'[a-zA-Z_][a-zA-Z0-9_]*(?!')/\n      },\n      {\n        scope: 'string',\n        variants: [\n          { begin: /b?r(#*)\"(.|\\n)*?\"\\1(?!#)/ },\n          {\n            begin: /b?'/,\n            end: /'/,\n            contains: [\n              {\n                scope: \"char.escape\",\n                match: /\\\\('|\\w|x\\w{2}|u\\w{4}|U\\w{8})/\n              }\n            ]\n          }\n        ]\n      },\n      {\n        className: 'number',\n        variants: [\n          { begin: '\\\\b0b([01_]+)' + NUMBER_SUFFIX },\n          { begin: '\\\\b0o([0-7_]+)' + NUMBER_SUFFIX },\n          { begin: '\\\\b0x([A-Fa-f0-9_]+)' + NUMBER_SUFFIX },\n          { begin: '\\\\b(\\\\d[\\\\d_]*(\\\\.[0-9_]+)?([eE][+-]?[0-9_]+)?)'\n                   + NUMBER_SUFFIX }\n        ],\n        relevance: 0\n      },\n      {\n        begin: [\n          /fn/,\n          /\\s+/,\n          UNDERSCORE_IDENT_RE\n        ],\n        className: {\n          1: \"keyword\",\n          3: \"title.function\"\n        }\n      },\n      {\n        className: 'meta',\n        begin: '#!?\\\\[',\n        end: '\\\\]',\n        contains: [\n          {\n            className: 'string',\n            begin: /\"/,\n            end: /\"/,\n            contains: [\n              hljs.BACKSLASH_ESCAPE\n            ]\n          }\n        ]\n      },\n      {\n        begin: [\n          /let/,\n          /\\s+/,\n          /(?:mut\\s+)?/,\n          UNDERSCORE_IDENT_RE\n        ],\n        className: {\n          1: \"keyword\",\n          3: \"keyword\",\n          4: \"variable\"\n        }\n      },\n      // must come before impl/for rule later\n      {\n        begin: [\n          /for/,\n          /\\s+/,\n          UNDERSCORE_IDENT_RE,\n          /\\s+/,\n          /in/\n        ],\n        className: {\n          1: \"keyword\",\n          3: \"variable\",\n          5: \"keyword\"\n        }\n      },\n      {\n        begin: [\n          /type/,\n          /\\s+/,\n          UNDERSCORE_IDENT_RE\n        ],\n        className: {\n          1: \"keyword\",\n          3: \"title.class\"\n        }\n      },\n      {\n        begin: [\n          /(?:trait|enum|struct|union|impl|for)/,\n          /\\s+/,\n          UNDERSCORE_IDENT_RE\n        ],\n        className: {\n          1: \"keyword\",\n          3: \"title.class\"\n        }\n      },\n      {\n        begin: hljs.IDENT_RE + '::',\n        keywords: {\n          keyword: \"Self\",\n          built_in: BUILTINS,\n          type: TYPES\n        }\n      },\n      {\n        className: \"punctuation\",\n        begin: '->'\n      },\n      FUNCTION_INVOKE\n    ]\n  };\n}\n\nexport { rust as default };\n"], "names": [], "mappings": "AAAA;;;;;;AAMA,GAEA,qBAAqB;;;AAErB,SAAS,KAAK,IAAI;IAChB,MAAM,QAAQ,KAAK,KAAK;IACxB,+CAA+C;IAC/C,sEAAsE;IACtE,MAAM,iBAAiB;IACvB,MAAM,sBAAsB,MAAM,MAAM,CAAC,gBAAgB,KAAK,mBAAmB;IACjF,MAAM,WAAW,MAAM,MAAM,CAAC,gBAAgB,KAAK,QAAQ;IAC3D,+CAA+C;IAC/C,MAAM,kBAAkB;QACtB,WAAW;QACX,WAAW;QACX,OAAO,MAAM,MAAM,CACjB,MACA,qCACA,UACA,MAAM,SAAS,CAAC;IACpB;IACA,MAAM,gBAAgB;IACtB,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,WAAW;QACf,YAAY;QACZ;QACA,SAAS;QACT;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,SAAS;QACT;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,OAAO;QACL,MAAM;QACN,SAAS;YAAE;SAAM;QACjB,UAAU;YACR,UAAU,KAAK,QAAQ,GAAG;YAC1B,MAAM;YACN,SAAS;YACT,SAAS;YACT,UAAU;QACZ;QACA,SAAS;QACT,UAAU;YACR,KAAK,mBAAmB;YACxB,KAAK,OAAO,CAAC,QAAQ,QAAQ;gBAAE,UAAU;oBAAE;iBAAQ;YAAC;YACpD,KAAK,OAAO,CAAC,KAAK,iBAAiB,EAAE;gBACnC,OAAO;gBACP,SAAS;YACX;YACA;gBACE,WAAW;gBACX,2CAA2C;gBAC3C,OAAO;YACT;YACA;gBACE,OAAO;gBACP,UAAU;oBACR;wBAAE,OAAO;oBAA2B;oBACpC;wBACE,OAAO;wBACP,KAAK;wBACL,UAAU;4BACR;gCACE,OAAO;gCACP,OAAO;4BACT;yBACD;oBACH;iBACD;YACH;YACA;gBACE,WAAW;gBACX,UAAU;oBACR;wBAAE,OAAO,kBAAkB;oBAAc;oBACzC;wBAAE,OAAO,mBAAmB;oBAAc;oBAC1C;wBAAE,OAAO,yBAAyB;oBAAc;oBAChD;wBAAE,OAAO,oDACE;oBAAc;iBAC1B;gBACD,WAAW;YACb;YACA;gBACE,OAAO;oBACL;oBACA;oBACA;iBACD;gBACD,WAAW;oBACT,GAAG;oBACH,GAAG;gBACL;YACF;YACA;gBACE,WAAW;gBACX,OAAO;gBACP,KAAK;gBACL,UAAU;oBACR;wBACE,WAAW;wBACX,OAAO;wBACP,KAAK;wBACL,UAAU;4BACR,KAAK,gBAAgB;yBACtB;oBACH;iBACD;YACH;YACA;gBACE,OAAO;oBACL;oBACA;oBACA;oBACA;iBACD;gBACD,WAAW;oBACT,GAAG;oBACH,GAAG;oBACH,GAAG;gBACL;YACF;YACA,uCAAuC;YACvC;gBACE,OAAO;oBACL;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,WAAW;oBACT,GAAG;oBACH,GAAG;oBACH,GAAG;gBACL;YACF;YACA;gBACE,OAAO;oBACL;oBACA;oBACA;iBACD;gBACD,WAAW;oBACT,GAAG;oBACH,GAAG;gBACL;YACF;YACA;gBACE,OAAO;oBACL;oBACA;oBACA;iBACD;gBACD,WAAW;oBACT,GAAG;oBACH,GAAG;gBACL;YACF;YACA;gBACE,OAAO,KAAK,QAAQ,GAAG;gBACvB,UAAU;oBACR,SAAS;oBACT,UAAU;oBACV,MAAM;gBACR;YACF;YACA;gBACE,WAAW;gBACX,OAAO;YACT;YACA;SACD;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 10037, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/scss.js"], "sourcesContent": ["const MODES = (hljs) => {\n  return {\n    IMPORTANT: {\n      scope: 'meta',\n      begin: '!important'\n    },\n    BLOCK_COMMENT: hljs.C_BLOCK_COMMENT_MODE,\n    HEXCOLOR: {\n      scope: 'number',\n      begin: /#(([0-9a-fA-F]{3,4})|(([0-9a-fA-F]{2}){3,4}))\\b/\n    },\n    FUNCTION_DISPATCH: {\n      className: \"built_in\",\n      begin: /[\\w-]+(?=\\()/\n    },\n    ATTRIBUTE_SELECTOR_MODE: {\n      scope: 'selector-attr',\n      begin: /\\[/,\n      end: /\\]/,\n      illegal: '$',\n      contains: [\n        hljs.APOS_STRING_MODE,\n        hljs.QUOTE_STRING_MODE\n      ]\n    },\n    CSS_NUMBER_MODE: {\n      scope: 'number',\n      begin: hljs.NUMBER_RE + '(' +\n        '%|em|ex|ch|rem' +\n        '|vw|vh|vmin|vmax' +\n        '|cm|mm|in|pt|pc|px' +\n        '|deg|grad|rad|turn' +\n        '|s|ms' +\n        '|Hz|kHz' +\n        '|dpi|dpcm|dppx' +\n        ')?',\n      relevance: 0\n    },\n    CSS_VARIABLE: {\n      className: \"attr\",\n      begin: /--[A-Za-z_][A-Za-z0-9_-]*/\n    }\n  };\n};\n\nconst HTML_TAGS = [\n  'a',\n  'abbr',\n  'address',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'blockquote',\n  'body',\n  'button',\n  'canvas',\n  'caption',\n  'cite',\n  'code',\n  'dd',\n  'del',\n  'details',\n  'dfn',\n  'div',\n  'dl',\n  'dt',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'header',\n  'hgroup',\n  'html',\n  'i',\n  'iframe',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'mark',\n  'menu',\n  'nav',\n  'object',\n  'ol',\n  'optgroup',\n  'option',\n  'p',\n  'picture',\n  'q',\n  'quote',\n  'samp',\n  'section',\n  'select',\n  'source',\n  'span',\n  'strong',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'ul',\n  'var',\n  'video'\n];\n\nconst SVG_TAGS = [\n  'defs',\n  'g',\n  'marker',\n  'mask',\n  'pattern',\n  'svg',\n  'switch',\n  'symbol',\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feFlood',\n  'feGaussianBlur',\n  'feImage',\n  'feMerge',\n  'feMorphology',\n  'feOffset',\n  'feSpecularLighting',\n  'feTile',\n  'feTurbulence',\n  'linearGradient',\n  'radialGradient',\n  'stop',\n  'circle',\n  'ellipse',\n  'image',\n  'line',\n  'path',\n  'polygon',\n  'polyline',\n  'rect',\n  'text',\n  'use',\n  'textPath',\n  'tspan',\n  'foreignObject',\n  'clipPath'\n];\n\nconst TAGS = [\n  ...HTML_TAGS,\n  ...SVG_TAGS,\n];\n\n// Sorting, then reversing makes sure longer attributes/elements like\n// `font-weight` are matched fully instead of getting false positives on say `font`\n\nconst MEDIA_FEATURES = [\n  'any-hover',\n  'any-pointer',\n  'aspect-ratio',\n  'color',\n  'color-gamut',\n  'color-index',\n  'device-aspect-ratio',\n  'device-height',\n  'device-width',\n  'display-mode',\n  'forced-colors',\n  'grid',\n  'height',\n  'hover',\n  'inverted-colors',\n  'monochrome',\n  'orientation',\n  'overflow-block',\n  'overflow-inline',\n  'pointer',\n  'prefers-color-scheme',\n  'prefers-contrast',\n  'prefers-reduced-motion',\n  'prefers-reduced-transparency',\n  'resolution',\n  'scan',\n  'scripting',\n  'update',\n  'width',\n  // TODO: find a better solution?\n  'min-width',\n  'max-width',\n  'min-height',\n  'max-height'\n].sort().reverse();\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-classes\nconst PSEUDO_CLASSES = [\n  'active',\n  'any-link',\n  'blank',\n  'checked',\n  'current',\n  'default',\n  'defined',\n  'dir', // dir()\n  'disabled',\n  'drop',\n  'empty',\n  'enabled',\n  'first',\n  'first-child',\n  'first-of-type',\n  'fullscreen',\n  'future',\n  'focus',\n  'focus-visible',\n  'focus-within',\n  'has', // has()\n  'host', // host or host()\n  'host-context', // host-context()\n  'hover',\n  'indeterminate',\n  'in-range',\n  'invalid',\n  'is', // is()\n  'lang', // lang()\n  'last-child',\n  'last-of-type',\n  'left',\n  'link',\n  'local-link',\n  'not', // not()\n  'nth-child', // nth-child()\n  'nth-col', // nth-col()\n  'nth-last-child', // nth-last-child()\n  'nth-last-col', // nth-last-col()\n  'nth-last-of-type', //nth-last-of-type()\n  'nth-of-type', //nth-of-type()\n  'only-child',\n  'only-of-type',\n  'optional',\n  'out-of-range',\n  'past',\n  'placeholder-shown',\n  'read-only',\n  'read-write',\n  'required',\n  'right',\n  'root',\n  'scope',\n  'target',\n  'target-within',\n  'user-invalid',\n  'valid',\n  'visited',\n  'where' // where()\n].sort().reverse();\n\n// https://developer.mozilla.org/en-US/docs/Web/CSS/Pseudo-elements\nconst PSEUDO_ELEMENTS = [\n  'after',\n  'backdrop',\n  'before',\n  'cue',\n  'cue-region',\n  'first-letter',\n  'first-line',\n  'grammar-error',\n  'marker',\n  'part',\n  'placeholder',\n  'selection',\n  'slotted',\n  'spelling-error'\n].sort().reverse();\n\nconst ATTRIBUTES = [\n  'accent-color',\n  'align-content',\n  'align-items',\n  'align-self',\n  'alignment-baseline',\n  'all',\n  'anchor-name',\n  'animation',\n  'animation-composition',\n  'animation-delay',\n  'animation-direction',\n  'animation-duration',\n  'animation-fill-mode',\n  'animation-iteration-count',\n  'animation-name',\n  'animation-play-state',\n  'animation-range',\n  'animation-range-end',\n  'animation-range-start',\n  'animation-timeline',\n  'animation-timing-function',\n  'appearance',\n  'aspect-ratio',\n  'backdrop-filter',\n  'backface-visibility',\n  'background',\n  'background-attachment',\n  'background-blend-mode',\n  'background-clip',\n  'background-color',\n  'background-image',\n  'background-origin',\n  'background-position',\n  'background-position-x',\n  'background-position-y',\n  'background-repeat',\n  'background-size',\n  'baseline-shift',\n  'block-size',\n  'border',\n  'border-block',\n  'border-block-color',\n  'border-block-end',\n  'border-block-end-color',\n  'border-block-end-style',\n  'border-block-end-width',\n  'border-block-start',\n  'border-block-start-color',\n  'border-block-start-style',\n  'border-block-start-width',\n  'border-block-style',\n  'border-block-width',\n  'border-bottom',\n  'border-bottom-color',\n  'border-bottom-left-radius',\n  'border-bottom-right-radius',\n  'border-bottom-style',\n  'border-bottom-width',\n  'border-collapse',\n  'border-color',\n  'border-end-end-radius',\n  'border-end-start-radius',\n  'border-image',\n  'border-image-outset',\n  'border-image-repeat',\n  'border-image-slice',\n  'border-image-source',\n  'border-image-width',\n  'border-inline',\n  'border-inline-color',\n  'border-inline-end',\n  'border-inline-end-color',\n  'border-inline-end-style',\n  'border-inline-end-width',\n  'border-inline-start',\n  'border-inline-start-color',\n  'border-inline-start-style',\n  'border-inline-start-width',\n  'border-inline-style',\n  'border-inline-width',\n  'border-left',\n  'border-left-color',\n  'border-left-style',\n  'border-left-width',\n  'border-radius',\n  'border-right',\n  'border-right-color',\n  'border-right-style',\n  'border-right-width',\n  'border-spacing',\n  'border-start-end-radius',\n  'border-start-start-radius',\n  'border-style',\n  'border-top',\n  'border-top-color',\n  'border-top-left-radius',\n  'border-top-right-radius',\n  'border-top-style',\n  'border-top-width',\n  'border-width',\n  'bottom',\n  'box-align',\n  'box-decoration-break',\n  'box-direction',\n  'box-flex',\n  'box-flex-group',\n  'box-lines',\n  'box-ordinal-group',\n  'box-orient',\n  'box-pack',\n  'box-shadow',\n  'box-sizing',\n  'break-after',\n  'break-before',\n  'break-inside',\n  'caption-side',\n  'caret-color',\n  'clear',\n  'clip',\n  'clip-path',\n  'clip-rule',\n  'color',\n  'color-interpolation',\n  'color-interpolation-filters',\n  'color-profile',\n  'color-rendering',\n  'color-scheme',\n  'column-count',\n  'column-fill',\n  'column-gap',\n  'column-rule',\n  'column-rule-color',\n  'column-rule-style',\n  'column-rule-width',\n  'column-span',\n  'column-width',\n  'columns',\n  'contain',\n  'contain-intrinsic-block-size',\n  'contain-intrinsic-height',\n  'contain-intrinsic-inline-size',\n  'contain-intrinsic-size',\n  'contain-intrinsic-width',\n  'container',\n  'container-name',\n  'container-type',\n  'content',\n  'content-visibility',\n  'counter-increment',\n  'counter-reset',\n  'counter-set',\n  'cue',\n  'cue-after',\n  'cue-before',\n  'cursor',\n  'cx',\n  'cy',\n  'direction',\n  'display',\n  'dominant-baseline',\n  'empty-cells',\n  'enable-background',\n  'field-sizing',\n  'fill',\n  'fill-opacity',\n  'fill-rule',\n  'filter',\n  'flex',\n  'flex-basis',\n  'flex-direction',\n  'flex-flow',\n  'flex-grow',\n  'flex-shrink',\n  'flex-wrap',\n  'float',\n  'flood-color',\n  'flood-opacity',\n  'flow',\n  'font',\n  'font-display',\n  'font-family',\n  'font-feature-settings',\n  'font-kerning',\n  'font-language-override',\n  'font-optical-sizing',\n  'font-palette',\n  'font-size',\n  'font-size-adjust',\n  'font-smooth',\n  'font-smoothing',\n  'font-stretch',\n  'font-style',\n  'font-synthesis',\n  'font-synthesis-position',\n  'font-synthesis-small-caps',\n  'font-synthesis-style',\n  'font-synthesis-weight',\n  'font-variant',\n  'font-variant-alternates',\n  'font-variant-caps',\n  'font-variant-east-asian',\n  'font-variant-emoji',\n  'font-variant-ligatures',\n  'font-variant-numeric',\n  'font-variant-position',\n  'font-variation-settings',\n  'font-weight',\n  'forced-color-adjust',\n  'gap',\n  'glyph-orientation-horizontal',\n  'glyph-orientation-vertical',\n  'grid',\n  'grid-area',\n  'grid-auto-columns',\n  'grid-auto-flow',\n  'grid-auto-rows',\n  'grid-column',\n  'grid-column-end',\n  'grid-column-start',\n  'grid-gap',\n  'grid-row',\n  'grid-row-end',\n  'grid-row-start',\n  'grid-template',\n  'grid-template-areas',\n  'grid-template-columns',\n  'grid-template-rows',\n  'hanging-punctuation',\n  'height',\n  'hyphenate-character',\n  'hyphenate-limit-chars',\n  'hyphens',\n  'icon',\n  'image-orientation',\n  'image-rendering',\n  'image-resolution',\n  'ime-mode',\n  'initial-letter',\n  'initial-letter-align',\n  'inline-size',\n  'inset',\n  'inset-area',\n  'inset-block',\n  'inset-block-end',\n  'inset-block-start',\n  'inset-inline',\n  'inset-inline-end',\n  'inset-inline-start',\n  'isolation',\n  'justify-content',\n  'justify-items',\n  'justify-self',\n  'kerning',\n  'left',\n  'letter-spacing',\n  'lighting-color',\n  'line-break',\n  'line-height',\n  'line-height-step',\n  'list-style',\n  'list-style-image',\n  'list-style-position',\n  'list-style-type',\n  'margin',\n  'margin-block',\n  'margin-block-end',\n  'margin-block-start',\n  'margin-bottom',\n  'margin-inline',\n  'margin-inline-end',\n  'margin-inline-start',\n  'margin-left',\n  'margin-right',\n  'margin-top',\n  'margin-trim',\n  'marker',\n  'marker-end',\n  'marker-mid',\n  'marker-start',\n  'marks',\n  'mask',\n  'mask-border',\n  'mask-border-mode',\n  'mask-border-outset',\n  'mask-border-repeat',\n  'mask-border-slice',\n  'mask-border-source',\n  'mask-border-width',\n  'mask-clip',\n  'mask-composite',\n  'mask-image',\n  'mask-mode',\n  'mask-origin',\n  'mask-position',\n  'mask-repeat',\n  'mask-size',\n  'mask-type',\n  'masonry-auto-flow',\n  'math-depth',\n  'math-shift',\n  'math-style',\n  'max-block-size',\n  'max-height',\n  'max-inline-size',\n  'max-width',\n  'min-block-size',\n  'min-height',\n  'min-inline-size',\n  'min-width',\n  'mix-blend-mode',\n  'nav-down',\n  'nav-index',\n  'nav-left',\n  'nav-right',\n  'nav-up',\n  'none',\n  'normal',\n  'object-fit',\n  'object-position',\n  'offset',\n  'offset-anchor',\n  'offset-distance',\n  'offset-path',\n  'offset-position',\n  'offset-rotate',\n  'opacity',\n  'order',\n  'orphans',\n  'outline',\n  'outline-color',\n  'outline-offset',\n  'outline-style',\n  'outline-width',\n  'overflow',\n  'overflow-anchor',\n  'overflow-block',\n  'overflow-clip-margin',\n  'overflow-inline',\n  'overflow-wrap',\n  'overflow-x',\n  'overflow-y',\n  'overlay',\n  'overscroll-behavior',\n  'overscroll-behavior-block',\n  'overscroll-behavior-inline',\n  'overscroll-behavior-x',\n  'overscroll-behavior-y',\n  'padding',\n  'padding-block',\n  'padding-block-end',\n  'padding-block-start',\n  'padding-bottom',\n  'padding-inline',\n  'padding-inline-end',\n  'padding-inline-start',\n  'padding-left',\n  'padding-right',\n  'padding-top',\n  'page',\n  'page-break-after',\n  'page-break-before',\n  'page-break-inside',\n  'paint-order',\n  'pause',\n  'pause-after',\n  'pause-before',\n  'perspective',\n  'perspective-origin',\n  'place-content',\n  'place-items',\n  'place-self',\n  'pointer-events',\n  'position',\n  'position-anchor',\n  'position-visibility',\n  'print-color-adjust',\n  'quotes',\n  'r',\n  'resize',\n  'rest',\n  'rest-after',\n  'rest-before',\n  'right',\n  'rotate',\n  'row-gap',\n  'ruby-align',\n  'ruby-position',\n  'scale',\n  'scroll-behavior',\n  'scroll-margin',\n  'scroll-margin-block',\n  'scroll-margin-block-end',\n  'scroll-margin-block-start',\n  'scroll-margin-bottom',\n  'scroll-margin-inline',\n  'scroll-margin-inline-end',\n  'scroll-margin-inline-start',\n  'scroll-margin-left',\n  'scroll-margin-right',\n  'scroll-margin-top',\n  'scroll-padding',\n  'scroll-padding-block',\n  'scroll-padding-block-end',\n  'scroll-padding-block-start',\n  'scroll-padding-bottom',\n  'scroll-padding-inline',\n  'scroll-padding-inline-end',\n  'scroll-padding-inline-start',\n  'scroll-padding-left',\n  'scroll-padding-right',\n  'scroll-padding-top',\n  'scroll-snap-align',\n  'scroll-snap-stop',\n  'scroll-snap-type',\n  'scroll-timeline',\n  'scroll-timeline-axis',\n  'scroll-timeline-name',\n  'scrollbar-color',\n  'scrollbar-gutter',\n  'scrollbar-width',\n  'shape-image-threshold',\n  'shape-margin',\n  'shape-outside',\n  'shape-rendering',\n  'speak',\n  'speak-as',\n  'src', // @font-face\n  'stop-color',\n  'stop-opacity',\n  'stroke',\n  'stroke-dasharray',\n  'stroke-dashoffset',\n  'stroke-linecap',\n  'stroke-linejoin',\n  'stroke-miterlimit',\n  'stroke-opacity',\n  'stroke-width',\n  'tab-size',\n  'table-layout',\n  'text-align',\n  'text-align-all',\n  'text-align-last',\n  'text-anchor',\n  'text-combine-upright',\n  'text-decoration',\n  'text-decoration-color',\n  'text-decoration-line',\n  'text-decoration-skip',\n  'text-decoration-skip-ink',\n  'text-decoration-style',\n  'text-decoration-thickness',\n  'text-emphasis',\n  'text-emphasis-color',\n  'text-emphasis-position',\n  'text-emphasis-style',\n  'text-indent',\n  'text-justify',\n  'text-orientation',\n  'text-overflow',\n  'text-rendering',\n  'text-shadow',\n  'text-size-adjust',\n  'text-transform',\n  'text-underline-offset',\n  'text-underline-position',\n  'text-wrap',\n  'text-wrap-mode',\n  'text-wrap-style',\n  'timeline-scope',\n  'top',\n  'touch-action',\n  'transform',\n  'transform-box',\n  'transform-origin',\n  'transform-style',\n  'transition',\n  'transition-behavior',\n  'transition-delay',\n  'transition-duration',\n  'transition-property',\n  'transition-timing-function',\n  'translate',\n  'unicode-bidi',\n  'user-modify',\n  'user-select',\n  'vector-effect',\n  'vertical-align',\n  'view-timeline',\n  'view-timeline-axis',\n  'view-timeline-inset',\n  'view-timeline-name',\n  'view-transition-name',\n  'visibility',\n  'voice-balance',\n  'voice-duration',\n  'voice-family',\n  'voice-pitch',\n  'voice-range',\n  'voice-rate',\n  'voice-stress',\n  'voice-volume',\n  'white-space',\n  'white-space-collapse',\n  'widows',\n  'width',\n  'will-change',\n  'word-break',\n  'word-spacing',\n  'word-wrap',\n  'writing-mode',\n  'x',\n  'y',\n  'z-index',\n  'zoom'\n].sort().reverse();\n\n/*\nLanguage: SCSS\nDescription: Scss is an extension of the syntax of CSS.\nAuthor: Kurt Emch <<EMAIL>>\nWebsite: https://sass-lang.com\nCategory: common, css, web\n*/\n\n\n/** @type LanguageFn */\nfunction scss(hljs) {\n  const modes = MODES(hljs);\n  const PSEUDO_ELEMENTS$1 = PSEUDO_ELEMENTS;\n  const PSEUDO_CLASSES$1 = PSEUDO_CLASSES;\n\n  const AT_IDENTIFIER = '@[a-z-]+'; // @font-face\n  const AT_MODIFIERS = \"and or not only\";\n  const IDENT_RE = '[a-zA-Z-][a-zA-Z0-9_-]*';\n  const VARIABLE = {\n    className: 'variable',\n    begin: '(\\\\$' + IDENT_RE + ')\\\\b',\n    relevance: 0\n  };\n\n  return {\n    name: 'SCSS',\n    case_insensitive: true,\n    illegal: '[=/|\\']',\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      // to recognize keyframe 40% etc which are outside the scope of our\n      // attribute value mode\n      modes.CSS_NUMBER_MODE,\n      {\n        className: 'selector-id',\n        begin: '#[A-Za-z0-9_-]+',\n        relevance: 0\n      },\n      {\n        className: 'selector-class',\n        begin: '\\\\.[A-Za-z0-9_-]+',\n        relevance: 0\n      },\n      modes.ATTRIBUTE_SELECTOR_MODE,\n      {\n        className: 'selector-tag',\n        begin: '\\\\b(' + TAGS.join('|') + ')\\\\b',\n        // was there, before, but why?\n        relevance: 0\n      },\n      {\n        className: 'selector-pseudo',\n        begin: ':(' + PSEUDO_CLASSES$1.join('|') + ')'\n      },\n      {\n        className: 'selector-pseudo',\n        begin: ':(:)?(' + PSEUDO_ELEMENTS$1.join('|') + ')'\n      },\n      VARIABLE,\n      { // pseudo-selector params\n        begin: /\\(/,\n        end: /\\)/,\n        contains: [ modes.CSS_NUMBER_MODE ]\n      },\n      modes.CSS_VARIABLE,\n      {\n        className: 'attribute',\n        begin: '\\\\b(' + ATTRIBUTES.join('|') + ')\\\\b'\n      },\n      { begin: '\\\\b(whitespace|wait|w-resize|visible|vertical-text|vertical-ideographic|uppercase|upper-roman|upper-alpha|underline|transparent|top|thin|thick|text|text-top|text-bottom|tb-rl|table-header-group|table-footer-group|sw-resize|super|strict|static|square|solid|small-caps|separate|se-resize|scroll|s-resize|rtl|row-resize|ridge|right|repeat|repeat-y|repeat-x|relative|progress|pointer|overline|outside|outset|oblique|nowrap|not-allowed|normal|none|nw-resize|no-repeat|no-drop|newspaper|ne-resize|n-resize|move|middle|medium|ltr|lr-tb|lowercase|lower-roman|lower-alpha|loose|list-item|line|line-through|line-edge|lighter|left|keep-all|justify|italic|inter-word|inter-ideograph|inside|inset|inline|inline-block|inherit|inactive|ideograph-space|ideograph-parenthesis|ideograph-numeric|ideograph-alpha|horizontal|hidden|help|hand|groove|fixed|ellipsis|e-resize|double|dotted|distribute|distribute-space|distribute-letter|distribute-all-lines|disc|disabled|default|decimal|dashed|crosshair|collapse|col-resize|circle|char|center|capitalize|break-word|break-all|bottom|both|bolder|bold|block|bidi-override|below|baseline|auto|always|all-scroll|absolute|table|table-cell)\\\\b' },\n      {\n        begin: /:/,\n        end: /[;}{]/,\n        relevance: 0,\n        contains: [\n          modes.BLOCK_COMMENT,\n          VARIABLE,\n          modes.HEXCOLOR,\n          modes.CSS_NUMBER_MODE,\n          hljs.QUOTE_STRING_MODE,\n          hljs.APOS_STRING_MODE,\n          modes.IMPORTANT,\n          modes.FUNCTION_DISPATCH\n        ]\n      },\n      // matching these here allows us to treat them more like regular CSS\n      // rules so everything between the {} gets regular rule highlighting,\n      // which is what we want for page and font-face\n      {\n        begin: '@(page|font-face)',\n        keywords: {\n          $pattern: AT_IDENTIFIER,\n          keyword: '@page @font-face'\n        }\n      },\n      {\n        begin: '@',\n        end: '[{;]',\n        returnBegin: true,\n        keywords: {\n          $pattern: /[a-z-]+/,\n          keyword: AT_MODIFIERS,\n          attribute: MEDIA_FEATURES.join(\" \")\n        },\n        contains: [\n          {\n            begin: AT_IDENTIFIER,\n            className: \"keyword\"\n          },\n          {\n            begin: /[a-z-]+(?=:)/,\n            className: \"attribute\"\n          },\n          VARIABLE,\n          hljs.QUOTE_STRING_MODE,\n          hljs.APOS_STRING_MODE,\n          modes.HEXCOLOR,\n          modes.CSS_NUMBER_MODE\n        ]\n      },\n      modes.FUNCTION_DISPATCH\n    ]\n  };\n}\n\nexport { scss as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,QAAQ,CAAC;IACb,OAAO;QACL,WAAW;YACT,OAAO;YACP,OAAO;QACT;QACA,eAAe,KAAK,oBAAoB;QACxC,UAAU;YACR,OAAO;YACP,OAAO;QACT;QACA,mBAAmB;YACjB,WAAW;YACX,OAAO;QACT;QACA,yBAAyB;YACvB,OAAO;YACP,OAAO;YACP,KAAK;YACL,SAAS;YACT,UAAU;gBACR,KAAK,gBAAgB;gBACrB,KAAK,iBAAiB;aACvB;QACH;QACA,iBAAiB;YACf,OAAO;YACP,OAAO,KAAK,SAAS,GAAG,MACtB,mBACA,qBACA,uBACA,uBACA,UACA,YACA,mBACA;YACF,WAAW;QACb;QACA,cAAc;YACZ,WAAW;YACX,OAAO;QACT;IACF;AACF;AAEA,MAAM,YAAY;IAChB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,OAAO;OACR;OACA;CACJ;AAED,qEAAqE;AACrE,mFAAmF;AAEnF,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,gCAAgC;IAChC;IACA;IACA;IACA;CACD,CAAC,IAAI,GAAG,OAAO;AAEhB,kEAAkE;AAClE,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,QAAQ,UAAU;CACnB,CAAC,IAAI,GAAG,OAAO;AAEhB,mEAAmE;AACnE,MAAM,kBAAkB;IACtB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD,CAAC,IAAI,GAAG,OAAO;AAEhB,MAAM,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD,CAAC,IAAI,GAAG,OAAO;AAEhB;;;;;;AAMA,GAGA,qBAAqB,GACrB,SAAS,KAAK,IAAI;IAChB,MAAM,QAAQ,MAAM;IACpB,MAAM,oBAAoB;IAC1B,MAAM,mBAAmB;IAEzB,MAAM,gBAAgB,YAAY,aAAa;IAC/C,MAAM,eAAe;IACrB,MAAM,WAAW;IACjB,MAAM,WAAW;QACf,WAAW;QACX,OAAO,SAAS,WAAW;QAC3B,WAAW;IACb;IAEA,OAAO;QACL,MAAM;QACN,kBAAkB;QAClB,SAAS;QACT,UAAU;YACR,KAAK,mBAAmB;YACxB,KAAK,oBAAoB;YACzB,mEAAmE;YACnE,uBAAuB;YACvB,MAAM,eAAe;YACrB;gBACE,WAAW;gBACX,OAAO;gBACP,WAAW;YACb;YACA;gBACE,WAAW;gBACX,OAAO;gBACP,WAAW;YACb;YACA,MAAM,uBAAuB;YAC7B;gBACE,WAAW;gBACX,OAAO,SAAS,KAAK,IAAI,CAAC,OAAO;gBACjC,8BAA8B;gBAC9B,WAAW;YACb;YACA;gBACE,WAAW;gBACX,OAAO,OAAO,iBAAiB,IAAI,CAAC,OAAO;YAC7C;YACA;gBACE,WAAW;gBACX,OAAO,WAAW,kBAAkB,IAAI,CAAC,OAAO;YAClD;YACA;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,UAAU;oBAAE,MAAM,eAAe;iBAAE;YACrC;YACA,MAAM,YAAY;YAClB;gBACE,WAAW;gBACX,OAAO,SAAS,WAAW,IAAI,CAAC,OAAO;YACzC;YACA;gBAAE,OAAO;YAA6oC;YACtpC;gBACE,OAAO;gBACP,KAAK;gBACL,WAAW;gBACX,UAAU;oBACR,MAAM,aAAa;oBACnB;oBACA,MAAM,QAAQ;oBACd,MAAM,eAAe;oBACrB,KAAK,iBAAiB;oBACtB,KAAK,gBAAgB;oBACrB,MAAM,SAAS;oBACf,MAAM,iBAAiB;iBACxB;YACH;YACA,oEAAoE;YACpE,qEAAqE;YACrE,+CAA+C;YAC/C;gBACE,OAAO;gBACP,UAAU;oBACR,UAAU;oBACV,SAAS;gBACX;YACF;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,aAAa;gBACb,UAAU;oBACR,UAAU;oBACV,SAAS;oBACT,WAAW,eAAe,IAAI,CAAC;gBACjC;gBACA,UAAU;oBACR;wBACE,OAAO;wBACP,WAAW;oBACb;oBACA;wBACE,OAAO;wBACP,WAAW;oBACb;oBACA;oBACA,KAAK,iBAAiB;oBACtB,KAAK,gBAAgB;oBACrB,MAAM,QAAQ;oBACd,MAAM,eAAe;iBACtB;YACH;YACA,MAAM,iBAAiB;SACxB;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 10965, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/shell.js"], "sourcesContent": ["/*\nLanguage: Shell Session\nRequires: bash.js\nAuthor: T<PERSON><PERSON><PERSON><PERSON><PERSON>e <<EMAIL>>\nCategory: common\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction shell(hljs) {\n  return {\n    name: 'Shell Session',\n    aliases: [\n      'console',\n      'shellsession'\n    ],\n    contains: [\n      {\n        className: 'meta.prompt',\n        // We cannot add \\s (spaces) in the regular expression otherwise it will be too broad and produce unexpected result.\n        // For instance, in the following example, it would match \"echo /path/to/home >\" as a prompt:\n        // echo /path/to/home > t.exe\n        begin: /^\\s{0,3}[/~\\w\\d[\\]()@-]*[>%$#][ ]?/,\n        starts: {\n          end: /[^\\\\](?=\\s*$)/,\n          subLanguage: 'bash'\n        }\n      }\n    ]\n  };\n}\n\nexport { shell as default };\n"], "names": [], "mappings": "AAAA;;;;;;AAMA,GAEA,qBAAqB;;;AACrB,SAAS,MAAM,IAAI;IACjB,OAAO;QACL,MAAM;QACN,SAAS;YACP;YACA;SACD;QACD,UAAU;YACR;gBACE,WAAW;gBACX,oHAAoH;gBACpH,6FAA6F;gBAC7F,6BAA6B;gBAC7B,OAAO;gBACP,QAAQ;oBACN,KAAK;oBACL,aAAa;gBACf;YACF;SACD;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 11003, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/sql.js"], "sourcesContent": ["/*\n Language: SQL\n Website: https://en.wikipedia.org/wiki/SQL\n Category: common, database\n */\n\n/*\n\nGoals:\n\nSQL is intended to highlight basic/common SQL keywords and expressions\n\n- If pretty much every single SQL server includes supports, then it's a canidate.\n- It is NOT intended to include tons of vendor specific keywords (Oracle, MySQL,\n  PostgreSQL) although the list of data types is purposely a bit more expansive.\n- For more specific SQL grammars please see:\n  - PostgreSQL and PL/pgSQL - core\n  - T-SQL - https://github.com/highlightjs/highlightjs-tsql\n  - sql_more (core)\n\n */\n\nfunction sql(hljs) {\n  const regex = hljs.regex;\n  const COMMENT_MODE = hljs.COMMENT('--', '$');\n  const STRING = {\n    scope: 'string',\n    variants: [\n      {\n        begin: /'/,\n        end: /'/,\n        contains: [ { match: /''/ } ]\n      }\n    ]\n  };\n  const QUOTED_IDENTIFIER = {\n    begin: /\"/,\n    end: /\"/,\n    contains: [ { match: /\"\"/ } ]\n  };\n\n  const LITERALS = [\n    \"true\",\n    \"false\",\n    // Not sure it's correct to call NULL literal, and clauses like IS [NOT] NULL look strange that way.\n    // \"null\",\n    \"unknown\"\n  ];\n\n  const MULTI_WORD_TYPES = [\n    \"double precision\",\n    \"large object\",\n    \"with timezone\",\n    \"without timezone\"\n  ];\n\n  const TYPES = [\n    'bigint',\n    'binary',\n    'blob',\n    'boolean',\n    'char',\n    'character',\n    'clob',\n    'date',\n    'dec',\n    'decfloat',\n    'decimal',\n    'float',\n    'int',\n    'integer',\n    'interval',\n    'nchar',\n    'nclob',\n    'national',\n    'numeric',\n    'real',\n    'row',\n    'smallint',\n    'time',\n    'timestamp',\n    'varchar',\n    'varying', // modifier (character varying)\n    'varbinary'\n  ];\n\n  const NON_RESERVED_WORDS = [\n    \"add\",\n    \"asc\",\n    \"collation\",\n    \"desc\",\n    \"final\",\n    \"first\",\n    \"last\",\n    \"view\"\n  ];\n\n  // https://jakewheat.github.io/sql-overview/sql-2016-foundation-grammar.html#reserved-word\n  const RESERVED_WORDS = [\n    \"abs\",\n    \"acos\",\n    \"all\",\n    \"allocate\",\n    \"alter\",\n    \"and\",\n    \"any\",\n    \"are\",\n    \"array\",\n    \"array_agg\",\n    \"array_max_cardinality\",\n    \"as\",\n    \"asensitive\",\n    \"asin\",\n    \"asymmetric\",\n    \"at\",\n    \"atan\",\n    \"atomic\",\n    \"authorization\",\n    \"avg\",\n    \"begin\",\n    \"begin_frame\",\n    \"begin_partition\",\n    \"between\",\n    \"bigint\",\n    \"binary\",\n    \"blob\",\n    \"boolean\",\n    \"both\",\n    \"by\",\n    \"call\",\n    \"called\",\n    \"cardinality\",\n    \"cascaded\",\n    \"case\",\n    \"cast\",\n    \"ceil\",\n    \"ceiling\",\n    \"char\",\n    \"char_length\",\n    \"character\",\n    \"character_length\",\n    \"check\",\n    \"classifier\",\n    \"clob\",\n    \"close\",\n    \"coalesce\",\n    \"collate\",\n    \"collect\",\n    \"column\",\n    \"commit\",\n    \"condition\",\n    \"connect\",\n    \"constraint\",\n    \"contains\",\n    \"convert\",\n    \"copy\",\n    \"corr\",\n    \"corresponding\",\n    \"cos\",\n    \"cosh\",\n    \"count\",\n    \"covar_pop\",\n    \"covar_samp\",\n    \"create\",\n    \"cross\",\n    \"cube\",\n    \"cume_dist\",\n    \"current\",\n    \"current_catalog\",\n    \"current_date\",\n    \"current_default_transform_group\",\n    \"current_path\",\n    \"current_role\",\n    \"current_row\",\n    \"current_schema\",\n    \"current_time\",\n    \"current_timestamp\",\n    \"current_path\",\n    \"current_role\",\n    \"current_transform_group_for_type\",\n    \"current_user\",\n    \"cursor\",\n    \"cycle\",\n    \"date\",\n    \"day\",\n    \"deallocate\",\n    \"dec\",\n    \"decimal\",\n    \"decfloat\",\n    \"declare\",\n    \"default\",\n    \"define\",\n    \"delete\",\n    \"dense_rank\",\n    \"deref\",\n    \"describe\",\n    \"deterministic\",\n    \"disconnect\",\n    \"distinct\",\n    \"double\",\n    \"drop\",\n    \"dynamic\",\n    \"each\",\n    \"element\",\n    \"else\",\n    \"empty\",\n    \"end\",\n    \"end_frame\",\n    \"end_partition\",\n    \"end-exec\",\n    \"equals\",\n    \"escape\",\n    \"every\",\n    \"except\",\n    \"exec\",\n    \"execute\",\n    \"exists\",\n    \"exp\",\n    \"external\",\n    \"extract\",\n    \"false\",\n    \"fetch\",\n    \"filter\",\n    \"first_value\",\n    \"float\",\n    \"floor\",\n    \"for\",\n    \"foreign\",\n    \"frame_row\",\n    \"free\",\n    \"from\",\n    \"full\",\n    \"function\",\n    \"fusion\",\n    \"get\",\n    \"global\",\n    \"grant\",\n    \"group\",\n    \"grouping\",\n    \"groups\",\n    \"having\",\n    \"hold\",\n    \"hour\",\n    \"identity\",\n    \"in\",\n    \"indicator\",\n    \"initial\",\n    \"inner\",\n    \"inout\",\n    \"insensitive\",\n    \"insert\",\n    \"int\",\n    \"integer\",\n    \"intersect\",\n    \"intersection\",\n    \"interval\",\n    \"into\",\n    \"is\",\n    \"join\",\n    \"json_array\",\n    \"json_arrayagg\",\n    \"json_exists\",\n    \"json_object\",\n    \"json_objectagg\",\n    \"json_query\",\n    \"json_table\",\n    \"json_table_primitive\",\n    \"json_value\",\n    \"lag\",\n    \"language\",\n    \"large\",\n    \"last_value\",\n    \"lateral\",\n    \"lead\",\n    \"leading\",\n    \"left\",\n    \"like\",\n    \"like_regex\",\n    \"listagg\",\n    \"ln\",\n    \"local\",\n    \"localtime\",\n    \"localtimestamp\",\n    \"log\",\n    \"log10\",\n    \"lower\",\n    \"match\",\n    \"match_number\",\n    \"match_recognize\",\n    \"matches\",\n    \"max\",\n    \"member\",\n    \"merge\",\n    \"method\",\n    \"min\",\n    \"minute\",\n    \"mod\",\n    \"modifies\",\n    \"module\",\n    \"month\",\n    \"multiset\",\n    \"national\",\n    \"natural\",\n    \"nchar\",\n    \"nclob\",\n    \"new\",\n    \"no\",\n    \"none\",\n    \"normalize\",\n    \"not\",\n    \"nth_value\",\n    \"ntile\",\n    \"null\",\n    \"nullif\",\n    \"numeric\",\n    \"octet_length\",\n    \"occurrences_regex\",\n    \"of\",\n    \"offset\",\n    \"old\",\n    \"omit\",\n    \"on\",\n    \"one\",\n    \"only\",\n    \"open\",\n    \"or\",\n    \"order\",\n    \"out\",\n    \"outer\",\n    \"over\",\n    \"overlaps\",\n    \"overlay\",\n    \"parameter\",\n    \"partition\",\n    \"pattern\",\n    \"per\",\n    \"percent\",\n    \"percent_rank\",\n    \"percentile_cont\",\n    \"percentile_disc\",\n    \"period\",\n    \"portion\",\n    \"position\",\n    \"position_regex\",\n    \"power\",\n    \"precedes\",\n    \"precision\",\n    \"prepare\",\n    \"primary\",\n    \"procedure\",\n    \"ptf\",\n    \"range\",\n    \"rank\",\n    \"reads\",\n    \"real\",\n    \"recursive\",\n    \"ref\",\n    \"references\",\n    \"referencing\",\n    \"regr_avgx\",\n    \"regr_avgy\",\n    \"regr_count\",\n    \"regr_intercept\",\n    \"regr_r2\",\n    \"regr_slope\",\n    \"regr_sxx\",\n    \"regr_sxy\",\n    \"regr_syy\",\n    \"release\",\n    \"result\",\n    \"return\",\n    \"returns\",\n    \"revoke\",\n    \"right\",\n    \"rollback\",\n    \"rollup\",\n    \"row\",\n    \"row_number\",\n    \"rows\",\n    \"running\",\n    \"savepoint\",\n    \"scope\",\n    \"scroll\",\n    \"search\",\n    \"second\",\n    \"seek\",\n    \"select\",\n    \"sensitive\",\n    \"session_user\",\n    \"set\",\n    \"show\",\n    \"similar\",\n    \"sin\",\n    \"sinh\",\n    \"skip\",\n    \"smallint\",\n    \"some\",\n    \"specific\",\n    \"specifictype\",\n    \"sql\",\n    \"sqlexception\",\n    \"sqlstate\",\n    \"sqlwarning\",\n    \"sqrt\",\n    \"start\",\n    \"static\",\n    \"stddev_pop\",\n    \"stddev_samp\",\n    \"submultiset\",\n    \"subset\",\n    \"substring\",\n    \"substring_regex\",\n    \"succeeds\",\n    \"sum\",\n    \"symmetric\",\n    \"system\",\n    \"system_time\",\n    \"system_user\",\n    \"table\",\n    \"tablesample\",\n    \"tan\",\n    \"tanh\",\n    \"then\",\n    \"time\",\n    \"timestamp\",\n    \"timezone_hour\",\n    \"timezone_minute\",\n    \"to\",\n    \"trailing\",\n    \"translate\",\n    \"translate_regex\",\n    \"translation\",\n    \"treat\",\n    \"trigger\",\n    \"trim\",\n    \"trim_array\",\n    \"true\",\n    \"truncate\",\n    \"uescape\",\n    \"union\",\n    \"unique\",\n    \"unknown\",\n    \"unnest\",\n    \"update\",\n    \"upper\",\n    \"user\",\n    \"using\",\n    \"value\",\n    \"values\",\n    \"value_of\",\n    \"var_pop\",\n    \"var_samp\",\n    \"varbinary\",\n    \"varchar\",\n    \"varying\",\n    \"versioning\",\n    \"when\",\n    \"whenever\",\n    \"where\",\n    \"width_bucket\",\n    \"window\",\n    \"with\",\n    \"within\",\n    \"without\",\n    \"year\",\n  ];\n\n  // these are reserved words we have identified to be functions\n  // and should only be highlighted in a dispatch-like context\n  // ie, array_agg(...), etc.\n  const RESERVED_FUNCTIONS = [\n    \"abs\",\n    \"acos\",\n    \"array_agg\",\n    \"asin\",\n    \"atan\",\n    \"avg\",\n    \"cast\",\n    \"ceil\",\n    \"ceiling\",\n    \"coalesce\",\n    \"corr\",\n    \"cos\",\n    \"cosh\",\n    \"count\",\n    \"covar_pop\",\n    \"covar_samp\",\n    \"cume_dist\",\n    \"dense_rank\",\n    \"deref\",\n    \"element\",\n    \"exp\",\n    \"extract\",\n    \"first_value\",\n    \"floor\",\n    \"json_array\",\n    \"json_arrayagg\",\n    \"json_exists\",\n    \"json_object\",\n    \"json_objectagg\",\n    \"json_query\",\n    \"json_table\",\n    \"json_table_primitive\",\n    \"json_value\",\n    \"lag\",\n    \"last_value\",\n    \"lead\",\n    \"listagg\",\n    \"ln\",\n    \"log\",\n    \"log10\",\n    \"lower\",\n    \"max\",\n    \"min\",\n    \"mod\",\n    \"nth_value\",\n    \"ntile\",\n    \"nullif\",\n    \"percent_rank\",\n    \"percentile_cont\",\n    \"percentile_disc\",\n    \"position\",\n    \"position_regex\",\n    \"power\",\n    \"rank\",\n    \"regr_avgx\",\n    \"regr_avgy\",\n    \"regr_count\",\n    \"regr_intercept\",\n    \"regr_r2\",\n    \"regr_slope\",\n    \"regr_sxx\",\n    \"regr_sxy\",\n    \"regr_syy\",\n    \"row_number\",\n    \"sin\",\n    \"sinh\",\n    \"sqrt\",\n    \"stddev_pop\",\n    \"stddev_samp\",\n    \"substring\",\n    \"substring_regex\",\n    \"sum\",\n    \"tan\",\n    \"tanh\",\n    \"translate\",\n    \"translate_regex\",\n    \"treat\",\n    \"trim\",\n    \"trim_array\",\n    \"unnest\",\n    \"upper\",\n    \"value_of\",\n    \"var_pop\",\n    \"var_samp\",\n    \"width_bucket\",\n  ];\n\n  // these functions can\n  const POSSIBLE_WITHOUT_PARENS = [\n    \"current_catalog\",\n    \"current_date\",\n    \"current_default_transform_group\",\n    \"current_path\",\n    \"current_role\",\n    \"current_schema\",\n    \"current_transform_group_for_type\",\n    \"current_user\",\n    \"session_user\",\n    \"system_time\",\n    \"system_user\",\n    \"current_time\",\n    \"localtime\",\n    \"current_timestamp\",\n    \"localtimestamp\"\n  ];\n\n  // those exist to boost relevance making these very\n  // \"SQL like\" keyword combos worth +1 extra relevance\n  const COMBOS = [\n    \"create table\",\n    \"insert into\",\n    \"primary key\",\n    \"foreign key\",\n    \"not null\",\n    \"alter table\",\n    \"add constraint\",\n    \"grouping sets\",\n    \"on overflow\",\n    \"character set\",\n    \"respect nulls\",\n    \"ignore nulls\",\n    \"nulls first\",\n    \"nulls last\",\n    \"depth first\",\n    \"breadth first\"\n  ];\n\n  const FUNCTIONS = RESERVED_FUNCTIONS;\n\n  const KEYWORDS = [\n    ...RESERVED_WORDS,\n    ...NON_RESERVED_WORDS\n  ].filter((keyword) => {\n    return !RESERVED_FUNCTIONS.includes(keyword);\n  });\n\n  const VARIABLE = {\n    scope: \"variable\",\n    match: /@[a-z0-9][a-z0-9_]*/,\n  };\n\n  const OPERATOR = {\n    scope: \"operator\",\n    match: /[-+*/=%^~]|&&?|\\|\\|?|!=?|<(?:=>?|<|>)?|>[>=]?/,\n    relevance: 0,\n  };\n\n  const FUNCTION_CALL = {\n    match: regex.concat(/\\b/, regex.either(...FUNCTIONS), /\\s*\\(/),\n    relevance: 0,\n    keywords: { built_in: FUNCTIONS }\n  };\n\n  // turns a multi-word keyword combo into a regex that doesn't\n  // care about extra whitespace etc.\n  // input: \"START QUERY\"\n  // output: /\\bSTART\\s+QUERY\\b/\n  function kws_to_regex(list) {\n    return regex.concat(\n      /\\b/,\n      regex.either(...list.map((kw) => {\n        return kw.replace(/\\s+/, \"\\\\s+\")\n      })),\n      /\\b/\n    )\n  }\n\n  const MULTI_WORD_KEYWORDS = {\n    scope: \"keyword\",\n    match: kws_to_regex(COMBOS),\n    relevance: 0,\n  };\n\n  // keywords with less than 3 letters are reduced in relevancy\n  function reduceRelevancy(list, {\n    exceptions, when\n  } = {}) {\n    const qualifyFn = when;\n    exceptions = exceptions || [];\n    return list.map((item) => {\n      if (item.match(/\\|\\d+$/) || exceptions.includes(item)) {\n        return item;\n      } else if (qualifyFn(item)) {\n        return `${item}|0`;\n      } else {\n        return item;\n      }\n    });\n  }\n\n  return {\n    name: 'SQL',\n    case_insensitive: true,\n    // does not include {} or HTML tags `</`\n    illegal: /[{}]|<\\//,\n    keywords: {\n      $pattern: /\\b[\\w\\.]+/,\n      keyword:\n        reduceRelevancy(KEYWORDS, { when: (x) => x.length < 3 }),\n      literal: LITERALS,\n      type: TYPES,\n      built_in: POSSIBLE_WITHOUT_PARENS\n    },\n    contains: [\n      {\n        scope: \"type\",\n        match: kws_to_regex(MULTI_WORD_TYPES)\n      },\n      MULTI_WORD_KEYWORDS,\n      FUNCTION_CALL,\n      VARIABLE,\n      STRING,\n      QUOTED_IDENTIFIER,\n      hljs.C_NUMBER_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      COMMENT_MODE,\n      OPERATOR\n    ]\n  };\n}\n\nexport { sql as default };\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED;;;;;;;;;;;;;;CAcC;;;AAED,SAAS,IAAI,IAAI;IACf,MAAM,QAAQ,KAAK,KAAK;IACxB,MAAM,eAAe,KAAK,OAAO,CAAC,MAAM;IACxC,MAAM,SAAS;QACb,OAAO;QACP,UAAU;YACR;gBACE,OAAO;gBACP,KAAK;gBACL,UAAU;oBAAE;wBAAE,OAAO;oBAAK;iBAAG;YAC/B;SACD;IACH;IACA,MAAM,oBAAoB;QACxB,OAAO;QACP,KAAK;QACL,UAAU;YAAE;gBAAE,OAAO;YAAK;SAAG;IAC/B;IAEA,MAAM,WAAW;QACf;QACA;QACA,oGAAoG;QACpG,UAAU;QACV;KACD;IAED,MAAM,mBAAmB;QACvB;QACA;QACA;QACA;KACD;IAED,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,qBAAqB;QACzB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,0FAA0F;IAC1F,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,8DAA8D;IAC9D,4DAA4D;IAC5D,2BAA2B;IAC3B,MAAM,qBAAqB;QACzB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,sBAAsB;IACtB,MAAM,0BAA0B;QAC9B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,mDAAmD;IACnD,qDAAqD;IACrD,MAAM,SAAS;QACb;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,YAAY;IAElB,MAAM,WAAW;WACZ;WACA;KACJ,CAAC,MAAM,CAAC,CAAC;QACR,OAAO,CAAC,mBAAmB,QAAQ,CAAC;IACtC;IAEA,MAAM,WAAW;QACf,OAAO;QACP,OAAO;IACT;IAEA,MAAM,WAAW;QACf,OAAO;QACP,OAAO;QACP,WAAW;IACb;IAEA,MAAM,gBAAgB;QACpB,OAAO,MAAM,MAAM,CAAC,MAAM,MAAM,MAAM,IAAI,YAAY;QACtD,WAAW;QACX,UAAU;YAAE,UAAU;QAAU;IAClC;IAEA,6DAA6D;IAC7D,mCAAmC;IACnC,uBAAuB;IACvB,8BAA8B;IAC9B,SAAS,aAAa,IAAI;QACxB,OAAO,MAAM,MAAM,CACjB,MACA,MAAM,MAAM,IAAI,KAAK,GAAG,CAAC,CAAC;YACxB,OAAO,GAAG,OAAO,CAAC,OAAO;QAC3B,KACA;IAEJ;IAEA,MAAM,sBAAsB;QAC1B,OAAO;QACP,OAAO,aAAa;QACpB,WAAW;IACb;IAEA,6DAA6D;IAC7D,SAAS,gBAAgB,IAAI,EAAE,EAC7B,UAAU,EAAE,IAAI,EACjB,GAAG,CAAC,CAAC;QACJ,MAAM,YAAY;QAClB,aAAa,cAAc,EAAE;QAC7B,OAAO,KAAK,GAAG,CAAC,CAAC;YACf,IAAI,KAAK,KAAK,CAAC,aAAa,WAAW,QAAQ,CAAC,OAAO;gBACrD,OAAO;YACT,OAAO,IAAI,UAAU,OAAO;gBAC1B,OAAO,GAAG,KAAK,EAAE,CAAC;YACpB,OAAO;gBACL,OAAO;YACT;QACF;IACF;IAEA,OAAO;QACL,MAAM;QACN,kBAAkB;QAClB,wCAAwC;QACxC,SAAS;QACT,UAAU;YACR,UAAU;YACV,SACE,gBAAgB,UAAU;gBAAE,MAAM,CAAC,IAAM,EAAE,MAAM,GAAG;YAAE;YACxD,SAAS;YACT,MAAM;YACN,UAAU;QACZ;QACA,UAAU;YACR;gBACE,OAAO;gBACP,OAAO,aAAa;YACtB;YACA;YACA;YACA;YACA;YACA;YACA,KAAK,aAAa;YAClB,KAAK,oBAAoB;YACzB;YACA;SACD;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 11688, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/swift.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * @param { Array<string | RegExp | Object> } args\n * @returns {object}\n */\nfunction stripOptionsFromArgs(args) {\n  const opts = args[args.length - 1];\n\n  if (typeof opts === 'object' && opts.constructor === Object) {\n    args.splice(args.length - 1, 1);\n    return opts;\n  } else {\n    return {};\n  }\n}\n\n/** @typedef { {capture?: boolean} } RegexEitherOptions */\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] | [...(RegExp | string)[], RegexEitherOptions]} args\n * @returns {string}\n */\nfunction either(...args) {\n  /** @type { object & {capture?: boolean} }  */\n  const opts = stripOptionsFromArgs(args);\n  const joined = '('\n    + (opts.capture ? \"\" : \"?:\")\n    + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\nconst keywordWrapper = keyword => concat(\n  /\\b/,\n  keyword,\n  /\\w$/.test(keyword) ? /\\b/ : /\\B/\n);\n\n// Keywords that require a leading dot.\nconst dotKeywords = [\n  'Protocol', // contextual\n  'Type' // contextual\n].map(keywordWrapper);\n\n// Keywords that may have a leading dot.\nconst optionalDotKeywords = [\n  'init',\n  'self'\n].map(keywordWrapper);\n\n// should register as keyword, not type\nconst keywordTypes = [\n  'Any',\n  'Self'\n];\n\n// Regular keywords and literals.\nconst keywords = [\n  // strings below will be fed into the regular `keywords` engine while regex\n  // will result in additional modes being created to scan for those keywords to\n  // avoid conflicts with other rules\n  'actor',\n  'any', // contextual\n  'associatedtype',\n  'async',\n  'await',\n  /as\\?/, // operator\n  /as!/, // operator\n  'as', // operator\n  'borrowing', // contextual\n  'break',\n  'case',\n  'catch',\n  'class',\n  'consume', // contextual\n  'consuming', // contextual\n  'continue',\n  'convenience', // contextual\n  'copy', // contextual\n  'default',\n  'defer',\n  'deinit',\n  'didSet', // contextual\n  'distributed',\n  'do',\n  'dynamic', // contextual\n  'each',\n  'else',\n  'enum',\n  'extension',\n  'fallthrough',\n  /fileprivate\\(set\\)/,\n  'fileprivate',\n  'final', // contextual\n  'for',\n  'func',\n  'get', // contextual\n  'guard',\n  'if',\n  'import',\n  'indirect', // contextual\n  'infix', // contextual\n  /init\\?/,\n  /init!/,\n  'inout',\n  /internal\\(set\\)/,\n  'internal',\n  'in',\n  'is', // operator\n  'isolated', // contextual\n  'nonisolated', // contextual\n  'lazy', // contextual\n  'let',\n  'macro',\n  'mutating', // contextual\n  'nonmutating', // contextual\n  /open\\(set\\)/, // contextual\n  'open', // contextual\n  'operator',\n  'optional', // contextual\n  'override', // contextual\n  'package',\n  'postfix', // contextual\n  'precedencegroup',\n  'prefix', // contextual\n  /private\\(set\\)/,\n  'private',\n  'protocol',\n  /public\\(set\\)/,\n  'public',\n  'repeat',\n  'required', // contextual\n  'rethrows',\n  'return',\n  'set', // contextual\n  'some', // contextual\n  'static',\n  'struct',\n  'subscript',\n  'super',\n  'switch',\n  'throws',\n  'throw',\n  /try\\?/, // operator\n  /try!/, // operator\n  'try', // operator\n  'typealias',\n  /unowned\\(safe\\)/, // contextual\n  /unowned\\(unsafe\\)/, // contextual\n  'unowned', // contextual\n  'var',\n  'weak', // contextual\n  'where',\n  'while',\n  'willSet' // contextual\n];\n\n// NOTE: Contextual keywords are reserved only in specific contexts.\n// Ideally, these should be matched using modes to avoid false positives.\n\n// Literals.\nconst literals = [\n  'false',\n  'nil',\n  'true'\n];\n\n// Keywords used in precedence groups.\nconst precedencegroupKeywords = [\n  'assignment',\n  'associativity',\n  'higherThan',\n  'left',\n  'lowerThan',\n  'none',\n  'right'\n];\n\n// Keywords that start with a number sign (#).\n// #(un)available is handled separately.\nconst numberSignKeywords = [\n  '#colorLiteral',\n  '#column',\n  '#dsohandle',\n  '#else',\n  '#elseif',\n  '#endif',\n  '#error',\n  '#file',\n  '#fileID',\n  '#fileLiteral',\n  '#filePath',\n  '#function',\n  '#if',\n  '#imageLiteral',\n  '#keyPath',\n  '#line',\n  '#selector',\n  '#sourceLocation',\n  '#warning'\n];\n\n// Global functions in the Standard Library.\nconst builtIns = [\n  'abs',\n  'all',\n  'any',\n  'assert',\n  'assertionFailure',\n  'debugPrint',\n  'dump',\n  'fatalError',\n  'getVaList',\n  'isKnownUniquelyReferenced',\n  'max',\n  'min',\n  'numericCast',\n  'pointwiseMax',\n  'pointwiseMin',\n  'precondition',\n  'preconditionFailure',\n  'print',\n  'readLine',\n  'repeatElement',\n  'sequence',\n  'stride',\n  'swap',\n  'swift_unboxFromSwiftValueWithType',\n  'transcode',\n  'type',\n  'unsafeBitCast',\n  'unsafeDowncast',\n  'withExtendedLifetime',\n  'withUnsafeMutablePointer',\n  'withUnsafePointer',\n  'withVaList',\n  'withoutActuallyEscaping',\n  'zip'\n];\n\n// Valid first characters for operators.\nconst operatorHead = either(\n  /[/=\\-+!*%<>&|^~?]/,\n  /[\\u00A1-\\u00A7]/,\n  /[\\u00A9\\u00AB]/,\n  /[\\u00AC\\u00AE]/,\n  /[\\u00B0\\u00B1]/,\n  /[\\u00B6\\u00BB\\u00BF\\u00D7\\u00F7]/,\n  /[\\u2016-\\u2017]/,\n  /[\\u2020-\\u2027]/,\n  /[\\u2030-\\u203E]/,\n  /[\\u2041-\\u2053]/,\n  /[\\u2055-\\u205E]/,\n  /[\\u2190-\\u23FF]/,\n  /[\\u2500-\\u2775]/,\n  /[\\u2794-\\u2BFF]/,\n  /[\\u2E00-\\u2E7F]/,\n  /[\\u3001-\\u3003]/,\n  /[\\u3008-\\u3020]/,\n  /[\\u3030]/\n);\n\n// Valid characters for operators.\nconst operatorCharacter = either(\n  operatorHead,\n  /[\\u0300-\\u036F]/,\n  /[\\u1DC0-\\u1DFF]/,\n  /[\\u20D0-\\u20FF]/,\n  /[\\uFE00-\\uFE0F]/,\n  /[\\uFE20-\\uFE2F]/\n  // TODO: The following characters are also allowed, but the regex isn't supported yet.\n  // /[\\u{E0100}-\\u{E01EF}]/u\n);\n\n// Valid operator.\nconst operator = concat(operatorHead, operatorCharacter, '*');\n\n// Valid first characters for identifiers.\nconst identifierHead = either(\n  /[a-zA-Z_]/,\n  /[\\u00A8\\u00AA\\u00AD\\u00AF\\u00B2-\\u00B5\\u00B7-\\u00BA]/,\n  /[\\u00BC-\\u00BE\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u00FF]/,\n  /[\\u0100-\\u02FF\\u0370-\\u167F\\u1681-\\u180D\\u180F-\\u1DBF]/,\n  /[\\u1E00-\\u1FFF]/,\n  /[\\u200B-\\u200D\\u202A-\\u202E\\u203F-\\u2040\\u2054\\u2060-\\u206F]/,\n  /[\\u2070-\\u20CF\\u2100-\\u218F\\u2460-\\u24FF\\u2776-\\u2793]/,\n  /[\\u2C00-\\u2DFF\\u2E80-\\u2FFF]/,\n  /[\\u3004-\\u3007\\u3021-\\u302F\\u3031-\\u303F\\u3040-\\uD7FF]/,\n  /[\\uF900-\\uFD3D\\uFD40-\\uFDCF\\uFDF0-\\uFE1F\\uFE30-\\uFE44]/,\n  /[\\uFE47-\\uFEFE\\uFF00-\\uFFFD]/ // Should be /[\\uFE47-\\uFFFD]/, but we have to exclude FEFF.\n  // The following characters are also allowed, but the regexes aren't supported yet.\n  // /[\\u{10000}-\\u{1FFFD}\\u{20000-\\u{2FFFD}\\u{30000}-\\u{3FFFD}\\u{40000}-\\u{4FFFD}]/u,\n  // /[\\u{50000}-\\u{5FFFD}\\u{60000-\\u{6FFFD}\\u{70000}-\\u{7FFFD}\\u{80000}-\\u{8FFFD}]/u,\n  // /[\\u{90000}-\\u{9FFFD}\\u{A0000-\\u{AFFFD}\\u{B0000}-\\u{BFFFD}\\u{C0000}-\\u{CFFFD}]/u,\n  // /[\\u{D0000}-\\u{DFFFD}\\u{E0000-\\u{EFFFD}]/u\n);\n\n// Valid characters for identifiers.\nconst identifierCharacter = either(\n  identifierHead,\n  /\\d/,\n  /[\\u0300-\\u036F\\u1DC0-\\u1DFF\\u20D0-\\u20FF\\uFE20-\\uFE2F]/\n);\n\n// Valid identifier.\nconst identifier = concat(identifierHead, identifierCharacter, '*');\n\n// Valid type identifier.\nconst typeIdentifier = concat(/[A-Z]/, identifierCharacter, '*');\n\n// Built-in attributes, which are highlighted as keywords.\n// @available is handled separately.\n// https://docs.swift.org/swift-book/documentation/the-swift-programming-language/attributes\nconst keywordAttributes = [\n  'attached',\n  'autoclosure',\n  concat(/convention\\(/, either('swift', 'block', 'c'), /\\)/),\n  'discardableResult',\n  'dynamicCallable',\n  'dynamicMemberLookup',\n  'escaping',\n  'freestanding',\n  'frozen',\n  'GKInspectable',\n  'IBAction',\n  'IBDesignable',\n  'IBInspectable',\n  'IBOutlet',\n  'IBSegueAction',\n  'inlinable',\n  'main',\n  'nonobjc',\n  'NSApplicationMain',\n  'NSCopying',\n  'NSManaged',\n  concat(/objc\\(/, identifier, /\\)/),\n  'objc',\n  'objcMembers',\n  'propertyWrapper',\n  'requires_stored_property_inits',\n  'resultBuilder',\n  'Sendable',\n  'testable',\n  'UIApplicationMain',\n  'unchecked',\n  'unknown',\n  'usableFromInline',\n  'warn_unqualified_access'\n];\n\n// Contextual keywords used in @available and #(un)available.\nconst availabilityKeywords = [\n  'iOS',\n  'iOSApplicationExtension',\n  'macOS',\n  'macOSApplicationExtension',\n  'macCatalyst',\n  'macCatalystApplicationExtension',\n  'watchOS',\n  'watchOSApplicationExtension',\n  'tvOS',\n  'tvOSApplicationExtension',\n  'swift'\n];\n\n/*\nLanguage: Swift\nDescription: Swift is a general-purpose programming language built using a modern approach to safety, performance, and software design patterns.\nAuthor: Steven Van Impe <<EMAIL>>\nContributors: Chris Eidhof <<EMAIL>>, Nate Cook <<EMAIL>>, Alexander Lichter <<EMAIL>>, Richard Gibson <gibson042@github>\nWebsite: https://swift.org\nCategory: common, system\n*/\n\n\n/** @type LanguageFn */\nfunction swift(hljs) {\n  const WHITESPACE = {\n    match: /\\s+/,\n    relevance: 0\n  };\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#ID411\n  const BLOCK_COMMENT = hljs.COMMENT(\n    '/\\\\*',\n    '\\\\*/',\n    { contains: [ 'self' ] }\n  );\n  const COMMENTS = [\n    hljs.C_LINE_COMMENT_MODE,\n    BLOCK_COMMENT\n  ];\n\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#ID413\n  // https://docs.swift.org/swift-book/ReferenceManual/zzSummaryOfTheGrammar.html\n  const DOT_KEYWORD = {\n    match: [\n      /\\./,\n      either(...dotKeywords, ...optionalDotKeywords)\n    ],\n    className: { 2: \"keyword\" }\n  };\n  const KEYWORD_GUARD = {\n    // Consume .keyword to prevent highlighting properties and methods as keywords.\n    match: concat(/\\./, either(...keywords)),\n    relevance: 0\n  };\n  const PLAIN_KEYWORDS = keywords\n    .filter(kw => typeof kw === 'string')\n    .concat([ \"_|0\" ]); // seems common, so 0 relevance\n  const REGEX_KEYWORDS = keywords\n    .filter(kw => typeof kw !== 'string') // find regex\n    .concat(keywordTypes)\n    .map(keywordWrapper);\n  const KEYWORD = { variants: [\n    {\n      className: 'keyword',\n      match: either(...REGEX_KEYWORDS, ...optionalDotKeywords)\n    }\n  ] };\n  // find all the regular keywords\n  const KEYWORDS = {\n    $pattern: either(\n      /\\b\\w+/, // regular keywords\n      /#\\w+/ // number keywords\n    ),\n    keyword: PLAIN_KEYWORDS\n      .concat(numberSignKeywords),\n    literal: literals\n  };\n  const KEYWORD_MODES = [\n    DOT_KEYWORD,\n    KEYWORD_GUARD,\n    KEYWORD\n  ];\n\n  // https://github.com/apple/swift/tree/main/stdlib/public/core\n  const BUILT_IN_GUARD = {\n    // Consume .built_in to prevent highlighting properties and methods.\n    match: concat(/\\./, either(...builtIns)),\n    relevance: 0\n  };\n  const BUILT_IN = {\n    className: 'built_in',\n    match: concat(/\\b/, either(...builtIns), /(?=\\()/)\n  };\n  const BUILT_INS = [\n    BUILT_IN_GUARD,\n    BUILT_IN\n  ];\n\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#ID418\n  const OPERATOR_GUARD = {\n    // Prevent -> from being highlighting as an operator.\n    match: /->/,\n    relevance: 0\n  };\n  const OPERATOR = {\n    className: 'operator',\n    relevance: 0,\n    variants: [\n      { match: operator },\n      {\n        // dot-operator: only operators that start with a dot are allowed to use dots as\n        // characters (..., ...<, .*, etc). So there rule here is: a dot followed by one or more\n        // characters that may also include dots.\n        match: `\\\\.(\\\\.|${operatorCharacter})+` }\n    ]\n  };\n  const OPERATORS = [\n    OPERATOR_GUARD,\n    OPERATOR\n  ];\n\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#grammar_numeric-literal\n  // TODO: Update for leading `-` after lookbehind is supported everywhere\n  const decimalDigits = '([0-9]_*)+';\n  const hexDigits = '([0-9a-fA-F]_*)+';\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    variants: [\n      // decimal floating-point-literal (subsumes decimal-literal)\n      { match: `\\\\b(${decimalDigits})(\\\\.(${decimalDigits}))?` + `([eE][+-]?(${decimalDigits}))?\\\\b` },\n      // hexadecimal floating-point-literal (subsumes hexadecimal-literal)\n      { match: `\\\\b0x(${hexDigits})(\\\\.(${hexDigits}))?` + `([pP][+-]?(${decimalDigits}))?\\\\b` },\n      // octal-literal\n      { match: /\\b0o([0-7]_*)+\\b/ },\n      // binary-literal\n      { match: /\\b0b([01]_*)+\\b/ }\n    ]\n  };\n\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#grammar_string-literal\n  const ESCAPED_CHARACTER = (rawDelimiter = \"\") => ({\n    className: 'subst',\n    variants: [\n      { match: concat(/\\\\/, rawDelimiter, /[0\\\\tnr\"']/) },\n      { match: concat(/\\\\/, rawDelimiter, /u\\{[0-9a-fA-F]{1,8}\\}/) }\n    ]\n  });\n  const ESCAPED_NEWLINE = (rawDelimiter = \"\") => ({\n    className: 'subst',\n    match: concat(/\\\\/, rawDelimiter, /[\\t ]*(?:[\\r\\n]|\\r\\n)/)\n  });\n  const INTERPOLATION = (rawDelimiter = \"\") => ({\n    className: 'subst',\n    label: \"interpol\",\n    begin: concat(/\\\\/, rawDelimiter, /\\(/),\n    end: /\\)/\n  });\n  const MULTILINE_STRING = (rawDelimiter = \"\") => ({\n    begin: concat(rawDelimiter, /\"\"\"/),\n    end: concat(/\"\"\"/, rawDelimiter),\n    contains: [\n      ESCAPED_CHARACTER(rawDelimiter),\n      ESCAPED_NEWLINE(rawDelimiter),\n      INTERPOLATION(rawDelimiter)\n    ]\n  });\n  const SINGLE_LINE_STRING = (rawDelimiter = \"\") => ({\n    begin: concat(rawDelimiter, /\"/),\n    end: concat(/\"/, rawDelimiter),\n    contains: [\n      ESCAPED_CHARACTER(rawDelimiter),\n      INTERPOLATION(rawDelimiter)\n    ]\n  });\n  const STRING = {\n    className: 'string',\n    variants: [\n      MULTILINE_STRING(),\n      MULTILINE_STRING(\"#\"),\n      MULTILINE_STRING(\"##\"),\n      MULTILINE_STRING(\"###\"),\n      SINGLE_LINE_STRING(),\n      SINGLE_LINE_STRING(\"#\"),\n      SINGLE_LINE_STRING(\"##\"),\n      SINGLE_LINE_STRING(\"###\")\n    ]\n  };\n\n  const REGEXP_CONTENTS = [\n    hljs.BACKSLASH_ESCAPE,\n    {\n      begin: /\\[/,\n      end: /\\]/,\n      relevance: 0,\n      contains: [ hljs.BACKSLASH_ESCAPE ]\n    }\n  ];\n\n  const BARE_REGEXP_LITERAL = {\n    begin: /\\/[^\\s](?=[^/\\n]*\\/)/,\n    end: /\\//,\n    contains: REGEXP_CONTENTS\n  };\n\n  const EXTENDED_REGEXP_LITERAL = (rawDelimiter) => {\n    const begin = concat(rawDelimiter, /\\//);\n    const end = concat(/\\//, rawDelimiter);\n    return {\n      begin,\n      end,\n      contains: [\n        ...REGEXP_CONTENTS,\n        {\n          scope: \"comment\",\n          begin: `#(?!.*${end})`,\n          end: /$/,\n        },\n      ],\n    };\n  };\n\n  // https://docs.swift.org/swift-book/documentation/the-swift-programming-language/lexicalstructure/#Regular-Expression-Literals\n  const REGEXP = {\n    scope: \"regexp\",\n    variants: [\n      EXTENDED_REGEXP_LITERAL('###'),\n      EXTENDED_REGEXP_LITERAL('##'),\n      EXTENDED_REGEXP_LITERAL('#'),\n      BARE_REGEXP_LITERAL\n    ]\n  };\n\n  // https://docs.swift.org/swift-book/ReferenceManual/LexicalStructure.html#ID412\n  const QUOTED_IDENTIFIER = { match: concat(/`/, identifier, /`/) };\n  const IMPLICIT_PARAMETER = {\n    className: 'variable',\n    match: /\\$\\d+/\n  };\n  const PROPERTY_WRAPPER_PROJECTION = {\n    className: 'variable',\n    match: `\\\\$${identifierCharacter}+`\n  };\n  const IDENTIFIERS = [\n    QUOTED_IDENTIFIER,\n    IMPLICIT_PARAMETER,\n    PROPERTY_WRAPPER_PROJECTION\n  ];\n\n  // https://docs.swift.org/swift-book/ReferenceManual/Attributes.html\n  const AVAILABLE_ATTRIBUTE = {\n    match: /(@|#(un)?)available/,\n    scope: 'keyword',\n    starts: { contains: [\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: availabilityKeywords,\n        contains: [\n          ...OPERATORS,\n          NUMBER,\n          STRING\n        ]\n      }\n    ] }\n  };\n\n  const KEYWORD_ATTRIBUTE = {\n    scope: 'keyword',\n    match: concat(/@/, either(...keywordAttributes), lookahead(either(/\\(/, /\\s+/))),\n  };\n\n  const USER_DEFINED_ATTRIBUTE = {\n    scope: 'meta',\n    match: concat(/@/, identifier)\n  };\n\n  const ATTRIBUTES = [\n    AVAILABLE_ATTRIBUTE,\n    KEYWORD_ATTRIBUTE,\n    USER_DEFINED_ATTRIBUTE\n  ];\n\n  // https://docs.swift.org/swift-book/ReferenceManual/Types.html\n  const TYPE = {\n    match: lookahead(/\\b[A-Z]/),\n    relevance: 0,\n    contains: [\n      { // Common Apple frameworks, for relevance boost\n        className: 'type',\n        match: concat(/(AV|CA|CF|CG|CI|CL|CM|CN|CT|MK|MP|MTK|MTL|NS|SCN|SK|UI|WK|XC)/, identifierCharacter, '+')\n      },\n      { // Type identifier\n        className: 'type',\n        match: typeIdentifier,\n        relevance: 0\n      },\n      { // Optional type\n        match: /[?!]+/,\n        relevance: 0\n      },\n      { // Variadic parameter\n        match: /\\.\\.\\./,\n        relevance: 0\n      },\n      { // Protocol composition\n        match: concat(/\\s+&\\s+/, lookahead(typeIdentifier)),\n        relevance: 0\n      }\n    ]\n  };\n  const GENERIC_ARGUMENTS = {\n    begin: /</,\n    end: />/,\n    keywords: KEYWORDS,\n    contains: [\n      ...COMMENTS,\n      ...KEYWORD_MODES,\n      ...ATTRIBUTES,\n      OPERATOR_GUARD,\n      TYPE\n    ]\n  };\n  TYPE.contains.push(GENERIC_ARGUMENTS);\n\n  // https://docs.swift.org/swift-book/ReferenceManual/Expressions.html#ID552\n  // Prevents element names from being highlighted as keywords.\n  const TUPLE_ELEMENT_NAME = {\n    match: concat(identifier, /\\s*:/),\n    keywords: \"_|0\",\n    relevance: 0\n  };\n  // Matches tuples as well as the parameter list of a function type.\n  const TUPLE = {\n    begin: /\\(/,\n    end: /\\)/,\n    relevance: 0,\n    keywords: KEYWORDS,\n    contains: [\n      'self',\n      TUPLE_ELEMENT_NAME,\n      ...COMMENTS,\n      REGEXP,\n      ...KEYWORD_MODES,\n      ...BUILT_INS,\n      ...OPERATORS,\n      NUMBER,\n      STRING,\n      ...IDENTIFIERS,\n      ...ATTRIBUTES,\n      TYPE\n    ]\n  };\n\n  const GENERIC_PARAMETERS = {\n    begin: /</,\n    end: />/,\n    keywords: 'repeat each',\n    contains: [\n      ...COMMENTS,\n      TYPE\n    ]\n  };\n  const FUNCTION_PARAMETER_NAME = {\n    begin: either(\n      lookahead(concat(identifier, /\\s*:/)),\n      lookahead(concat(identifier, /\\s+/, identifier, /\\s*:/))\n    ),\n    end: /:/,\n    relevance: 0,\n    contains: [\n      {\n        className: 'keyword',\n        match: /\\b_\\b/\n      },\n      {\n        className: 'params',\n        match: identifier\n      }\n    ]\n  };\n  const FUNCTION_PARAMETERS = {\n    begin: /\\(/,\n    end: /\\)/,\n    keywords: KEYWORDS,\n    contains: [\n      FUNCTION_PARAMETER_NAME,\n      ...COMMENTS,\n      ...KEYWORD_MODES,\n      ...OPERATORS,\n      NUMBER,\n      STRING,\n      ...ATTRIBUTES,\n      TYPE,\n      TUPLE\n    ],\n    endsParent: true,\n    illegal: /[\"']/\n  };\n  // https://docs.swift.org/swift-book/ReferenceManual/Declarations.html#ID362\n  // https://docs.swift.org/swift-book/documentation/the-swift-programming-language/declarations/#Macro-Declaration\n  const FUNCTION_OR_MACRO = {\n    match: [\n      /(func|macro)/,\n      /\\s+/,\n      either(QUOTED_IDENTIFIER.match, identifier, operator)\n    ],\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    contains: [\n      GENERIC_PARAMETERS,\n      FUNCTION_PARAMETERS,\n      WHITESPACE\n    ],\n    illegal: [\n      /\\[/,\n      /%/\n    ]\n  };\n\n  // https://docs.swift.org/swift-book/ReferenceManual/Declarations.html#ID375\n  // https://docs.swift.org/swift-book/ReferenceManual/Declarations.html#ID379\n  const INIT_SUBSCRIPT = {\n    match: [\n      /\\b(?:subscript|init[?!]?)/,\n      /\\s*(?=[<(])/,\n    ],\n    className: { 1: \"keyword\" },\n    contains: [\n      GENERIC_PARAMETERS,\n      FUNCTION_PARAMETERS,\n      WHITESPACE\n    ],\n    illegal: /\\[|%/\n  };\n  // https://docs.swift.org/swift-book/ReferenceManual/Declarations.html#ID380\n  const OPERATOR_DECLARATION = {\n    match: [\n      /operator/,\n      /\\s+/,\n      operator\n    ],\n    className: {\n      1: \"keyword\",\n      3: \"title\"\n    }\n  };\n\n  // https://docs.swift.org/swift-book/ReferenceManual/Declarations.html#ID550\n  const PRECEDENCEGROUP = {\n    begin: [\n      /precedencegroup/,\n      /\\s+/,\n      typeIdentifier\n    ],\n    className: {\n      1: \"keyword\",\n      3: \"title\"\n    },\n    contains: [ TYPE ],\n    keywords: [\n      ...precedencegroupKeywords,\n      ...literals\n    ],\n    end: /}/\n  };\n\n  const CLASS_FUNC_DECLARATION = {\n    match: [\n      /class\\b/,          \n      /\\s+/,\n      /func\\b/,\n      /\\s+/,\n      /\\b[A-Za-z_][A-Za-z0-9_]*\\b/ \n    ],\n    scope: {\n      1: \"keyword\",\n      3: \"keyword\",\n      5: \"title.function\"\n    }\n  };\n\n  const CLASS_VAR_DECLARATION = {\n    match: [\n      /class\\b/,\n      /\\s+/,          \n      /var\\b/, \n    ],\n    scope: {\n      1: \"keyword\",\n      3: \"keyword\"\n    }\n  };\n\n  const TYPE_DECLARATION = {\n    begin: [\n      /(struct|protocol|class|extension|enum|actor)/,\n      /\\s+/,\n      identifier,\n      /\\s*/,\n    ],\n    beginScope: {\n      1: \"keyword\",\n      3: \"title.class\"\n    },\n    keywords: KEYWORDS,\n    contains: [\n      GENERIC_PARAMETERS,\n      ...KEYWORD_MODES,\n      {\n        begin: /:/,\n        end: /\\{/,\n        keywords: KEYWORDS,\n        contains: [\n          {\n            scope: \"title.class.inherited\",\n            match: typeIdentifier,\n          },\n          ...KEYWORD_MODES,\n        ],\n        relevance: 0,\n      },\n    ]\n  };\n\n  // Add supported submodes to string interpolation.\n  for (const variant of STRING.variants) {\n    const interpolation = variant.contains.find(mode => mode.label === \"interpol\");\n    // TODO: Interpolation can contain any expression, so there's room for improvement here.\n    interpolation.keywords = KEYWORDS;\n    const submodes = [\n      ...KEYWORD_MODES,\n      ...BUILT_INS,\n      ...OPERATORS,\n      NUMBER,\n      STRING,\n      ...IDENTIFIERS\n    ];\n    interpolation.contains = [\n      ...submodes,\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        contains: [\n          'self',\n          ...submodes\n        ]\n      }\n    ];\n  }\n\n  return {\n    name: 'Swift',\n    keywords: KEYWORDS,\n    contains: [\n      ...COMMENTS,\n      FUNCTION_OR_MACRO,\n      INIT_SUBSCRIPT,\n      CLASS_FUNC_DECLARATION,\n      CLASS_VAR_DECLARATION,\n      TYPE_DECLARATION,\n      OPERATOR_DECLARATION,\n      PRECEDENCEGROUP,\n      {\n        beginKeywords: 'import',\n        end: /$/,\n        contains: [ ...COMMENTS ],\n        relevance: 0\n      },\n      REGEXP,\n      ...KEYWORD_MODES,\n      ...BUILT_INS,\n      ...OPERATORS,\n      NUMBER,\n      STRING,\n      ...IDENTIFIERS,\n      ...ATTRIBUTES,\n      TYPE,\n      TUPLE\n    ]\n  };\n}\n\nexport { swift as default };\n"], "names": [], "mappings": "AAAA;;;GAGG,GAEH;;;CAGC;;;AACD,SAAS,OAAO,EAAE;IAChB,IAAI,CAAC,IAAI,OAAO;IAChB,IAAI,OAAO,OAAO,UAAU,OAAO;IAEnC,OAAO,GAAG,MAAM;AAClB;AAEA;;;CAGC,GACD,SAAS,UAAU,EAAE;IACnB,OAAO,OAAO,OAAO,IAAI;AAC3B;AAEA;;;CAGC,GACD,SAAS,OAAO,GAAG,IAAI;IACrB,MAAM,SAAS,KAAK,GAAG,CAAC,CAAC,IAAM,OAAO,IAAI,IAAI,CAAC;IAC/C,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,qBAAqB,IAAI;IAChC,MAAM,OAAO,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;IAElC,IAAI,OAAO,SAAS,YAAY,KAAK,WAAW,KAAK,QAAQ;QAC3D,KAAK,MAAM,CAAC,KAAK,MAAM,GAAG,GAAG;QAC7B,OAAO;IACT,OAAO;QACL,OAAO,CAAC;IACV;AACF;AAEA,wDAAwD,GAExD;;;;;;CAMC,GACD,SAAS,OAAO,GAAG,IAAI;IACrB,4CAA4C,GAC5C,MAAM,OAAO,qBAAqB;IAClC,MAAM,SAAS,MACX,CAAC,KAAK,OAAO,GAAG,KAAK,IAAI,IACzB,KAAK,GAAG,CAAC,CAAC,IAAM,OAAO,IAAI,IAAI,CAAC,OAAO;IAC3C,OAAO;AACT;AAEA,MAAM,iBAAiB,CAAA,UAAW,OAChC,MACA,SACA,MAAM,IAAI,CAAC,WAAW,OAAO;AAG/B,uCAAuC;AACvC,MAAM,cAAc;IAClB;IACA,OAAO,aAAa;CACrB,CAAC,GAAG,CAAC;AAEN,wCAAwC;AACxC,MAAM,sBAAsB;IAC1B;IACA;CACD,CAAC,GAAG,CAAC;AAEN,uCAAuC;AACvC,MAAM,eAAe;IACnB;IACA;CACD;AAED,iCAAiC;AACjC,MAAM,WAAW;IACf,2EAA2E;IAC3E,8EAA8E;IAC9E,mCAAmC;IACnC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,UAAU,aAAa;CACxB;AAED,oEAAoE;AACpE,yEAAyE;AAEzE,YAAY;AACZ,MAAM,WAAW;IACf;IACA;IACA;CACD;AAED,sCAAsC;AACtC,MAAM,0BAA0B;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,8CAA8C;AAC9C,wCAAwC;AACxC,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,4CAA4C;AAC5C,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,wCAAwC;AACxC,MAAM,eAAe,OACnB,qBACA,mBACA,kBACA,kBACA,kBACA,oCACA,mBACA,mBACA,mBACA,mBACA,mBACA,mBACA,mBACA,mBACA,mBACA,mBACA,mBACA;AAGF,kCAAkC;AAClC,MAAM,oBAAoB,OACxB,cACA,mBACA,mBACA,mBACA,mBACA;AAKF,kBAAkB;AAClB,MAAM,WAAW,OAAO,cAAc,mBAAmB;AAEzD,0CAA0C;AAC1C,MAAM,iBAAiB,OACrB,aACA,wDACA,0DACA,0DACA,mBACA,gEACA,0DACA,gCACA,0DACA,0DACA,+BAA+B,4DAA4D;;AAQ7F,oCAAoC;AACpC,MAAM,sBAAsB,OAC1B,gBACA,MACA;AAGF,oBAAoB;AACpB,MAAM,aAAa,OAAO,gBAAgB,qBAAqB;AAE/D,yBAAyB;AACzB,MAAM,iBAAiB,OAAO,SAAS,qBAAqB;AAE5D,0DAA0D;AAC1D,oCAAoC;AACpC,4FAA4F;AAC5F,MAAM,oBAAoB;IACxB;IACA;IACA,OAAO,gBAAgB,OAAO,SAAS,SAAS,MAAM;IACtD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,OAAO,UAAU,YAAY;IAC7B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,6DAA6D;AAC7D,MAAM,uBAAuB;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED;;;;;;;AAOA,GAGA,qBAAqB,GACrB,SAAS,MAAM,IAAI;IACjB,MAAM,aAAa;QACjB,OAAO;QACP,WAAW;IACb;IACA,gFAAgF;IAChF,MAAM,gBAAgB,KAAK,OAAO,CAChC,QACA,QACA;QAAE,UAAU;YAAE;SAAQ;IAAC;IAEzB,MAAM,WAAW;QACf,KAAK,mBAAmB;QACxB;KACD;IAED,gFAAgF;IAChF,+EAA+E;IAC/E,MAAM,cAAc;QAClB,OAAO;YACL;YACA,UAAU,gBAAgB;SAC3B;QACD,WAAW;YAAE,GAAG;QAAU;IAC5B;IACA,MAAM,gBAAgB;QACpB,+EAA+E;QAC/E,OAAO,OAAO,MAAM,UAAU;QAC9B,WAAW;IACb;IACA,MAAM,iBAAiB,SACpB,MAAM,CAAC,CAAA,KAAM,OAAO,OAAO,UAC3B,MAAM,CAAC;QAAE;KAAO,GAAG,+BAA+B;IACrD,MAAM,iBAAiB,SACpB,MAAM,CAAC,CAAA,KAAM,OAAO,OAAO,UAAU,aAAa;KAClD,MAAM,CAAC,cACP,GAAG,CAAC;IACP,MAAM,UAAU;QAAE,UAAU;YAC1B;gBACE,WAAW;gBACX,OAAO,UAAU,mBAAmB;YACtC;SACD;IAAC;IACF,gCAAgC;IAChC,MAAM,WAAW;QACf,UAAU,OACR,SACA,OAAO,kBAAkB;;QAE3B,SAAS,eACN,MAAM,CAAC;QACV,SAAS;IACX;IACA,MAAM,gBAAgB;QACpB;QACA;QACA;KACD;IAED,8DAA8D;IAC9D,MAAM,iBAAiB;QACrB,oEAAoE;QACpE,OAAO,OAAO,MAAM,UAAU;QAC9B,WAAW;IACb;IACA,MAAM,WAAW;QACf,WAAW;QACX,OAAO,OAAO,MAAM,UAAU,WAAW;IAC3C;IACA,MAAM,YAAY;QAChB;QACA;KACD;IAED,gFAAgF;IAChF,MAAM,iBAAiB;QACrB,qDAAqD;QACrD,OAAO;QACP,WAAW;IACb;IACA,MAAM,WAAW;QACf,WAAW;QACX,WAAW;QACX,UAAU;YACR;gBAAE,OAAO;YAAS;YAClB;gBACE,gFAAgF;gBAChF,wFAAwF;gBACxF,yCAAyC;gBACzC,OAAO,CAAC,QAAQ,EAAE,kBAAkB,EAAE,CAAC;YAAC;SAC3C;IACH;IACA,MAAM,YAAY;QAChB;QACA;KACD;IAED,kGAAkG;IAClG,wEAAwE;IACxE,MAAM,gBAAgB;IACtB,MAAM,YAAY;IAClB,MAAM,SAAS;QACb,WAAW;QACX,WAAW;QACX,UAAU;YACR,4DAA4D;YAC5D;gBAAE,OAAO,CAAC,IAAI,EAAE,cAAc,MAAM,EAAE,cAAc,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,cAAc,MAAM,CAAC;YAAC;YAC/F,oEAAoE;YACpE;gBAAE,OAAO,CAAC,MAAM,EAAE,UAAU,MAAM,EAAE,UAAU,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,cAAc,MAAM,CAAC;YAAC;YACzF,gBAAgB;YAChB;gBAAE,OAAO;YAAmB;YAC5B,iBAAiB;YACjB;gBAAE,OAAO;YAAkB;SAC5B;IACH;IAEA,iGAAiG;IACjG,MAAM,oBAAoB,CAAC,eAAe,EAAE,GAAK,CAAC;YAChD,WAAW;YACX,UAAU;gBACR;oBAAE,OAAO,OAAO,MAAM,cAAc;gBAAc;gBAClD;oBAAE,OAAO,OAAO,MAAM,cAAc;gBAAyB;aAC9D;QACH,CAAC;IACD,MAAM,kBAAkB,CAAC,eAAe,EAAE,GAAK,CAAC;YAC9C,WAAW;YACX,OAAO,OAAO,MAAM,cAAc;QACpC,CAAC;IACD,MAAM,gBAAgB,CAAC,eAAe,EAAE,GAAK,CAAC;YAC5C,WAAW;YACX,OAAO;YACP,OAAO,OAAO,MAAM,cAAc;YAClC,KAAK;QACP,CAAC;IACD,MAAM,mBAAmB,CAAC,eAAe,EAAE,GAAK,CAAC;YAC/C,OAAO,OAAO,cAAc;YAC5B,KAAK,OAAO,OAAO;YACnB,UAAU;gBACR,kBAAkB;gBAClB,gBAAgB;gBAChB,cAAc;aACf;QACH,CAAC;IACD,MAAM,qBAAqB,CAAC,eAAe,EAAE,GAAK,CAAC;YACjD,OAAO,OAAO,cAAc;YAC5B,KAAK,OAAO,KAAK;YACjB,UAAU;gBACR,kBAAkB;gBAClB,cAAc;aACf;QACH,CAAC;IACD,MAAM,SAAS;QACb,WAAW;QACX,UAAU;YACR;YACA,iBAAiB;YACjB,iBAAiB;YACjB,iBAAiB;YACjB;YACA,mBAAmB;YACnB,mBAAmB;YACnB,mBAAmB;SACpB;IACH;IAEA,MAAM,kBAAkB;QACtB,KAAK,gBAAgB;QACrB;YACE,OAAO;YACP,KAAK;YACL,WAAW;YACX,UAAU;gBAAE,KAAK,gBAAgB;aAAE;QACrC;KACD;IAED,MAAM,sBAAsB;QAC1B,OAAO;QACP,KAAK;QACL,UAAU;IACZ;IAEA,MAAM,0BAA0B,CAAC;QAC/B,MAAM,QAAQ,OAAO,cAAc;QACnC,MAAM,MAAM,OAAO,MAAM;QACzB,OAAO;YACL;YACA;YACA,UAAU;mBACL;gBACH;oBACE,OAAO;oBACP,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;oBACtB,KAAK;gBACP;aACD;QACH;IACF;IAEA,+HAA+H;IAC/H,MAAM,SAAS;QACb,OAAO;QACP,UAAU;YACR,wBAAwB;YACxB,wBAAwB;YACxB,wBAAwB;YACxB;SACD;IACH;IAEA,gFAAgF;IAChF,MAAM,oBAAoB;QAAE,OAAO,OAAO,KAAK,YAAY;IAAK;IAChE,MAAM,qBAAqB;QACzB,WAAW;QACX,OAAO;IACT;IACA,MAAM,8BAA8B;QAClC,WAAW;QACX,OAAO,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC;IACrC;IACA,MAAM,cAAc;QAClB;QACA;QACA;KACD;IAED,oEAAoE;IACpE,MAAM,sBAAsB;QAC1B,OAAO;QACP,OAAO;QACP,QAAQ;YAAE,UAAU;gBAClB;oBACE,OAAO;oBACP,KAAK;oBACL,UAAU;oBACV,UAAU;2BACL;wBACH;wBACA;qBACD;gBACH;aACD;QAAC;IACJ;IAEA,MAAM,oBAAoB;QACxB,OAAO;QACP,OAAO,OAAO,KAAK,UAAU,oBAAoB,UAAU,OAAO,MAAM;IAC1E;IAEA,MAAM,yBAAyB;QAC7B,OAAO;QACP,OAAO,OAAO,KAAK;IACrB;IAEA,MAAM,aAAa;QACjB;QACA;QACA;KACD;IAED,+DAA+D;IAC/D,MAAM,OAAO;QACX,OAAO,UAAU;QACjB,WAAW;QACX,UAAU;YACR;gBACE,WAAW;gBACX,OAAO,OAAO,iEAAiE,qBAAqB;YACtG;YACA;gBACE,WAAW;gBACX,OAAO;gBACP,WAAW;YACb;YACA;gBACE,OAAO;gBACP,WAAW;YACb;YACA;gBACE,OAAO;gBACP,WAAW;YACb;YACA;gBACE,OAAO,OAAO,WAAW,UAAU;gBACnC,WAAW;YACb;SACD;IACH;IACA,MAAM,oBAAoB;QACxB,OAAO;QACP,KAAK;QACL,UAAU;QACV,UAAU;eACL;eACA;eACA;YACH;YACA;SACD;IACH;IACA,KAAK,QAAQ,CAAC,IAAI,CAAC;IAEnB,2EAA2E;IAC3E,6DAA6D;IAC7D,MAAM,qBAAqB;QACzB,OAAO,OAAO,YAAY;QAC1B,UAAU;QACV,WAAW;IACb;IACA,mEAAmE;IACnE,MAAM,QAAQ;QACZ,OAAO;QACP,KAAK;QACL,WAAW;QACX,UAAU;QACV,UAAU;YACR;YACA;eACG;YACH;eACG;eACA;eACA;YACH;YACA;eACG;eACA;YACH;SACD;IACH;IAEA,MAAM,qBAAqB;QACzB,OAAO;QACP,KAAK;QACL,UAAU;QACV,UAAU;eACL;YACH;SACD;IACH;IACA,MAAM,0BAA0B;QAC9B,OAAO,OACL,UAAU,OAAO,YAAY,UAC7B,UAAU,OAAO,YAAY,OAAO,YAAY;QAElD,KAAK;QACL,WAAW;QACX,UAAU;YACR;gBACE,WAAW;gBACX,OAAO;YACT;YACA;gBACE,WAAW;gBACX,OAAO;YACT;SACD;IACH;IACA,MAAM,sBAAsB;QAC1B,OAAO;QACP,KAAK;QACL,UAAU;QACV,UAAU;YACR;eACG;eACA;eACA;YACH;YACA;eACG;YACH;YACA;SACD;QACD,YAAY;QACZ,SAAS;IACX;IACA,4EAA4E;IAC5E,iHAAiH;IACjH,MAAM,oBAAoB;QACxB,OAAO;YACL;YACA;YACA,OAAO,kBAAkB,KAAK,EAAE,YAAY;SAC7C;QACD,WAAW;YACT,GAAG;YACH,GAAG;QACL;QACA,UAAU;YACR;YACA;YACA;SACD;QACD,SAAS;YACP;YACA;SACD;IACH;IAEA,4EAA4E;IAC5E,4EAA4E;IAC5E,MAAM,iBAAiB;QACrB,OAAO;YACL;YACA;SACD;QACD,WAAW;YAAE,GAAG;QAAU;QAC1B,UAAU;YACR;YACA;YACA;SACD;QACD,SAAS;IACX;IACA,4EAA4E;IAC5E,MAAM,uBAAuB;QAC3B,OAAO;YACL;YACA;YACA;SACD;QACD,WAAW;YACT,GAAG;YACH,GAAG;QACL;IACF;IAEA,4EAA4E;IAC5E,MAAM,kBAAkB;QACtB,OAAO;YACL;YACA;YACA;SACD;QACD,WAAW;YACT,GAAG;YACH,GAAG;QACL;QACA,UAAU;YAAE;SAAM;QAClB,UAAU;eACL;eACA;SACJ;QACD,KAAK;IACP;IAEA,MAAM,yBAAyB;QAC7B,OAAO;YACL;YACA;YACA;YACA;YACA;SACD;QACD,OAAO;YACL,GAAG;YACH,GAAG;YACH,GAAG;QACL;IACF;IAEA,MAAM,wBAAwB;QAC5B,OAAO;YACL;YACA;YACA;SACD;QACD,OAAO;YACL,GAAG;YACH,GAAG;QACL;IACF;IAEA,MAAM,mBAAmB;QACvB,OAAO;YACL;YACA;YACA;YACA;SACD;QACD,YAAY;YACV,GAAG;YACH,GAAG;QACL;QACA,UAAU;QACV,UAAU;YACR;eACG;YACH;gBACE,OAAO;gBACP,KAAK;gBACL,UAAU;gBACV,UAAU;oBACR;wBACE,OAAO;wBACP,OAAO;oBACT;uBACG;iBACJ;gBACD,WAAW;YACb;SACD;IACH;IAEA,kDAAkD;IAClD,KAAK,MAAM,WAAW,OAAO,QAAQ,CAAE;QACrC,MAAM,gBAAgB,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK;QACnE,wFAAwF;QACxF,cAAc,QAAQ,GAAG;QACzB,MAAM,WAAW;eACZ;eACA;eACA;YACH;YACA;eACG;SACJ;QACD,cAAc,QAAQ,GAAG;eACpB;YACH;gBACE,OAAO;gBACP,KAAK;gBACL,UAAU;oBACR;uBACG;iBACJ;YACH;SACD;IACH;IAEA,OAAO;QACL,MAAM;QACN,UAAU;QACV,UAAU;eACL;YACH;YACA;YACA;YACA;YACA;YACA;YACA;YACA;gBACE,eAAe;gBACf,KAAK;gBACL,UAAU;uBAAK;iBAAU;gBACzB,WAAW;YACb;YACA;eACG;eACA;eACA;YACH;YACA;eACG;eACA;YACH;YACA;SACD;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 12573, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/typescript.js"], "sourcesContent": ["const IDENT_RE = '[A-Za-z$_][0-9A-Za-z$_]*';\nconst KEYWORDS = [\n  \"as\", // for exports\n  \"in\",\n  \"of\",\n  \"if\",\n  \"for\",\n  \"while\",\n  \"finally\",\n  \"var\",\n  \"new\",\n  \"function\",\n  \"do\",\n  \"return\",\n  \"void\",\n  \"else\",\n  \"break\",\n  \"catch\",\n  \"instanceof\",\n  \"with\",\n  \"throw\",\n  \"case\",\n  \"default\",\n  \"try\",\n  \"switch\",\n  \"continue\",\n  \"typeof\",\n  \"delete\",\n  \"let\",\n  \"yield\",\n  \"const\",\n  \"class\",\n  // J<PERSON> handles these with a special rule\n  // \"get\",\n  // \"set\",\n  \"debugger\",\n  \"async\",\n  \"await\",\n  \"static\",\n  \"import\",\n  \"from\",\n  \"export\",\n  \"extends\",\n  // It's reached stage 3, which is \"recommended for implementation\":\n  \"using\"\n];\nconst LITERALS = [\n  \"true\",\n  \"false\",\n  \"null\",\n  \"undefined\",\n  \"NaN\",\n  \"Infinity\"\n];\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects\nconst TYPES = [\n  // Fundamental objects\n  \"Object\",\n  \"Function\",\n  \"Boolean\",\n  \"Symbol\",\n  // numbers and dates\n  \"Math\",\n  \"Date\",\n  \"Number\",\n  \"BigInt\",\n  // text\n  \"String\",\n  \"RegExp\",\n  // Indexed collections\n  \"Array\",\n  \"Float32Array\",\n  \"Float64Array\",\n  \"Int8Array\",\n  \"Uint8Array\",\n  \"Uint8ClampedArray\",\n  \"Int16Array\",\n  \"Int32Array\",\n  \"Uint16Array\",\n  \"Uint32Array\",\n  \"BigInt64Array\",\n  \"BigUint64Array\",\n  // Keyed collections\n  \"Set\",\n  \"Map\",\n  \"WeakSet\",\n  \"WeakMap\",\n  // Structured data\n  \"ArrayBuffer\",\n  \"SharedArrayBuffer\",\n  \"Atomics\",\n  \"DataView\",\n  \"JSON\",\n  // Control abstraction objects\n  \"Promise\",\n  \"Generator\",\n  \"GeneratorFunction\",\n  \"AsyncFunction\",\n  // Reflection\n  \"Reflect\",\n  \"Proxy\",\n  // Internationalization\n  \"Intl\",\n  // WebAssembly\n  \"WebAssembly\"\n];\n\nconst ERROR_TYPES = [\n  \"Error\",\n  \"EvalError\",\n  \"InternalError\",\n  \"RangeError\",\n  \"ReferenceError\",\n  \"SyntaxError\",\n  \"TypeError\",\n  \"URIError\"\n];\n\nconst BUILT_IN_GLOBALS = [\n  \"setInterval\",\n  \"setTimeout\",\n  \"clearInterval\",\n  \"clearTimeout\",\n\n  \"require\",\n  \"exports\",\n\n  \"eval\",\n  \"isFinite\",\n  \"isNaN\",\n  \"parseFloat\",\n  \"parseInt\",\n  \"decodeURI\",\n  \"decodeURIComponent\",\n  \"encodeURI\",\n  \"encodeURIComponent\",\n  \"escape\",\n  \"unescape\"\n];\n\nconst BUILT_IN_VARIABLES = [\n  \"arguments\",\n  \"this\",\n  \"super\",\n  \"console\",\n  \"window\",\n  \"document\",\n  \"localStorage\",\n  \"sessionStorage\",\n  \"module\",\n  \"global\" // Node.js\n];\n\nconst BUILT_INS = [].concat(\n  BUILT_IN_GLOBALS,\n  TYPES,\n  ERROR_TYPES\n);\n\n/*\nLanguage: JavaScript\nDescription: JavaScript (JS) is a lightweight, interpreted, or just-in-time compiled programming language with first-class functions.\nCategory: common, scripting, web\nWebsite: https://developer.mozilla.org/en-US/docs/Web/JavaScript\n*/\n\n\n/** @type LanguageFn */\nfunction javascript(hljs) {\n  const regex = hljs.regex;\n  /**\n   * Takes a string like \"<Booger\" and checks to see\n   * if we can find a matching \"</Booger\" later in the\n   * content.\n   * @param {RegExpMatchArray} match\n   * @param {{after:number}} param1\n   */\n  const hasClosingTag = (match, { after }) => {\n    const tag = \"</\" + match[0].slice(1);\n    const pos = match.input.indexOf(tag, after);\n    return pos !== -1;\n  };\n\n  const IDENT_RE$1 = IDENT_RE;\n  const FRAGMENT = {\n    begin: '<>',\n    end: '</>'\n  };\n  // to avoid some special cases inside isTrulyOpeningTag\n  const XML_SELF_CLOSING = /<[A-Za-z0-9\\\\._:-]+\\s*\\/>/;\n  const XML_TAG = {\n    begin: /<[A-Za-z0-9\\\\._:-]+/,\n    end: /\\/[A-Za-z0-9\\\\._:-]+>|\\/>/,\n    /**\n     * @param {RegExpMatchArray} match\n     * @param {CallbackResponse} response\n     */\n    isTrulyOpeningTag: (match, response) => {\n      const afterMatchIndex = match[0].length + match.index;\n      const nextChar = match.input[afterMatchIndex];\n      if (\n        // HTML should not include another raw `<` inside a tag\n        // nested type?\n        // `<Array<Array<number>>`, etc.\n        nextChar === \"<\" ||\n        // the , gives away that this is not HTML\n        // `<T, A extends keyof T, V>`\n        nextChar === \",\"\n        ) {\n        response.ignoreMatch();\n        return;\n      }\n\n      // `<something>`\n      // Quite possibly a tag, lets look for a matching closing tag...\n      if (nextChar === \">\") {\n        // if we cannot find a matching closing tag, then we\n        // will ignore it\n        if (!hasClosingTag(match, { after: afterMatchIndex })) {\n          response.ignoreMatch();\n        }\n      }\n\n      // `<blah />` (self-closing)\n      // handled by simpleSelfClosing rule\n\n      let m;\n      const afterMatch = match.input.substring(afterMatchIndex);\n\n      // some more template typing stuff\n      //  <T = any>(key?: string) => Modify<\n      if ((m = afterMatch.match(/^\\s*=/))) {\n        response.ignoreMatch();\n        return;\n      }\n\n      // `<From extends string>`\n      // technically this could be HTML, but it smells like a type\n      // NOTE: This is ugh, but added specifically for https://github.com/highlightjs/highlight.js/issues/3276\n      if ((m = afterMatch.match(/^\\s+extends\\s+/))) {\n        if (m.index === 0) {\n          response.ignoreMatch();\n          // eslint-disable-next-line no-useless-return\n          return;\n        }\n      }\n    }\n  };\n  const KEYWORDS$1 = {\n    $pattern: IDENT_RE,\n    keyword: KEYWORDS,\n    literal: LITERALS,\n    built_in: BUILT_INS,\n    \"variable.language\": BUILT_IN_VARIABLES\n  };\n\n  // https://tc39.es/ecma262/#sec-literals-numeric-literals\n  const decimalDigits = '[0-9](_?[0-9])*';\n  const frac = `\\\\.(${decimalDigits})`;\n  // DecimalIntegerLiteral, including Annex B NonOctalDecimalIntegerLiteral\n  // https://tc39.es/ecma262/#sec-additional-syntax-numeric-literals\n  const decimalInteger = `0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*`;\n  const NUMBER = {\n    className: 'number',\n    variants: [\n      // DecimalLiteral\n      { begin: `(\\\\b(${decimalInteger})((${frac})|\\\\.)?|(${frac}))` +\n        `[eE][+-]?(${decimalDigits})\\\\b` },\n      { begin: `\\\\b(${decimalInteger})\\\\b((${frac})\\\\b|\\\\.)?|(${frac})\\\\b` },\n\n      // DecimalBigIntegerLiteral\n      { begin: `\\\\b(0|[1-9](_?[0-9])*)n\\\\b` },\n\n      // NonDecimalIntegerLiteral\n      { begin: \"\\\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\\\b\" },\n      { begin: \"\\\\b0[bB][0-1](_?[0-1])*n?\\\\b\" },\n      { begin: \"\\\\b0[oO][0-7](_?[0-7])*n?\\\\b\" },\n\n      // LegacyOctalIntegerLiteral (does not include underscore separators)\n      // https://tc39.es/ecma262/#sec-additional-syntax-numeric-literals\n      { begin: \"\\\\b0[0-7]+n?\\\\b\" },\n    ],\n    relevance: 0\n  };\n\n  const SUBST = {\n    className: 'subst',\n    begin: '\\\\$\\\\{',\n    end: '\\\\}',\n    keywords: KEYWORDS$1,\n    contains: [] // defined later\n  };\n  const HTML_TEMPLATE = {\n    begin: '\\.?html`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [\n        hljs.BACKSLASH_ESCAPE,\n        SUBST\n      ],\n      subLanguage: 'xml'\n    }\n  };\n  const CSS_TEMPLATE = {\n    begin: '\\.?css`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [\n        hljs.BACKSLASH_ESCAPE,\n        SUBST\n      ],\n      subLanguage: 'css'\n    }\n  };\n  const GRAPHQL_TEMPLATE = {\n    begin: '\\.?gql`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [\n        hljs.BACKSLASH_ESCAPE,\n        SUBST\n      ],\n      subLanguage: 'graphql'\n    }\n  };\n  const TEMPLATE_STRING = {\n    className: 'string',\n    begin: '`',\n    end: '`',\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      SUBST\n    ]\n  };\n  const JSDOC_COMMENT = hljs.COMMENT(\n    /\\/\\*\\*(?!\\/)/,\n    '\\\\*/',\n    {\n      relevance: 0,\n      contains: [\n        {\n          begin: '(?=@[A-Za-z]+)',\n          relevance: 0,\n          contains: [\n            {\n              className: 'doctag',\n              begin: '@[A-Za-z]+'\n            },\n            {\n              className: 'type',\n              begin: '\\\\{',\n              end: '\\\\}',\n              excludeEnd: true,\n              excludeBegin: true,\n              relevance: 0\n            },\n            {\n              className: 'variable',\n              begin: IDENT_RE$1 + '(?=\\\\s*(-)|$)',\n              endsParent: true,\n              relevance: 0\n            },\n            // eat spaces (not newlines) so we can find\n            // types or variables\n            {\n              begin: /(?=[^\\n])\\s/,\n              relevance: 0\n            }\n          ]\n        }\n      ]\n    }\n  );\n  const COMMENT = {\n    className: \"comment\",\n    variants: [\n      JSDOC_COMMENT,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.C_LINE_COMMENT_MODE\n    ]\n  };\n  const SUBST_INTERNALS = [\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE,\n    HTML_TEMPLATE,\n    CSS_TEMPLATE,\n    GRAPHQL_TEMPLATE,\n    TEMPLATE_STRING,\n    // Skip numbers when they are part of a variable name\n    { match: /\\$\\d+/ },\n    NUMBER,\n    // This is intentional:\n    // See https://github.com/highlightjs/highlight.js/issues/3288\n    // hljs.REGEXP_MODE\n  ];\n  SUBST.contains = SUBST_INTERNALS\n    .concat({\n      // we need to pair up {} inside our subst to prevent\n      // it from ending too early by matching another }\n      begin: /\\{/,\n      end: /\\}/,\n      keywords: KEYWORDS$1,\n      contains: [\n        \"self\"\n      ].concat(SUBST_INTERNALS)\n    });\n  const SUBST_AND_COMMENTS = [].concat(COMMENT, SUBST.contains);\n  const PARAMS_CONTAINS = SUBST_AND_COMMENTS.concat([\n    // eat recursive parens in sub expressions\n    {\n      begin: /(\\s*)\\(/,\n      end: /\\)/,\n      keywords: KEYWORDS$1,\n      contains: [\"self\"].concat(SUBST_AND_COMMENTS)\n    }\n  ]);\n  const PARAMS = {\n    className: 'params',\n    // convert this to negative lookbehind in v12\n    begin: /(\\s*)\\(/, // to match the parms with\n    end: /\\)/,\n    excludeBegin: true,\n    excludeEnd: true,\n    keywords: KEYWORDS$1,\n    contains: PARAMS_CONTAINS\n  };\n\n  // ES6 classes\n  const CLASS_OR_EXTENDS = {\n    variants: [\n      // class Car extends vehicle\n      {\n        match: [\n          /class/,\n          /\\s+/,\n          IDENT_RE$1,\n          /\\s+/,\n          /extends/,\n          /\\s+/,\n          regex.concat(IDENT_RE$1, \"(\", regex.concat(/\\./, IDENT_RE$1), \")*\")\n        ],\n        scope: {\n          1: \"keyword\",\n          3: \"title.class\",\n          5: \"keyword\",\n          7: \"title.class.inherited\"\n        }\n      },\n      // class Car\n      {\n        match: [\n          /class/,\n          /\\s+/,\n          IDENT_RE$1\n        ],\n        scope: {\n          1: \"keyword\",\n          3: \"title.class\"\n        }\n      },\n\n    ]\n  };\n\n  const CLASS_REFERENCE = {\n    relevance: 0,\n    match:\n    regex.either(\n      // Hard coded exceptions\n      /\\bJSON/,\n      // Float32Array, OutT\n      /\\b[A-Z][a-z]+([A-Z][a-z]*|\\d)*/,\n      // CSSFactory, CSSFactoryT\n      /\\b[A-Z]{2,}([A-Z][a-z]+|\\d)+([A-Z][a-z]*)*/,\n      // FPs, FPsT\n      /\\b[A-Z]{2,}[a-z]+([A-Z][a-z]+|\\d)*([A-Z][a-z]*)*/,\n      // P\n      // single letters are not highlighted\n      // BLAH\n      // this will be flagged as a UPPER_CASE_CONSTANT instead\n    ),\n    className: \"title.class\",\n    keywords: {\n      _: [\n        // se we still get relevance credit for JS library classes\n        ...TYPES,\n        ...ERROR_TYPES\n      ]\n    }\n  };\n\n  const USE_STRICT = {\n    label: \"use_strict\",\n    className: 'meta',\n    relevance: 10,\n    begin: /^\\s*['\"]use (strict|asm)['\"]/\n  };\n\n  const FUNCTION_DEFINITION = {\n    variants: [\n      {\n        match: [\n          /function/,\n          /\\s+/,\n          IDENT_RE$1,\n          /(?=\\s*\\()/\n        ]\n      },\n      // anonymous function\n      {\n        match: [\n          /function/,\n          /\\s*(?=\\()/\n        ]\n      }\n    ],\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    label: \"func.def\",\n    contains: [ PARAMS ],\n    illegal: /%/\n  };\n\n  const UPPER_CASE_CONSTANT = {\n    relevance: 0,\n    match: /\\b[A-Z][A-Z_0-9]+\\b/,\n    className: \"variable.constant\"\n  };\n\n  function noneOf(list) {\n    return regex.concat(\"(?!\", list.join(\"|\"), \")\");\n  }\n\n  const FUNCTION_CALL = {\n    match: regex.concat(\n      /\\b/,\n      noneOf([\n        ...BUILT_IN_GLOBALS,\n        \"super\",\n        \"import\"\n      ].map(x => `${x}\\\\s*\\\\(`)),\n      IDENT_RE$1, regex.lookahead(/\\s*\\(/)),\n    className: \"title.function\",\n    relevance: 0\n  };\n\n  const PROPERTY_ACCESS = {\n    begin: regex.concat(/\\./, regex.lookahead(\n      regex.concat(IDENT_RE$1, /(?![0-9A-Za-z$_(])/)\n    )),\n    end: IDENT_RE$1,\n    excludeBegin: true,\n    keywords: \"prototype\",\n    className: \"property\",\n    relevance: 0\n  };\n\n  const GETTER_OR_SETTER = {\n    match: [\n      /get|set/,\n      /\\s+/,\n      IDENT_RE$1,\n      /(?=\\()/\n    ],\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    contains: [\n      { // eat to avoid empty params\n        begin: /\\(\\)/\n      },\n      PARAMS\n    ]\n  };\n\n  const FUNC_LEAD_IN_RE = '(\\\\(' +\n    '[^()]*(\\\\(' +\n    '[^()]*(\\\\(' +\n    '[^()]*' +\n    '\\\\)[^()]*)*' +\n    '\\\\)[^()]*)*' +\n    '\\\\)|' + hljs.UNDERSCORE_IDENT_RE + ')\\\\s*=>';\n\n  const FUNCTION_VARIABLE = {\n    match: [\n      /const|var|let/, /\\s+/,\n      IDENT_RE$1, /\\s*/,\n      /=\\s*/,\n      /(async\\s*)?/, // async is optional\n      regex.lookahead(FUNC_LEAD_IN_RE)\n    ],\n    keywords: \"async\",\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    contains: [\n      PARAMS\n    ]\n  };\n\n  return {\n    name: 'JavaScript',\n    aliases: ['js', 'jsx', 'mjs', 'cjs'],\n    keywords: KEYWORDS$1,\n    // this will be extended by TypeScript\n    exports: { PARAMS_CONTAINS, CLASS_REFERENCE },\n    illegal: /#(?![$_A-z])/,\n    contains: [\n      hljs.SHEBANG({\n        label: \"shebang\",\n        binary: \"node\",\n        relevance: 5\n      }),\n      USE_STRICT,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      HTML_TEMPLATE,\n      CSS_TEMPLATE,\n      GRAPHQL_TEMPLATE,\n      TEMPLATE_STRING,\n      COMMENT,\n      // Skip numbers when they are part of a variable name\n      { match: /\\$\\d+/ },\n      NUMBER,\n      CLASS_REFERENCE,\n      {\n        scope: 'attr',\n        match: IDENT_RE$1 + regex.lookahead(':'),\n        relevance: 0\n      },\n      FUNCTION_VARIABLE,\n      { // \"value\" container\n        begin: '(' + hljs.RE_STARTERS_RE + '|\\\\b(case|return|throw)\\\\b)\\\\s*',\n        keywords: 'return throw case',\n        relevance: 0,\n        contains: [\n          COMMENT,\n          hljs.REGEXP_MODE,\n          {\n            className: 'function',\n            // we have to count the parens to make sure we actually have the\n            // correct bounding ( ) before the =>.  There could be any number of\n            // sub-expressions inside also surrounded by parens.\n            begin: FUNC_LEAD_IN_RE,\n            returnBegin: true,\n            end: '\\\\s*=>',\n            contains: [\n              {\n                className: 'params',\n                variants: [\n                  {\n                    begin: hljs.UNDERSCORE_IDENT_RE,\n                    relevance: 0\n                  },\n                  {\n                    className: null,\n                    begin: /\\(\\s*\\)/,\n                    skip: true\n                  },\n                  {\n                    begin: /(\\s*)\\(/,\n                    end: /\\)/,\n                    excludeBegin: true,\n                    excludeEnd: true,\n                    keywords: KEYWORDS$1,\n                    contains: PARAMS_CONTAINS\n                  }\n                ]\n              }\n            ]\n          },\n          { // could be a comma delimited list of params to a function call\n            begin: /,/,\n            relevance: 0\n          },\n          {\n            match: /\\s+/,\n            relevance: 0\n          },\n          { // JSX\n            variants: [\n              { begin: FRAGMENT.begin, end: FRAGMENT.end },\n              { match: XML_SELF_CLOSING },\n              {\n                begin: XML_TAG.begin,\n                // we carefully check the opening tag to see if it truly\n                // is a tag and not a false positive\n                'on:begin': XML_TAG.isTrulyOpeningTag,\n                end: XML_TAG.end\n              }\n            ],\n            subLanguage: 'xml',\n            contains: [\n              {\n                begin: XML_TAG.begin,\n                end: XML_TAG.end,\n                skip: true,\n                contains: ['self']\n              }\n            ]\n          }\n        ],\n      },\n      FUNCTION_DEFINITION,\n      {\n        // prevent this from getting swallowed up by function\n        // since they appear \"function like\"\n        beginKeywords: \"while if switch catch for\"\n      },\n      {\n        // we have to count the parens to make sure we actually have the correct\n        // bounding ( ).  There could be any number of sub-expressions inside\n        // also surrounded by parens.\n        begin: '\\\\b(?!function)' + hljs.UNDERSCORE_IDENT_RE +\n          '\\\\(' + // first parens\n          '[^()]*(\\\\(' +\n            '[^()]*(\\\\(' +\n              '[^()]*' +\n            '\\\\)[^()]*)*' +\n          '\\\\)[^()]*)*' +\n          '\\\\)\\\\s*\\\\{', // end parens\n        returnBegin:true,\n        label: \"func.def\",\n        contains: [\n          PARAMS,\n          hljs.inherit(hljs.TITLE_MODE, { begin: IDENT_RE$1, className: \"title.function\" })\n        ]\n      },\n      // catch ... so it won't trigger the property rule below\n      {\n        match: /\\.\\.\\./,\n        relevance: 0\n      },\n      PROPERTY_ACCESS,\n      // hack: prevents detection of keywords in some circumstances\n      // .keyword()\n      // $keyword = x\n      {\n        match: '\\\\$' + IDENT_RE$1,\n        relevance: 0\n      },\n      {\n        match: [ /\\bconstructor(?=\\s*\\()/ ],\n        className: { 1: \"title.function\" },\n        contains: [ PARAMS ]\n      },\n      FUNCTION_CALL,\n      UPPER_CASE_CONSTANT,\n      CLASS_OR_EXTENDS,\n      GETTER_OR_SETTER,\n      {\n        match: /\\$[(.]/ // relevance booster for a pattern common to JS libs: `$(something)` and `$.something`\n      }\n    ]\n  };\n}\n\n/*\nLanguage: TypeScript\nAuthor: Panu Horsmalahti <<EMAIL>>\nContributors: Ike Ku <<EMAIL>>\nDescription: TypeScript is a strict superset of JavaScript\nWebsite: https://www.typescriptlang.org\nCategory: common, scripting\n*/\n\n\n/** @type LanguageFn */\nfunction typescript(hljs) {\n  const regex = hljs.regex;\n  const tsLanguage = javascript(hljs);\n\n  const IDENT_RE$1 = IDENT_RE;\n  const TYPES = [\n    \"any\",\n    \"void\",\n    \"number\",\n    \"boolean\",\n    \"string\",\n    \"object\",\n    \"never\",\n    \"symbol\",\n    \"bigint\",\n    \"unknown\"\n  ];\n  const NAMESPACE = {\n    begin: [\n      /namespace/,\n      /\\s+/,\n      hljs.IDENT_RE\n    ],\n    beginScope: {\n      1: \"keyword\",\n      3: \"title.class\"\n    }\n  };\n  const INTERFACE = {\n    beginKeywords: 'interface',\n    end: /\\{/,\n    excludeEnd: true,\n    keywords: {\n      keyword: 'interface extends',\n      built_in: TYPES\n    },\n    contains: [ tsLanguage.exports.CLASS_REFERENCE ]\n  };\n  const USE_STRICT = {\n    className: 'meta',\n    relevance: 10,\n    begin: /^\\s*['\"]use strict['\"]/\n  };\n  const TS_SPECIFIC_KEYWORDS = [\n    \"type\",\n    // \"namespace\",\n    \"interface\",\n    \"public\",\n    \"private\",\n    \"protected\",\n    \"implements\",\n    \"declare\",\n    \"abstract\",\n    \"readonly\",\n    \"enum\",\n    \"override\",\n    \"satisfies\"\n  ];\n  /*\n    namespace is a TS keyword but it's fine to use it as a variable name too.\n    const message = 'foo';\n    const namespace = 'bar';\n  */\n  const KEYWORDS$1 = {\n    $pattern: IDENT_RE,\n    keyword: KEYWORDS.concat(TS_SPECIFIC_KEYWORDS),\n    literal: LITERALS,\n    built_in: BUILT_INS.concat(TYPES),\n    \"variable.language\": BUILT_IN_VARIABLES\n  };\n\n  const DECORATOR = {\n    className: 'meta',\n    begin: '@' + IDENT_RE$1,\n  };\n\n  const swapMode = (mode, label, replacement) => {\n    const indx = mode.contains.findIndex(m => m.label === label);\n    if (indx === -1) { throw new Error(\"can not find mode to replace\"); }\n\n    mode.contains.splice(indx, 1, replacement);\n  };\n\n\n  // this should update anywhere keywords is used since\n  // it will be the same actual JS object\n  Object.assign(tsLanguage.keywords, KEYWORDS$1);\n\n  tsLanguage.exports.PARAMS_CONTAINS.push(DECORATOR);\n\n  // highlight the function params\n  const ATTRIBUTE_HIGHLIGHT = tsLanguage.contains.find(c => c.scope === \"attr\");\n\n  // take default attr rule and extend it to support optionals\n  const OPTIONAL_KEY_OR_ARGUMENT = Object.assign({},\n    ATTRIBUTE_HIGHLIGHT,\n    { match: regex.concat(IDENT_RE$1, regex.lookahead(/\\s*\\?:/)) }\n  );\n  tsLanguage.exports.PARAMS_CONTAINS.push([\n    tsLanguage.exports.CLASS_REFERENCE, // class reference for highlighting the params types\n    ATTRIBUTE_HIGHLIGHT, // highlight the params key\n    OPTIONAL_KEY_OR_ARGUMENT, // Added for optional property assignment highlighting\n  ]);\n\n  // Add the optional property assignment highlighting for objects or classes\n  tsLanguage.contains = tsLanguage.contains.concat([\n    DECORATOR,\n    NAMESPACE,\n    INTERFACE,\n    OPTIONAL_KEY_OR_ARGUMENT, // Added for optional property assignment highlighting\n  ]);\n\n  // TS gets a simpler shebang rule than JS\n  swapMode(tsLanguage, \"shebang\", hljs.SHEBANG());\n  // JS use strict rule purposely excludes `asm` which makes no sense\n  swapMode(tsLanguage, \"use_strict\", USE_STRICT);\n\n  const functionDeclaration = tsLanguage.contains.find(m => m.label === \"func.def\");\n  functionDeclaration.relevance = 0; // () => {} is more typical in TypeScript\n\n  Object.assign(tsLanguage, {\n    name: 'TypeScript',\n    aliases: [\n      'ts',\n      'tsx',\n      'mts',\n      'cts'\n    ]\n  });\n\n  return tsLanguage;\n}\n\nexport { typescript as default };\n"], "names": [], "mappings": ";;;AAAA,MAAM,WAAW;AACjB,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,uCAAuC;IACvC,SAAS;IACT,SAAS;IACT;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,mEAAmE;IACnE;CACD;AACD,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;IACA;CACD;AAED,mFAAmF;AACnF,MAAM,QAAQ;IACZ,sBAAsB;IACtB;IACA;IACA;IACA;IACA,oBAAoB;IACpB;IACA;IACA;IACA;IACA,OAAO;IACP;IACA;IACA,sBAAsB;IACtB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,oBAAoB;IACpB;IACA;IACA;IACA;IACA,kBAAkB;IAClB;IACA;IACA;IACA;IACA;IACA,8BAA8B;IAC9B;IACA;IACA;IACA;IACA,aAAa;IACb;IACA;IACA,uBAAuB;IACvB;IACA,cAAc;IACd;CACD;AAED,MAAM,cAAc;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,mBAAmB;IACvB;IACA;IACA;IACA;IAEA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,SAAS,UAAU;CACpB;AAED,MAAM,YAAY,EAAE,CAAC,MAAM,CACzB,kBACA,OACA;AAGF;;;;;AAKA,GAGA,qBAAqB,GACrB,SAAS,WAAW,IAAI;IACtB,MAAM,QAAQ,KAAK,KAAK;IACxB;;;;;;GAMC,GACD,MAAM,gBAAgB,CAAC,OAAO,EAAE,KAAK,EAAE;QACrC,MAAM,MAAM,OAAO,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC;QAClC,MAAM,MAAM,MAAM,KAAK,CAAC,OAAO,CAAC,KAAK;QACrC,OAAO,QAAQ,CAAC;IAClB;IAEA,MAAM,aAAa;IACnB,MAAM,WAAW;QACf,OAAO;QACP,KAAK;IACP;IACA,uDAAuD;IACvD,MAAM,mBAAmB;IACzB,MAAM,UAAU;QACd,OAAO;QACP,KAAK;QACL;;;KAGC,GACD,mBAAmB,CAAC,OAAO;YACzB,MAAM,kBAAkB,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG,MAAM,KAAK;YACrD,MAAM,WAAW,MAAM,KAAK,CAAC,gBAAgB;YAC7C,IACE,uDAAuD;YACvD,eAAe;YACf,gCAAgC;YAChC,aAAa,OACb,yCAAyC;YACzC,8BAA8B;YAC9B,aAAa,KACX;gBACF,SAAS,WAAW;gBACpB;YACF;YAEA,gBAAgB;YAChB,gEAAgE;YAChE,IAAI,aAAa,KAAK;gBACpB,oDAAoD;gBACpD,iBAAiB;gBACjB,IAAI,CAAC,cAAc,OAAO;oBAAE,OAAO;gBAAgB,IAAI;oBACrD,SAAS,WAAW;gBACtB;YACF;YAEA,4BAA4B;YAC5B,oCAAoC;YAEpC,IAAI;YACJ,MAAM,aAAa,MAAM,KAAK,CAAC,SAAS,CAAC;YAEzC,kCAAkC;YAClC,sCAAsC;YACtC,IAAK,IAAI,WAAW,KAAK,CAAC,UAAW;gBACnC,SAAS,WAAW;gBACpB;YACF;YAEA,0BAA0B;YAC1B,4DAA4D;YAC5D,wGAAwG;YACxG,IAAK,IAAI,WAAW,KAAK,CAAC,mBAAoB;gBAC5C,IAAI,EAAE,KAAK,KAAK,GAAG;oBACjB,SAAS,WAAW;oBACpB,6CAA6C;oBAC7C;gBACF;YACF;QACF;IACF;IACA,MAAM,aAAa;QACjB,UAAU;QACV,SAAS;QACT,SAAS;QACT,UAAU;QACV,qBAAqB;IACvB;IAEA,yDAAyD;IACzD,MAAM,gBAAgB;IACtB,MAAM,OAAO,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;IACpC,yEAAyE;IACzE,kEAAkE;IAClE,MAAM,iBAAiB,CAAC,mCAAmC,CAAC;IAC5D,MAAM,SAAS;QACb,WAAW;QACX,UAAU;YACR,iBAAiB;YACjB;gBAAE,OAAO,CAAC,KAAK,EAAE,eAAe,GAAG,EAAE,KAAK,SAAS,EAAE,KAAK,EAAE,CAAC,GAC3D,CAAC,UAAU,EAAE,cAAc,IAAI,CAAC;YAAC;YACnC;gBAAE,OAAO,CAAC,IAAI,EAAE,eAAe,MAAM,EAAE,KAAK,YAAY,EAAE,KAAK,IAAI,CAAC;YAAC;YAErE,2BAA2B;YAC3B;gBAAE,OAAO,CAAC,0BAA0B,CAAC;YAAC;YAEtC,2BAA2B;YAC3B;gBAAE,OAAO;YAA2C;YACpD;gBAAE,OAAO;YAA+B;YACxC;gBAAE,OAAO;YAA+B;YAExC,qEAAqE;YACrE,kEAAkE;YAClE;gBAAE,OAAO;YAAkB;SAC5B;QACD,WAAW;IACb;IAEA,MAAM,QAAQ;QACZ,WAAW;QACX,OAAO;QACP,KAAK;QACL,UAAU;QACV,UAAU,EAAE,CAAC,gBAAgB;IAC/B;IACA,MAAM,gBAAgB;QACpB,OAAO;QACP,KAAK;QACL,QAAQ;YACN,KAAK;YACL,WAAW;YACX,UAAU;gBACR,KAAK,gBAAgB;gBACrB;aACD;YACD,aAAa;QACf;IACF;IACA,MAAM,eAAe;QACnB,OAAO;QACP,KAAK;QACL,QAAQ;YACN,KAAK;YACL,WAAW;YACX,UAAU;gBACR,KAAK,gBAAgB;gBACrB;aACD;YACD,aAAa;QACf;IACF;IACA,MAAM,mBAAmB;QACvB,OAAO;QACP,KAAK;QACL,QAAQ;YACN,KAAK;YACL,WAAW;YACX,UAAU;gBACR,KAAK,gBAAgB;gBACrB;aACD;YACD,aAAa;QACf;IACF;IACA,MAAM,kBAAkB;QACtB,WAAW;QACX,OAAO;QACP,KAAK;QACL,UAAU;YACR,KAAK,gBAAgB;YACrB;SACD;IACH;IACA,MAAM,gBAAgB,KAAK,OAAO,CAChC,gBACA,QACA;QACE,WAAW;QACX,UAAU;YACR;gBACE,OAAO;gBACP,WAAW;gBACX,UAAU;oBACR;wBACE,WAAW;wBACX,OAAO;oBACT;oBACA;wBACE,WAAW;wBACX,OAAO;wBACP,KAAK;wBACL,YAAY;wBACZ,cAAc;wBACd,WAAW;oBACb;oBACA;wBACE,WAAW;wBACX,OAAO,aAAa;wBACpB,YAAY;wBACZ,WAAW;oBACb;oBACA,2CAA2C;oBAC3C,qBAAqB;oBACrB;wBACE,OAAO;wBACP,WAAW;oBACb;iBACD;YACH;SACD;IACH;IAEF,MAAM,UAAU;QACd,WAAW;QACX,UAAU;YACR;YACA,KAAK,oBAAoB;YACzB,KAAK,mBAAmB;SACzB;IACH;IACA,MAAM,kBAAkB;QACtB,KAAK,gBAAgB;QACrB,KAAK,iBAAiB;QACtB;QACA;QACA;QACA;QACA,qDAAqD;QACrD;YAAE,OAAO;QAAQ;QACjB;KAID;IACD,MAAM,QAAQ,GAAG,gBACd,MAAM,CAAC;QACN,oDAAoD;QACpD,iDAAiD;QACjD,OAAO;QACP,KAAK;QACL,UAAU;QACV,UAAU;YACR;SACD,CAAC,MAAM,CAAC;IACX;IACF,MAAM,qBAAqB,EAAE,CAAC,MAAM,CAAC,SAAS,MAAM,QAAQ;IAC5D,MAAM,kBAAkB,mBAAmB,MAAM,CAAC;QAChD,0CAA0C;QAC1C;YACE,OAAO;YACP,KAAK;YACL,UAAU;YACV,UAAU;gBAAC;aAAO,CAAC,MAAM,CAAC;QAC5B;KACD;IACD,MAAM,SAAS;QACb,WAAW;QACX,6CAA6C;QAC7C,OAAO;QACP,KAAK;QACL,cAAc;QACd,YAAY;QACZ,UAAU;QACV,UAAU;IACZ;IAEA,cAAc;IACd,MAAM,mBAAmB;QACvB,UAAU;YACR,4BAA4B;YAC5B;gBACE,OAAO;oBACL;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA,MAAM,MAAM,CAAC,YAAY,KAAK,MAAM,MAAM,CAAC,MAAM,aAAa;iBAC/D;gBACD,OAAO;oBACL,GAAG;oBACH,GAAG;oBACH,GAAG;oBACH,GAAG;gBACL;YACF;YACA,YAAY;YACZ;gBACE,OAAO;oBACL;oBACA;oBACA;iBACD;gBACD,OAAO;oBACL,GAAG;oBACH,GAAG;gBACL;YACF;SAED;IACH;IAEA,MAAM,kBAAkB;QACtB,WAAW;QACX,OACA,MAAM,MAAM,CACV,wBAAwB;QACxB,UACA,qBAAqB;QACrB,kCACA,0BAA0B;QAC1B,8CACA,YAAY;QACZ;QAMF,WAAW;QACX,UAAU;YACR,GAAG;gBACD,0DAA0D;mBACvD;mBACA;aACJ;QACH;IACF;IAEA,MAAM,aAAa;QACjB,OAAO;QACP,WAAW;QACX,WAAW;QACX,OAAO;IACT;IAEA,MAAM,sBAAsB;QAC1B,UAAU;YACR;gBACE,OAAO;oBACL;oBACA;oBACA;oBACA;iBACD;YACH;YACA,qBAAqB;YACrB;gBACE,OAAO;oBACL;oBACA;iBACD;YACH;SACD;QACD,WAAW;YACT,GAAG;YACH,GAAG;QACL;QACA,OAAO;QACP,UAAU;YAAE;SAAQ;QACpB,SAAS;IACX;IAEA,MAAM,sBAAsB;QAC1B,WAAW;QACX,OAAO;QACP,WAAW;IACb;IAEA,SAAS,OAAO,IAAI;QAClB,OAAO,MAAM,MAAM,CAAC,OAAO,KAAK,IAAI,CAAC,MAAM;IAC7C;IAEA,MAAM,gBAAgB;QACpB,OAAO,MAAM,MAAM,CACjB,MACA,OAAO;eACF;YACH;YACA;SACD,CAAC,GAAG,CAAC,CAAA,IAAK,GAAG,EAAE,OAAO,CAAC,IACxB,YAAY,MAAM,SAAS,CAAC;QAC9B,WAAW;QACX,WAAW;IACb;IAEA,MAAM,kBAAkB;QACtB,OAAO,MAAM,MAAM,CAAC,MAAM,MAAM,SAAS,CACvC,MAAM,MAAM,CAAC,YAAY;QAE3B,KAAK;QACL,cAAc;QACd,UAAU;QACV,WAAW;QACX,WAAW;IACb;IAEA,MAAM,mBAAmB;QACvB,OAAO;YACL;YACA;YACA;YACA;SACD;QACD,WAAW;YACT,GAAG;YACH,GAAG;QACL;QACA,UAAU;YACR;gBACE,OAAO;YACT;YACA;SACD;IACH;IAEA,MAAM,kBAAkB,SACtB,eACA,eACA,WACA,gBACA,gBACA,SAAS,KAAK,mBAAmB,GAAG;IAEtC,MAAM,oBAAoB;QACxB,OAAO;YACL;YAAiB;YACjB;YAAY;YACZ;YACA;YACA,MAAM,SAAS,CAAC;SACjB;QACD,UAAU;QACV,WAAW;YACT,GAAG;YACH,GAAG;QACL;QACA,UAAU;YACR;SACD;IACH;IAEA,OAAO;QACL,MAAM;QACN,SAAS;YAAC;YAAM;YAAO;YAAO;SAAM;QACpC,UAAU;QACV,sCAAsC;QACtC,SAAS;YAAE;YAAiB;QAAgB;QAC5C,SAAS;QACT,UAAU;YACR,KAAK,OAAO,CAAC;gBACX,OAAO;gBACP,QAAQ;gBACR,WAAW;YACb;YACA;YACA,KAAK,gBAAgB;YACrB,KAAK,iBAAiB;YACtB;YACA;YACA;YACA;YACA;YACA,qDAAqD;YACrD;gBAAE,OAAO;YAAQ;YACjB;YACA;YACA;gBACE,OAAO;gBACP,OAAO,aAAa,MAAM,SAAS,CAAC;gBACpC,WAAW;YACb;YACA;YACA;gBACE,OAAO,MAAM,KAAK,cAAc,GAAG;gBACnC,UAAU;gBACV,WAAW;gBACX,UAAU;oBACR;oBACA,KAAK,WAAW;oBAChB;wBACE,WAAW;wBACX,gEAAgE;wBAChE,oEAAoE;wBACpE,oDAAoD;wBACpD,OAAO;wBACP,aAAa;wBACb,KAAK;wBACL,UAAU;4BACR;gCACE,WAAW;gCACX,UAAU;oCACR;wCACE,OAAO,KAAK,mBAAmB;wCAC/B,WAAW;oCACb;oCACA;wCACE,WAAW;wCACX,OAAO;wCACP,MAAM;oCACR;oCACA;wCACE,OAAO;wCACP,KAAK;wCACL,cAAc;wCACd,YAAY;wCACZ,UAAU;wCACV,UAAU;oCACZ;iCACD;4BACH;yBACD;oBACH;oBACA;wBACE,OAAO;wBACP,WAAW;oBACb;oBACA;wBACE,OAAO;wBACP,WAAW;oBACb;oBACA;wBACE,UAAU;4BACR;gCAAE,OAAO,SAAS,KAAK;gCAAE,KAAK,SAAS,GAAG;4BAAC;4BAC3C;gCAAE,OAAO;4BAAiB;4BAC1B;gCACE,OAAO,QAAQ,KAAK;gCACpB,wDAAwD;gCACxD,oCAAoC;gCACpC,YAAY,QAAQ,iBAAiB;gCACrC,KAAK,QAAQ,GAAG;4BAClB;yBACD;wBACD,aAAa;wBACb,UAAU;4BACR;gCACE,OAAO,QAAQ,KAAK;gCACpB,KAAK,QAAQ,GAAG;gCAChB,MAAM;gCACN,UAAU;oCAAC;iCAAO;4BACpB;yBACD;oBACH;iBACD;YACH;YACA;YACA;gBACE,qDAAqD;gBACrD,oCAAoC;gBACpC,eAAe;YACjB;YACA;gBACE,wEAAwE;gBACxE,qEAAqE;gBACrE,6BAA6B;gBAC7B,OAAO,oBAAoB,KAAK,mBAAmB,GACjD,QAAQ,eAAe;gBACvB,eACE,eACE,WACF,gBACF,gBACA;gBACF,aAAY;gBACZ,OAAO;gBACP,UAAU;oBACR;oBACA,KAAK,OAAO,CAAC,KAAK,UAAU,EAAE;wBAAE,OAAO;wBAAY,WAAW;oBAAiB;iBAChF;YACH;YACA,wDAAwD;YACxD;gBACE,OAAO;gBACP,WAAW;YACb;YACA;YACA,6DAA6D;YAC7D,aAAa;YACb,eAAe;YACf;gBACE,OAAO,QAAQ;gBACf,WAAW;YACb;YACA;gBACE,OAAO;oBAAE;iBAA0B;gBACnC,WAAW;oBAAE,GAAG;gBAAiB;gBACjC,UAAU;oBAAE;iBAAQ;YACtB;YACA;YACA;YACA;YACA;YACA;gBACE,OAAO,SAAS,sFAAsF;YACxG;SACD;IACH;AACF;AAEA;;;;;;;AAOA,GAGA,qBAAqB,GACrB,SAAS,WAAW,IAAI;IACtB,MAAM,QAAQ,KAAK,KAAK;IACxB,MAAM,aAAa,WAAW;IAE9B,MAAM,aAAa;IACnB,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,YAAY;QAChB,OAAO;YACL;YACA;YACA,KAAK,QAAQ;SACd;QACD,YAAY;YACV,GAAG;YACH,GAAG;QACL;IACF;IACA,MAAM,YAAY;QAChB,eAAe;QACf,KAAK;QACL,YAAY;QACZ,UAAU;YACR,SAAS;YACT,UAAU;QACZ;QACA,UAAU;YAAE,WAAW,OAAO,CAAC,eAAe;SAAE;IAClD;IACA,MAAM,aAAa;QACjB,WAAW;QACX,WAAW;QACX,OAAO;IACT;IACA,MAAM,uBAAuB;QAC3B;QACA,eAAe;QACf;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD;;;;EAIA,GACA,MAAM,aAAa;QACjB,UAAU;QACV,SAAS,SAAS,MAAM,CAAC;QACzB,SAAS;QACT,UAAU,UAAU,MAAM,CAAC;QAC3B,qBAAqB;IACvB;IAEA,MAAM,YAAY;QAChB,WAAW;QACX,OAAO,MAAM;IACf;IAEA,MAAM,WAAW,CAAC,MAAM,OAAO;QAC7B,MAAM,OAAO,KAAK,QAAQ,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;QACtD,IAAI,SAAS,CAAC,GAAG;YAAE,MAAM,IAAI,MAAM;QAAiC;QAEpE,KAAK,QAAQ,CAAC,MAAM,CAAC,MAAM,GAAG;IAChC;IAGA,qDAAqD;IACrD,uCAAuC;IACvC,OAAO,MAAM,CAAC,WAAW,QAAQ,EAAE;IAEnC,WAAW,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC;IAExC,gCAAgC;IAChC,MAAM,sBAAsB,WAAW,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;IAEtE,4DAA4D;IAC5D,MAAM,2BAA2B,OAAO,MAAM,CAAC,CAAC,GAC9C,qBACA;QAAE,OAAO,MAAM,MAAM,CAAC,YAAY,MAAM,SAAS,CAAC;IAAW;IAE/D,WAAW,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC;QACtC,WAAW,OAAO,CAAC,eAAe;QAClC;QACA;KACD;IAED,2EAA2E;IAC3E,WAAW,QAAQ,GAAG,WAAW,QAAQ,CAAC,MAAM,CAAC;QAC/C;QACA;QACA;QACA;KACD;IAED,yCAAyC;IACzC,SAAS,YAAY,WAAW,KAAK,OAAO;IAC5C,mEAAmE;IACnE,SAAS,YAAY,cAAc;IAEnC,MAAM,sBAAsB,WAAW,QAAQ,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;IACtE,oBAAoB,SAAS,GAAG,GAAG,yCAAyC;IAE5E,OAAO,MAAM,CAAC,YAAY;QACxB,MAAM;QACN,SAAS;YACP;YACA;YACA;YACA;SACD;IACH;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 13446, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/vbnet.js"], "sourcesContent": ["/*\nLanguage: Visual Basic .NET\nDescription: Visual Basic .NET (VB.NET) is a multi-paradigm, object-oriented programming language, implemented on the .NET Framework.\nAuthors: <AUTHORS>\nWebsite: https://docs.microsoft.com/dotnet/visual-basic/getting-started\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction vbnet(hljs) {\n  const regex = hljs.regex;\n  /**\n   * Character Literal\n   * Either a single character (\"a\"C) or an escaped double quote (\"\"\"\"C).\n   */\n  const CHARACTER = {\n    className: 'string',\n    begin: /\"(\"\"|[^/n])\"C\\b/\n  };\n\n  const STRING = {\n    className: 'string',\n    begin: /\"/,\n    end: /\"/,\n    illegal: /\\n/,\n    contains: [\n      {\n        // double quote escape\n        begin: /\"\"/ }\n    ]\n  };\n\n  /** Date Literals consist of a date, a time, or both separated by whitespace, surrounded by # */\n  const MM_DD_YYYY = /\\d{1,2}\\/\\d{1,2}\\/\\d{4}/;\n  const YYYY_MM_DD = /\\d{4}-\\d{1,2}-\\d{1,2}/;\n  const TIME_12H = /(\\d|1[012])(:\\d+){0,2} *(AM|PM)/;\n  const TIME_24H = /\\d{1,2}(:\\d{1,2}){1,2}/;\n  const DATE = {\n    className: 'literal',\n    variants: [\n      {\n        // #YYYY-MM-DD# (ISO-Date) or #M/D/YYYY# (US-Date)\n        begin: regex.concat(/# */, regex.either(YYYY_MM_DD, MM_DD_YYYY), / *#/) },\n      {\n        // #H:mm[:ss]# (24h Time)\n        begin: regex.concat(/# */, TIME_24H, / *#/) },\n      {\n        // #h[:mm[:ss]] A# (12h Time)\n        begin: regex.concat(/# */, TIME_12H, / *#/) },\n      {\n        // date plus time\n        begin: regex.concat(\n          /# */,\n          regex.either(YYYY_MM_DD, MM_DD_YYYY),\n          / +/,\n          regex.either(TIME_12H, TIME_24H),\n          / *#/\n        ) }\n    ]\n  };\n\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    variants: [\n      {\n        // Float\n        begin: /\\b\\d[\\d_]*((\\.[\\d_]+(E[+-]?[\\d_]+)?)|(E[+-]?[\\d_]+))[RFD@!#]?/ },\n      {\n        // Integer (base 10)\n        begin: /\\b\\d[\\d_]*((U?[SIL])|[%&])?/ },\n      {\n        // Integer (base 16)\n        begin: /&H[\\dA-F_]+((U?[SIL])|[%&])?/ },\n      {\n        // Integer (base 8)\n        begin: /&O[0-7_]+((U?[SIL])|[%&])?/ },\n      {\n        // Integer (base 2)\n        begin: /&B[01_]+((U?[SIL])|[%&])?/ }\n    ]\n  };\n\n  const LABEL = {\n    className: 'label',\n    begin: /^\\w+:/\n  };\n\n  const DOC_COMMENT = hljs.COMMENT(/'''/, /$/, { contains: [\n    {\n      className: 'doctag',\n      begin: /<\\/?/,\n      end: />/\n    }\n  ] });\n\n  const COMMENT = hljs.COMMENT(null, /$/, { variants: [\n    { begin: /'/ },\n    {\n      // TODO: Use multi-class for leading spaces\n      begin: /([\\t ]|^)REM(?=\\s)/ }\n  ] });\n\n  const DIRECTIVES = {\n    className: 'meta',\n    // TODO: Use multi-class for indentation once available\n    begin: /[\\t ]*#(const|disable|else|elseif|enable|end|externalsource|if|region)\\b/,\n    end: /$/,\n    keywords: { keyword:\n        'const disable else elseif enable end externalsource if region then' },\n    contains: [ COMMENT ]\n  };\n\n  return {\n    name: 'Visual Basic .NET',\n    aliases: [ 'vb' ],\n    case_insensitive: true,\n    classNameAliases: { label: 'symbol' },\n    keywords: {\n      keyword:\n        'addhandler alias aggregate ansi as async assembly auto binary by byref byval ' /* a-b */\n        + 'call case catch class compare const continue custom declare default delegate dim distinct do ' /* c-d */\n        + 'each equals else elseif end enum erase error event exit explicit finally for friend from function ' /* e-f */\n        + 'get global goto group handles if implements imports in inherits interface into iterator ' /* g-i */\n        + 'join key let lib loop me mid module mustinherit mustoverride mybase myclass ' /* j-m */\n        + 'namespace narrowing new next notinheritable notoverridable ' /* n */\n        + 'of off on operator option optional order overloads overridable overrides ' /* o */\n        + 'paramarray partial preserve private property protected public ' /* p */\n        + 'raiseevent readonly redim removehandler resume return ' /* r */\n        + 'select set shadows shared skip static step stop structure strict sub synclock ' /* s */\n        + 'take text then throw to try unicode until using when where while widening with withevents writeonly yield' /* t-y */,\n      built_in:\n        // Operators https://docs.microsoft.com/dotnet/visual-basic/language-reference/operators\n        'addressof and andalso await directcast gettype getxmlnamespace is isfalse isnot istrue like mod nameof new not or orelse trycast typeof xor '\n        // Type Conversion Functions https://docs.microsoft.com/dotnet/visual-basic/language-reference/functions/type-conversion-functions\n        + 'cbool cbyte cchar cdate cdbl cdec cint clng cobj csbyte cshort csng cstr cuint culng cushort',\n      type:\n        // Data types https://docs.microsoft.com/dotnet/visual-basic/language-reference/data-types\n        'boolean byte char date decimal double integer long object sbyte short single string uinteger ulong ushort',\n      literal: 'true false nothing'\n    },\n    illegal:\n      '//|\\\\{|\\\\}|endif|gosub|variant|wend|^\\\\$ ' /* reserved deprecated keywords */,\n    contains: [\n      CHARACTER,\n      STRING,\n      DATE,\n      NUMBER,\n      LABEL,\n      DOC_COMMENT,\n      COMMENT,\n      DIRECTIVES\n    ]\n  };\n}\n\nexport { vbnet as default };\n"], "names": [], "mappings": "AAAA;;;;;;AAMA,GAEA,qBAAqB;;;AACrB,SAAS,MAAM,IAAI;IACjB,MAAM,QAAQ,KAAK,KAAK;IACxB;;;GAGC,GACD,MAAM,YAAY;QAChB,WAAW;QACX,OAAO;IACT;IAEA,MAAM,SAAS;QACb,WAAW;QACX,OAAO;QACP,KAAK;QACL,SAAS;QACT,UAAU;YACR;gBACE,sBAAsB;gBACtB,OAAO;YAAK;SACf;IACH;IAEA,8FAA8F,GAC9F,MAAM,aAAa;IACnB,MAAM,aAAa;IACnB,MAAM,WAAW;IACjB,MAAM,WAAW;IACjB,MAAM,OAAO;QACX,WAAW;QACX,UAAU;YACR;gBACE,kDAAkD;gBAClD,OAAO,MAAM,MAAM,CAAC,OAAO,MAAM,MAAM,CAAC,YAAY,aAAa;YAAO;YAC1E;gBACE,yBAAyB;gBACzB,OAAO,MAAM,MAAM,CAAC,OAAO,UAAU;YAAO;YAC9C;gBACE,6BAA6B;gBAC7B,OAAO,MAAM,MAAM,CAAC,OAAO,UAAU;YAAO;YAC9C;gBACE,iBAAiB;gBACjB,OAAO,MAAM,MAAM,CACjB,OACA,MAAM,MAAM,CAAC,YAAY,aACzB,MACA,MAAM,MAAM,CAAC,UAAU,WACvB;YACA;SACL;IACH;IAEA,MAAM,SAAS;QACb,WAAW;QACX,WAAW;QACX,UAAU;YACR;gBACE,QAAQ;gBACR,OAAO;YAAgE;YACzE;gBACE,oBAAoB;gBACpB,OAAO;YAA8B;YACvC;gBACE,oBAAoB;gBACpB,OAAO;YAA+B;YACxC;gBACE,mBAAmB;gBACnB,OAAO;YAA6B;YACtC;gBACE,mBAAmB;gBACnB,OAAO;YAA4B;SACtC;IACH;IAEA,MAAM,QAAQ;QACZ,WAAW;QACX,OAAO;IACT;IAEA,MAAM,cAAc,KAAK,OAAO,CAAC,OAAO,KAAK;QAAE,UAAU;YACvD;gBACE,WAAW;gBACX,OAAO;gBACP,KAAK;YACP;SACD;IAAC;IAEF,MAAM,UAAU,KAAK,OAAO,CAAC,MAAM,KAAK;QAAE,UAAU;YAClD;gBAAE,OAAO;YAAI;YACb;gBACE,2CAA2C;gBAC3C,OAAO;YAAqB;SAC/B;IAAC;IAEF,MAAM,aAAa;QACjB,WAAW;QACX,uDAAuD;QACvD,OAAO;QACP,KAAK;QACL,UAAU;YAAE,SACR;QAAqE;QACzE,UAAU;YAAE;SAAS;IACvB;IAEA,OAAO;QACL,MAAM;QACN,SAAS;YAAE;SAAM;QACjB,kBAAkB;QAClB,kBAAkB;YAAE,OAAO;QAAS;QACpC,UAAU;YACR,SACE,gFAAgF,OAAO,MACrF,gGAAgG,OAAO,MACvG,qGAAqG,OAAO,MAC5G,2FAA2F,OAAO,MAClG,+EAA+E,OAAO,MACtF,8DAA8D,KAAK,MACnE,4EAA4E,KAAK,MACjF,iEAAiE,KAAK,MACtE,yDAAyD,KAAK,MAC9D,iFAAiF,KAAK,MACtF,4GAA4G,OAAO;YACvH,UACE,wFAAwF;YACxF,iJAEE;YACJ,MACE,0FAA0F;YAC1F;YACF,SAAS;QACX;QACA,SACE,4CAA4C,gCAAgC;QAC9E,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 13600, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/wasm.js"], "sourcesContent": ["/*\nLanguage: WebAssembly\nWebsite: https://webassembly.org\nDescription:  Wasm is designed as a portable compilation target for programming languages, enabling deployment on the web for client and server applications.\nCategory: web, common\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction wasm(hljs) {\n  hljs.regex;\n  const BLOCK_COMMENT = hljs.COMMENT(/\\(;/, /;\\)/);\n  BLOCK_COMMENT.contains.push(\"self\");\n  const LINE_COMMENT = hljs.COMMENT(/;;/, /$/);\n\n  const KWS = [\n    \"anyfunc\",\n    \"block\",\n    \"br\",\n    \"br_if\",\n    \"br_table\",\n    \"call\",\n    \"call_indirect\",\n    \"data\",\n    \"drop\",\n    \"elem\",\n    \"else\",\n    \"end\",\n    \"export\",\n    \"func\",\n    \"global.get\",\n    \"global.set\",\n    \"local.get\",\n    \"local.set\",\n    \"local.tee\",\n    \"get_global\",\n    \"get_local\",\n    \"global\",\n    \"if\",\n    \"import\",\n    \"local\",\n    \"loop\",\n    \"memory\",\n    \"memory.grow\",\n    \"memory.size\",\n    \"module\",\n    \"mut\",\n    \"nop\",\n    \"offset\",\n    \"param\",\n    \"result\",\n    \"return\",\n    \"select\",\n    \"set_global\",\n    \"set_local\",\n    \"start\",\n    \"table\",\n    \"tee_local\",\n    \"then\",\n    \"type\",\n    \"unreachable\"\n  ];\n\n  const FUNCTION_REFERENCE = {\n    begin: [\n      /(?:func|call|call_indirect)/,\n      /\\s+/,\n      /\\$[^\\s)]+/\n    ],\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    }\n  };\n\n  const ARGUMENT = {\n    className: \"variable\",\n    begin: /\\$[\\w_]+/\n  };\n\n  const PARENS = {\n    match: /(\\((?!;)|\\))+/,\n    className: \"punctuation\",\n    relevance: 0\n  };\n\n  const NUMBER = {\n    className: \"number\",\n    relevance: 0,\n    // borrowed from Prism, TODO: split out into variants\n    match: /[+-]?\\b(?:\\d(?:_?\\d)*(?:\\.\\d(?:_?\\d)*)?(?:[eE][+-]?\\d(?:_?\\d)*)?|0x[\\da-fA-F](?:_?[\\da-fA-F])*(?:\\.[\\da-fA-F](?:_?[\\da-fA-D])*)?(?:[pP][+-]?\\d(?:_?\\d)*)?)\\b|\\binf\\b|\\bnan(?::0x[\\da-fA-F](?:_?[\\da-fA-D])*)?\\b/\n  };\n\n  const TYPE = {\n    // look-ahead prevents us from gobbling up opcodes\n    match: /(i32|i64|f32|f64)(?!\\.)/,\n    className: \"type\"\n  };\n\n  const MATH_OPERATIONS = {\n    className: \"keyword\",\n    // borrowed from Prism, TODO: split out into variants\n    match: /\\b(f32|f64|i32|i64)(?:\\.(?:abs|add|and|ceil|clz|const|convert_[su]\\/i(?:32|64)|copysign|ctz|demote\\/f64|div(?:_[su])?|eqz?|extend_[su]\\/i32|floor|ge(?:_[su])?|gt(?:_[su])?|le(?:_[su])?|load(?:(?:8|16|32)_[su])?|lt(?:_[su])?|max|min|mul|nearest|neg?|or|popcnt|promote\\/f32|reinterpret\\/[fi](?:32|64)|rem_[su]|rot[lr]|shl|shr_[su]|store(?:8|16|32)?|sqrt|sub|trunc(?:_[su]\\/f(?:32|64))?|wrap\\/i64|xor))\\b/\n  };\n\n  const OFFSET_ALIGN = {\n    match: [\n      /(?:offset|align)/,\n      /\\s*/,\n      /=/\n    ],\n    className: {\n      1: \"keyword\",\n      3: \"operator\"\n    }\n  };\n\n  return {\n    name: 'WebAssembly',\n    keywords: {\n      $pattern: /[\\w.]+/,\n      keyword: KWS\n    },\n    contains: [\n      LINE_COMMENT,\n      BLOCK_COMMENT,\n      OFFSET_ALIGN,\n      ARGUMENT,\n      PARENS,\n      FUNCTION_REFERENCE,\n      hljs.QUOTE_STRING_MODE,\n      TYPE,\n      MATH_OPERATIONS,\n      NUMBER\n    ]\n  };\n}\n\nexport { wasm as default };\n"], "names": [], "mappings": "AAAA;;;;;;AAMA,GAEA,qBAAqB;;;AACrB,SAAS,KAAK,IAAI;IAChB,KAAK,KAAK;IACV,MAAM,gBAAgB,KAAK,OAAO,CAAC,OAAO;IAC1C,cAAc,QAAQ,CAAC,IAAI,CAAC;IAC5B,MAAM,eAAe,KAAK,OAAO,CAAC,MAAM;IAExC,MAAM,MAAM;QACV;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,qBAAqB;QACzB,OAAO;YACL;YACA;YACA;SACD;QACD,WAAW;YACT,GAAG;YACH,GAAG;QACL;IACF;IAEA,MAAM,WAAW;QACf,WAAW;QACX,OAAO;IACT;IAEA,MAAM,SAAS;QACb,OAAO;QACP,WAAW;QACX,WAAW;IACb;IAEA,MAAM,SAAS;QACb,WAAW;QACX,WAAW;QACX,qDAAqD;QACrD,OAAO;IACT;IAEA,MAAM,OAAO;QACX,kDAAkD;QAClD,OAAO;QACP,WAAW;IACb;IAEA,MAAM,kBAAkB;QACtB,WAAW;QACX,qDAAqD;QACrD,OAAO;IACT;IAEA,MAAM,eAAe;QACnB,OAAO;YACL;YACA;YACA;SACD;QACD,WAAW;YACT,GAAG;YACH,GAAG;QACL;IACF;IAEA,OAAO;QACL,MAAM;QACN,UAAU;YACR,UAAU;YACV,SAAS;QACX;QACA,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA,KAAK,iBAAiB;YACtB;YACA;YACA;SACD;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 13735, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/xml.js"], "sourcesContent": ["/*\nLanguage: HTML, XML\nWebsite: https://www.w3.org/XML/\nCategory: common, web\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction xml(hljs) {\n  const regex = hljs.regex;\n  // XML names can have the following additional letters: https://www.w3.org/TR/xml/#NT-NameChar\n  // OTHER_NAME_CHARS = /[:\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]/;\n  // Element names start with NAME_START_CHAR followed by optional other Unicode letters, ASCII digits, hyphens, underscores, and periods\n  // const TAG_NAME_RE = regex.concat(/[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]/, regex.optional(/[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*:/), /[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*/);;\n  // const XML_IDENT_RE = /[A-Z_a-z:\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]+/;\n  // const TAG_NAME_RE = regex.concat(/[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]/, regex.optional(/[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*:/), /[A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*/);\n  // however, to cater for performance and more Unicode support rely simply on the Unicode letter class\n  const TAG_NAME_RE = regex.concat(/[\\p{L}_]/u, regex.optional(/[\\p{L}0-9_.-]*:/u), /[\\p{L}0-9_.-]*/u);\n  const XML_IDENT_RE = /[\\p{L}0-9._:-]+/u;\n  const XML_ENTITIES = {\n    className: 'symbol',\n    begin: /&[a-z]+;|&#[0-9]+;|&#x[a-f0-9]+;/\n  };\n  const XML_META_KEYWORDS = {\n    begin: /\\s/,\n    contains: [\n      {\n        className: 'keyword',\n        begin: /#?[a-z_][a-z1-9_-]+/,\n        illegal: /\\n/\n      }\n    ]\n  };\n  const XML_META_PAR_KEYWORDS = hljs.inherit(XML_META_KEYWORDS, {\n    begin: /\\(/,\n    end: /\\)/\n  });\n  const APOS_META_STRING_MODE = hljs.inherit(hljs.APOS_STRING_MODE, { className: 'string' });\n  const QUOTE_META_STRING_MODE = hljs.inherit(hljs.QUOTE_STRING_MODE, { className: 'string' });\n  const TAG_INTERNALS = {\n    endsWithParent: true,\n    illegal: /</,\n    relevance: 0,\n    contains: [\n      {\n        className: 'attr',\n        begin: XML_IDENT_RE,\n        relevance: 0\n      },\n      {\n        begin: /=\\s*/,\n        relevance: 0,\n        contains: [\n          {\n            className: 'string',\n            endsParent: true,\n            variants: [\n              {\n                begin: /\"/,\n                end: /\"/,\n                contains: [ XML_ENTITIES ]\n              },\n              {\n                begin: /'/,\n                end: /'/,\n                contains: [ XML_ENTITIES ]\n              },\n              { begin: /[^\\s\"'=<>`]+/ }\n            ]\n          }\n        ]\n      }\n    ]\n  };\n  return {\n    name: 'HTML, XML',\n    aliases: [\n      'html',\n      'xhtml',\n      'rss',\n      'atom',\n      'xjb',\n      'xsd',\n      'xsl',\n      'plist',\n      'wsf',\n      'svg'\n    ],\n    case_insensitive: true,\n    unicodeRegex: true,\n    contains: [\n      {\n        className: 'meta',\n        begin: /<![a-z]/,\n        end: />/,\n        relevance: 10,\n        contains: [\n          XML_META_KEYWORDS,\n          QUOTE_META_STRING_MODE,\n          APOS_META_STRING_MODE,\n          XML_META_PAR_KEYWORDS,\n          {\n            begin: /\\[/,\n            end: /\\]/,\n            contains: [\n              {\n                className: 'meta',\n                begin: /<![a-z]/,\n                end: />/,\n                contains: [\n                  XML_META_KEYWORDS,\n                  XML_META_PAR_KEYWORDS,\n                  QUOTE_META_STRING_MODE,\n                  APOS_META_STRING_MODE\n                ]\n              }\n            ]\n          }\n        ]\n      },\n      hljs.COMMENT(\n        /<!--/,\n        /-->/,\n        { relevance: 10 }\n      ),\n      {\n        begin: /<!\\[CDATA\\[/,\n        end: /\\]\\]>/,\n        relevance: 10\n      },\n      XML_ENTITIES,\n      // xml processing instructions\n      {\n        className: 'meta',\n        end: /\\?>/,\n        variants: [\n          {\n            begin: /<\\?xml/,\n            relevance: 10,\n            contains: [\n              QUOTE_META_STRING_MODE\n            ]\n          },\n          {\n            begin: /<\\?[a-z][a-z0-9]+/,\n          }\n        ]\n\n      },\n      {\n        className: 'tag',\n        /*\n        The lookahead pattern (?=...) ensures that 'begin' only matches\n        '<style' as a single word, followed by a whitespace or an\n        ending bracket.\n        */\n        begin: /<style(?=\\s|>)/,\n        end: />/,\n        keywords: { name: 'style' },\n        contains: [ TAG_INTERNALS ],\n        starts: {\n          end: /<\\/style>/,\n          returnEnd: true,\n          subLanguage: [\n            'css',\n            'xml'\n          ]\n        }\n      },\n      {\n        className: 'tag',\n        // See the comment in the <style tag about the lookahead pattern\n        begin: /<script(?=\\s|>)/,\n        end: />/,\n        keywords: { name: 'script' },\n        contains: [ TAG_INTERNALS ],\n        starts: {\n          end: /<\\/script>/,\n          returnEnd: true,\n          subLanguage: [\n            'javascript',\n            'handlebars',\n            'xml'\n          ]\n        }\n      },\n      // we need this for now for jSX\n      {\n        className: 'tag',\n        begin: /<>|<\\/>/\n      },\n      // open tag\n      {\n        className: 'tag',\n        begin: regex.concat(\n          /</,\n          regex.lookahead(regex.concat(\n            TAG_NAME_RE,\n            // <tag/>\n            // <tag>\n            // <tag ...\n            regex.either(/\\/>/, />/, /\\s/)\n          ))\n        ),\n        end: /\\/?>/,\n        contains: [\n          {\n            className: 'name',\n            begin: TAG_NAME_RE,\n            relevance: 0,\n            starts: TAG_INTERNALS\n          }\n        ]\n      },\n      // close tag\n      {\n        className: 'tag',\n        begin: regex.concat(\n          /<\\//,\n          regex.lookahead(regex.concat(\n            TAG_NAME_RE, />/\n          ))\n        ),\n        contains: [\n          {\n            className: 'name',\n            begin: TAG_NAME_RE,\n            relevance: 0\n          },\n          {\n            begin: />/,\n            relevance: 0,\n            endsParent: true\n          }\n        ]\n      }\n    ]\n  };\n}\n\nexport { xml as default };\n"], "names": [], "mappings": "AAAA;;;;;AAKA,GAEA,qBAAqB;;;AACrB,SAAS,IAAI,IAAI;IACf,MAAM,QAAQ,KAAK,KAAK;IACxB,8FAA8F;IAC9F,kEAAkE;IAClE,uIAAuI;IACvI,wlBAAwlB;IACxlB,2NAA2N;IAC3N,ulBAAulB;IACvlB,qGAAqG;IACrG,MAAM,cAAc,MAAM,MAAM,CAAC,aAAa,MAAM,QAAQ,CAAC,qBAAqB;IAClF,MAAM,eAAe;IACrB,MAAM,eAAe;QACnB,WAAW;QACX,OAAO;IACT;IACA,MAAM,oBAAoB;QACxB,OAAO;QACP,UAAU;YACR;gBACE,WAAW;gBACX,OAAO;gBACP,SAAS;YACX;SACD;IACH;IACA,MAAM,wBAAwB,KAAK,OAAO,CAAC,mBAAmB;QAC5D,OAAO;QACP,KAAK;IACP;IACA,MAAM,wBAAwB,KAAK,OAAO,CAAC,KAAK,gBAAgB,EAAE;QAAE,WAAW;IAAS;IACxF,MAAM,yBAAyB,KAAK,OAAO,CAAC,KAAK,iBAAiB,EAAE;QAAE,WAAW;IAAS;IAC1F,MAAM,gBAAgB;QACpB,gBAAgB;QAChB,SAAS;QACT,WAAW;QACX,UAAU;YACR;gBACE,WAAW;gBACX,OAAO;gBACP,WAAW;YACb;YACA;gBACE,OAAO;gBACP,WAAW;gBACX,UAAU;oBACR;wBACE,WAAW;wBACX,YAAY;wBACZ,UAAU;4BACR;gCACE,OAAO;gCACP,KAAK;gCACL,UAAU;oCAAE;iCAAc;4BAC5B;4BACA;gCACE,OAAO;gCACP,KAAK;gCACL,UAAU;oCAAE;iCAAc;4BAC5B;4BACA;gCAAE,OAAO;4BAAe;yBACzB;oBACH;iBACD;YACH;SACD;IACH;IACA,OAAO;QACL,MAAM;QACN,SAAS;YACP;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,kBAAkB;QAClB,cAAc;QACd,UAAU;YACR;gBACE,WAAW;gBACX,OAAO;gBACP,KAAK;gBACL,WAAW;gBACX,UAAU;oBACR;oBACA;oBACA;oBACA;oBACA;wBACE,OAAO;wBACP,KAAK;wBACL,UAAU;4BACR;gCACE,WAAW;gCACX,OAAO;gCACP,KAAK;gCACL,UAAU;oCACR;oCACA;oCACA;oCACA;iCACD;4BACH;yBACD;oBACH;iBACD;YACH;YACA,KAAK,OAAO,CACV,QACA,OACA;gBAAE,WAAW;YAAG;YAElB;gBACE,OAAO;gBACP,KAAK;gBACL,WAAW;YACb;YACA;YACA,8BAA8B;YAC9B;gBACE,WAAW;gBACX,KAAK;gBACL,UAAU;oBACR;wBACE,OAAO;wBACP,WAAW;wBACX,UAAU;4BACR;yBACD;oBACH;oBACA;wBACE,OAAO;oBACT;iBACD;YAEH;YACA;gBACE,WAAW;gBACX;;;;QAIA,GACA,OAAO;gBACP,KAAK;gBACL,UAAU;oBAAE,MAAM;gBAAQ;gBAC1B,UAAU;oBAAE;iBAAe;gBAC3B,QAAQ;oBACN,KAAK;oBACL,WAAW;oBACX,aAAa;wBACX;wBACA;qBACD;gBACH;YACF;YACA;gBACE,WAAW;gBACX,gEAAgE;gBAChE,OAAO;gBACP,KAAK;gBACL,UAAU;oBAAE,MAAM;gBAAS;gBAC3B,UAAU;oBAAE;iBAAe;gBAC3B,QAAQ;oBACN,KAAK;oBACL,WAAW;oBACX,aAAa;wBACX;wBACA;wBACA;qBACD;gBACH;YACF;YACA,+BAA+B;YAC/B;gBACE,WAAW;gBACX,OAAO;YACT;YACA,WAAW;YACX;gBACE,WAAW;gBACX,OAAO,MAAM,MAAM,CACjB,KACA,MAAM,SAAS,CAAC,MAAM,MAAM,CAC1B,aACA,SAAS;gBACT,QAAQ;gBACR,WAAW;gBACX,MAAM,MAAM,CAAC,OAAO,KAAK;gBAG7B,KAAK;gBACL,UAAU;oBACR;wBACE,WAAW;wBACX,OAAO;wBACP,WAAW;wBACX,QAAQ;oBACV;iBACD;YACH;YACA,YAAY;YACZ;gBACE,WAAW;gBACX,OAAO,MAAM,MAAM,CACjB,OACA,MAAM,SAAS,CAAC,MAAM,MAAM,CAC1B,aAAa;gBAGjB,UAAU;oBACR;wBACE,WAAW;wBACX,OAAO;wBACP,WAAW;oBACb;oBACA;wBACE,OAAO;wBACP,WAAW;wBACX,YAAY;oBACd;iBACD;YACH;SACD;IACH;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 13984, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/languages/yaml.js"], "sourcesContent": ["/*\nLanguage: YAML\nDescription: Yet Another Markdown Language\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nRequires: ruby.js\nWebsite: https://yaml.org\nCategory: common, config\n*/\nfunction yaml(hljs) {\n  const LITERALS = 'true false yes no null';\n\n  // YAML spec allows non-reserved URI characters in tags.\n  const URI_CHARACTERS = '[\\\\w#;/?:@&=+$,.~*\\'()[\\\\]]+';\n\n  // Define keys as starting with a word character\n  // ...containing word chars, spaces, colons, forward-slashes, hyphens and periods\n  // ...and ending with a colon followed immediately by a space, tab or newline.\n  // The YAML spec allows for much more than this, but this covers most use-cases.\n  const KEY = {\n    className: 'attr',\n    variants: [\n      // added brackets support and special char support\n      { begin: /[\\w*@][\\w*@ :()\\./-]*:(?=[ \\t]|$)/ },\n      { // double quoted keys - with brackets and special char support\n        begin: /\"[\\w*@][\\w*@ :()\\./-]*\":(?=[ \\t]|$)/ },\n      { // single quoted keys - with brackets and special char support\n        begin: /'[\\w*@][\\w*@ :()\\./-]*':(?=[ \\t]|$)/ },\n    ]\n  };\n  \n  const TEMPLATE_VARIABLES = {\n    className: 'template-variable',\n    variants: [\n      { // jinja templates Ansible\n        begin: /\\{\\{/,\n        end: /\\}\\}/\n      },\n      { // Ruby i18n\n        begin: /%\\{/,\n        end: /\\}/\n      }\n    ]\n  };\n\n  const SINGLE_QUOTE_STRING = {\n    className: 'string',\n    relevance: 0,\n    begin: /'/,\n    end: /'/,\n    contains: [\n      {\n        match: /''/,\n        scope: 'char.escape',\n        relevance: 0\n      }\n    ]\n  };\n\n  const STRING = {\n    className: 'string',\n    relevance: 0,\n    variants: [\n      {\n        begin: /\"/,\n        end: /\"/\n      },\n      { begin: /\\S+/ }\n    ],\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      TEMPLATE_VARIABLES\n    ]\n  };\n\n  // Strings inside of value containers (objects) can't contain braces,\n  // brackets, or commas\n  const CONTAINER_STRING = hljs.inherit(STRING, { variants: [\n    {\n      begin: /'/,\n      end: /'/,\n      contains: [\n        {\n          begin: /''/,\n          relevance: 0\n        }\n      ]\n    },\n    {\n      begin: /\"/,\n      end: /\"/\n    },\n    { begin: /[^\\s,{}[\\]]+/ }\n  ] });\n\n  const DATE_RE = '[0-9]{4}(-[0-9][0-9]){0,2}';\n  const TIME_RE = '([Tt \\\\t][0-9][0-9]?(:[0-9][0-9]){2})?';\n  const FRACTION_RE = '(\\\\.[0-9]*)?';\n  const ZONE_RE = '([ \\\\t])*(Z|[-+][0-9][0-9]?(:[0-9][0-9])?)?';\n  const TIMESTAMP = {\n    className: 'number',\n    begin: '\\\\b' + DATE_RE + TIME_RE + FRACTION_RE + ZONE_RE + '\\\\b'\n  };\n\n  const VALUE_CONTAINER = {\n    end: ',',\n    endsWithParent: true,\n    excludeEnd: true,\n    keywords: LITERALS,\n    relevance: 0\n  };\n  const OBJECT = {\n    begin: /\\{/,\n    end: /\\}/,\n    contains: [ VALUE_CONTAINER ],\n    illegal: '\\\\n',\n    relevance: 0\n  };\n  const ARRAY = {\n    begin: '\\\\[',\n    end: '\\\\]',\n    contains: [ VALUE_CONTAINER ],\n    illegal: '\\\\n',\n    relevance: 0\n  };\n\n  const MODES = [\n    KEY,\n    {\n      className: 'meta',\n      begin: '^---\\\\s*$',\n      relevance: 10\n    },\n    { // multi line string\n      // Blocks start with a | or > followed by a newline\n      //\n      // Indentation of subsequent lines must be the same to\n      // be considered part of the block\n      className: 'string',\n      begin: '[\\\\|>]([1-9]?[+-])?[ ]*\\\\n( +)[^ ][^\\\\n]*\\\\n(\\\\2[^\\\\n]+\\\\n?)*'\n    },\n    { // Ruby/Rails erb\n      begin: '<%[%=-]?',\n      end: '[%-]?%>',\n      subLanguage: 'ruby',\n      excludeBegin: true,\n      excludeEnd: true,\n      relevance: 0\n    },\n    { // named tags\n      className: 'type',\n      begin: '!\\\\w+!' + URI_CHARACTERS\n    },\n    // https://yaml.org/spec/1.2/spec.html#id2784064\n    { // verbatim tags\n      className: 'type',\n      begin: '!<' + URI_CHARACTERS + \">\"\n    },\n    { // primary tags\n      className: 'type',\n      begin: '!' + URI_CHARACTERS\n    },\n    { // secondary tags\n      className: 'type',\n      begin: '!!' + URI_CHARACTERS\n    },\n    { // fragment id &ref\n      className: 'meta',\n      begin: '&' + hljs.UNDERSCORE_IDENT_RE + '$'\n    },\n    { // fragment reference *ref\n      className: 'meta',\n      begin: '\\\\*' + hljs.UNDERSCORE_IDENT_RE + '$'\n    },\n    { // array listing\n      className: 'bullet',\n      // TODO: remove |$ hack when we have proper look-ahead support\n      begin: '-(?=[ ]|$)',\n      relevance: 0\n    },\n    hljs.HASH_COMMENT_MODE,\n    {\n      beginKeywords: LITERALS,\n      keywords: { literal: LITERALS }\n    },\n    TIMESTAMP,\n    // numbers are any valid C-style number that\n    // sit isolated from other words\n    {\n      className: 'number',\n      begin: hljs.C_NUMBER_RE + '\\\\b',\n      relevance: 0\n    },\n    OBJECT,\n    ARRAY,\n    SINGLE_QUOTE_STRING,\n    STRING\n  ];\n\n  const VALUE_MODES = [ ...MODES ];\n  VALUE_MODES.pop();\n  VALUE_MODES.push(CONTAINER_STRING);\n  VALUE_CONTAINER.contains = VALUE_MODES;\n\n  return {\n    name: 'YAML',\n    case_insensitive: true,\n    aliases: [ 'yml' ],\n    contains: MODES\n  };\n}\n\nexport { yaml as default };\n"], "names": [], "mappings": "AAAA;;;;;;;;AAQA;;;AACA,SAAS,KAAK,IAAI;IAChB,MAAM,WAAW;IAEjB,wDAAwD;IACxD,MAAM,iBAAiB;IAEvB,gDAAgD;IAChD,iFAAiF;IACjF,8EAA8E;IAC9E,gFAAgF;IAChF,MAAM,MAAM;QACV,WAAW;QACX,UAAU;YACR,kDAAkD;YAClD;gBAAE,OAAO;YAAoC;YAC7C;gBACE,OAAO;YAAsC;YAC/C;gBACE,OAAO;YAAsC;SAChD;IACH;IAEA,MAAM,qBAAqB;QACzB,WAAW;QACX,UAAU;YACR;gBACE,OAAO;gBACP,KAAK;YACP;YACA;gBACE,OAAO;gBACP,KAAK;YACP;SACD;IACH;IAEA,MAAM,sBAAsB;QAC1B,WAAW;QACX,WAAW;QACX,OAAO;QACP,KAAK;QACL,UAAU;YACR;gBACE,OAAO;gBACP,OAAO;gBACP,WAAW;YACb;SACD;IACH;IAEA,MAAM,SAAS;QACb,WAAW;QACX,WAAW;QACX,UAAU;YACR;gBACE,OAAO;gBACP,KAAK;YACP;YACA;gBAAE,OAAO;YAAM;SAChB;QACD,UAAU;YACR,KAAK,gBAAgB;YACrB;SACD;IACH;IAEA,qEAAqE;IACrE,sBAAsB;IACtB,MAAM,mBAAmB,KAAK,OAAO,CAAC,QAAQ;QAAE,UAAU;YACxD;gBACE,OAAO;gBACP,KAAK;gBACL,UAAU;oBACR;wBACE,OAAO;wBACP,WAAW;oBACb;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;YACP;YACA;gBAAE,OAAO;YAAe;SACzB;IAAC;IAEF,MAAM,UAAU;IAChB,MAAM,UAAU;IAChB,MAAM,cAAc;IACpB,MAAM,UAAU;IAChB,MAAM,YAAY;QAChB,WAAW;QACX,OAAO,QAAQ,UAAU,UAAU,cAAc,UAAU;IAC7D;IAEA,MAAM,kBAAkB;QACtB,KAAK;QACL,gBAAgB;QAChB,YAAY;QACZ,UAAU;QACV,WAAW;IACb;IACA,MAAM,SAAS;QACb,OAAO;QACP,KAAK;QACL,UAAU;YAAE;SAAiB;QAC7B,SAAS;QACT,WAAW;IACb;IACA,MAAM,QAAQ;QACZ,OAAO;QACP,KAAK;QACL,UAAU;YAAE;SAAiB;QAC7B,SAAS;QACT,WAAW;IACb;IAEA,MAAM,QAAQ;QACZ;QACA;YACE,WAAW;YACX,OAAO;YACP,WAAW;QACb;QACA;YACE,mDAAmD;YACnD,EAAE;YACF,sDAAsD;YACtD,kCAAkC;YAClC,WAAW;YACX,OAAO;QACT;QACA;YACE,OAAO;YACP,KAAK;YACL,aAAa;YACb,cAAc;YACd,YAAY;YACZ,WAAW;QACb;QACA;YACE,WAAW;YACX,OAAO,WAAW;QACpB;QACA,gDAAgD;QAChD;YACE,WAAW;YACX,OAAO,OAAO,iBAAiB;QACjC;QACA;YACE,WAAW;YACX,OAAO,MAAM;QACf;QACA;YACE,WAAW;YACX,OAAO,OAAO;QAChB;QACA;YACE,WAAW;YACX,OAAO,MAAM,KAAK,mBAAmB,GAAG;QAC1C;QACA;YACE,WAAW;YACX,OAAO,QAAQ,KAAK,mBAAmB,GAAG;QAC5C;QACA;YACE,WAAW;YACX,8DAA8D;YAC9D,OAAO;YACP,WAAW;QACb;QACA,KAAK,iBAAiB;QACtB;YACE,eAAe;YACf,UAAU;gBAAE,SAAS;YAAS;QAChC;QACA;QACA,4CAA4C;QAC5C,gCAAgC;QAChC;YACE,WAAW;YACX,OAAO,KAAK,WAAW,GAAG;YAC1B,WAAW;QACb;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,cAAc;WAAK;KAAO;IAChC,YAAY,GAAG;IACf,YAAY,IAAI,CAAC;IACjB,gBAAgB,QAAQ,GAAG;IAE3B,OAAO;QACL,MAAM;QACN,kBAAkB;QAClB,SAAS;YAAE;SAAO;QAClB,UAAU;IACZ;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 14213, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/highlight.js%4011.11.1/node_modules/highlight.js/es/core.js"], "sourcesContent": ["// https://nodejs.org/api/packages.html#packages_writing_dual_packages_while_avoiding_or_minimizing_hazards\nimport HighlightJS from '../lib/core.js';\nexport { HighlightJS };\nexport default HighlightJS;\n"], "names": [], "mappings": "AAAA,2GAA2G;;;;AAC3G;;;uCAEe,+MAAA,CAAA,UAAW", "ignoreList": [0], "debugId": null}}]}