(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-container.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "article": "tweet-container-module__OCDp-G__article",
  "root": "tweet-container-module__OCDp-G__root",
});
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-container.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TweetContainer": (()=>TweetContainer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$container$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-container.module.css [app-client] (css module)");
;
;
;
;
const TweetContainer = ({ className, children })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])('react-tweet-theme', __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$container$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].root, className),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("article", {
            className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$container$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].article,
            children: children
        })
    });
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/avatar-img.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "AvatarImg": (()=>AvatarImg)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
;
const AvatarImg = (props)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("img", {
        ...props
    });
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-header.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "author": "tweet-header-module__eMp9Yq__author",
  "authorFollow": "tweet-header-module__eMp9Yq__authorFollow",
  "authorLink": "tweet-header-module__eMp9Yq__authorLink",
  "authorLinkText": "tweet-header-module__eMp9Yq__authorLinkText",
  "authorMeta": "tweet-header-module__eMp9Yq__authorMeta",
  "authorVerified": "tweet-header-module__eMp9Yq__authorVerified",
  "avatar": "tweet-header-module__eMp9Yq__avatar",
  "avatarOverflow": "tweet-header-module__eMp9Yq__avatarOverflow",
  "avatarShadow": "tweet-header-module__eMp9Yq__avatarShadow",
  "avatarSquare": "tweet-header-module__eMp9Yq__avatarSquare",
  "brand": "tweet-header-module__eMp9Yq__brand",
  "follow": "tweet-header-module__eMp9Yq__follow",
  "header": "tweet-header-module__eMp9Yq__header",
  "separator": "tweet-header-module__eMp9Yq__separator",
  "twitterIcon": "tweet-header-module__eMp9Yq__twitterIcon",
  "username": "tweet-header-module__eMp9Yq__username",
});
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/icons/icons.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "verified": "icons-module__Sw1aPW__verified",
});
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/icons/verified.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Verified": (()=>Verified)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$icons$2f$icons$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/icons/icons.module.css [app-client] (css module)");
;
;
const Verified = ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("svg", {
        viewBox: "0 0 24 24",
        "aria-label": "Verified account",
        role: "img",
        className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$icons$2f$icons$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].verified,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("g", {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("path", {
                d: "M22.25 12c0-1.43-.88-2.67-2.19-3.34.46-1.39.2-2.9-.81-3.91s-2.52-1.27-3.91-.81c-.66-1.31-1.91-2.19-3.34-2.19s-2.67.88-3.33 2.19c-1.4-.46-2.91-.2-3.92.81s-1.26 2.52-.8 3.91c-1.31.67-2.2 1.91-2.2 3.34s.89 2.67 2.2 3.34c-.46 1.39-.21 2.9.8 3.91s2.52 1.26 3.91.81c.67 1.31 1.91 2.19 3.34 2.19s2.68-.88 3.34-2.19c1.39.45 2.9.2 3.91-.81s1.27-2.52.81-3.91c1.31-.67 2.19-1.91 2.19-3.34zm-11.71 4.2L6.8 12.46l1.41-1.42 2.26 2.26 4.8-5.23 1.47 1.36-6.2 6.77z"
            })
        })
    });
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/icons/verified-business.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "VerifiedBusiness": (()=>VerifiedBusiness)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$icons$2f$icons$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/icons/icons.module.css [app-client] (css module)");
;
;
const VerifiedBusiness = ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("svg", {
        viewBox: "0 0 22 22",
        "aria-label": "Verified account",
        role: "img",
        className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$icons$2f$icons$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].verified,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])("g", {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])("linearGradient", {
                    gradientUnits: "userSpaceOnUse",
                    id: "0-a",
                    x1: "4.411",
                    x2: "18.083",
                    y1: "2.495",
                    y2: "21.508",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("stop", {
                            offset: "0",
                            stopColor: "#f4e72a"
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("stop", {
                            offset: ".539",
                            stopColor: "#cd8105"
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("stop", {
                            offset: ".68",
                            stopColor: "#cb7b00"
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("stop", {
                            offset: "1",
                            stopColor: "#f4ec26"
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("stop", {
                            offset: "1",
                            stopColor: "#f4e72a"
                        })
                    ]
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])("linearGradient", {
                    gradientUnits: "userSpaceOnUse",
                    id: "0-b",
                    x1: "5.355",
                    x2: "16.361",
                    y1: "3.395",
                    y2: "19.133",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("stop", {
                            offset: "0",
                            stopColor: "#f9e87f"
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("stop", {
                            offset: ".406",
                            stopColor: "#e2b719"
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("stop", {
                            offset: ".989",
                            stopColor: "#e2b719"
                        })
                    ]
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])("g", {
                    clipRule: "evenodd",
                    fillRule: "evenodd",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("path", {
                            d: "M13.324 3.848L11 1.6 8.676 3.848l-3.201-.453-.559 3.184L2.06 8.095 3.48 11l-1.42 2.904 2.856 1.516.559 3.184 3.201-.452L11 20.4l2.324-2.248 3.201.452.559-3.184 2.856-1.516L18.52 11l1.42-2.905-2.856-1.516-.559-3.184zm-7.09 7.575l3.428 3.428 5.683-6.206-1.347-1.247-4.4 4.795-2.072-2.072z",
                            fill: "url(#0-a)"
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("path", {
                            d: "M13.101 4.533L11 2.5 8.899 4.533l-2.895-.41-.505 2.88-2.583 1.37L4.2 11l-1.284 2.627 2.583 1.37.505 2.88 2.895-.41L11 19.5l2.101-2.033 2.895.41.505-2.88 2.583-1.37L17.8 11l1.284-2.627-2.583-1.37-.505-2.88zm-6.868 6.89l3.429 3.428 5.683-6.206-1.347-1.247-4.4 4.795-2.072-2.072z",
                            fill: "url(#0-b)"
                        }),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("path", {
                            d: "M6.233 11.423l3.429 3.428 5.65-6.17.038-.033-.005 1.398-5.683 6.206-3.429-3.429-.003-1.405.005.003z",
                            fill: "#d18800"
                        })
                    ]
                })
            ]
        })
    });
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/icons/verified-government.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "VerifiedGovernment": (()=>VerifiedGovernment)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$icons$2f$icons$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/icons/icons.module.css [app-client] (css module)");
;
;
const VerifiedGovernment = ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("svg", {
        viewBox: "0 0 22 22",
        "aria-label": "Verified account",
        role: "img",
        className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$icons$2f$icons$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].verified,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("g", {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("path", {
                clipRule: "evenodd",
                d: "M12.05 2.056c-.568-.608-1.532-.608-2.1 0l-1.393 1.49c-.284.303-.685.47-1.1.455L5.42 3.932c-.832-.028-1.514.654-1.486 1.486l.069 2.039c.014.415-.152.816-.456 1.1l-1.49 1.392c-.608.568-.608 1.533 0 2.101l1.49 1.393c.304.284.47.684.456 1.1l-.07 2.038c-.027.832.655 1.514 1.487 1.486l2.038-.069c.415-.014.816.152 1.1.455l1.392 1.49c.569.609 1.533.609 2.102 0l1.393-1.49c.283-.303.684-.47 1.099-.455l2.038.069c.832.028 1.515-.654 1.486-1.486L18 14.542c-.015-.415.152-.815.455-1.099l1.49-1.393c.608-.568.608-1.533 0-2.101l-1.49-1.393c-.303-.283-.47-.684-.455-1.1l.068-2.038c.029-.832-.654-1.514-1.486-1.486l-2.038.07c-.415.013-.816-.153-1.1-.456zm-5.817 9.367l3.429 3.428 5.683-6.206-1.347-1.247-4.4 4.795-2.072-2.072z",
                fillRule: "evenodd"
            })
        })
    });
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/verified-badge.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "verifiedBlue": "verified-badge-module__SIj1QG__verifiedBlue",
  "verifiedGovernment": "verified-badge-module__SIj1QG__verifiedGovernment",
  "verifiedOld": "verified-badge-module__SIj1QG__verifiedOld",
});
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/verified-badge.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "VerifiedBadge": (()=>VerifiedBadge)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$icons$2f$verified$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/icons/verified.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$icons$2f$verified$2d$business$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/icons/verified-business.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$icons$2f$verified$2d$government$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/icons/verified-government.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$verified$2d$badge$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/verified-badge.module.css [app-client] (css module)");
;
;
;
;
const VerifiedBadge = ({ user, className })=>{
    const verified = user.verified || user.is_blue_verified || user.verified_type;
    let icon = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$icons$2f$verified$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Verified"], {});
    let iconClassName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$verified$2d$badge$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].verifiedBlue;
    if (verified) {
        if (!user.is_blue_verified) {
            iconClassName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$verified$2d$badge$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].verifiedOld;
        }
        switch(user.verified_type){
            case 'Government':
                icon = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$icons$2f$verified$2d$government$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VerifiedGovernment"], {});
                iconClassName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$verified$2d$badge$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].verifiedGovernment;
                break;
            case 'Business':
                icon = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$icons$2f$verified$2d$business$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VerifiedBusiness"], {});
                iconClassName = null;
                break;
        }
    }
    return verified ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(className, iconClassName),
        children: icon
    }) : null;
};
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-header.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TweetHeader": (()=>TweetHeader)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$avatar$2d$img$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/avatar-img.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$header$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-header.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$verified$2d$badge$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/verified-badge.js [app-client] (ecmascript)");
;
;
;
;
;
const TweetHeader = ({ tweet, components })=>{
    var _components_AvatarImg;
    const Img = (_components_AvatarImg = components == null ? void 0 : components.AvatarImg) != null ? _components_AvatarImg : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$avatar$2d$img$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AvatarImg"];
    const { user } = tweet;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$header$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].header,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])("a", {
                href: tweet.url,
                className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$header$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].avatar,
                target: "_blank",
                rel: "noopener noreferrer",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$header$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].avatarOverflow, user.profile_image_shape === 'Square' && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$header$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].avatarSquare),
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(Img, {
                            src: user.profile_image_url_https,
                            alt: user.name,
                            width: 48,
                            height: 48
                        })
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$header$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].avatarOverflow,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$header$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].avatarShadow
                        })
                    })
                ]
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$header$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].author,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])("a", {
                        href: tweet.url,
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$header$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].authorLink,
                        target: "_blank",
                        rel: "noopener noreferrer",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$header$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].authorLinkText,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("span", {
                                    title: user.name,
                                    children: user.name
                                })
                            }),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$verified$2d$badge$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VerifiedBadge"], {
                                user: user,
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$header$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].authorVerified
                            })
                        ]
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$header$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].authorMeta,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("a", {
                                href: tweet.url,
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$header$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].username,
                                target: "_blank",
                                rel: "noopener noreferrer",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])("span", {
                                    title: `@${user.screen_name}`,
                                    children: [
                                        "@",
                                        user.screen_name
                                    ]
                                })
                            }),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])("div", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$header$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].authorFollow,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("span", {
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$header$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].separator,
                                        children: "·"
                                    }),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("a", {
                                        href: user.follow_url,
                                        className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$header$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].follow,
                                        target: "_blank",
                                        rel: "noopener noreferrer",
                                        children: "Follow"
                                    })
                                ]
                            })
                        ]
                    })
                ]
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("a", {
                href: tweet.url,
                className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$header$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].brand,
                target: "_blank",
                rel: "noopener noreferrer",
                "aria-label": "View on Twitter",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("svg", {
                    viewBox: "0 0 24 24",
                    "aria-hidden": "true",
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$header$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].twitterIcon,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("g", {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("path", {
                            d: "M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"
                        })
                    })
                })
            })
        ]
    });
};
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "root": "tweet-in-reply-to-module__Rykmaq__root",
});
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TweetInReplyTo": (()=>TweetInReplyTo)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$in$2d$reply$2d$to$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.module.css [app-client] (css module)");
;
;
const TweetInReplyTo = ({ tweet })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])("a", {
        href: tweet.in_reply_to_url,
        className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$in$2d$reply$2d$to$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].root,
        target: "_blank",
        rel: "noopener noreferrer",
        children: [
            "Replying to @",
            tweet.in_reply_to_screen_name
        ]
    });
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-link.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "root": "tweet-link-module__yeOBbW__root",
});
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-link.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TweetLink": (()=>TweetLink)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$link$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-link.module.css [app-client] (css module)");
;
;
const TweetLink = ({ href, children })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("a", {
        href: href,
        className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$link$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].root,
        target: "_blank",
        rel: "noopener noreferrer nofollow",
        children: children
    });
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-body.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "root": "tweet-body-module__P73Vuq__root",
});
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-body.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TweetBody": (()=>TweetBody)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$body$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-body.module.css [app-client] (css module)");
;
;
;
const TweetBody = ({ tweet })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("p", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$body$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].root,
        lang: tweet.lang,
        dir: "auto",
        children: tweet.entities.map((item, i)=>{
            switch(item.type){
                case 'hashtag':
                case 'mention':
                case 'url':
                case 'symbol':
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TweetLink"], {
                        href: item.href,
                        children: item.text
                    }, i);
                case 'media':
                    // Media text is currently never displayed, some tweets however might have indices
                    // that do match `display_text_range` so for those cases we ignore the content.
                    return;
                default:
                    // We use `dangerouslySetInnerHTML` to preserve the text encoding.
                    // https://github.com/vercel-labs/react-tweet/issues/29
                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("span", {
                        dangerouslySetInnerHTML: {
                            __html: item.text
                        }
                    }, i);
            }
        })
    });
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/utils.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "enrichTweet": (()=>enrichTweet),
    "formatNumber": (()=>formatNumber),
    "getMediaUrl": (()=>getMediaUrl),
    "getMp4Video": (()=>getMp4Video),
    "getMp4Videos": (()=>getMp4Videos)
});
const getTweetUrl = (tweet)=>`https://x.com/${tweet.user.screen_name}/status/${tweet.id_str}`;
const getUserUrl = (usernameOrTweet)=>`https://x.com/${typeof usernameOrTweet === 'string' ? usernameOrTweet : usernameOrTweet.user.screen_name}`;
const getLikeUrl = (tweet)=>`https://x.com/intent/like?tweet_id=${tweet.id_str}`;
const getReplyUrl = (tweet)=>`https://x.com/intent/tweet?in_reply_to=${tweet.id_str}`;
const getFollowUrl = (tweet)=>`https://x.com/intent/follow?screen_name=${tweet.user.screen_name}`;
const getHashtagUrl = (hashtag)=>`https://x.com/hashtag/${hashtag.text}`;
const getSymbolUrl = (symbol)=>`https://x.com/search?q=%24${symbol.text}`;
const getInReplyToUrl = (tweet)=>`https://x.com/${tweet.in_reply_to_screen_name}/status/${tweet.in_reply_to_status_id_str}`;
const getMediaUrl = (media, size)=>{
    const url = new URL(media.media_url_https);
    const extension = url.pathname.split('.').pop();
    if (!extension) return media.media_url_https;
    url.pathname = url.pathname.replace(`.${extension}`, '');
    url.searchParams.set('format', extension);
    url.searchParams.set('name', size);
    return url.toString();
};
const getMp4Videos = (media)=>{
    const { variants } = media.video_info;
    const sortedMp4Videos = variants.filter((vid)=>vid.content_type === 'video/mp4').sort((a, b)=>{
        var _b_bitrate, _a_bitrate;
        return ((_b_bitrate = b.bitrate) != null ? _b_bitrate : 0) - ((_a_bitrate = a.bitrate) != null ? _a_bitrate : 0);
    });
    return sortedMp4Videos;
};
const getMp4Video = (media)=>{
    const mp4Videos = getMp4Videos(media);
    // Skip the highest quality video and use the next quality
    return mp4Videos.length > 1 ? mp4Videos[1] : mp4Videos[0];
};
const formatNumber = (n)=>{
    if (n > 999999) return `${(n / 1000000).toFixed(1)}M`;
    if (n > 999) return `${(n / 1000).toFixed(1)}K`;
    return n.toString();
};
function getEntities(tweet) {
    const textMap = Array.from(tweet.text);
    const result = [
        {
            indices: tweet.display_text_range,
            type: 'text'
        }
    ];
    addEntities(result, 'hashtag', tweet.entities.hashtags);
    addEntities(result, 'mention', tweet.entities.user_mentions);
    addEntities(result, 'url', tweet.entities.urls);
    addEntities(result, 'symbol', tweet.entities.symbols);
    if (tweet.entities.media) {
        addEntities(result, 'media', tweet.entities.media);
    }
    fixRange(tweet, result);
    return result.map((entity)=>{
        const text = textMap.slice(entity.indices[0], entity.indices[1]).join('');
        switch(entity.type){
            case 'hashtag':
                return Object.assign(entity, {
                    href: getHashtagUrl(entity),
                    text
                });
            case 'mention':
                return Object.assign(entity, {
                    href: getUserUrl(entity.screen_name),
                    text
                });
            case 'url':
            case 'media':
                return Object.assign(entity, {
                    href: entity.expanded_url,
                    text: entity.display_url
                });
            case 'symbol':
                return Object.assign(entity, {
                    href: getSymbolUrl(entity),
                    text
                });
            default:
                return Object.assign(entity, {
                    text
                });
        }
    });
}
function addEntities(result, type, entities) {
    for (const entity of entities){
        for (const [i, item] of result.entries()){
            if (item.indices[0] > entity.indices[0] || item.indices[1] < entity.indices[1]) {
                continue;
            }
            const items = [
                {
                    ...entity,
                    type
                }
            ];
            if (item.indices[0] < entity.indices[0]) {
                items.unshift({
                    indices: [
                        item.indices[0],
                        entity.indices[0]
                    ],
                    type: 'text'
                });
            }
            if (item.indices[1] > entity.indices[1]) {
                items.push({
                    indices: [
                        entity.indices[1],
                        item.indices[1]
                    ],
                    type: 'text'
                });
            }
            result.splice(i, 1, ...items);
            break; // Break out of the loop to avoid iterating over the new items
        }
    }
}
/**
 * Update display_text_range to work w/ Array.from
 * Array.from is unicode aware, unlike string.slice()
 */ function fixRange(tweet, entities) {
    if (tweet.entities.media && tweet.entities.media[0].indices[0] < tweet.display_text_range[1]) {
        tweet.display_text_range[1] = tweet.entities.media[0].indices[0];
    }
    const lastEntity = entities.at(-1);
    if (lastEntity && lastEntity.indices[1] > tweet.display_text_range[1]) {
        lastEntity.indices[1] = tweet.display_text_range[1];
    }
}
const enrichTweet = (tweet)=>({
        ...tweet,
        url: getTweetUrl(tweet),
        user: {
            ...tweet.user,
            url: getUserUrl(tweet),
            follow_url: getFollowUrl(tweet)
        },
        like_url: getLikeUrl(tweet),
        reply_url: getReplyUrl(tweet),
        in_reply_to_url: tweet.in_reply_to_screen_name ? getInReplyToUrl(tweet) : undefined,
        entities: getEntities(tweet),
        quoted_tweet: tweet.quoted_tweet ? {
            ...tweet.quoted_tweet,
            url: getTweetUrl(tweet.quoted_tweet),
            entities: getEntities(tweet.quoted_tweet)
        } : undefined
    });
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-media.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "grid2Columns": "tweet-media-module__0eLn2W__grid2Columns",
  "grid2x2": "tweet-media-module__0eLn2W__grid2x2",
  "grid3": "tweet-media-module__0eLn2W__grid3",
  "image": "tweet-media-module__0eLn2W__image",
  "mediaContainer": "tweet-media-module__0eLn2W__mediaContainer",
  "mediaLink": "tweet-media-module__0eLn2W__mediaLink",
  "mediaWrapper": "tweet-media-module__0eLn2W__mediaWrapper",
  "root": "tweet-media-module__0eLn2W__root",
  "rounded": "tweet-media-module__0eLn2W__rounded",
  "skeleton": "tweet-media-module__0eLn2W__skeleton",
});
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-media-video.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "anchor": "tweet-media-video-module__kMdDIG__anchor",
  "videoButton": "tweet-media-video-module__kMdDIG__videoButton",
  "videoButtonIcon": "tweet-media-video-module__kMdDIG__videoButtonIcon",
  "viewReplies": "tweet-media-video-module__kMdDIG__viewReplies",
  "watchOnTwitter": "tweet-media-video-module__kMdDIG__watchOnTwitter",
});
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-media-video.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TweetMediaVideo": (()=>TweetMediaVideo)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$media$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-media.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$media$2d$video$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-media-video.module.css [app-client] (css module)");
'use client';
;
;
;
;
;
;
const TweetMediaVideo = ({ tweet, media })=>{
    const [playButton, setPlayButton] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [isPlaying, setIsPlaying] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [ended, setEnded] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const mp4Video = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getMp4Video"])(media);
    let timeout = 0;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("video", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$media$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].image,
                poster: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getMediaUrl"])(media, 'small'),
                controls: !playButton,
                playsInline: true,
                preload: "none",
                tabIndex: playButton ? -1 : 0,
                onPlay: ()=>{
                    if (timeout) window.clearTimeout(timeout);
                    if (!isPlaying) setIsPlaying(true);
                    if (ended) setEnded(false);
                },
                onPause: ()=>{
                    // When the video is seeked (moved to a different timestamp), it will pause for a moment
                    // before resuming. We don't want to show the message in that case so we wait a bit.
                    if (timeout) window.clearTimeout(timeout);
                    timeout = window.setTimeout(()=>{
                        if (isPlaying) setIsPlaying(false);
                        timeout = 0;
                    }, 100);
                },
                onEnded: ()=>{
                    setEnded(true);
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("source", {
                    src: mp4Video.url,
                    type: mp4Video.content_type
                })
            }),
            playButton && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("button", {
                type: "button",
                className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$media$2d$video$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].videoButton,
                "aria-label": "View video on X",
                onClick: (e)=>{
                    const video = e.currentTarget.previousSibling;
                    e.preventDefault();
                    setPlayButton(false);
                    video.load();
                    video.play().then(()=>{
                        setIsPlaying(true);
                        video.focus();
                    }).catch((error)=>{
                        console.error('Error playing video:', error);
                        setPlayButton(true);
                        setIsPlaying(false);
                    });
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("svg", {
                    viewBox: "0 0 24 24",
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$media$2d$video$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].videoButtonIcon,
                    "aria-hidden": "true",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("g", {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("path", {
                            d: "M21 12L4 2v20l17-10z"
                        })
                    })
                })
            }),
            !isPlaying && !ended && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$media$2d$video$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].watchOnTwitter,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("a", {
                    href: tweet.url,
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$media$2d$video$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].anchor,
                    target: "_blank",
                    rel: "noopener noreferrer",
                    children: playButton ? 'Watch on X' : 'Continue watching on X'
                })
            }),
            ended && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("a", {
                href: tweet.url,
                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$media$2d$video$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].anchor, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$media$2d$video$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].viewReplies),
                target: "_blank",
                rel: "noopener noreferrer",
                children: "View replies"
            })
        ]
    });
};
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/media-img.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "MediaImg": (()=>MediaImg)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
;
const MediaImg = (props)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("img", {
        ...props
    });
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-media.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TweetMedia": (()=>TweetMedia)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$media$2d$video$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-media-video.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$media$2d$img$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/media-img.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$media$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-media.module.css [app-client] (css module)");
;
;
;
;
;
;
;
const getSkeletonStyle = (media, itemCount)=>{
    let paddingBottom = 56.25 // default of 16x9
    ;
    // if we only have 1 item, show at original ratio
    if (itemCount === 1) paddingBottom = 100 / media.original_info.width * media.original_info.height;
    // if we have 2 items, double the default to be 16x9 total
    if (itemCount === 2) paddingBottom = paddingBottom * 2;
    return {
        width: media.type === 'photo' ? undefined : 'unset',
        paddingBottom: `${paddingBottom}%`
    };
};
const TweetMedia = ({ tweet, components, quoted })=>{
    var _tweet_mediaDetails, _tweet_mediaDetails1;
    var _tweet_mediaDetails_length;
    const length = (_tweet_mediaDetails_length = (_tweet_mediaDetails = tweet.mediaDetails) == null ? void 0 : _tweet_mediaDetails.length) != null ? _tweet_mediaDetails_length : 0;
    var _components_MediaImg;
    const Img = (_components_MediaImg = components == null ? void 0 : components.MediaImg) != null ? _components_MediaImg : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$media$2d$img$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MediaImg"];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$media$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].root, !quoted && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$media$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].rounded),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$media$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].mediaWrapper, length > 1 && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$media$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].grid2Columns, length === 3 && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$media$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].grid3, length > 4 && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$media$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].grid2x2),
            children: (_tweet_mediaDetails1 = tweet.mediaDetails) == null ? void 0 : _tweet_mediaDetails1.map((media)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                    children: media.type === 'photo' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])("a", {
                        href: tweet.url,
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$media$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].mediaContainer, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$media$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].mediaLink),
                        target: "_blank",
                        rel: "noopener noreferrer",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$media$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].skeleton,
                                style: getSkeletonStyle(media, length)
                            }),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(Img, {
                                src: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getMediaUrl"])(media, 'small'),
                                alt: media.ext_alt_text || 'Image',
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$media$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].image,
                                draggable: true
                            })
                        ]
                    }, media.media_url_https) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$media$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].mediaContainer,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
                                className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$media$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].skeleton,
                                style: getSkeletonStyle(media, length)
                            }),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$media$2d$video$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TweetMediaVideo"], {
                                tweet: tweet,
                                media: media
                            })
                        ]
                    }, media.media_url_https)
                }, media.media_url_https))
        })
    });
};
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/date-utils.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "formatDate": (()=>formatDate)
});
const options = {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
    weekday: 'short',
    month: 'short',
    day: 'numeric',
    year: 'numeric'
};
const formatter = new Intl.DateTimeFormat('en-US', options);
const partsArrayToObject = (parts)=>{
    const result = {};
    for (const part of parts){
        result[part.type] = part.value;
    }
    return result;
};
const formatDate = (date)=>{
    const parts = partsArrayToObject(formatter.formatToParts(date));
    const formattedTime = `${parts.hour}:${parts.minute} ${parts.dayPeriod}`;
    const formattedDate = `${parts.month} ${parts.day}, ${parts.year}`;
    return `${formattedTime} · ${formattedDate}`;
};
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "root": "tweet-info-created-at-module__d3DV9a__root",
});
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TweetInfoCreatedAt": (()=>TweetInfoCreatedAt)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$date$2d$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/date-utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$info$2d$created$2d$at$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.module.css [app-client] (css module)");
;
;
;
const TweetInfoCreatedAt = ({ tweet })=>{
    const createdAt = new Date(tweet.created_at);
    const formattedCreatedAtDate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$date$2d$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatDate"])(createdAt);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("a", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$info$2d$created$2d$at$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].root,
        href: tweet.url,
        target: "_blank",
        rel: "noopener noreferrer",
        "aria-label": formattedCreatedAtDate,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("time", {
            dateTime: createdAt.toISOString(),
            children: formattedCreatedAtDate
        })
    });
};
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-info.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "info": "tweet-info-module__t5sx6W__info",
  "infoIcon": "tweet-info-module__t5sx6W__infoIcon",
  "infoLink": "tweet-info-module__t5sx6W__infoLink",
});
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-info.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TweetInfo": (()=>TweetInfo)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$info$2d$created$2d$at$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$info$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-info.module.css [app-client] (css module)");
;
;
;
const TweetInfo = ({ tweet })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$info$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].info,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$info$2d$created$2d$at$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TweetInfoCreatedAt"], {
                tweet: tweet
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("a", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$info$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].infoLink,
                href: "https://help.x.com/en/x-for-websites-ads-info-and-privacy",
                target: "_blank",
                rel: "noopener noreferrer",
                "aria-label": "Twitter for Websites, Ads Information and Privacy",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("svg", {
                    viewBox: "0 0 24 24",
                    "aria-hidden": "true",
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$info$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].infoIcon,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("g", {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("path", {
                            d: "M13.5 8.5c0 .83-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5S11.17 7 12 7s1.5.67 1.5 1.5zM13 17v-5h-2v5h2zm-1 5.25c5.66 0 10.25-4.59 10.25-10.25S17.66 1.75 12 1.75 1.75 6.34 1.75 12 6.34 22.25 12 22.25zM20.25 12c0 4.56-3.69 8.25-8.25 8.25S3.75 16.56 3.75 12 7.44 3.75 12 3.75s8.25 3.69 8.25 8.25z"
                        })
                    })
                })
            })
        ]
    });
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-actions.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "actions": "tweet-actions-module__4dwWOa__actions",
  "copy": "tweet-actions-module__4dwWOa__copy",
  "copyIcon": "tweet-actions-module__4dwWOa__copyIcon",
  "copyIconWrapper": "tweet-actions-module__4dwWOa__copyIconWrapper",
  "copyText": "tweet-actions-module__4dwWOa__copyText",
  "like": "tweet-actions-module__4dwWOa__like",
  "likeCount": "tweet-actions-module__4dwWOa__likeCount",
  "likeIcon": "tweet-actions-module__4dwWOa__likeIcon",
  "likeIconWrapper": "tweet-actions-module__4dwWOa__likeIconWrapper",
  "reply": "tweet-actions-module__4dwWOa__reply",
  "replyIcon": "tweet-actions-module__4dwWOa__replyIcon",
  "replyIconWrapper": "tweet-actions-module__4dwWOa__replyIconWrapper",
  "replyText": "tweet-actions-module__4dwWOa__replyText",
});
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-actions-copy.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TweetActionsCopy": (()=>TweetActionsCopy)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$actions$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-actions.module.css [app-client] (css module)");
'use client';
;
;
;
const TweetActionsCopy = ({ tweet })=>{
    const [copied, setCopied] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const handleCopy = ()=>{
        navigator.clipboard.writeText(tweet.url);
        setCopied(true);
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TweetActionsCopy.useEffect": ()=>{
            if (copied) {
                const timeout = setTimeout({
                    "TweetActionsCopy.useEffect.timeout": ()=>{
                        setCopied(false);
                    }
                }["TweetActionsCopy.useEffect.timeout"], 6000);
                return ({
                    "TweetActionsCopy.useEffect": ()=>clearTimeout(timeout)
                })["TweetActionsCopy.useEffect"];
            }
        }
    }["TweetActionsCopy.useEffect"], [
        copied
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])("button", {
        type: "button",
        className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$actions$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].copy,
        "aria-label": "Copy link",
        onClick: handleCopy,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$actions$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].copyIconWrapper,
                children: copied ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("svg", {
                    viewBox: "0 0 24 24",
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$actions$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].copyIcon,
                    "aria-hidden": "true",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("g", {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("path", {
                            d: "M9.64 18.952l-5.55-4.861 1.317-1.504 3.951 3.459 8.459-10.948L19.4 6.32 9.64 18.952z"
                        })
                    })
                }) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("svg", {
                    viewBox: "0 0 24 24",
                    className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$actions$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].copyIcon,
                    "aria-hidden": "true",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("g", {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("path", {
                            d: "M18.36 5.64c-1.95-1.96-5.11-1.96-7.07 0L9.88 7.05 8.46 5.64l1.42-1.42c2.73-2.73 7.16-2.73 9.9 0 2.73 2.74 2.73 7.17 0 9.9l-1.42 1.42-1.41-1.42 1.41-1.41c1.96-1.96 1.96-5.12 0-7.07zm-2.12 3.53l-7.07 7.07-1.41-1.41 7.07-7.07 1.41 1.41zm-12.02.71l1.42-1.42 1.41 1.42-1.41 1.41c-1.96 1.96-1.96 5.12 0 7.07 1.95 1.96 5.11 1.96 7.07 0l1.41-1.41 1.42 1.41-1.42 1.42c-2.73 2.73-7.16 2.73-9.9 0-2.73-2.74-2.73-7.17 0-9.9z"
                        })
                    })
                })
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("span", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$actions$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].copyText,
                children: copied ? 'Copied!' : 'Copy link'
            })
        ]
    });
};
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-actions.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TweetActions": (()=>TweetActions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$actions$2d$copy$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-actions-copy.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$actions$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-actions.module.css [app-client] (css module)");
;
;
;
;
const TweetActions = ({ tweet })=>{
    const favoriteCount = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatNumber"])(tweet.favorite_count);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$actions$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].actions,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])("a", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$actions$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].like,
                href: tweet.like_url,
                target: "_blank",
                rel: "noopener noreferrer",
                "aria-label": `Like. This Tweet has ${favoriteCount} likes`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$actions$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].likeIconWrapper,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("svg", {
                            viewBox: "0 0 24 24",
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$actions$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].likeIcon,
                            "aria-hidden": "true",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("g", {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("path", {
                                    d: "M20.884 13.19c-1.351 2.48-4.001 5.12-8.379 7.67l-.503.3-.504-.3c-4.379-2.55-7.029-5.19-8.382-7.67-1.36-2.5-1.41-4.86-.514-6.67.887-1.79 2.647-2.91 4.601-3.01 1.651-.09 3.368.56 4.798 2.01 1.429-1.45 3.146-2.1 4.796-2.01 1.954.1 3.714 1.22 4.601 3.01.896 1.81.846 4.17-.514 6.67z"
                                })
                            })
                        })
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("span", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$actions$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].likeCount,
                        children: favoriteCount
                    })
                ]
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])("a", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$actions$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].reply,
                href: tweet.reply_url,
                target: "_blank",
                rel: "noopener noreferrer",
                "aria-label": "Reply to this Tweet on Twitter",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$actions$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].replyIconWrapper,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("svg", {
                            viewBox: "0 0 24 24",
                            className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$actions$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].replyIcon,
                            "aria-hidden": "true",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("g", {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("path", {
                                    d: "M1.751 10c0-4.42 3.584-8 8.005-8h4.366c4.49 0 8.129 3.64 8.129 8.13 0 2.96-1.607 5.68-4.196 7.11l-8.054 4.46v-3.69h-.067c-4.49.1-8.183-3.51-8.183-8.01z"
                                })
                            })
                        })
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("span", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$actions$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].replyText,
                        children: "Reply"
                    })
                ]
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$actions$2d$copy$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TweetActionsCopy"], {
                tweet: tweet
            })
        ]
    });
};
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-replies.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "link": "tweet-replies-module__gLz8CW__link",
  "replies": "tweet-replies-module__gLz8CW__replies",
  "text": "tweet-replies-module__gLz8CW__text",
});
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-replies.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TweetReplies": (()=>TweetReplies)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$replies$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-replies.module.css [app-client] (css module)");
;
;
;
const TweetReplies = ({ tweet })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$replies$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].replies,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("a", {
            className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$replies$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].link,
            href: tweet.url,
            target: "_blank",
            rel: "noopener noreferrer",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("span", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$replies$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].text,
                children: tweet.conversation_count === 0 ? 'Read more on X' : tweet.conversation_count === 1 ? `Read ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatNumber"])(tweet.conversation_count)} reply` : `Read ${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatNumber"])(tweet.conversation_count)} replies`
            })
        })
    });
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "article": "quoted-tweet-container-module__NjboUa__article",
  "root": "quoted-tweet-container-module__NjboUa__root",
});
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "QuotedTweetContainer": (()=>QuotedTweetContainer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$quoted$2d$tweet$2f$quoted$2d$tweet$2d$container$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.module.css [app-client] (css module)");
'use client';
;
;
const QuotedTweetContainer = ({ tweet, children })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$quoted$2d$tweet$2f$quoted$2d$tweet$2d$container$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].root,
        onClick: (e)=>{
            e.preventDefault();
            window.open(tweet.url, '_blank');
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("article", {
            className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$quoted$2d$tweet$2f$quoted$2d$tweet$2d$container$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].article,
            children: children
        })
    });
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "author": "quoted-tweet-header-module__zEsWUa__author",
  "authorText": "quoted-tweet-header-module__zEsWUa__authorText",
  "avatar": "quoted-tweet-header-module__zEsWUa__avatar",
  "avatarSquare": "quoted-tweet-header-module__zEsWUa__avatarSquare",
  "header": "quoted-tweet-header-module__zEsWUa__header",
  "username": "quoted-tweet-header-module__zEsWUa__username",
});
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "QuotedTweetHeader": (()=>QuotedTweetHeader)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$avatar$2d$img$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/avatar-img.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$quoted$2d$tweet$2f$quoted$2d$tweet$2d$header$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.module.css [app-client] (css module)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$verified$2d$badge$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/verified-badge.js [app-client] (ecmascript)");
;
;
;
;
;
const QuotedTweetHeader = ({ tweet })=>{
    const { user } = tweet;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])("div", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$quoted$2d$tweet$2f$quoted$2d$tweet$2d$header$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].header,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("a", {
                href: tweet.url,
                className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$quoted$2d$tweet$2f$quoted$2d$tweet$2d$header$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].avatar,
                target: "_blank",
                rel: "noopener noreferrer",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$clsx$40$2$2e$1$2e$1$2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$quoted$2d$tweet$2f$quoted$2d$tweet$2d$header$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].avatarOverflow, user.profile_image_shape === 'Square' && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$quoted$2d$tweet$2f$quoted$2d$tweet$2d$header$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].avatarSquare),
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$avatar$2d$img$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AvatarImg"], {
                        src: user.profile_image_url_https,
                        alt: user.name,
                        width: 20,
                        height: 20
                    })
                })
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])("div", {
                className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$quoted$2d$tweet$2f$quoted$2d$tweet$2d$header$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].author,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$quoted$2d$tweet$2f$quoted$2d$tweet$2d$header$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].authorText,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("span", {
                            title: user.name,
                            children: user.name
                        })
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$verified$2d$badge$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["VerifiedBadge"], {
                        user: user
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
                        className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$quoted$2d$tweet$2f$quoted$2d$tweet$2d$header$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].username,
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])("span", {
                            title: `@${user.screen_name}`,
                            children: [
                                "@",
                                user.screen_name
                            ]
                        })
                    })
                ]
            })
        ]
    });
};
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "root": "quoted-tweet-body-module__qtcc7q__root",
});
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "QuotedTweetBody": (()=>QuotedTweetBody)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$quoted$2d$tweet$2f$quoted$2d$tweet$2d$body$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.module.css [app-client] (css module)");
;
;
const QuotedTweetBody = ({ tweet })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("p", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$quoted$2d$tweet$2f$quoted$2d$tweet$2d$body$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].root,
        lang: tweet.lang,
        dir: "auto",
        children: tweet.entities.map((item, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("span", {
                dangerouslySetInnerHTML: {
                    __html: item.text
                }
            }, i))
    });
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "QuotedTweet": (()=>QuotedTweet)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$quoted$2d$tweet$2f$quoted$2d$tweet$2d$container$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$quoted$2d$tweet$2f$quoted$2d$tweet$2d$header$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$quoted$2d$tweet$2f$quoted$2d$tweet$2d$body$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$media$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-media.js [app-client] (ecmascript)");
;
;
;
;
;
const QuotedTweet = ({ tweet })=>{
    var _tweet_mediaDetails;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$quoted$2d$tweet$2f$quoted$2d$tweet$2d$container$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QuotedTweetContainer"], {
        tweet: tweet,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$quoted$2d$tweet$2f$quoted$2d$tweet$2d$header$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QuotedTweetHeader"], {
                tweet: tweet
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$quoted$2d$tweet$2f$quoted$2d$tweet$2d$body$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QuotedTweetBody"], {
                tweet: tweet
            }),
            ((_tweet_mediaDetails = tweet.mediaDetails) == null ? void 0 : _tweet_mediaDetails.length) ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$media$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TweetMedia"], {
                quoted: true,
                tweet: tweet
            }) : null
        ]
    });
};
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/embedded-tweet.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "EmbeddedTweet": (()=>EmbeddedTweet)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$container$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-container.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$header$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-header.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$in$2d$reply$2d$to$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$body$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-body.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$media$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-media.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$info$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-info.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$actions$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-actions.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$replies$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-replies.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$quoted$2d$tweet$2f$quoted$2d$tweet$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
const EmbeddedTweet = ({ tweet: t, components })=>{
    var _tweet_mediaDetails;
    // useMemo does nothing for RSC but it helps when the component is used in the client (e.g by SWR)
    const tweet = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "EmbeddedTweet.useMemo[tweet]": ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["enrichTweet"])(t)
    }["EmbeddedTweet.useMemo[tweet]"], [
        t
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$container$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TweetContainer"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$header$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TweetHeader"], {
                tweet: tweet,
                components: components
            }),
            tweet.in_reply_to_status_id_str && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$in$2d$reply$2d$to$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TweetInReplyTo"], {
                tweet: tweet
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$body$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TweetBody"], {
                tweet: tweet
            }),
            ((_tweet_mediaDetails = tweet.mediaDetails) == null ? void 0 : _tweet_mediaDetails.length) ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$media$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TweetMedia"], {
                tweet: tweet,
                components: components
            }) : null,
            tweet.quoted_tweet && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$quoted$2d$tweet$2f$quoted$2d$tweet$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QuotedTweet"], {
                tweet: tweet.quoted_tweet
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$info$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TweetInfo"], {
                tweet: tweet
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$actions$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TweetActions"], {
                tweet: tweet
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$replies$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TweetReplies"], {
                tweet: tweet
            })
        ]
    });
};
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-not-found.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "root": "tweet-not-found-module__utwuua__root",
});
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-not-found.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TweetNotFound": (()=>TweetNotFound)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$container$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-container.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$not$2d$found$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-not-found.module.css [app-client] (css module)");
;
;
;
const TweetNotFound = (_props)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$container$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TweetContainer"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])("div", {
            className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$not$2d$found$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].root,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("h3", {
                    children: "Tweet not found"
                }),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("p", {
                    children: "The embedded tweet could not be found…"
                })
            ]
        })
    });
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/skeleton.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "loading": "skeleton-module__22bsIq__loading",
  "skeleton": "skeleton-module__22bsIq__skeleton",
});
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/skeleton.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Skeleton": (()=>Skeleton)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$skeleton$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/skeleton.module.css [app-client] (css module)");
;
;
const Skeleton = ({ style })=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("span", {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$skeleton$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].skeleton,
        style: style
    });
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.module.css [app-client] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "root": "tweet-skeleton-module__CfvzVa__root",
});
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TweetSkeleton": (()=>TweetSkeleton)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$container$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-container.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/skeleton.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$skeleton$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.module.css [app-client] (css module)");
;
;
;
;
const TweetSkeleton = ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxs"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$container$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TweetContainer"], {
        className: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$skeleton$2e$module$2e$css__$5b$app$2d$client$5d$__$28$css__module$29$__["default"].root,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Skeleton"], {
                style: {
                    height: '3rem',
                    marginBottom: '0.75rem'
                }
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Skeleton"], {
                style: {
                    height: '6rem',
                    margin: '0.5rem 0'
                }
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])("div", {
                style: {
                    borderTop: 'var(--tweet-border)',
                    margin: '0.5rem 0'
                }
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Skeleton"], {
                style: {
                    height: '2rem'
                }
            }),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Skeleton"], {
                style: {
                    height: '2rem',
                    borderRadius: '9999px',
                    marginTop: '0.5rem'
                }
            })
        ]
    });
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/api/fetch-tweet.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "TwitterApiError": (()=>TwitterApiError),
    "fetchTweet": (()=>fetchTweet)
});
const SYNDICATION_URL = 'https://cdn.syndication.twimg.com';
class TwitterApiError extends Error {
    constructor({ message, status, data }){
        super(message);
        this.name = 'TwitterApiError';
        this.status = status;
        this.data = data;
    }
}
const TWEET_ID = /^[0-9]+$/;
function getToken(id) {
    return (Number(id) / 1e15 * Math.PI).toString(6 ** 2).replace(/(0+|\.)/g, '');
}
async function fetchTweet(id, fetchOptions) {
    var _res_headers_get;
    if (id.length > 40 || !TWEET_ID.test(id)) {
        throw new Error(`Invalid tweet id: ${id}`);
    }
    const url = new URL(`${SYNDICATION_URL}/tweet-result`);
    url.searchParams.set('id', id);
    url.searchParams.set('lang', 'en');
    url.searchParams.set('features', [
        'tfw_timeline_list:',
        'tfw_follower_count_sunset:true',
        'tfw_tweet_edit_backend:on',
        'tfw_refsrc_session:on',
        'tfw_fosnr_soft_interventions_enabled:on',
        'tfw_show_birdwatch_pivots_enabled:on',
        'tfw_show_business_verified_badge:on',
        'tfw_duplicate_scribes_to_settings:on',
        'tfw_use_profile_image_shape_enabled:on',
        'tfw_show_blue_verified_badge:on',
        'tfw_legacy_timeline_sunset:true',
        'tfw_show_gov_verified_badge:on',
        'tfw_show_business_affiliate_badge:on',
        'tfw_tweet_edit_frontend:on'
    ].join(';'));
    url.searchParams.set('token', getToken(id));
    const res = await fetch(url.toString(), fetchOptions);
    const isJson = (_res_headers_get = res.headers.get('content-type')) == null ? void 0 : _res_headers_get.includes('application/json');
    const data = isJson ? await res.json() : undefined;
    if (res.ok) {
        if ((data == null ? void 0 : data.__typename) === 'TweetTombstone') {
            return {
                tombstone: true
            };
        }
        return {
            data
        };
    }
    if (res.status === 404) {
        return {
            notFound: true
        };
    }
    throw new TwitterApiError({
        message: typeof data.error === 'string' ? data.error : `Failed to fetch tweet at "${url}" with "${res.status}".`,
        status: res.status,
        data
    });
}
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/hooks.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useMounted": (()=>useMounted),
    "useTweet": (()=>useTweet)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$swr$40$2$2e$3$2e$3_react$40$19$2e$1$2e$0$2f$node_modules$2f$swr$2f$dist$2f$index$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/swr@2.3.3_react@19.1.0/node_modules/swr/dist/index/index.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$api$2f$fetch$2d$tweet$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/api/fetch-tweet.js [app-client] (ecmascript)");
'use client';
;
;
;
// Avoids an error when used in the pages directory where useSWR might be in `default`.
const useSWR = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$swr$40$2$2e$3$2e$3_react$40$19$2e$1$2e$0$2f$node_modules$2f$swr$2f$dist$2f$index$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].default || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$swr$40$2$2e$3$2e$3_react$40$19$2e$1$2e$0$2f$node_modules$2f$swr$2f$dist$2f$index$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"];
const host = 'https://react-tweet.vercel.app';
async function fetcher([url, fetchOptions]) {
    const res = await fetch(url, fetchOptions);
    const json = await res.json();
    // We return null in case `json.data` is undefined, that way we can check for "loading" by
    // checking if data is `undefined`. `null` means it was fetched.
    if (res.ok) return json.data || null;
    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$api$2f$fetch$2d$tweet$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TwitterApiError"]({
        message: `Failed to fetch tweet at "${url}" with "${res.status}".`,
        data: json,
        status: res.status
    });
}
const useTweet = (id, apiUrl, fetchOptions)=>{
    const { isLoading, data, error } = useSWR({
        "useTweet.useSWR": ()=>apiUrl || id ? [
                apiUrl || id && `${host}/api/tweet/${id}`,
                fetchOptions
            ] : null
    }["useTweet.useSWR"], fetcher, {
        revalidateIfStale: false,
        revalidateOnFocus: false,
        shouldRetryOnError: false
    });
    return {
        // If data is `undefined` then it might be the first render where SWR hasn't started doing
        // any work, so we set `isLoading` to `true`.
        isLoading: Boolean(isLoading || data === undefined && !error),
        data,
        error
    };
};
const useMounted = ()=>{
    const [mounted, setMounted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useMounted.useEffect": ()=>setMounted(true)
    }["useMounted.useEffect"], []);
    return mounted;
};
}}),
"[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/swr.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Tweet": (()=>Tweet)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.0_@opentelemetry+_ee4fdf7136c0ae57e36e5bb24f18c6a1/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$embedded$2d$tweet$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/embedded-tweet.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$not$2d$found$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-not-found.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$hooks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/hooks.js [app-client] (ecmascript)");
'use client';
;
;
;
const Tweet = ({ id, apiUrl, fallback = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$skeleton$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TweetSkeleton"], {}), components, fetchOptions, onError })=>{
    const { data, error, isLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$hooks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTweet"])(id, apiUrl, fetchOptions);
    if (isLoading) return fallback;
    if (error || !data) {
        const NotFound = (components == null ? void 0 : components.TweetNotFound) || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$tweet$2d$not$2d$found$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TweetNotFound"];
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(NotFound, {
            error: onError ? onError(error) : error
        });
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$0_$40$opentelemetry$2b$_ee4fdf7136c0ae57e36e5bb24f18c6a1$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$tweet$40$3$2e$2$2e$2_react$2d$dom_a2b6e01c7594fd9ef81716433bc4e3ba$2f$node_modules$2f$react$2d$tweet$2f$dist$2f$twitter$2d$theme$2f$embedded$2d$tweet$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EmbeddedTweet"], {
        tweet: data,
        components: components
    });
};
}}),
}]);

//# sourceMappingURL=5aae6_react-tweet_dist_b6618734._.js.map