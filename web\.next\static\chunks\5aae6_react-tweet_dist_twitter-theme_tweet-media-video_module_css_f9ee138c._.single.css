/* [project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-media-video.module.css [app-client] (css) */
.tweet-media-video-module__kMdDIG__anchor {
  color: #fff;
  cursor: pointer;
  user-select: none;
  text-overflow: ellipsis;
  white-space: nowrap;
  border: 1px solid #0000;
  border-radius: 9999px;
  outline-style: none;
  align-items: center;
  padding: 0 1rem;
  font-weight: 700;
  text-decoration: none;
  transition: background-color .2s;
  display: flex;
}

.tweet-media-video-module__kMdDIG__videoButton {
  background-color: var(--tweet-color-blue-primary);
  cursor: pointer;
  border: 4px solid #fff;
  border-radius: 9999px;
  justify-content: center;
  align-items: center;
  width: 67px;
  height: 67px;
  transition-property: background-color;
  transition-duration: .2s;
  display: flex;
  position: relative;
}

.tweet-media-video-module__kMdDIG__videoButton:hover, .tweet-media-video-module__kMdDIG__videoButton:focus-visible {
  background-color: var(--tweet-color-blue-primary-hover);
}

.tweet-media-video-module__kMdDIG__videoButtonIcon {
  color: #fff;
  fill: currentColor;
  user-select: none;
  width: calc(50% + 4px);
  max-width: 100%;
  height: calc(50% + 4px);
  margin-left: 3px;
}

.tweet-media-video-module__kMdDIG__watchOnTwitter {
  position: absolute;
  top: 12px;
  right: 8px;
}

.tweet-media-video-module__kMdDIG__watchOnTwitter > a {
  backdrop-filter: blur(4px);
  background-color: #0f1419bf;
  min-width: 2rem;
  min-height: 2rem;
  font-size: .875rem;
  line-height: 1rem;
}

.tweet-media-video-module__kMdDIG__watchOnTwitter > a:hover {
  background-color: #272c30bf;
}

.tweet-media-video-module__kMdDIG__viewReplies {
  background-color: var(--tweet-color-blue-primary);
  border-color: var(--tweet-color-blue-primary);
  min-height: 2rem;
  font-size: .9375rem;
  line-height: 1.25rem;
  position: relative;
}

.tweet-media-video-module__kMdDIG__viewReplies:hover {
  background-color: var(--tweet-color-blue-primary-hover);
}

/*# sourceMappingURL=5aae6_react-tweet_dist_twitter-theme_tweet-media-video_module_css_f9ee138c._.single.css.map*/