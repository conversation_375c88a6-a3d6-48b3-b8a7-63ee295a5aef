{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-container.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"article\": \"tweet-container-module__OCDp-G__article\",\n  \"root\": \"tweet-container-module__OCDp-G__root\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-container.js"], "sourcesContent": ["import { jsx as _jsx } from \"react/jsx-runtime\";\nimport clsx from 'clsx';\nimport s from './tweet-container.module.css';\nimport './theme.css';\nexport const TweetContainer = ({ className, children })=>/*#__PURE__*/ _jsx(\"div\", {\n        className: clsx('react-tweet-theme', s.root, className),\n        children: /*#__PURE__*/ _jsx(\"article\", {\n            className: s.article,\n            children: children\n        })\n    });\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;;AAEO,MAAM,iBAAiB,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC3E,WAAW,CAAA,GAAA,yLAAA,CAAA,UAAI,AAAD,EAAE,qBAAqB,wSAAA,CAAA,UAAC,CAAC,IAAI,EAAE;QAC7C,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,WAAW;YACpC,WAAW,wSAAA,CAAA,UAAC,CAAC,OAAO;YACpB,UAAU;QACd;IACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/avatar-img.js"], "sourcesContent": ["import { jsx as _jsx } from \"react/jsx-runtime\";\n// eslint-disable-next-line jsx-a11y/alt-text -- The alt text is part of `...props`\nexport const AvatarImg = (props)=>/*#__PURE__*/ _jsx(\"img\", {\n        ...props\n    });\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,YAAY,CAAC,QAAQ,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QACpD,GAAG,KAAK;IACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-header.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"author\": \"tweet-header-module__eMp9Yq__author\",\n  \"authorFollow\": \"tweet-header-module__eMp9Yq__authorFollow\",\n  \"authorLink\": \"tweet-header-module__eMp9Yq__authorLink\",\n  \"authorLinkText\": \"tweet-header-module__eMp9Yq__authorLinkText\",\n  \"authorMeta\": \"tweet-header-module__eMp9Yq__authorMeta\",\n  \"authorVerified\": \"tweet-header-module__eMp9Yq__authorVerified\",\n  \"avatar\": \"tweet-header-module__eMp9Yq__avatar\",\n  \"avatarOverflow\": \"tweet-header-module__eMp9Yq__avatarOverflow\",\n  \"avatarShadow\": \"tweet-header-module__eMp9Yq__avatarShadow\",\n  \"avatarSquare\": \"tweet-header-module__eMp9Yq__avatarSquare\",\n  \"brand\": \"tweet-header-module__eMp9Yq__brand\",\n  \"follow\": \"tweet-header-module__eMp9Yq__follow\",\n  \"header\": \"tweet-header-module__eMp9Yq__header\",\n  \"separator\": \"tweet-header-module__eMp9Yq__separator\",\n  \"twitterIcon\": \"tweet-header-module__eMp9Yq__twitterIcon\",\n  \"username\": \"tweet-header-module__eMp9Yq__username\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/icons/icons.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"verified\": \"icons-module__Sw1aPW__verified\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/icons/verified.js"], "sourcesContent": ["import { jsx as _jsx } from \"react/jsx-runtime\";\nimport s from './icons.module.css';\nexport const Verified = ()=>/*#__PURE__*/ _jsx(\"svg\", {\n        viewBox: \"0 0 24 24\",\n        \"aria-label\": \"Verified account\",\n        role: \"img\",\n        className: s.verified,\n        children: /*#__PURE__*/ _jsx(\"g\", {\n            children: /*#__PURE__*/ _jsx(\"path\", {\n                d: \"M22.25 12c0-1.43-.88-2.67-2.19-3.34.46-1.39.2-2.9-.81-3.91s-2.52-1.27-3.91-.81c-.66-1.31-1.91-2.19-3.34-2.19s-2.67.88-3.33 2.19c-1.4-.46-2.91-.2-3.92.81s-1.26 2.52-.8 3.91c-1.31.67-2.2 1.91-2.2 3.34s.89 2.67 2.2 3.34c-.46 1.39-.21 2.9.8 3.91s2.52 1.26 3.91.81c.67 1.31 1.91 2.19 3.34 2.19s2.68-.88 3.34-2.19c1.39.45 2.9.2 3.91-.81s1.27-2.52.81-3.91c1.31-.67 2.19-1.91 2.19-3.34zm-11.71 4.2L6.8 12.46l1.41-1.42 2.26 2.26 4.8-5.23 1.47 1.36-6.2 6.77z\"\n            })\n        })\n    });\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,MAAM,WAAW,IAAI,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC9C,SAAS;QACT,cAAc;QACd,MAAM;QACN,WAAW,oSAAA,CAAA,UAAC,CAAC,QAAQ;QACrB,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,KAAK;YAC9B,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;gBACjC,GAAG;YACP;QACJ;IACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/icons/verified-business.js"], "sourcesContent": ["import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport s from './icons.module.css';\nexport const VerifiedBusiness = ()=>/*#__PURE__*/ _jsx(\"svg\", {\n        viewBox: \"0 0 22 22\",\n        \"aria-label\": \"Verified account\",\n        role: \"img\",\n        className: s.verified,\n        children: /*#__PURE__*/ _jsxs(\"g\", {\n            children: [\n                /*#__PURE__*/ _jsxs(\"linearGradient\", {\n                    gradientUnits: \"userSpaceOnUse\",\n                    id: \"0-a\",\n                    x1: \"4.411\",\n                    x2: \"18.083\",\n                    y1: \"2.495\",\n                    y2: \"21.508\",\n                    children: [\n                        /*#__PURE__*/ _jsx(\"stop\", {\n                            offset: \"0\",\n                            stopColor: \"#f4e72a\"\n                        }),\n                        /*#__PURE__*/ _jsx(\"stop\", {\n                            offset: \".539\",\n                            stopColor: \"#cd8105\"\n                        }),\n                        /*#__PURE__*/ _jsx(\"stop\", {\n                            offset: \".68\",\n                            stopColor: \"#cb7b00\"\n                        }),\n                        /*#__PURE__*/ _jsx(\"stop\", {\n                            offset: \"1\",\n                            stopColor: \"#f4ec26\"\n                        }),\n                        /*#__PURE__*/ _jsx(\"stop\", {\n                            offset: \"1\",\n                            stopColor: \"#f4e72a\"\n                        })\n                    ]\n                }),\n                /*#__PURE__*/ _jsxs(\"linearGradient\", {\n                    gradientUnits: \"userSpaceOnUse\",\n                    id: \"0-b\",\n                    x1: \"5.355\",\n                    x2: \"16.361\",\n                    y1: \"3.395\",\n                    y2: \"19.133\",\n                    children: [\n                        /*#__PURE__*/ _jsx(\"stop\", {\n                            offset: \"0\",\n                            stopColor: \"#f9e87f\"\n                        }),\n                        /*#__PURE__*/ _jsx(\"stop\", {\n                            offset: \".406\",\n                            stopColor: \"#e2b719\"\n                        }),\n                        /*#__PURE__*/ _jsx(\"stop\", {\n                            offset: \".989\",\n                            stopColor: \"#e2b719\"\n                        })\n                    ]\n                }),\n                /*#__PURE__*/ _jsxs(\"g\", {\n                    clipRule: \"evenodd\",\n                    fillRule: \"evenodd\",\n                    children: [\n                        /*#__PURE__*/ _jsx(\"path\", {\n                            d: \"M13.324 3.848L11 1.6 8.676 3.848l-3.201-.453-.559 3.184L2.06 8.095 3.48 11l-1.42 2.904 2.856 1.516.559 3.184 3.201-.452L11 20.4l2.324-2.248 3.201.452.559-3.184 2.856-1.516L18.52 11l1.42-2.905-2.856-1.516-.559-3.184zm-7.09 7.575l3.428 3.428 5.683-6.206-1.347-1.247-4.4 4.795-2.072-2.072z\",\n                            fill: \"url(#0-a)\"\n                        }),\n                        /*#__PURE__*/ _jsx(\"path\", {\n                            d: \"M13.101 4.533L11 2.5 8.899 4.533l-2.895-.41-.505 2.88-2.583 1.37L4.2 11l-1.284 2.627 2.583 1.37.505 2.88 2.895-.41L11 19.5l2.101-2.033 2.895.41.505-2.88 2.583-1.37L17.8 11l1.284-2.627-2.583-1.37-.505-2.88zm-6.868 6.89l3.429 3.428 5.683-6.206-1.347-1.247-4.4 4.795-2.072-2.072z\",\n                            fill: \"url(#0-b)\"\n                        }),\n                        /*#__PURE__*/ _jsx(\"path\", {\n                            d: \"M6.233 11.423l3.429 3.428 5.65-6.17.038-.033-.005 1.398-5.683 6.206-3.429-3.429-.003-1.405.005.003z\",\n                            fill: \"#d18800\"\n                        })\n                    ]\n                })\n            ]\n        })\n    });\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,MAAM,mBAAmB,IAAI,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QACtD,SAAS;QACT,cAAc;QACd,MAAM;QACN,WAAW,oSAAA,CAAA,UAAC,CAAC,QAAQ;QACrB,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,OAAK,AAAD,EAAE,KAAK;YAC/B,UAAU;gBACN,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,OAAK,AAAD,EAAE,kBAAkB;oBAClC,eAAe;oBACf,IAAI;oBACJ,IAAI;oBACJ,IAAI;oBACJ,IAAI;oBACJ,IAAI;oBACJ,UAAU;wBACN,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;4BACvB,QAAQ;4BACR,WAAW;wBACf;wBACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;4BACvB,QAAQ;4BACR,WAAW;wBACf;wBACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;4BACvB,QAAQ;4BACR,WAAW;wBACf;wBACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;4BACvB,QAAQ;4BACR,WAAW;wBACf;wBACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;4BACvB,QAAQ;4BACR,WAAW;wBACf;qBACH;gBACL;gBACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,OAAK,AAAD,EAAE,kBAAkB;oBAClC,eAAe;oBACf,IAAI;oBACJ,IAAI;oBACJ,IAAI;oBACJ,IAAI;oBACJ,IAAI;oBACJ,UAAU;wBACN,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;4BACvB,QAAQ;4BACR,WAAW;wBACf;wBACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;4BACvB,QAAQ;4BACR,WAAW;wBACf;wBACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;4BACvB,QAAQ;4BACR,WAAW;wBACf;qBACH;gBACL;gBACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,OAAK,AAAD,EAAE,KAAK;oBACrB,UAAU;oBACV,UAAU;oBACV,UAAU;wBACN,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;4BACvB,GAAG;4BACH,MAAM;wBACV;wBACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;4BACvB,GAAG;4BACH,MAAM;wBACV;wBACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;4BACvB,GAAG;4BACH,MAAM;wBACV;qBACH;gBACL;aACH;QACL;IACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/icons/verified-government.js"], "sourcesContent": ["import { jsx as _jsx } from \"react/jsx-runtime\";\nimport s from './icons.module.css';\nexport const VerifiedGovernment = ()=>/*#__PURE__*/ _jsx(\"svg\", {\n        viewBox: \"0 0 22 22\",\n        \"aria-label\": \"Verified account\",\n        role: \"img\",\n        className: s.verified,\n        children: /*#__PURE__*/ _jsx(\"g\", {\n            children: /*#__PURE__*/ _jsx(\"path\", {\n                clipRule: \"evenodd\",\n                d: \"M12.05 2.056c-.568-.608-1.532-.608-2.1 0l-1.393 1.49c-.284.303-.685.47-1.1.455L5.42 3.932c-.832-.028-1.514.654-1.486 1.486l.069 2.039c.014.415-.152.816-.456 1.1l-1.49 1.392c-.608.568-.608 1.533 0 2.101l1.49 1.393c.304.284.47.684.456 1.1l-.07 2.038c-.027.832.655 1.514 1.487 1.486l2.038-.069c.415-.014.816.152 1.1.455l1.392 1.49c.569.609 1.533.609 2.102 0l1.393-1.49c.283-.303.684-.47 1.099-.455l2.038.069c.832.028 1.515-.654 1.486-1.486L18 14.542c-.015-.415.152-.815.455-1.099l1.49-1.393c.608-.568.608-1.533 0-2.101l-1.49-1.393c-.303-.283-.47-.684-.455-1.1l.068-2.038c.029-.832-.654-1.514-1.486-1.486l-2.038.07c-.415.013-.816-.153-1.1-.456zm-5.817 9.367l3.429 3.428 5.683-6.206-1.347-1.247-4.4 4.795-2.072-2.072z\",\n                fillRule: \"evenodd\"\n            })\n        })\n    });\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,MAAM,qBAAqB,IAAI,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QACxD,SAAS;QACT,cAAc;QACd,MAAM;QACN,WAAW,oSAAA,CAAA,UAAC,CAAC,QAAQ;QACrB,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,KAAK;YAC9B,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;gBACjC,UAAU;gBACV,GAAG;gBACH,UAAU;YACd;QACJ;IACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/verified-badge.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"verifiedBlue\": \"verified-badge-module__SIj1QG__verifiedBlue\",\n  \"verifiedGovernment\": \"verified-badge-module__SIj1QG__verifiedGovernment\",\n  \"verifiedOld\": \"verified-badge-module__SIj1QG__verifiedOld\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/verified-badge.js"], "sourcesContent": ["import { jsx as _jsx } from \"react/jsx-runtime\";\nimport clsx from 'clsx';\nimport { Verified, VerifiedBusiness, VerifiedGovernment } from './icons/index.js';\nimport s from './verified-badge.module.css';\nexport const VerifiedBadge = ({ user, className })=>{\n    const verified = user.verified || user.is_blue_verified || user.verified_type;\n    let icon = /*#__PURE__*/ _jsx(Verified, {});\n    let iconClassName = s.verifiedBlue;\n    if (verified) {\n        if (!user.is_blue_verified) {\n            iconClassName = s.verifiedOld;\n        }\n        switch(user.verified_type){\n            case 'Government':\n                icon = /*#__PURE__*/ _jsx(VerifiedGovernment, {});\n                iconClassName = s.verifiedGovernment;\n                break;\n            case 'Business':\n                icon = /*#__PURE__*/ _jsx(VerifiedBusiness, {});\n                iconClassName = null;\n                break;\n        }\n    }\n    return verified ? /*#__PURE__*/ _jsx(\"div\", {\n        className: clsx(className, iconClassName),\n        children: icon\n    }) : null;\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAAA;AAAA;AACA;;;;;AACO,MAAM,gBAAgB,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE;IAC7C,MAAM,WAAW,KAAK,QAAQ,IAAI,KAAK,gBAAgB,IAAI,KAAK,aAAa;IAC7E,IAAI,OAAO,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,2RAAA,CAAA,WAAQ,EAAE,CAAC;IACzC,IAAI,gBAAgB,uSAAA,CAAA,UAAC,CAAC,YAAY;IAClC,IAAI,UAAU;QACV,IAAI,CAAC,KAAK,gBAAgB,EAAE;YACxB,gBAAgB,uSAAA,CAAA,UAAC,CAAC,WAAW;QACjC;QACA,OAAO,KAAK,aAAa;YACrB,KAAK;gBACD,OAAO,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,ySAAA,CAAA,qBAAkB,EAAE,CAAC;gBAC/C,gBAAgB,uSAAA,CAAA,UAAC,CAAC,kBAAkB;gBACpC;YACJ,KAAK;gBACD,OAAO,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,uSAAA,CAAA,mBAAgB,EAAE,CAAC;gBAC7C,gBAAgB;gBAChB;QACR;IACJ;IACA,OAAO,WAAW,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QACxC,WAAW,CAAA,GAAA,yLAAA,CAAA,UAAI,AAAD,EAAE,WAAW;QAC3B,UAAU;IACd,KAAK;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-header.js"], "sourcesContent": ["import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport clsx from 'clsx';\nimport { AvatarImg } from './avatar-img.js';\nimport s from './tweet-header.module.css';\nimport { VerifiedBadge } from './verified-badge.js';\nexport const TweetHeader = ({ tweet, components })=>{\n    var _components_AvatarImg;\n    const Img = (_components_AvatarImg = components == null ? void 0 : components.AvatarImg) != null ? _components_AvatarImg : AvatarImg;\n    const { user } = tweet;\n    return /*#__PURE__*/ _jsxs(\"div\", {\n        className: s.header,\n        children: [\n            /*#__PURE__*/ _jsxs(\"a\", {\n                href: tweet.url,\n                className: s.avatar,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                children: [\n                    /*#__PURE__*/ _jsx(\"div\", {\n                        className: clsx(s.avatarOverflow, user.profile_image_shape === 'Square' && s.avatarSquare),\n                        children: /*#__PURE__*/ _jsx(Img, {\n                            src: user.profile_image_url_https,\n                            alt: user.name,\n                            width: 48,\n                            height: 48\n                        })\n                    }),\n                    /*#__PURE__*/ _jsx(\"div\", {\n                        className: s.avatarOverflow,\n                        children: /*#__PURE__*/ _jsx(\"div\", {\n                            className: s.avatarShadow\n                        })\n                    })\n                ]\n            }),\n            /*#__PURE__*/ _jsxs(\"div\", {\n                className: s.author,\n                children: [\n                    /*#__PURE__*/ _jsxs(\"a\", {\n                        href: tweet.url,\n                        className: s.authorLink,\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        children: [\n                            /*#__PURE__*/ _jsx(\"div\", {\n                                className: s.authorLinkText,\n                                children: /*#__PURE__*/ _jsx(\"span\", {\n                                    title: user.name,\n                                    children: user.name\n                                })\n                            }),\n                            /*#__PURE__*/ _jsx(VerifiedBadge, {\n                                user: user,\n                                className: s.authorVerified\n                            })\n                        ]\n                    }),\n                    /*#__PURE__*/ _jsxs(\"div\", {\n                        className: s.authorMeta,\n                        children: [\n                            /*#__PURE__*/ _jsx(\"a\", {\n                                href: tweet.url,\n                                className: s.username,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                children: /*#__PURE__*/ _jsxs(\"span\", {\n                                    title: `@${user.screen_name}`,\n                                    children: [\n                                        \"@\",\n                                        user.screen_name\n                                    ]\n                                })\n                            }),\n                            /*#__PURE__*/ _jsxs(\"div\", {\n                                className: s.authorFollow,\n                                children: [\n                                    /*#__PURE__*/ _jsx(\"span\", {\n                                        className: s.separator,\n                                        children: \"·\"\n                                    }),\n                                    /*#__PURE__*/ _jsx(\"a\", {\n                                        href: user.follow_url,\n                                        className: s.follow,\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        children: \"Follow\"\n                                    })\n                                ]\n                            })\n                        ]\n                    })\n                ]\n            }),\n            /*#__PURE__*/ _jsx(\"a\", {\n                href: tweet.url,\n                className: s.brand,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                \"aria-label\": \"View on Twitter\",\n                children: /*#__PURE__*/ _jsx(\"svg\", {\n                    viewBox: \"0 0 24 24\",\n                    \"aria-hidden\": \"true\",\n                    className: s.twitterIcon,\n                    children: /*#__PURE__*/ _jsx(\"g\", {\n                        children: /*#__PURE__*/ _jsx(\"path\", {\n                            d: \"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z\"\n                        })\n                    })\n                })\n            })\n        ]\n    });\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AACO,MAAM,cAAc,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE;IAC7C,IAAI;IACJ,MAAM,MAAM,CAAC,wBAAwB,cAAc,OAAO,KAAK,IAAI,WAAW,SAAS,KAAK,OAAO,wBAAwB,uRAAA,CAAA,YAAS;IACpI,MAAM,EAAE,IAAI,EAAE,GAAG;IACjB,OAAO,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,OAAK,AAAD,EAAE,OAAO;QAC9B,WAAW,qSAAA,CAAA,UAAC,CAAC,MAAM;QACnB,UAAU;YACN,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,OAAK,AAAD,EAAE,KAAK;gBACrB,MAAM,MAAM,GAAG;gBACf,WAAW,qSAAA,CAAA,UAAC,CAAC,MAAM;gBACnB,QAAQ;gBACR,KAAK;gBACL,UAAU;oBACN,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;wBACtB,WAAW,CAAA,GAAA,yLAAA,CAAA,UAAI,AAAD,EAAE,qSAAA,CAAA,UAAC,CAAC,cAAc,EAAE,KAAK,mBAAmB,KAAK,YAAY,qSAAA,CAAA,UAAC,CAAC,YAAY;wBACzF,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,KAAK;4BAC9B,KAAK,KAAK,uBAAuB;4BACjC,KAAK,KAAK,IAAI;4BACd,OAAO;4BACP,QAAQ;wBACZ;oBACJ;oBACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;wBACtB,WAAW,qSAAA,CAAA,UAAC,CAAC,cAAc;wBAC3B,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;4BAChC,WAAW,qSAAA,CAAA,UAAC,CAAC,YAAY;wBAC7B;oBACJ;iBACH;YACL;YACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,OAAK,AAAD,EAAE,OAAO;gBACvB,WAAW,qSAAA,CAAA,UAAC,CAAC,MAAM;gBACnB,UAAU;oBACN,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,OAAK,AAAD,EAAE,KAAK;wBACrB,MAAM,MAAM,GAAG;wBACf,WAAW,qSAAA,CAAA,UAAC,CAAC,UAAU;wBACvB,QAAQ;wBACR,KAAK;wBACL,UAAU;4BACN,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;gCACtB,WAAW,qSAAA,CAAA,UAAC,CAAC,cAAc;gCAC3B,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;oCACjC,OAAO,KAAK,IAAI;oCAChB,UAAU,KAAK,IAAI;gCACvB;4BACJ;4BACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,2RAAA,CAAA,gBAAa,EAAE;gCAC9B,MAAM;gCACN,WAAW,qSAAA,CAAA,UAAC,CAAC,cAAc;4BAC/B;yBACH;oBACL;oBACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,OAAK,AAAD,EAAE,OAAO;wBACvB,WAAW,qSAAA,CAAA,UAAC,CAAC,UAAU;wBACvB,UAAU;4BACN,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,KAAK;gCACpB,MAAM,MAAM,GAAG;gCACf,WAAW,qSAAA,CAAA,UAAC,CAAC,QAAQ;gCACrB,QAAQ;gCACR,KAAK;gCACL,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,OAAK,AAAD,EAAE,QAAQ;oCAClC,OAAO,CAAC,CAAC,EAAE,KAAK,WAAW,EAAE;oCAC7B,UAAU;wCACN;wCACA,KAAK,WAAW;qCACnB;gCACL;4BACJ;4BACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,OAAK,AAAD,EAAE,OAAO;gCACvB,WAAW,qSAAA,CAAA,UAAC,CAAC,YAAY;gCACzB,UAAU;oCACN,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;wCACvB,WAAW,qSAAA,CAAA,UAAC,CAAC,SAAS;wCACtB,UAAU;oCACd;oCACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,KAAK;wCACpB,MAAM,KAAK,UAAU;wCACrB,WAAW,qSAAA,CAAA,UAAC,CAAC,MAAM;wCACnB,QAAQ;wCACR,KAAK;wCACL,UAAU;oCACd;iCACH;4BACL;yBACH;oBACL;iBACH;YACL;YACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,KAAK;gBACpB,MAAM,MAAM,GAAG;gBACf,WAAW,qSAAA,CAAA,UAAC,CAAC,KAAK;gBAClB,QAAQ;gBACR,KAAK;gBACL,cAAc;gBACd,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;oBAChC,SAAS;oBACT,eAAe;oBACf,WAAW,qSAAA,CAAA,UAAC,CAAC,WAAW;oBACxB,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,KAAK;wBAC9B,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;4BACjC,GAAG;wBACP;oBACJ;gBACJ;YACJ;SACH;IACL;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 406, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"root\": \"tweet-in-reply-to-module__Rykmaq__root\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 415, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-in-reply-to.js"], "sourcesContent": ["import { jsxs as _jsxs } from \"react/jsx-runtime\";\nimport s from './tweet-in-reply-to.module.css';\nexport const TweetInReplyTo = ({ tweet })=>/*#__PURE__*/ _jsxs(\"a\", {\n        href: tweet.in_reply_to_url,\n        className: s.root,\n        target: \"_blank\",\n        rel: \"noopener noreferrer\",\n        children: [\n            \"Replying to @\",\n            tweet.in_reply_to_screen_name\n        ]\n    });\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,MAAM,iBAAiB,CAAC,EAAE,KAAK,EAAE,GAAG,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,OAAK,AAAD,EAAE,KAAK;QAC5D,MAAM,MAAM,eAAe;QAC3B,WAAW,gTAAA,CAAA,UAAC,CAAC,IAAI;QACjB,QAAQ;QACR,KAAK;QACL,UAAU;YACN;YACA,MAAM,uBAAuB;SAChC;IACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 437, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-link.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"root\": \"tweet-link-module__yeOBbW__root\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 446, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-link.js"], "sourcesContent": ["import { jsx as _jsx } from \"react/jsx-runtime\";\nimport s from './tweet-link.module.css';\nexport const TweetLink = ({ href, children })=>/*#__PURE__*/ _jsx(\"a\", {\n        href: href,\n        className: s.root,\n        target: \"_blank\",\n        rel: \"noopener noreferrer nofollow\",\n        children: children\n    });\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,MAAM,YAAY,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,KAAK;QAC/D,MAAM;QACN,WAAW,mSAAA,CAAA,UAAC,CAAC,IAAI;QACjB,QAAQ;QACR,KAAK;QACL,UAAU;IACd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 465, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-body.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"root\": \"tweet-body-module__P73Vuq__root\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 474, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-body.js"], "sourcesContent": ["import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { TweetLink } from './tweet-link.js';\nimport s from './tweet-body.module.css';\nexport const TweetBody = ({ tweet })=>/*#__PURE__*/ _jsx(\"p\", {\n        className: s.root,\n        lang: tweet.lang,\n        dir: \"auto\",\n        children: tweet.entities.map((item, i)=>{\n            switch(item.type){\n                case 'hashtag':\n                case 'mention':\n                case 'url':\n                case 'symbol':\n                    return /*#__PURE__*/ _jsx(TweetLink, {\n                        href: item.href,\n                        children: item.text\n                    }, i);\n                case 'media':\n                    // Media text is currently never displayed, some tweets however might have indices\n                    // that do match `display_text_range` so for those cases we ignore the content.\n                    return;\n                default:\n                    // We use `dangerouslySetInnerHTML` to preserve the text encoding.\n                    // https://github.com/vercel-labs/react-tweet/issues/29\n                    return /*#__PURE__*/ _jsx(\"span\", {\n                        dangerouslySetInnerHTML: {\n                            __html: item.text\n                        }\n                    }, i);\n            }\n        })\n    });\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,MAAM,YAAY,CAAC,EAAE,KAAK,EAAE,GAAG,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,KAAK;QACtD,WAAW,mSAAA,CAAA,UAAC,CAAC,IAAI;QACjB,MAAM,MAAM,IAAI;QAChB,KAAK;QACL,UAAU,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM;YAChC,OAAO,KAAK,IAAI;gBACZ,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBACD,OAAO,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,uRAAA,CAAA,YAAS,EAAE;wBACjC,MAAM,KAAK,IAAI;wBACf,UAAU,KAAK,IAAI;oBACvB,GAAG;gBACP,KAAK;oBACD,kFAAkF;oBAClF,+EAA+E;oBAC/E;gBACJ;oBACI,kEAAkE;oBAClE,uDAAuD;oBACvD,OAAO,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;wBAC9B,yBAAyB;4BACrB,QAAQ,KAAK,IAAI;wBACrB;oBACJ,GAAG;YACX;QACJ;IACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 518, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/utils.js"], "sourcesContent": ["const getTweetUrl = (tweet)=>`https://x.com/${tweet.user.screen_name}/status/${tweet.id_str}`;\nconst getUserUrl = (usernameOrTweet)=>`https://x.com/${typeof usernameOrTweet === 'string' ? usernameOrTweet : usernameOrTweet.user.screen_name}`;\nconst getLikeUrl = (tweet)=>`https://x.com/intent/like?tweet_id=${tweet.id_str}`;\nconst getReplyUrl = (tweet)=>`https://x.com/intent/tweet?in_reply_to=${tweet.id_str}`;\nconst getFollowUrl = (tweet)=>`https://x.com/intent/follow?screen_name=${tweet.user.screen_name}`;\nconst getHashtagUrl = (hashtag)=>`https://x.com/hashtag/${hashtag.text}`;\nconst getSymbolUrl = (symbol)=>`https://x.com/search?q=%24${symbol.text}`;\nconst getInReplyToUrl = (tweet)=>`https://x.com/${tweet.in_reply_to_screen_name}/status/${tweet.in_reply_to_status_id_str}`;\nexport const getMediaUrl = (media, size)=>{\n    const url = new URL(media.media_url_https);\n    const extension = url.pathname.split('.').pop();\n    if (!extension) return media.media_url_https;\n    url.pathname = url.pathname.replace(`.${extension}`, '');\n    url.searchParams.set('format', extension);\n    url.searchParams.set('name', size);\n    return url.toString();\n};\nexport const getMp4Videos = (media)=>{\n    const { variants } = media.video_info;\n    const sortedMp4Videos = variants.filter((vid)=>vid.content_type === 'video/mp4').sort((a, b)=>{\n        var _b_bitrate, _a_bitrate;\n        return ((_b_bitrate = b.bitrate) != null ? _b_bitrate : 0) - ((_a_bitrate = a.bitrate) != null ? _a_bitrate : 0);\n    });\n    return sortedMp4Videos;\n};\nexport const getMp4Video = (media)=>{\n    const mp4Videos = getMp4Videos(media);\n    // Skip the highest quality video and use the next quality\n    return mp4Videos.length > 1 ? mp4Videos[1] : mp4Videos[0];\n};\nexport const formatNumber = (n)=>{\n    if (n > 999999) return `${(n / 1000000).toFixed(1)}M`;\n    if (n > 999) return `${(n / 1000).toFixed(1)}K`;\n    return n.toString();\n};\nfunction getEntities(tweet) {\n    const textMap = Array.from(tweet.text);\n    const result = [\n        {\n            indices: tweet.display_text_range,\n            type: 'text'\n        }\n    ];\n    addEntities(result, 'hashtag', tweet.entities.hashtags);\n    addEntities(result, 'mention', tweet.entities.user_mentions);\n    addEntities(result, 'url', tweet.entities.urls);\n    addEntities(result, 'symbol', tweet.entities.symbols);\n    if (tweet.entities.media) {\n        addEntities(result, 'media', tweet.entities.media);\n    }\n    fixRange(tweet, result);\n    return result.map((entity)=>{\n        const text = textMap.slice(entity.indices[0], entity.indices[1]).join('');\n        switch(entity.type){\n            case 'hashtag':\n                return Object.assign(entity, {\n                    href: getHashtagUrl(entity),\n                    text\n                });\n            case 'mention':\n                return Object.assign(entity, {\n                    href: getUserUrl(entity.screen_name),\n                    text\n                });\n            case 'url':\n            case 'media':\n                return Object.assign(entity, {\n                    href: entity.expanded_url,\n                    text: entity.display_url\n                });\n            case 'symbol':\n                return Object.assign(entity, {\n                    href: getSymbolUrl(entity),\n                    text\n                });\n            default:\n                return Object.assign(entity, {\n                    text\n                });\n        }\n    });\n}\nfunction addEntities(result, type, entities) {\n    for (const entity of entities){\n        for (const [i, item] of result.entries()){\n            if (item.indices[0] > entity.indices[0] || item.indices[1] < entity.indices[1]) {\n                continue;\n            }\n            const items = [\n                {\n                    ...entity,\n                    type\n                }\n            ];\n            if (item.indices[0] < entity.indices[0]) {\n                items.unshift({\n                    indices: [\n                        item.indices[0],\n                        entity.indices[0]\n                    ],\n                    type: 'text'\n                });\n            }\n            if (item.indices[1] > entity.indices[1]) {\n                items.push({\n                    indices: [\n                        entity.indices[1],\n                        item.indices[1]\n                    ],\n                    type: 'text'\n                });\n            }\n            result.splice(i, 1, ...items);\n            break; // Break out of the loop to avoid iterating over the new items\n        }\n    }\n}\n/**\n * Update display_text_range to work w/ Array.from\n * Array.from is unicode aware, unlike string.slice()\n */ function fixRange(tweet, entities) {\n    if (tweet.entities.media && tweet.entities.media[0].indices[0] < tweet.display_text_range[1]) {\n        tweet.display_text_range[1] = tweet.entities.media[0].indices[0];\n    }\n    const lastEntity = entities.at(-1);\n    if (lastEntity && lastEntity.indices[1] > tweet.display_text_range[1]) {\n        lastEntity.indices[1] = tweet.display_text_range[1];\n    }\n}\n/**\n * Enriches a tweet with additional data used to more easily use the tweet in a UI.\n */ export const enrichTweet = (tweet)=>({\n        ...tweet,\n        url: getTweetUrl(tweet),\n        user: {\n            ...tweet.user,\n            url: getUserUrl(tweet),\n            follow_url: getFollowUrl(tweet)\n        },\n        like_url: getLikeUrl(tweet),\n        reply_url: getReplyUrl(tweet),\n        in_reply_to_url: tweet.in_reply_to_screen_name ? getInReplyToUrl(tweet) : undefined,\n        entities: getEntities(tweet),\n        quoted_tweet: tweet.quoted_tweet ? {\n            ...tweet.quoted_tweet,\n            url: getTweetUrl(tweet.quoted_tweet),\n            entities: getEntities(tweet.quoted_tweet)\n        } : undefined\n    });\n"], "names": [], "mappings": ";;;;;;;AAAA,MAAM,cAAc,CAAC,QAAQ,CAAC,cAAc,EAAE,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,MAAM,MAAM,EAAE;AAC7F,MAAM,aAAa,CAAC,kBAAkB,CAAC,cAAc,EAAE,OAAO,oBAAoB,WAAW,kBAAkB,gBAAgB,IAAI,CAAC,WAAW,EAAE;AACjJ,MAAM,aAAa,CAAC,QAAQ,CAAC,mCAAmC,EAAE,MAAM,MAAM,EAAE;AAChF,MAAM,cAAc,CAAC,QAAQ,CAAC,uCAAuC,EAAE,MAAM,MAAM,EAAE;AACrF,MAAM,eAAe,CAAC,QAAQ,CAAC,wCAAwC,EAAE,MAAM,IAAI,CAAC,WAAW,EAAE;AACjG,MAAM,gBAAgB,CAAC,UAAU,CAAC,sBAAsB,EAAE,QAAQ,IAAI,EAAE;AACxE,MAAM,eAAe,CAAC,SAAS,CAAC,0BAA0B,EAAE,OAAO,IAAI,EAAE;AACzE,MAAM,kBAAkB,CAAC,QAAQ,CAAC,cAAc,EAAE,MAAM,uBAAuB,CAAC,QAAQ,EAAE,MAAM,yBAAyB,EAAE;AACpH,MAAM,cAAc,CAAC,OAAO;IAC/B,MAAM,MAAM,IAAI,IAAI,MAAM,eAAe;IACzC,MAAM,YAAY,IAAI,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG;IAC7C,IAAI,CAAC,WAAW,OAAO,MAAM,eAAe;IAC5C,IAAI,QAAQ,GAAG,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE;IACrD,IAAI,YAAY,CAAC,GAAG,CAAC,UAAU;IAC/B,IAAI,YAAY,CAAC,GAAG,CAAC,QAAQ;IAC7B,OAAO,IAAI,QAAQ;AACvB;AACO,MAAM,eAAe,CAAC;IACzB,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,UAAU;IACrC,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAC,MAAM,IAAI,YAAY,KAAK,aAAa,IAAI,CAAC,CAAC,GAAG;QACtF,IAAI,YAAY;QAChB,OAAO,CAAC,CAAC,aAAa,EAAE,OAAO,KAAK,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,OAAO,KAAK,OAAO,aAAa,CAAC;IACnH;IACA,OAAO;AACX;AACO,MAAM,cAAc,CAAC;IACxB,MAAM,YAAY,aAAa;IAC/B,0DAA0D;IAC1D,OAAO,UAAU,MAAM,GAAG,IAAI,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE;AAC7D;AACO,MAAM,eAAe,CAAC;IACzB,IAAI,IAAI,QAAQ,OAAO,GAAG,CAAC,IAAI,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACrD,IAAI,IAAI,KAAK,OAAO,GAAG,CAAC,IAAI,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IAC/C,OAAO,EAAE,QAAQ;AACrB;AACA,SAAS,YAAY,KAAK;IACtB,MAAM,UAAU,MAAM,IAAI,CAAC,MAAM,IAAI;IACrC,MAAM,SAAS;QACX;YACI,SAAS,MAAM,kBAAkB;YACjC,MAAM;QACV;KACH;IACD,YAAY,QAAQ,WAAW,MAAM,QAAQ,CAAC,QAAQ;IACtD,YAAY,QAAQ,WAAW,MAAM,QAAQ,CAAC,aAAa;IAC3D,YAAY,QAAQ,OAAO,MAAM,QAAQ,CAAC,IAAI;IAC9C,YAAY,QAAQ,UAAU,MAAM,QAAQ,CAAC,OAAO;IACpD,IAAI,MAAM,QAAQ,CAAC,KAAK,EAAE;QACtB,YAAY,QAAQ,SAAS,MAAM,QAAQ,CAAC,KAAK;IACrD;IACA,SAAS,OAAO;IAChB,OAAO,OAAO,GAAG,CAAC,CAAC;QACf,MAAM,OAAO,QAAQ,KAAK,CAAC,OAAO,OAAO,CAAC,EAAE,EAAE,OAAO,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC;QACtE,OAAO,OAAO,IAAI;YACd,KAAK;gBACD,OAAO,OAAO,MAAM,CAAC,QAAQ;oBACzB,MAAM,cAAc;oBACpB;gBACJ;YACJ,KAAK;gBACD,OAAO,OAAO,MAAM,CAAC,QAAQ;oBACzB,MAAM,WAAW,OAAO,WAAW;oBACnC;gBACJ;YACJ,KAAK;YACL,KAAK;gBACD,OAAO,OAAO,MAAM,CAAC,QAAQ;oBACzB,MAAM,OAAO,YAAY;oBACzB,MAAM,OAAO,WAAW;gBAC5B;YACJ,KAAK;gBACD,OAAO,OAAO,MAAM,CAAC,QAAQ;oBACzB,MAAM,aAAa;oBACnB;gBACJ;YACJ;gBACI,OAAO,OAAO,MAAM,CAAC,QAAQ;oBACzB;gBACJ;QACR;IACJ;AACJ;AACA,SAAS,YAAY,MAAM,EAAE,IAAI,EAAE,QAAQ;IACvC,KAAK,MAAM,UAAU,SAAS;QAC1B,KAAK,MAAM,CAAC,GAAG,KAAK,IAAI,OAAO,OAAO,GAAG;YACrC,IAAI,KAAK,OAAO,CAAC,EAAE,GAAG,OAAO,OAAO,CAAC,EAAE,IAAI,KAAK,OAAO,CAAC,EAAE,GAAG,OAAO,OAAO,CAAC,EAAE,EAAE;gBAC5E;YACJ;YACA,MAAM,QAAQ;gBACV;oBACI,GAAG,MAAM;oBACT;gBACJ;aACH;YACD,IAAI,KAAK,OAAO,CAAC,EAAE,GAAG,OAAO,OAAO,CAAC,EAAE,EAAE;gBACrC,MAAM,OAAO,CAAC;oBACV,SAAS;wBACL,KAAK,OAAO,CAAC,EAAE;wBACf,OAAO,OAAO,CAAC,EAAE;qBACpB;oBACD,MAAM;gBACV;YACJ;YACA,IAAI,KAAK,OAAO,CAAC,EAAE,GAAG,OAAO,OAAO,CAAC,EAAE,EAAE;gBACrC,MAAM,IAAI,CAAC;oBACP,SAAS;wBACL,OAAO,OAAO,CAAC,EAAE;wBACjB,KAAK,OAAO,CAAC,EAAE;qBAClB;oBACD,MAAM;gBACV;YACJ;YACA,OAAO,MAAM,CAAC,GAAG,MAAM;YACvB,OAAO,8DAA8D;QACzE;IACJ;AACJ;AACA;;;CAGC,GAAG,SAAS,SAAS,KAAK,EAAE,QAAQ;IACjC,IAAI,MAAM,QAAQ,CAAC,KAAK,IAAI,MAAM,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,GAAG,MAAM,kBAAkB,CAAC,EAAE,EAAE;QAC1F,MAAM,kBAAkB,CAAC,EAAE,GAAG,MAAM,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE;IACpE;IACA,MAAM,aAAa,SAAS,EAAE,CAAC,CAAC;IAChC,IAAI,cAAc,WAAW,OAAO,CAAC,EAAE,GAAG,MAAM,kBAAkB,CAAC,EAAE,EAAE;QACnE,WAAW,OAAO,CAAC,EAAE,GAAG,MAAM,kBAAkB,CAAC,EAAE;IACvD;AACJ;AAGW,MAAM,cAAc,CAAC,QAAQ,CAAC;QACjC,GAAG,KAAK;QACR,KAAK,YAAY;QACjB,MAAM;YACF,GAAG,MAAM,IAAI;YACb,KAAK,WAAW;YAChB,YAAY,aAAa;QAC7B;QACA,UAAU,WAAW;QACrB,WAAW,YAAY;QACvB,iBAAiB,MAAM,uBAAuB,GAAG,gBAAgB,SAAS;QAC1E,UAAU,YAAY;QACtB,cAAc,MAAM,YAAY,GAAG;YAC/B,GAAG,MAAM,YAAY;YACrB,KAAK,YAAY,MAAM,YAAY;YACnC,UAAU,YAAY,MAAM,YAAY;QAC5C,IAAI;IACR,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 677, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-media.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"grid2Columns\": \"tweet-media-module__0eLn2W__grid2Columns\",\n  \"grid2x2\": \"tweet-media-module__0eLn2W__grid2x2\",\n  \"grid3\": \"tweet-media-module__0eLn2W__grid3\",\n  \"image\": \"tweet-media-module__0eLn2W__image\",\n  \"mediaContainer\": \"tweet-media-module__0eLn2W__mediaContainer\",\n  \"mediaLink\": \"tweet-media-module__0eLn2W__mediaLink\",\n  \"mediaWrapper\": \"tweet-media-module__0eLn2W__mediaWrapper\",\n  \"root\": \"tweet-media-module__0eLn2W__root\",\n  \"rounded\": \"tweet-media-module__0eLn2W__rounded\",\n  \"skeleton\": \"tweet-media-module__0eLn2W__skeleton\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 694, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-media-video.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"anchor\": \"tweet-media-video-module__kMdDIG__anchor\",\n  \"videoButton\": \"tweet-media-video-module__kMdDIG__videoButton\",\n  \"videoButtonIcon\": \"tweet-media-video-module__kMdDIG__videoButtonIcon\",\n  \"viewReplies\": \"tweet-media-video-module__kMdDIG__viewReplies\",\n  \"watchOnTwitter\": \"tweet-media-video-module__kMdDIG__watchOnTwitter\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 707, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-media-video.js"], "sourcesContent": ["'use client';\nimport { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { useState } from 'react';\nimport clsx from 'clsx';\nimport { getMediaUrl, getMp4Video } from '../utils.js';\nimport mediaStyles from './tweet-media.module.css';\nimport s from './tweet-media-video.module.css';\nexport const TweetMediaVideo = ({ tweet, media })=>{\n    const [playButton, setPlayButton] = useState(true);\n    const [isPlaying, setIsPlaying] = useState(false);\n    const [ended, setEnded] = useState(false);\n    const mp4Video = getMp4Video(media);\n    let timeout = 0;\n    return /*#__PURE__*/ _jsxs(_Fragment, {\n        children: [\n            /*#__PURE__*/ _jsx(\"video\", {\n                className: mediaStyles.image,\n                poster: getMediaUrl(media, 'small'),\n                controls: !playButton,\n                playsInline: true,\n                preload: \"none\",\n                tabIndex: playButton ? -1 : 0,\n                onPlay: ()=>{\n                    if (timeout) window.clearTimeout(timeout);\n                    if (!isPlaying) setIsPlaying(true);\n                    if (ended) setEnded(false);\n                },\n                onPause: ()=>{\n                    // When the video is seeked (moved to a different timestamp), it will pause for a moment\n                    // before resuming. We don't want to show the message in that case so we wait a bit.\n                    if (timeout) window.clearTimeout(timeout);\n                    timeout = window.setTimeout(()=>{\n                        if (isPlaying) setIsPlaying(false);\n                        timeout = 0;\n                    }, 100);\n                },\n                onEnded: ()=>{\n                    setEnded(true);\n                },\n                children: /*#__PURE__*/ _jsx(\"source\", {\n                    src: mp4Video.url,\n                    type: mp4Video.content_type\n                })\n            }),\n            playButton && /*#__PURE__*/ _jsx(\"button\", {\n                type: \"button\",\n                className: s.videoButton,\n                \"aria-label\": \"View video on X\",\n                onClick: (e)=>{\n                    const video = e.currentTarget.previousSibling;\n                    e.preventDefault();\n                    setPlayButton(false);\n                    video.load();\n                    video.play().then(()=>{\n                        setIsPlaying(true);\n                        video.focus();\n                    }).catch((error)=>{\n                        console.error('Error playing video:', error);\n                        setPlayButton(true);\n                        setIsPlaying(false);\n                    });\n                },\n                children: /*#__PURE__*/ _jsx(\"svg\", {\n                    viewBox: \"0 0 24 24\",\n                    className: s.videoButtonIcon,\n                    \"aria-hidden\": \"true\",\n                    children: /*#__PURE__*/ _jsx(\"g\", {\n                        children: /*#__PURE__*/ _jsx(\"path\", {\n                            d: \"M21 12L4 2v20l17-10z\"\n                        })\n                    })\n                })\n            }),\n            !isPlaying && !ended && /*#__PURE__*/ _jsx(\"div\", {\n                className: s.watchOnTwitter,\n                children: /*#__PURE__*/ _jsx(\"a\", {\n                    href: tweet.url,\n                    className: s.anchor,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    children: playButton ? 'Watch on X' : 'Continue watching on X'\n                })\n            }),\n            ended && /*#__PURE__*/ _jsx(\"a\", {\n                href: tweet.url,\n                className: clsx(s.anchor, s.viewReplies),\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                children: \"View replies\"\n            })\n        ]\n    });\n};\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAOO,MAAM,kBAAkB,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE;IAC5C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,WAAW,CAAA,GAAA,2PAAA,CAAA,cAAW,AAAD,EAAE;IAC7B,IAAI,UAAU;IACd,OAAO,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,OAAK,AAAD,EAAE,+QAAA,CAAA,WAAS,EAAE;QAClC,UAAU;YACN,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,SAAS;gBACxB,WAAW,oSAAA,CAAA,UAAW,CAAC,KAAK;gBAC5B,QAAQ,CAAA,GAAA,2PAAA,CAAA,cAAW,AAAD,EAAE,OAAO;gBAC3B,UAAU,CAAC;gBACX,aAAa;gBACb,SAAS;gBACT,UAAU,aAAa,CAAC,IAAI;gBAC5B,QAAQ;oBACJ,IAAI,SAAS,OAAO,YAAY,CAAC;oBACjC,IAAI,CAAC,WAAW,aAAa;oBAC7B,IAAI,OAAO,SAAS;gBACxB;gBACA,SAAS;oBACL,wFAAwF;oBACxF,oFAAoF;oBACpF,IAAI,SAAS,OAAO,YAAY,CAAC;oBACjC,UAAU,OAAO,UAAU,CAAC;wBACxB,IAAI,WAAW,aAAa;wBAC5B,UAAU;oBACd,GAAG;gBACP;gBACA,SAAS;oBACL,SAAS;gBACb;gBACA,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,UAAU;oBACnC,KAAK,SAAS,GAAG;oBACjB,MAAM,SAAS,YAAY;gBAC/B;YACJ;YACA,cAAc,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,UAAU;gBACvC,MAAM;gBACN,WAAW,6SAAA,CAAA,UAAC,CAAC,WAAW;gBACxB,cAAc;gBACd,SAAS,CAAC;oBACN,MAAM,QAAQ,EAAE,aAAa,CAAC,eAAe;oBAC7C,EAAE,cAAc;oBAChB,cAAc;oBACd,MAAM,IAAI;oBACV,MAAM,IAAI,GAAG,IAAI,CAAC;wBACd,aAAa;wBACb,MAAM,KAAK;oBACf,GAAG,KAAK,CAAC,CAAC;wBACN,QAAQ,KAAK,CAAC,wBAAwB;wBACtC,cAAc;wBACd,aAAa;oBACjB;gBACJ;gBACA,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;oBAChC,SAAS;oBACT,WAAW,6SAAA,CAAA,UAAC,CAAC,eAAe;oBAC5B,eAAe;oBACf,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,KAAK;wBAC9B,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;4BACjC,GAAG;wBACP;oBACJ;gBACJ;YACJ;YACA,CAAC,aAAa,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;gBAC9C,WAAW,6SAAA,CAAA,UAAC,CAAC,cAAc;gBAC3B,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,KAAK;oBAC9B,MAAM,MAAM,GAAG;oBACf,WAAW,6SAAA,CAAA,UAAC,CAAC,MAAM;oBACnB,QAAQ;oBACR,KAAK;oBACL,UAAU,aAAa,eAAe;gBAC1C;YACJ;YACA,SAAS,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,KAAK;gBAC7B,MAAM,MAAM,GAAG;gBACf,WAAW,CAAA,GAAA,yLAAA,CAAA,UAAI,AAAD,EAAE,6SAAA,CAAA,UAAC,CAAC,MAAM,EAAE,6SAAA,CAAA,UAAC,CAAC,WAAW;gBACvC,QAAQ;gBACR,KAAK;gBACL,UAAU;YACd;SACH;IACL;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 815, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/media-img.js"], "sourcesContent": ["import { jsx as _jsx } from \"react/jsx-runtime\";\n// eslint-disable-next-line jsx-a11y/alt-text -- The alt text is part of `...props`\nexport const MediaImg = (props)=>/*#__PURE__*/ _jsx(\"img\", {\n        ...props\n    });\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,WAAW,CAAC,QAAQ,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QACnD,GAAG,KAAK;IACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 829, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-media.js"], "sourcesContent": ["import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { Fragment } from 'react';\nimport clsx from 'clsx';\nimport { getMediaUrl } from '../utils.js';\nimport { TweetMediaVideo } from './tweet-media-video.js';\nimport { MediaImg } from './media-img.js';\nimport s from './tweet-media.module.css';\nconst getSkeletonStyle = (media, itemCount)=>{\n    let paddingBottom = 56.25 // default of 16x9\n    ;\n    // if we only have 1 item, show at original ratio\n    if (itemCount === 1) paddingBottom = 100 / media.original_info.width * media.original_info.height;\n    // if we have 2 items, double the default to be 16x9 total\n    if (itemCount === 2) paddingBottom = paddingBottom * 2;\n    return {\n        width: media.type === 'photo' ? undefined : 'unset',\n        paddingBottom: `${paddingBottom}%`\n    };\n};\nexport const TweetMedia = ({ tweet, components, quoted })=>{\n    var _tweet_mediaDetails, _tweet_mediaDetails1;\n    var _tweet_mediaDetails_length;\n    const length = (_tweet_mediaDetails_length = (_tweet_mediaDetails = tweet.mediaDetails) == null ? void 0 : _tweet_mediaDetails.length) != null ? _tweet_mediaDetails_length : 0;\n    var _components_MediaImg;\n    const Img = (_components_MediaImg = components == null ? void 0 : components.MediaImg) != null ? _components_MediaImg : MediaImg;\n    return /*#__PURE__*/ _jsx(\"div\", {\n        className: clsx(s.root, !quoted && s.rounded),\n        children: /*#__PURE__*/ _jsx(\"div\", {\n            className: clsx(s.mediaWrapper, length > 1 && s.grid2Columns, length === 3 && s.grid3, length > 4 && s.grid2x2),\n            children: (_tweet_mediaDetails1 = tweet.mediaDetails) == null ? void 0 : _tweet_mediaDetails1.map((media)=>/*#__PURE__*/ _jsx(Fragment, {\n                    children: media.type === 'photo' ? /*#__PURE__*/ _jsxs(\"a\", {\n                        href: tweet.url,\n                        className: clsx(s.mediaContainer, s.mediaLink),\n                        target: \"_blank\",\n                        rel: \"noopener noreferrer\",\n                        children: [\n                            /*#__PURE__*/ _jsx(\"div\", {\n                                className: s.skeleton,\n                                style: getSkeletonStyle(media, length)\n                            }),\n                            /*#__PURE__*/ _jsx(Img, {\n                                src: getMediaUrl(media, 'small'),\n                                alt: media.ext_alt_text || 'Image',\n                                className: s.image,\n                                draggable: true\n                            })\n                        ]\n                    }, media.media_url_https) : /*#__PURE__*/ _jsxs(\"div\", {\n                        className: s.mediaContainer,\n                        children: [\n                            /*#__PURE__*/ _jsx(\"div\", {\n                                className: s.skeleton,\n                                style: getSkeletonStyle(media, length)\n                            }),\n                            /*#__PURE__*/ _jsx(TweetMediaVideo, {\n                                tweet: tweet,\n                                media: media\n                            })\n                        ]\n                    }, media.media_url_https)\n                }, media.media_url_https))\n        })\n    });\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AACA,MAAM,mBAAmB,CAAC,OAAO;IAC7B,IAAI,gBAAgB,MAAM,kBAAkB;;IAE5C,iDAAiD;IACjD,IAAI,cAAc,GAAG,gBAAgB,MAAM,MAAM,aAAa,CAAC,KAAK,GAAG,MAAM,aAAa,CAAC,MAAM;IACjG,0DAA0D;IAC1D,IAAI,cAAc,GAAG,gBAAgB,gBAAgB;IACrD,OAAO;QACH,OAAO,MAAM,IAAI,KAAK,UAAU,YAAY;QAC5C,eAAe,GAAG,cAAc,CAAC,CAAC;IACtC;AACJ;AACO,MAAM,aAAa,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE;IACpD,IAAI,qBAAqB;IACzB,IAAI;IACJ,MAAM,SAAS,CAAC,6BAA6B,CAAC,sBAAsB,MAAM,YAAY,KAAK,OAAO,KAAK,IAAI,oBAAoB,MAAM,KAAK,OAAO,6BAA6B;IAC9K,IAAI;IACJ,MAAM,MAAM,CAAC,uBAAuB,cAAc,OAAO,KAAK,IAAI,WAAW,QAAQ,KAAK,OAAO,uBAAuB,sRAAA,CAAA,WAAQ;IAChI,OAAO,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC7B,WAAW,CAAA,GAAA,yLAAA,CAAA,UAAI,AAAD,EAAE,oSAAA,CAAA,UAAC,CAAC,IAAI,EAAE,CAAC,UAAU,oSAAA,CAAA,UAAC,CAAC,OAAO;QAC5C,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;YAChC,WAAW,CAAA,GAAA,yLAAA,CAAA,UAAI,AAAD,EAAE,oSAAA,CAAA,UAAC,CAAC,YAAY,EAAE,SAAS,KAAK,oSAAA,CAAA,UAAC,CAAC,YAAY,EAAE,WAAW,KAAK,oSAAA,CAAA,UAAC,CAAC,KAAK,EAAE,SAAS,KAAK,oSAAA,CAAA,UAAC,CAAC,OAAO;YAC9G,UAAU,CAAC,uBAAuB,MAAM,YAAY,KAAK,OAAO,KAAK,IAAI,qBAAqB,GAAG,CAAC,CAAC,QAAQ,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,sQAAA,CAAA,WAAQ,EAAE;oBAChI,UAAU,MAAM,IAAI,KAAK,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,OAAK,AAAD,EAAE,KAAK;wBACxD,MAAM,MAAM,GAAG;wBACf,WAAW,CAAA,GAAA,yLAAA,CAAA,UAAI,AAAD,EAAE,oSAAA,CAAA,UAAC,CAAC,cAAc,EAAE,oSAAA,CAAA,UAAC,CAAC,SAAS;wBAC7C,QAAQ;wBACR,KAAK;wBACL,UAAU;4BACN,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;gCACtB,WAAW,oSAAA,CAAA,UAAC,CAAC,QAAQ;gCACrB,OAAO,iBAAiB,OAAO;4BACnC;4BACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,KAAK;gCACpB,KAAK,CAAA,GAAA,2PAAA,CAAA,cAAW,AAAD,EAAE,OAAO;gCACxB,KAAK,MAAM,YAAY,IAAI;gCAC3B,WAAW,oSAAA,CAAA,UAAC,CAAC,KAAK;gCAClB,WAAW;4BACf;yBACH;oBACL,GAAG,MAAM,eAAe,IAAI,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,OAAK,AAAD,EAAE,OAAO;wBACnD,WAAW,oSAAA,CAAA,UAAC,CAAC,cAAc;wBAC3B,UAAU;4BACN,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;gCACtB,WAAW,oSAAA,CAAA,UAAC,CAAC,QAAQ;gCACrB,OAAO,iBAAiB,OAAO;4BACnC;4BACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,iSAAA,CAAA,kBAAe,EAAE;gCAChC,OAAO;gCACP,OAAO;4BACX;yBACH;oBACL,GAAG,MAAM,eAAe;gBAC5B,GAAG,MAAM,eAAe;QAChC;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 909, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/date-utils.js"], "sourcesContent": ["const options = {\n    hour: 'numeric',\n    minute: '2-digit',\n    hour12: true,\n    weekday: 'short',\n    month: 'short',\n    day: 'numeric',\n    year: 'numeric'\n};\nconst formatter = new Intl.DateTimeFormat('en-US', options);\nconst partsArrayToObject = (parts)=>{\n    const result = {};\n    for (const part of parts){\n        result[part.type] = part.value;\n    }\n    return result;\n};\nexport const formatDate = (date)=>{\n    const parts = partsArrayToObject(formatter.formatToParts(date));\n    const formattedTime = `${parts.hour}:${parts.minute} ${parts.dayPeriod}`;\n    const formattedDate = `${parts.month} ${parts.day}, ${parts.year}`;\n    return `${formattedTime} · ${formattedDate}`;\n};\n"], "names": [], "mappings": ";;;AAAA,MAAM,UAAU;IACZ,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,SAAS;IACT,OAAO;IACP,KAAK;IACL,MAAM;AACV;AACA,MAAM,YAAY,IAAI,KAAK,cAAc,CAAC,SAAS;AACnD,MAAM,qBAAqB,CAAC;IACxB,MAAM,SAAS,CAAC;IAChB,KAAK,MAAM,QAAQ,MAAM;QACrB,MAAM,CAAC,KAAK,IAAI,CAAC,GAAG,KAAK,KAAK;IAClC;IACA,OAAO;AACX;AACO,MAAM,aAAa,CAAC;IACvB,MAAM,QAAQ,mBAAmB,UAAU,aAAa,CAAC;IACzD,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,CAAC,CAAC,EAAE,MAAM,SAAS,EAAE;IACxE,MAAM,gBAAgB,GAAG,MAAM,KAAK,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC,EAAE,EAAE,MAAM,IAAI,EAAE;IAClE,OAAO,GAAG,cAAc,GAAG,EAAE,eAAe;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"root\": \"tweet-info-created-at-module__d3DV9a__root\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 949, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-info-created-at.js"], "sourcesContent": ["import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { formatDate } from '../date-utils.js';\nimport s from './tweet-info-created-at.module.css';\nexport const TweetInfoCreatedAt = ({ tweet })=>{\n    const createdAt = new Date(tweet.created_at);\n    const formattedCreatedAtDate = formatDate(createdAt);\n    return /*#__PURE__*/ _jsx(\"a\", {\n        className: s.root,\n        href: tweet.url,\n        target: \"_blank\",\n        rel: \"noopener noreferrer\",\n        \"aria-label\": formattedCreatedAtDate,\n        children: /*#__PURE__*/ _jsx(\"time\", {\n            dateTime: createdAt.toISOString(),\n            children: formattedCreatedAtDate\n        })\n    });\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,MAAM,qBAAqB,CAAC,EAAE,KAAK,EAAE;IACxC,MAAM,YAAY,IAAI,KAAK,MAAM,UAAU;IAC3C,MAAM,yBAAyB,CAAA,GAAA,mQAAA,CAAA,aAAU,AAAD,EAAE;IAC1C,OAAO,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,KAAK;QAC3B,WAAW,oTAAA,CAAA,UAAC,CAAC,IAAI;QACjB,MAAM,MAAM,GAAG;QACf,QAAQ;QACR,KAAK;QACL,cAAc;QACd,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;YACjC,UAAU,UAAU,WAAW;YAC/B,UAAU;QACd;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 978, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-info.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"info\": \"tweet-info-module__t5sx6W__info\",\n  \"infoIcon\": \"tweet-info-module__t5sx6W__infoIcon\",\n  \"infoLink\": \"tweet-info-module__t5sx6W__infoLink\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 989, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-info.js"], "sourcesContent": ["import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { TweetInfoCreatedAt } from './tweet-info-created-at.js';\nimport s from './tweet-info.module.css';\nexport const TweetInfo = ({ tweet })=>/*#__PURE__*/ _jsxs(\"div\", {\n        className: s.info,\n        children: [\n            /*#__PURE__*/ _jsx(TweetInfoCreatedAt, {\n                tweet: tweet\n            }),\n            /*#__PURE__*/ _jsx(\"a\", {\n                className: s.infoLink,\n                href: \"https://help.x.com/en/x-for-websites-ads-info-and-privacy\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                \"aria-label\": \"Twitter for Websites, Ads Information and Privacy\",\n                children: /*#__PURE__*/ _jsx(\"svg\", {\n                    viewBox: \"0 0 24 24\",\n                    \"aria-hidden\": \"true\",\n                    className: s.infoIcon,\n                    children: /*#__PURE__*/ _jsx(\"g\", {\n                        children: /*#__PURE__*/ _jsx(\"path\", {\n                            d: \"M13.5 8.5c0 .83-.67 1.5-1.5 1.5s-1.5-.67-1.5-1.5S11.17 7 12 7s1.5.67 1.5 1.5zM13 17v-5h-2v5h2zm-1 5.25c5.66 0 10.25-4.59 10.25-10.25S17.66 1.75 12 1.75 1.75 6.34 1.75 12 6.34 22.25 12 22.25zM20.25 12c0 4.56-3.69 8.25-8.25 8.25S3.75 16.56 3.75 12 7.44 3.75 12 3.75s8.25 3.69 8.25 8.25z\"\n                        })\n                    })\n                })\n            })\n        ]\n    });\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,MAAM,YAAY,CAAC,EAAE,KAAK,EAAE,GAAG,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,OAAK,AAAD,EAAE,OAAO;QACzD,WAAW,mSAAA,CAAA,UAAC,CAAC,IAAI;QACjB,UAAU;YACN,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,wSAAA,CAAA,qBAAkB,EAAE;gBACnC,OAAO;YACX;YACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,KAAK;gBACpB,WAAW,mSAAA,CAAA,UAAC,CAAC,QAAQ;gBACrB,MAAM;gBACN,QAAQ;gBACR,KAAK;gBACL,cAAc;gBACd,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;oBAChC,SAAS;oBACT,eAAe;oBACf,WAAW,mSAAA,CAAA,UAAC,CAAC,QAAQ;oBACrB,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,KAAK;wBAC9B,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;4BACjC,GAAG;wBACP;oBACJ;gBACJ;YACJ;SACH;IACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1028, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-actions.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"actions\": \"tweet-actions-module__4dwWOa__actions\",\n  \"copy\": \"tweet-actions-module__4dwWOa__copy\",\n  \"copyIcon\": \"tweet-actions-module__4dwWOa__copyIcon\",\n  \"copyIconWrapper\": \"tweet-actions-module__4dwWOa__copyIconWrapper\",\n  \"copyText\": \"tweet-actions-module__4dwWOa__copyText\",\n  \"like\": \"tweet-actions-module__4dwWOa__like\",\n  \"likeCount\": \"tweet-actions-module__4dwWOa__likeCount\",\n  \"likeIcon\": \"tweet-actions-module__4dwWOa__likeIcon\",\n  \"likeIconWrapper\": \"tweet-actions-module__4dwWOa__likeIconWrapper\",\n  \"reply\": \"tweet-actions-module__4dwWOa__reply\",\n  \"replyIcon\": \"tweet-actions-module__4dwWOa__replyIcon\",\n  \"replyIconWrapper\": \"tweet-actions-module__4dwWOa__replyIconWrapper\",\n  \"replyText\": \"tweet-actions-module__4dwWOa__replyText\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 1049, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-actions-copy.js"], "sourcesContent": ["'use client';\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { useState, useEffect } from 'react';\nimport s from './tweet-actions.module.css';\nexport const TweetActionsCopy = ({ tweet })=>{\n    const [copied, setCopied] = useState(false);\n    const handleCopy = ()=>{\n        navigator.clipboard.writeText(tweet.url);\n        setCopied(true);\n    };\n    useEffect(()=>{\n        if (copied) {\n            const timeout = setTimeout(()=>{\n                setCopied(false);\n            }, 6000);\n            return ()=>clearTimeout(timeout);\n        }\n    }, [\n        copied\n    ]);\n    return /*#__PURE__*/ _jsxs(\"button\", {\n        type: \"button\",\n        className: s.copy,\n        \"aria-label\": \"Copy link\",\n        onClick: handleCopy,\n        children: [\n            /*#__PURE__*/ _jsx(\"div\", {\n                className: s.copyIconWrapper,\n                children: copied ? /*#__PURE__*/ _jsx(\"svg\", {\n                    viewBox: \"0 0 24 24\",\n                    className: s.copyIcon,\n                    \"aria-hidden\": \"true\",\n                    children: /*#__PURE__*/ _jsx(\"g\", {\n                        children: /*#__PURE__*/ _jsx(\"path\", {\n                            d: \"M9.64 18.952l-5.55-4.861 1.317-1.504 3.951 3.459 8.459-10.948L19.4 6.32 9.64 18.952z\"\n                        })\n                    })\n                }) : /*#__PURE__*/ _jsx(\"svg\", {\n                    viewBox: \"0 0 24 24\",\n                    className: s.copyIcon,\n                    \"aria-hidden\": \"true\",\n                    children: /*#__PURE__*/ _jsx(\"g\", {\n                        children: /*#__PURE__*/ _jsx(\"path\", {\n                            d: \"M18.36 5.64c-1.95-1.96-5.11-1.96-7.07 0L9.88 7.05 8.46 5.64l1.42-1.42c2.73-2.73 7.16-2.73 9.9 0 2.73 2.74 2.73 7.17 0 9.9l-1.42 1.42-1.41-1.42 1.41-1.41c1.96-1.96 1.96-5.12 0-7.07zm-2.12 3.53l-7.07 7.07-1.41-1.41 7.07-7.07 1.41 1.41zm-12.02.71l1.42-1.42 1.41 1.42-1.41 1.41c-1.96 1.96-1.96 5.12 0 7.07 1.95 1.96 5.11 1.96 7.07 0l1.41-1.41 1.42 1.41-1.42 1.42c-2.73 2.73-7.16 2.73-9.9 0-2.73-2.74-2.73-7.17 0-9.9z\"\n                        })\n                    })\n                })\n            }),\n            /*#__PURE__*/ _jsx(\"span\", {\n                className: s.copyText,\n                children: copied ? 'Copied!' : 'Copy link'\n            })\n        ]\n    });\n};\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AAHA;;;;AAIO,MAAM,mBAAmB,CAAC,EAAE,KAAK,EAAE;IACtC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,aAAa;QACf,UAAU,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG;QACvC,UAAU;IACd;IACA,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;sCAAE;YACN,IAAI,QAAQ;gBACR,MAAM,UAAU;0DAAW;wBACvB,UAAU;oBACd;yDAAG;gBACH;kDAAO,IAAI,aAAa;;YAC5B;QACJ;qCAAG;QACC;KACH;IACD,OAAO,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,OAAK,AAAD,EAAE,UAAU;QACjC,MAAM;QACN,WAAW,sSAAA,CAAA,UAAC,CAAC,IAAI;QACjB,cAAc;QACd,SAAS;QACT,UAAU;YACN,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;gBACtB,WAAW,sSAAA,CAAA,UAAC,CAAC,eAAe;gBAC5B,UAAU,SAAS,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;oBACzC,SAAS;oBACT,WAAW,sSAAA,CAAA,UAAC,CAAC,QAAQ;oBACrB,eAAe;oBACf,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,KAAK;wBAC9B,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;4BACjC,GAAG;wBACP;oBACJ;gBACJ,KAAK,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;oBAC3B,SAAS;oBACT,WAAW,sSAAA,CAAA,UAAC,CAAC,QAAQ;oBACrB,eAAe;oBACf,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,KAAK;wBAC9B,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;4BACjC,GAAG;wBACP;oBACJ;gBACJ;YACJ;YACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;gBACvB,WAAW,sSAAA,CAAA,UAAC,CAAC,QAAQ;gBACrB,UAAU,SAAS,YAAY;YACnC;SACH;IACL;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1122, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-actions.js"], "sourcesContent": ["import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { formatNumber } from '../utils.js';\nimport { TweetActionsCopy } from './tweet-actions-copy.js';\nimport s from './tweet-actions.module.css';\nexport const TweetActions = ({ tweet })=>{\n    const favoriteCount = formatNumber(tweet.favorite_count);\n    return /*#__PURE__*/ _jsxs(\"div\", {\n        className: s.actions,\n        children: [\n            /*#__PURE__*/ _jsxs(\"a\", {\n                className: s.like,\n                href: tweet.like_url,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                \"aria-label\": `Like. This Tweet has ${favoriteCount} likes`,\n                children: [\n                    /*#__PURE__*/ _jsx(\"div\", {\n                        className: s.likeIconWrapper,\n                        children: /*#__PURE__*/ _jsx(\"svg\", {\n                            viewBox: \"0 0 24 24\",\n                            className: s.likeIcon,\n                            \"aria-hidden\": \"true\",\n                            children: /*#__PURE__*/ _jsx(\"g\", {\n                                children: /*#__PURE__*/ _jsx(\"path\", {\n                                    d: \"M20.884 13.19c-1.351 2.48-4.001 5.12-8.379 7.67l-.503.3-.504-.3c-4.379-2.55-7.029-5.19-8.382-7.67-1.36-2.5-1.41-4.86-.514-6.67.887-1.79 2.647-2.91 4.601-3.01 1.651-.09 3.368.56 4.798 2.01 1.429-1.45 3.146-2.1 4.796-2.01 1.954.1 3.714 1.22 4.601 3.01.896 1.81.846 4.17-.514 6.67z\"\n                                })\n                            })\n                        })\n                    }),\n                    /*#__PURE__*/ _jsx(\"span\", {\n                        className: s.likeCount,\n                        children: favoriteCount\n                    })\n                ]\n            }),\n            /*#__PURE__*/ _jsxs(\"a\", {\n                className: s.reply,\n                href: tweet.reply_url,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                \"aria-label\": \"Reply to this Tweet on Twitter\",\n                children: [\n                    /*#__PURE__*/ _jsx(\"div\", {\n                        className: s.replyIconWrapper,\n                        children: /*#__PURE__*/ _jsx(\"svg\", {\n                            viewBox: \"0 0 24 24\",\n                            className: s.replyIcon,\n                            \"aria-hidden\": \"true\",\n                            children: /*#__PURE__*/ _jsx(\"g\", {\n                                children: /*#__PURE__*/ _jsx(\"path\", {\n                                    d: \"M1.751 10c0-4.42 3.584-8 8.005-8h4.366c4.49 0 8.129 3.64 8.129 8.13 0 2.96-1.607 5.68-4.196 7.11l-8.054 4.46v-3.69h-.067c-4.49.1-8.183-3.51-8.183-8.01z\"\n                                })\n                            })\n                        })\n                    }),\n                    /*#__PURE__*/ _jsx(\"span\", {\n                        className: s.replyText,\n                        children: \"Reply\"\n                    })\n                ]\n            }),\n            /*#__PURE__*/ _jsx(TweetActionsCopy, {\n                tweet: tweet\n            })\n        ]\n    });\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACO,MAAM,eAAe,CAAC,EAAE,KAAK,EAAE;IAClC,MAAM,gBAAgB,CAAA,GAAA,2PAAA,CAAA,eAAY,AAAD,EAAE,MAAM,cAAc;IACvD,OAAO,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,OAAK,AAAD,EAAE,OAAO;QAC9B,WAAW,sSAAA,CAAA,UAAC,CAAC,OAAO;QACpB,UAAU;YACN,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,OAAK,AAAD,EAAE,KAAK;gBACrB,WAAW,sSAAA,CAAA,UAAC,CAAC,IAAI;gBACjB,MAAM,MAAM,QAAQ;gBACpB,QAAQ;gBACR,KAAK;gBACL,cAAc,CAAC,qBAAqB,EAAE,cAAc,MAAM,CAAC;gBAC3D,UAAU;oBACN,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;wBACtB,WAAW,sSAAA,CAAA,UAAC,CAAC,eAAe;wBAC5B,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;4BAChC,SAAS;4BACT,WAAW,sSAAA,CAAA,UAAC,CAAC,QAAQ;4BACrB,eAAe;4BACf,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,KAAK;gCAC9B,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;oCACjC,GAAG;gCACP;4BACJ;wBACJ;oBACJ;oBACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;wBACvB,WAAW,sSAAA,CAAA,UAAC,CAAC,SAAS;wBACtB,UAAU;oBACd;iBACH;YACL;YACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,OAAK,AAAD,EAAE,KAAK;gBACrB,WAAW,sSAAA,CAAA,UAAC,CAAC,KAAK;gBAClB,MAAM,MAAM,SAAS;gBACrB,QAAQ;gBACR,KAAK;gBACL,cAAc;gBACd,UAAU;oBACN,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;wBACtB,WAAW,sSAAA,CAAA,UAAC,CAAC,gBAAgB;wBAC7B,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;4BAChC,SAAS;4BACT,WAAW,sSAAA,CAAA,UAAC,CAAC,SAAS;4BACtB,eAAe;4BACf,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,KAAK;gCAC9B,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;oCACjC,GAAG;gCACP;4BACJ;wBACJ;oBACJ;oBACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;wBACvB,WAAW,sSAAA,CAAA,UAAC,CAAC,SAAS;wBACtB,UAAU;oBACd;iBACH;YACL;YACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,kSAAA,CAAA,mBAAgB,EAAE;gBACjC,OAAO;YACX;SACH;IACL;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1201, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-replies.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"link\": \"tweet-replies-module__gLz8CW__link\",\n  \"replies\": \"tweet-replies-module__gLz8CW__replies\",\n  \"text\": \"tweet-replies-module__gLz8CW__text\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 1212, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-replies.js"], "sourcesContent": ["import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { formatNumber } from '../utils.js';\nimport s from './tweet-replies.module.css';\nexport const TweetReplies = ({ tweet })=>/*#__PURE__*/ _jsx(\"div\", {\n        className: s.replies,\n        children: /*#__PURE__*/ _jsx(\"a\", {\n            className: s.link,\n            href: tweet.url,\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            children: /*#__PURE__*/ _jsx(\"span\", {\n                className: s.text,\n                children: tweet.conversation_count === 0 ? 'Read more on X' : tweet.conversation_count === 1 ? `Read ${formatNumber(tweet.conversation_count)} reply` : `Read ${formatNumber(tweet.conversation_count)} replies`\n            })\n        })\n    });\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,MAAM,eAAe,CAAC,EAAE,KAAK,EAAE,GAAG,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC3D,WAAW,sSAAA,CAAA,UAAC,CAAC,OAAO;QACpB,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,KAAK;YAC9B,WAAW,sSAAA,CAAA,UAAC,CAAC,IAAI;YACjB,MAAM,MAAM,GAAG;YACf,QAAQ;YACR,KAAK;YACL,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;gBACjC,WAAW,sSAAA,CAAA,UAAC,CAAC,IAAI;gBACjB,UAAU,MAAM,kBAAkB,KAAK,IAAI,mBAAmB,MAAM,kBAAkB,KAAK,IAAI,CAAC,KAAK,EAAE,CAAA,GAAA,2PAAA,CAAA,eAAY,AAAD,EAAE,MAAM,kBAAkB,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,CAAA,GAAA,2PAAA,CAAA,eAAY,AAAD,EAAE,MAAM,kBAAkB,EAAE,QAAQ,CAAC;YACpN;QACJ;IACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1239, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"article\": \"quoted-tweet-container-module__NjboUa__article\",\n  \"root\": \"quoted-tweet-container-module__NjboUa__root\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 1249, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-container.js"], "sourcesContent": ["'use client';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport s from './quoted-tweet-container.module.css';\nexport const QuotedTweetContainer = ({ tweet, children })=>/*#__PURE__*/ _jsx(\"div\", {\n        className: s.root,\n        onClick: (e)=>{\n            e.preventDefault();\n            window.open(tweet.url, '_blank');\n        },\n        children: /*#__PURE__*/ _jsx(\"article\", {\n            className: s.article,\n            children: children\n        })\n    });\n"], "names": [], "mappings": ";;;AACA;AACA;AAFA;;;AAGO,MAAM,uBAAuB,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;QAC7E,WAAW,qUAAA,CAAA,UAAC,CAAC,IAAI;QACjB,SAAS,CAAC;YACN,EAAE,cAAc;YAChB,OAAO,IAAI,CAAC,MAAM,GAAG,EAAE;QAC3B;QACA,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,WAAW;YACpC,WAAW,qUAAA,CAAA,UAAC,CAAC,OAAO;YACpB,UAAU;QACd;IACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1273, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"author\": \"quoted-tweet-header-module__zEsWUa__author\",\n  \"authorText\": \"quoted-tweet-header-module__zEsWUa__authorText\",\n  \"avatar\": \"quoted-tweet-header-module__zEsWUa__avatar\",\n  \"avatarSquare\": \"quoted-tweet-header-module__zEsWUa__avatarSquare\",\n  \"header\": \"quoted-tweet-header-module__zEsWUa__header\",\n  \"username\": \"quoted-tweet-header-module__zEsWUa__username\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 1287, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-header.js"], "sourcesContent": ["import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport clsx from 'clsx';\nimport { AvatarImg } from '../avatar-img.js';\nimport s from './quoted-tweet-header.module.css';\nimport { VerifiedBadge } from '../verified-badge.js';\nexport const QuotedTweetHeader = ({ tweet })=>{\n    const { user } = tweet;\n    return /*#__PURE__*/ _jsxs(\"div\", {\n        className: s.header,\n        children: [\n            /*#__PURE__*/ _jsx(\"a\", {\n                href: tweet.url,\n                className: s.avatar,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                children: /*#__PURE__*/ _jsx(\"div\", {\n                    className: clsx(s.avatarOverflow, user.profile_image_shape === 'Square' && s.avatarSquare),\n                    children: /*#__PURE__*/ _jsx(AvatarImg, {\n                        src: user.profile_image_url_https,\n                        alt: user.name,\n                        width: 20,\n                        height: 20\n                    })\n                })\n            }),\n            /*#__PURE__*/ _jsxs(\"div\", {\n                className: s.author,\n                children: [\n                    /*#__PURE__*/ _jsx(\"div\", {\n                        className: s.authorText,\n                        children: /*#__PURE__*/ _jsx(\"span\", {\n                            title: user.name,\n                            children: user.name\n                        })\n                    }),\n                    /*#__PURE__*/ _jsx(VerifiedBadge, {\n                        user: user\n                    }),\n                    /*#__PURE__*/ _jsx(\"div\", {\n                        className: s.username,\n                        children: /*#__PURE__*/ _jsxs(\"span\", {\n                            title: `@${user.screen_name}`,\n                            children: [\n                                \"@\",\n                                user.screen_name\n                            ]\n                        })\n                    })\n                ]\n            })\n        ]\n    });\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AACO,MAAM,oBAAoB,CAAC,EAAE,KAAK,EAAE;IACvC,MAAM,EAAE,IAAI,EAAE,GAAG;IACjB,OAAO,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,OAAK,AAAD,EAAE,OAAO;QAC9B,WAAW,kUAAA,CAAA,UAAC,CAAC,MAAM;QACnB,UAAU;YACN,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,KAAK;gBACpB,MAAM,MAAM,GAAG;gBACf,WAAW,kUAAA,CAAA,UAAC,CAAC,MAAM;gBACnB,QAAQ;gBACR,KAAK;gBACL,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;oBAChC,WAAW,CAAA,GAAA,yLAAA,CAAA,UAAI,AAAD,EAAE,kUAAA,CAAA,UAAC,CAAC,cAAc,EAAE,KAAK,mBAAmB,KAAK,YAAY,kUAAA,CAAA,UAAC,CAAC,YAAY;oBACzF,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,uRAAA,CAAA,YAAS,EAAE;wBACpC,KAAK,KAAK,uBAAuB;wBACjC,KAAK,KAAK,IAAI;wBACd,OAAO;wBACP,QAAQ;oBACZ;gBACJ;YACJ;YACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,OAAK,AAAD,EAAE,OAAO;gBACvB,WAAW,kUAAA,CAAA,UAAC,CAAC,MAAM;gBACnB,UAAU;oBACN,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;wBACtB,WAAW,kUAAA,CAAA,UAAC,CAAC,UAAU;wBACvB,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;4BACjC,OAAO,KAAK,IAAI;4BAChB,UAAU,KAAK,IAAI;wBACvB;oBACJ;oBACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,2RAAA,CAAA,gBAAa,EAAE;wBAC9B,MAAM;oBACV;oBACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;wBACtB,WAAW,kUAAA,CAAA,UAAC,CAAC,QAAQ;wBACrB,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,OAAK,AAAD,EAAE,QAAQ;4BAClC,OAAO,CAAC,CAAC,EAAE,KAAK,WAAW,EAAE;4BAC7B,UAAU;gCACN;gCACA,KAAK,WAAW;6BACnB;wBACL;oBACJ;iBACH;YACL;SACH;IACL;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1353, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"root\": \"quoted-tweet-body-module__qtcc7q__root\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 1362, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet-body.js"], "sourcesContent": ["import { jsx as _jsx } from \"react/jsx-runtime\";\nimport s from './quoted-tweet-body.module.css';\nexport const QuotedTweetBody = ({ tweet })=>/*#__PURE__*/ _jsx(\"p\", {\n        className: s.root,\n        lang: tweet.lang,\n        dir: \"auto\",\n        children: tweet.entities.map((item, i)=>/*#__PURE__*/ _jsx(\"span\", {\n                dangerouslySetInnerHTML: {\n                    __html: item.text\n                }\n            }, i))\n    });\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,MAAM,kBAAkB,CAAC,EAAE,KAAK,EAAE,GAAG,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,KAAK;QAC5D,WAAW,gUAAA,CAAA,UAAC,CAAC,IAAI;QACjB,MAAM,MAAM,IAAI;QAChB,KAAK;QACL,UAAU,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,IAAI,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;gBAC3D,yBAAyB;oBACrB,QAAQ,KAAK,IAAI;gBACrB;YACJ,GAAG;IACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1385, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/quoted-tweet/quoted-tweet.js"], "sourcesContent": ["import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { QuotedTweetContainer } from './quoted-tweet-container.js';\nimport { QuotedTweetHeader } from './quoted-tweet-header.js';\nimport { QuotedTweetBody } from './quoted-tweet-body.js';\nimport { TweetMedia } from '../tweet-media.js';\nexport const QuotedTweet = ({ tweet })=>{\n    var _tweet_mediaDetails;\n    return /*#__PURE__*/ _jsxs(QuotedTweetContainer, {\n        tweet: tweet,\n        children: [\n            /*#__PURE__*/ _jsx(QuotedTweetHeader, {\n                tweet: tweet\n            }),\n            /*#__PURE__*/ _jsx(QuotedTweetBody, {\n                tweet: tweet\n            }),\n            ((_tweet_mediaDetails = tweet.mediaDetails) == null ? void 0 : _tweet_mediaDetails.length) ? /*#__PURE__*/ _jsx(TweetMedia, {\n                quoted: true,\n                tweet: tweet\n            }) : null\n        ]\n    });\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AACO,MAAM,cAAc,CAAC,EAAE,KAAK,EAAE;IACjC,IAAI;IACJ,OAAO,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,OAAK,AAAD,EAAE,yTAAA,CAAA,uBAAoB,EAAE;QAC7C,OAAO;QACP,UAAU;YACN,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,sTAAA,CAAA,oBAAiB,EAAE;gBAClC,OAAO;YACX;YACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,oTAAA,CAAA,kBAAe,EAAE;gBAChC,OAAO;YACX;YACA,CAAC,CAAC,sBAAsB,MAAM,YAAY,KAAK,OAAO,KAAK,IAAI,oBAAoB,MAAM,IAAI,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,wRAAA,CAAA,aAAU,EAAE;gBACxH,QAAQ;gBACR,OAAO;YACX,KAAK;SACR;IACL;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1422, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/embedded-tweet.js"], "sourcesContent": ["import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { TweetContainer } from './tweet-container.js';\nimport { TweetHeader } from './tweet-header.js';\nimport { TweetInReplyTo } from './tweet-in-reply-to.js';\nimport { TweetBody } from './tweet-body.js';\nimport { TweetMedia } from './tweet-media.js';\nimport { TweetInfo } from './tweet-info.js';\nimport { TweetActions } from './tweet-actions.js';\nimport { TweetReplies } from './tweet-replies.js';\nimport { QuotedTweet } from './quoted-tweet/index.js';\nimport { enrichTweet } from '../utils.js';\nimport { useMemo } from 'react';\nexport const EmbeddedTweet = ({ tweet: t, components })=>{\n    var _tweet_mediaDetails;\n    // useMemo does nothing for RSC but it helps when the component is used in the client (e.g by SWR)\n    const tweet = useMemo(()=>enrichTweet(t), [\n        t\n    ]);\n    return /*#__PURE__*/ _jsxs(TweetContainer, {\n        children: [\n            /*#__PURE__*/ _jsx(TweetHeader, {\n                tweet: tweet,\n                components: components\n            }),\n            tweet.in_reply_to_status_id_str && /*#__PURE__*/ _jsx(TweetInReplyTo, {\n                tweet: tweet\n            }),\n            /*#__PURE__*/ _jsx(TweetBody, {\n                tweet: tweet\n            }),\n            ((_tweet_mediaDetails = tweet.mediaDetails) == null ? void 0 : _tweet_mediaDetails.length) ? /*#__PURE__*/ _jsx(TweetMedia, {\n                tweet: tweet,\n                components: components\n            }) : null,\n            tweet.quoted_tweet && /*#__PURE__*/ _jsx(QuotedTweet, {\n                tweet: tweet.quoted_tweet\n            }),\n            /*#__PURE__*/ _jsx(TweetInfo, {\n                tweet: tweet\n            }),\n            /*#__PURE__*/ _jsx(TweetActions, {\n                tweet: tweet\n            }),\n            /*#__PURE__*/ _jsx(TweetReplies, {\n                tweet: tweet\n            })\n        ]\n    });\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AACO,MAAM,gBAAgB,CAAC,EAAE,OAAO,CAAC,EAAE,UAAU,EAAE;IAClD,IAAI;IACJ,kGAAkG;IAClG,MAAM,QAAQ,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;wCAAE,IAAI,CAAA,GAAA,2PAAA,CAAA,cAAW,AAAD,EAAE;uCAAI;QACtC;KACH;IACD,OAAO,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,OAAK,AAAD,EAAE,4RAAA,CAAA,iBAAc,EAAE;QACvC,UAAU;YACN,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,yRAAA,CAAA,cAAW,EAAE;gBAC5B,OAAO;gBACP,YAAY;YAChB;YACA,MAAM,yBAAyB,IAAI,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,oSAAA,CAAA,iBAAc,EAAE;gBAClE,OAAO;YACX;YACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,uRAAA,CAAA,YAAS,EAAE;gBAC1B,OAAO;YACX;YACA,CAAC,CAAC,sBAAsB,MAAM,YAAY,KAAK,OAAO,KAAK,IAAI,oBAAoB,MAAM,IAAI,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,wRAAA,CAAA,aAAU,EAAE;gBACxH,OAAO;gBACP,YAAY;YAChB,KAAK;YACL,MAAM,YAAY,IAAI,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,4SAAA,CAAA,cAAW,EAAE;gBAClD,OAAO,MAAM,YAAY;YAC7B;YACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,uRAAA,CAAA,YAAS,EAAE;gBAC1B,OAAO;YACX;YACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,0RAAA,CAAA,eAAY,EAAE;gBAC7B,OAAO;YACX;YACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,0RAAA,CAAA,eAAY,EAAE;gBAC7B,OAAO;YACX;SACH;IACL;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1493, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-not-found.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"root\": \"tweet-not-found-module__utwuua__root\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 1502, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-not-found.js"], "sourcesContent": ["import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { TweetContainer } from './tweet-container.js';\nimport styles from './tweet-not-found.module.css';\nexport const TweetNotFound = (_props)=>/*#__PURE__*/ _jsx(TweetContainer, {\n        children: /*#__PURE__*/ _jsxs(\"div\", {\n            className: styles.root,\n            children: [\n                /*#__PURE__*/ _jsx(\"h3\", {\n                    children: \"Tweet not found\"\n                }),\n                /*#__PURE__*/ _jsx(\"p\", {\n                    children: \"The embedded tweet could not be found…\"\n                })\n            ]\n        })\n    });\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACO,MAAM,gBAAgB,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,4RAAA,CAAA,iBAAc,EAAE;QAClE,UAAU,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,OAAK,AAAD,EAAE,OAAO;YACjC,WAAW,2SAAA,CAAA,UAAM,CAAC,IAAI;YACtB,UAAU;gBACN,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,MAAM;oBACrB,UAAU;gBACd;gBACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,KAAK;oBACpB,UAAU;gBACd;aACH;QACL;IACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1529, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/skeleton.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"loading\": \"skeleton-module__22bsIq__loading\",\n  \"skeleton\": \"skeleton-module__22bsIq__skeleton\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 1539, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/skeleton.js"], "sourcesContent": ["import { jsx as _jsx } from \"react/jsx-runtime\";\nimport styles from './skeleton.module.css';\nexport const Skeleton = ({ style })=>/*#__PURE__*/ _jsx(\"span\", {\n        className: styles.skeleton,\n        style: style\n    });\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,MAAM,WAAW,CAAC,EAAE,KAAK,EAAE,GAAG,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,QAAQ;QACxD,WAAW,8RAAA,CAAA,UAAM,CAAC,QAAQ;QAC1B,OAAO;IACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1555, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"root\": \"tweet-skeleton-module__CfvzVa__root\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 1564, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-skeleton.js"], "sourcesContent": ["import { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { TweetContainer } from './tweet-container.js';\nimport { Skeleton } from './skeleton.js';\nimport styles from './tweet-skeleton.module.css';\nexport const TweetSkeleton = ()=>/*#__PURE__*/ _jsxs(TweetContainer, {\n        className: styles.root,\n        children: [\n            /*#__PURE__*/ _jsx(Skeleton, {\n                style: {\n                    height: '3rem',\n                    marginBottom: '0.75rem'\n                }\n            }),\n            /*#__PURE__*/ _jsx(Skeleton, {\n                style: {\n                    height: '6rem',\n                    margin: '0.5rem 0'\n                }\n            }),\n            /*#__PURE__*/ _jsx(\"div\", {\n                style: {\n                    borderTop: 'var(--tweet-border)',\n                    margin: '0.5rem 0'\n                }\n            }),\n            /*#__PURE__*/ _jsx(Skeleton, {\n                style: {\n                    height: '2rem'\n                }\n            }),\n            /*#__PURE__*/ _jsx(Skeleton, {\n                style: {\n                    height: '2rem',\n                    borderRadius: '9999px',\n                    marginTop: '0.5rem'\n                }\n            })\n        ]\n    });\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACO,MAAM,gBAAgB,IAAI,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,OAAK,AAAD,EAAE,4RAAA,CAAA,iBAAc,EAAE;QAC7D,WAAW,uSAAA,CAAA,UAAM,CAAC,IAAI;QACtB,UAAU;YACN,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,kRAAA,CAAA,WAAQ,EAAE;gBACzB,OAAO;oBACH,QAAQ;oBACR,cAAc;gBAClB;YACJ;YACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,kRAAA,CAAA,WAAQ,EAAE;gBACzB,OAAO;oBACH,QAAQ;oBACR,QAAQ;gBACZ;YACJ;YACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,OAAO;gBACtB,OAAO;oBACH,WAAW;oBACX,QAAQ;gBACZ;YACJ;YACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,kRAAA,CAAA,WAAQ,EAAE;gBACzB,OAAO;oBACH,QAAQ;gBACZ;YACJ;YACA,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,kRAAA,CAAA,WAAQ,EAAE;gBACzB,OAAO;oBACH,QAAQ;oBACR,cAAc;oBACd,WAAW;gBACf;YACJ;SACH;IACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1616, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/api/fetch-tweet.js"], "sourcesContent": ["const SYNDICATION_URL = 'https://cdn.syndication.twimg.com';\nexport class TwitterApiError extends Error {\n    constructor({ message, status, data }){\n        super(message);\n        this.name = 'TwitterApiError';\n        this.status = status;\n        this.data = data;\n    }\n}\nconst TWEET_ID = /^[0-9]+$/;\nfunction getToken(id) {\n    return (Number(id) / 1e15 * Math.PI).toString(6 ** 2).replace(/(0+|\\.)/g, '');\n}\n/**\n * Fetches a tweet from the Twitter syndication API.\n */ export async function fetchTweet(id, fetchOptions) {\n    var _res_headers_get;\n    if (id.length > 40 || !TWEET_ID.test(id)) {\n        throw new Error(`Invalid tweet id: ${id}`);\n    }\n    const url = new URL(`${SYNDICATION_URL}/tweet-result`);\n    url.searchParams.set('id', id);\n    url.searchParams.set('lang', 'en');\n    url.searchParams.set('features', [\n        'tfw_timeline_list:',\n        'tfw_follower_count_sunset:true',\n        'tfw_tweet_edit_backend:on',\n        'tfw_refsrc_session:on',\n        'tfw_fosnr_soft_interventions_enabled:on',\n        'tfw_show_birdwatch_pivots_enabled:on',\n        'tfw_show_business_verified_badge:on',\n        'tfw_duplicate_scribes_to_settings:on',\n        'tfw_use_profile_image_shape_enabled:on',\n        'tfw_show_blue_verified_badge:on',\n        'tfw_legacy_timeline_sunset:true',\n        'tfw_show_gov_verified_badge:on',\n        'tfw_show_business_affiliate_badge:on',\n        'tfw_tweet_edit_frontend:on'\n    ].join(';'));\n    url.searchParams.set('token', getToken(id));\n    const res = await fetch(url.toString(), fetchOptions);\n    const isJson = (_res_headers_get = res.headers.get('content-type')) == null ? void 0 : _res_headers_get.includes('application/json');\n    const data = isJson ? await res.json() : undefined;\n    if (res.ok) {\n        if ((data == null ? void 0 : data.__typename) === 'TweetTombstone') {\n            return {\n                tombstone: true\n            };\n        }\n        return {\n            data\n        };\n    }\n    if (res.status === 404) {\n        return {\n            notFound: true\n        };\n    }\n    throw new TwitterApiError({\n        message: typeof data.error === 'string' ? data.error : `Failed to fetch tweet at \"${url}\" with \"${res.status}\".`,\n        status: res.status,\n        data\n    });\n}\n"], "names": [], "mappings": ";;;;AAAA,MAAM,kBAAkB;AACjB,MAAM,wBAAwB;IACjC,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;QAClC,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,IAAI,GAAG;IAChB;AACJ;AACA,MAAM,WAAW;AACjB,SAAS,SAAS,EAAE;IAChB,OAAO,CAAC,OAAO,MAAM,OAAO,KAAK,EAAE,EAAE,QAAQ,CAAC,KAAK,GAAG,OAAO,CAAC,YAAY;AAC9E;AAGW,eAAe,WAAW,EAAE,EAAE,YAAY;IACjD,IAAI;IACJ,IAAI,GAAG,MAAM,GAAG,MAAM,CAAC,SAAS,IAAI,CAAC,KAAK;QACtC,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,IAAI;IAC7C;IACA,MAAM,MAAM,IAAI,IAAI,GAAG,gBAAgB,aAAa,CAAC;IACrD,IAAI,YAAY,CAAC,GAAG,CAAC,MAAM;IAC3B,IAAI,YAAY,CAAC,GAAG,CAAC,QAAQ;IAC7B,IAAI,YAAY,CAAC,GAAG,CAAC,YAAY;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACH,CAAC,IAAI,CAAC;IACP,IAAI,YAAY,CAAC,GAAG,CAAC,SAAS,SAAS;IACvC,MAAM,MAAM,MAAM,MAAM,IAAI,QAAQ,IAAI;IACxC,MAAM,SAAS,CAAC,mBAAmB,IAAI,OAAO,CAAC,GAAG,CAAC,eAAe,KAAK,OAAO,KAAK,IAAI,iBAAiB,QAAQ,CAAC;IACjH,MAAM,OAAO,SAAS,MAAM,IAAI,IAAI,KAAK;IACzC,IAAI,IAAI,EAAE,EAAE;QACR,IAAI,CAAC,QAAQ,OAAO,KAAK,IAAI,KAAK,UAAU,MAAM,kBAAkB;YAChE,OAAO;gBACH,WAAW;YACf;QACJ;QACA,OAAO;YACH;QACJ;IACJ;IACA,IAAI,IAAI,MAAM,KAAK,KAAK;QACpB,OAAO;YACH,UAAU;QACd;IACJ;IACA,MAAM,IAAI,gBAAgB;QACtB,SAAS,OAAO,KAAK,KAAK,KAAK,WAAW,KAAK,KAAK,GAAG,CAAC,0BAA0B,EAAE,IAAI,QAAQ,EAAE,IAAI,MAAM,CAAC,EAAE,CAAC;QAChH,QAAQ,IAAI,MAAM;QAClB;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1688, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/hooks.js"], "sourcesContent": ["'use client';\nimport { useEffect, useState } from 'react';\nimport swr from 'swr';\nimport { TwitterApiError } from './api/index.js';\n// Avoids an error when used in the pages directory where useSWR might be in `default`.\nconst useSWR = swr.default || swr;\nconst host = 'https://react-tweet.vercel.app';\nasync function fetcher([url, fetchOptions]) {\n    const res = await fetch(url, fetchOptions);\n    const json = await res.json();\n    // We return null in case `json.data` is undefined, that way we can check for \"loading\" by\n    // checking if data is `undefined`. `null` means it was fetched.\n    if (res.ok) return json.data || null;\n    throw new TwitterApiError({\n        message: `Failed to fetch tweet at \"${url}\" with \"${res.status}\".`,\n        data: json,\n        status: res.status\n    });\n}\n/**\n * SWR hook for fetching a tweet in the browser.\n */ export const useTweet = (id, apiUrl, fetchOptions)=>{\n    const { isLoading, data, error } = useSWR(()=>apiUrl || id ? [\n            apiUrl || id && `${host}/api/tweet/${id}`,\n            fetchOptions\n        ] : null, fetcher, {\n        revalidateIfStale: false,\n        revalidateOnFocus: false,\n        shouldRetryOnError: false\n    });\n    return {\n        // If data is `undefined` then it might be the first render where SWR hasn't started doing\n        // any work, so we set `isLoading` to `true`.\n        isLoading: Boolean(isLoading || data === undefined && !error),\n        data,\n        error\n    };\n};\nexport const useMounted = ()=>{\n    const [mounted, setMounted] = useState(false);\n    useEffect(()=>setMounted(true), []);\n    return mounted;\n};\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;AAIA,uFAAuF;AACvF,MAAM,SAAS,uOAAA,CAAA,UAAG,CAAC,OAAO,IAAI,uOAAA,CAAA,UAAG;AACjC,MAAM,OAAO;AACb,eAAe,QAAQ,CAAC,KAAK,aAAa;IACtC,MAAM,MAAM,MAAM,MAAM,KAAK;IAC7B,MAAM,OAAO,MAAM,IAAI,IAAI;IAC3B,0FAA0F;IAC1F,gEAAgE;IAChE,IAAI,IAAI,EAAE,EAAE,OAAO,KAAK,IAAI,IAAI;IAChC,MAAM,IAAI,2QAAA,CAAA,kBAAe,CAAC;QACtB,SAAS,CAAC,0BAA0B,EAAE,IAAI,QAAQ,EAAE,IAAI,MAAM,CAAC,EAAE,CAAC;QAClE,MAAM;QACN,QAAQ,IAAI,MAAM;IACtB;AACJ;AAGW,MAAM,WAAW,CAAC,IAAI,QAAQ;IACrC,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG;2BAAO,IAAI,UAAU,KAAK;gBACrD,UAAU,MAAM,GAAG,KAAK,WAAW,EAAE,IAAI;gBACzC;aACH,GAAG;0BAAM,SAAS;QACnB,mBAAmB;QACnB,mBAAmB;QACnB,oBAAoB;IACxB;IACA,OAAO;QACH,0FAA0F;QAC1F,6CAA6C;QAC7C,WAAW,QAAQ,aAAa,SAAS,aAAa,CAAC;QACvD;QACA;IACJ;AACJ;AACO,MAAM,aAAa;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;gCAAE,IAAI,WAAW;+BAAO,EAAE;IAClC,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1746, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/node_modules/.pnpm/react-tweet%403.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/swr.js"], "sourcesContent": ["'use client';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { EmbeddedTweet, TweetNotFound, TweetSkeleton } from './twitter-theme/components.js';\nimport { useTweet } from './hooks.js';\nexport const Tweet = ({ id, apiUrl, fallback = /*#__PURE__*/ _jsx(TweetSkeleton, {}), components, fetchOptions, onError })=>{\n    const { data, error, isLoading } = useTweet(id, apiUrl, fetchOptions);\n    if (isLoading) return fallback;\n    if (error || !data) {\n        const NotFound = (components == null ? void 0 : components.TweetNotFound) || TweetNotFound;\n        return /*#__PURE__*/ _jsx(NotFound, {\n            error: onError ? onError(error) : error\n        });\n    }\n    return /*#__PURE__*/ _jsx(EmbeddedTweet, {\n        tweet: data,\n        components: components\n    });\n};\n"], "names": [], "mappings": ";;;AACA;AACA;AAAA;AAAA;AACA;AAHA;;;;AAIO,MAAM,QAAQ,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,WAAW,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,2RAAA,CAAA,gBAAa,EAAE,CAAC,EAAE,EAAE,UAAU,EAAE,YAAY,EAAE,OAAO,EAAE;IACrH,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,2PAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,QAAQ;IACxD,IAAI,WAAW,OAAO;IACtB,IAAI,SAAS,CAAC,MAAM;QAChB,MAAM,WAAW,CAAC,cAAc,OAAO,KAAK,IAAI,WAAW,aAAa,KAAK,+RAAA,CAAA,gBAAa;QAC1F,OAAO,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,UAAU;YAChC,OAAO,UAAU,QAAQ,SAAS;QACtC;IACJ;IACA,OAAO,WAAW,GAAG,CAAA,GAAA,+QAAA,CAAA,MAAI,AAAD,EAAE,2RAAA,CAAA,gBAAa,EAAE;QACrC,OAAO;QACP,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}]}