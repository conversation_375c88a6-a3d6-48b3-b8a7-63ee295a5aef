{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 20, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/components/magicui/number-ticker.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const NumberTicker = registerClientReference(\n    function() { throw new Error(\"Attempted to call NumberTicker() from the server but NumberTicker is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/magicui/number-ticker.tsx <module evaluation>\",\n    \"NumberTicker\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0EACA", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/components/magicui/number-ticker.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const NumberTicker = registerClientReference(\n    function() { throw new Error(\"Attempted to call NumberTicker() from the server but NumberTicker is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/magicui/number-ticker.tsx\",\n    \"NumberTicker\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,sDACA", "debugId": null}}, {"offset": {"line": 48, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/lib/utils.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AAE/B;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/components/ui/button.tsx"], "sourcesContent": ["import { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\nimport * as React from \"react\";\r\n\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  },\r\n);\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean;\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(\r\n        buttonVariants({ variant, size, className }),\r\n        \"cursor-pointer active:scale-105\",\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAGA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,uVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,eAAe;YAAE;YAAS;YAAM;QAAU,IAC1C;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/app/chat/components/site-header.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { StarFilledIcon, GitHubLogoIcon } from \"@radix-ui/react-icons\";\r\nimport Link from \"next/link\";\r\n\r\nimport { NumberTicker } from \"~/components/magicui/number-ticker\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport { env } from \"~/env\";\r\n\r\nexport async function SiteHeader() {\r\n  return (\r\n    <header className=\"supports-backdrop-blur:bg-background/80 bg-background/40 sticky top-0 left-0 z-40 flex h-15 w-full flex-col items-center backdrop-blur-lg\">\r\n      <div className=\"container flex h-15 items-center justify-between px-3\">\r\n        <div className=\"text-xl font-medium\">\r\n          <span className=\"mr-1 text-2xl\">🦌</span>\r\n          <span>DeerFlow</span>\r\n        </div>\r\n        <div className=\"relative flex items-center\">\r\n          <div\r\n            className=\"pointer-events-none absolute inset-0 z-0 h-full w-full rounded-full opacity-60 blur-2xl\"\r\n            style={{\r\n              background: \"linear-gradient(90deg, #ff80b5 0%, #9089fc 100%)\",\r\n              filter: \"blur(32px)\",\r\n            }}\r\n          />\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            asChild\r\n            className=\"group relative z-10\"\r\n          >\r\n            <Link href=\"https://github.com/bytedance/deer-flow\" target=\"_blank\">\r\n              <GitHubLogoIcon className=\"size-4\" />\r\n              Star on GitHub\r\n              {env.NEXT_PUBLIC_STATIC_WEBSITE_ONLY &&\r\n                env.GITHUB_OAUTH_TOKEN && <StarCounter />}\r\n            </Link>\r\n          </Button>\r\n        </div>\r\n      </div>\r\n      <hr className=\"from-border/0 via-border/70 to-border/0 m-0 h-px w-full border-none bg-gradient-to-r\" />\r\n    </header>\r\n  );\r\n}\r\n\r\nexport async function StarCounter() {\r\n  let stars = 1000; // Default value\r\n\r\n  try {\r\n    const response = await fetch(\r\n      \"https://api.github.com/repos/bytedance/deer-flow\",\r\n      {\r\n        headers: env.GITHUB_OAUTH_TOKEN\r\n          ? {\r\n              Authorization: `Bearer ${env.GITHUB_OAUTH_TOKEN}`,\r\n              \"Content-Type\": \"application/json\",\r\n            }\r\n          : {},\r\n        next: {\r\n          revalidate: 3600,\r\n        },\r\n      },\r\n    );\r\n\r\n    if (response.ok) {\r\n      const data = await response.json();\r\n      stars = data.stargazers_count ?? stars; // Update stars if API response is valid\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Error fetching GitHub stars:\", error);\r\n  }\r\n  return (\r\n    <>\r\n      <StarFilledIcon className=\"size-4 transition-colors duration-300 group-hover:text-yellow-500\" />\r\n      {stars && (\r\n        <NumberTicker className=\"font-mono tabular-nums\" value={stars} />\r\n      )}\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;;AAE/B;AACA;AAEA;AACA;AACA;;;;;;;AAEO,eAAe;IACpB,qBACE,uVAAC;QAAO,WAAU;;0BAChB,uVAAC;gBAAI,WAAU;;kCACb,uVAAC;wBAAI,WAAU;;0CACb,uVAAC;gCAAK,WAAU;0CAAgB;;;;;;0CAChC,uVAAC;0CAAK;;;;;;;;;;;;kCAER,uVAAC;wBAAI,WAAU;;0CACb,uVAAC;gCACC,WAAU;gCACV,OAAO;oCACL,YAAY;oCACZ,QAAQ;gCACV;;;;;;0CAEF,uVAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,OAAO;gCACP,WAAU;0CAEV,cAAA,uVAAC,qQAAA,CAAA,UAAI;oCAAC,MAAK;oCAAyC,QAAO;;sDACzD,uVAAC,kRAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;wCAAW;wCAEpC,0GAAA,CAAA,MAAG,CAAC,+BAA+B,IAClC,0GAAA,CAAA,MAAG,CAAC,kBAAkB,kBAAI,uVAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAKrC,uVAAC;gBAAG,WAAU;;;;;;;;;;;;AAGpB;AAEO,eAAe;IACpB,IAAI,QAAQ,MAAM,gBAAgB;IAElC,IAAI;QACF,MAAM,WAAW,MAAM,MACrB,oDACA;YACE,SAAS,0GAAA,CAAA,MAAG,CAAC,kBAAkB,GAC3B;gBACE,eAAe,CAAC,OAAO,EAAE,0GAAA,CAAA,MAAG,CAAC,kBAAkB,EAAE;gBACjD,gBAAgB;YAClB,IACA,CAAC;YACL,MAAM;gBACJ,YAAY;YACd;QACF;QAGF,IAAI,SAAS,EAAE,EAAE;YACf,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,KAAK,gBAAgB,IAAI,OAAO,wCAAwC;QAClF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;IAChD;IACA,qBACE;;0BACE,uVAAC,kRAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;YACzB,uBACC,uVAAC,iJAAA,CAAA,eAAY;gBAAC,WAAU;gBAAyB,OAAO;;;;;;;;AAIhE", "debugId": null}}, {"offset": {"line": 300, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/components/magicui/aurora-text.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuroraText = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraText() from the server but AuroraText is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/magicui/aurora-text.tsx <module evaluation>\",\n    \"AuroraText\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,wEACA", "debugId": null}}, {"offset": {"line": 314, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/components/magicui/aurora-text.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuroraText = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraText() from the server but AuroraText is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/magicui/aurora-text.tsx\",\n    \"AuroraText\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,oDACA", "debugId": null}}, {"offset": {"line": 328, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 338, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/components/magicui/flickering-grid.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const FlickeringGrid = registerClientReference(\n    function() { throw new Error(\"Attempted to call FlickeringGrid() from the server but FlickeringGrid is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/magicui/flickering-grid.tsx <module evaluation>\",\n    \"FlickeringGrid\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,4EACA", "debugId": null}}, {"offset": {"line": 352, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/components/magicui/flickering-grid.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const FlickeringGrid = registerClientReference(\n    function() { throw new Error(\"Attempted to call FlickeringGrid() from the server but FlickeringGrid is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/magicui/flickering-grid.tsx\",\n    \"FlickeringGrid\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,wDACA", "debugId": null}}, {"offset": {"line": 366, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 376, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/app/landing/components/jumbotron.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { GithubFilled } from \"@ant-design/icons\";\r\nimport { ChevronRight } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\n\r\nimport { AuroraText } from \"~/components/magicui/aurora-text\";\r\nimport { FlickeringGrid } from \"~/components/magicui/flickering-grid\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport { env } from \"~/env\";\r\n\r\nexport function Jumbotron() {\r\n  return (\r\n    <section className=\"flex h-[95vh] w-full flex-col items-center justify-center pb-15\">\r\n      <FlickeringGrid\r\n        id=\"deer-hero-bg\"\r\n        className={`absolute inset-0 z-0 [mask-image:radial-gradient(800px_circle_at_center,white,transparent)]`}\r\n        squareSize={4}\r\n        gridGap={4}\r\n        color=\"#60A5FA\"\r\n        maxOpacity={0.133}\r\n        flickerChance={0.1}\r\n      />\r\n      <FlickeringGrid\r\n        id=\"deer-hero\"\r\n        className=\"absolute inset-0 z-0 translate-y-[2vh] mask-[url(/images/deer-hero.svg)] mask-size-[100vw] mask-center mask-no-repeat md:mask-size-[72vh]\"\r\n        squareSize={3}\r\n        gridGap={6}\r\n        color=\"#60A5FA\"\r\n        maxOpacity={0.64}\r\n        flickerChance={0.12}\r\n      />\r\n      <div className=\"relative z-10 flex flex-col items-center justify-center gap-12\">\r\n        <h1 className=\"text-center text-4xl font-bold md:text-6xl\">\r\n          <span className=\"bg-gradient-to-r from-white via-gray-200 to-gray-400 bg-clip-text text-transparent\">\r\n            Deep Research{\" \"}\r\n          </span>\r\n          <AuroraText>at Your Fingertips</AuroraText>\r\n        </h1>\r\n        <p className=\"max-w-4xl p-2 text-center text-sm opacity-85 md:text-2xl\">\r\n          Meet DeerFlow, your personal Deep Research assistant. With powerful\r\n          tools like search engines, web crawlers, Python and MCP services, it\r\n          delivers instant insights, comprehensive reports, and even captivating\r\n          podcasts.\r\n        </p>\r\n        <div className=\"flex gap-6\">\r\n          <Button className=\"hidden text-lg md:flex md:w-42\" size=\"lg\" asChild>\r\n            <Link\r\n              target={\r\n                env.NEXT_PUBLIC_STATIC_WEBSITE_ONLY ? \"_blank\" : undefined\r\n              }\r\n              href={\r\n                env.NEXT_PUBLIC_STATIC_WEBSITE_ONLY\r\n                  ? \"https://github.com/bytedance/deer-flow\"\r\n                  : \"/chat\"\r\n              }\r\n            >\r\n              Get Started <ChevronRight />\r\n            </Link>\r\n          </Button>\r\n          {!env.NEXT_PUBLIC_STATIC_WEBSITE_ONLY && (\r\n            <Button\r\n              className=\"w-42 text-lg\"\r\n              size=\"lg\"\r\n              variant=\"outline\"\r\n              asChild\r\n            >\r\n              <Link\r\n                href=\"https://github.com/bytedance/deer-flow\"\r\n                target=\"_blank\"\r\n              >\r\n                <GithubFilled />\r\n                Learn More\r\n              </Link>\r\n            </Button>\r\n          )}\r\n        </div>\r\n      </div>\r\n      <div className=\"absolute bottom-8 flex text-xs opacity-50\">\r\n        <p>* DEER stands for Deep Exploration and Efficient Research.</p>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;AAEO,SAAS;IACd,qBACE,uVAAC;QAAQ,WAAU;;0BACjB,uVAAC,mJAAA,CAAA,iBAAc;gBACb,IAAG;gBACH,WAAW,CAAC,2FAA2F,CAAC;gBACxG,YAAY;gBACZ,SAAS;gBACT,OAAM;gBACN,YAAY;gBACZ,eAAe;;;;;;0BAEjB,uVAAC,mJAAA,CAAA,iBAAc;gBACb,IAAG;gBACH,WAAU;gBACV,YAAY;gBACZ,SAAS;gBACT,OAAM;gBACN,YAAY;gBACZ,eAAe;;;;;;0BAEjB,uVAAC;gBAAI,WAAU;;kCACb,uVAAC;wBAAG,WAAU;;0CACZ,uVAAC;gCAAK,WAAU;;oCAAqF;oCACrF;;;;;;;0CAEhB,uVAAC,+IAAA,CAAA,aAAU;0CAAC;;;;;;;;;;;;kCAEd,uVAAC;wBAAE,WAAU;kCAA2D;;;;;;kCAMxE,uVAAC;wBAAI,WAAU;;0CACb,uVAAC,kIAAA,CAAA,SAAM;gCAAC,WAAU;gCAAiC,MAAK;gCAAK,OAAO;0CAClE,cAAA,uVAAC,qQAAA,CAAA,UAAI;oCACH,QACE,0GAAA,CAAA,MAAG,CAAC,+BAA+B,GAAG,WAAW;oCAEnD,MACE,0GAAA,CAAA,MAAG,CAAC,+BAA+B,GAC/B,2CACA;;wCAEP;sDACa,uVAAC,0SAAA,CAAA,eAAY;;;;;;;;;;;;;;;;4BAG5B,CAAC,0GAAA,CAAA,MAAG,CAAC,+BAA+B,kBACnC,uVAAC,kIAAA,CAAA,SAAM;gCACL,WAAU;gCACV,MAAK;gCACL,SAAQ;gCACR,OAAO;0CAEP,cAAA,uVAAC,qQAAA,CAAA,UAAI;oCACH,MAAK;oCACL,QAAO;;sDAEP,uVAAC,4TAAA,CAAA,eAAY;;;;;wCAAG;;;;;;;;;;;;;;;;;;;;;;;;0BAO1B,uVAAC;gBAAI,WAAU;0BACb,cAAA,uVAAC;8BAAE;;;;;;;;;;;;;;;;;AAIX", "debugId": null}}, {"offset": {"line": 558, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/app/landing/components/ray.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nexport function Ray() {\r\n  return (\r\n    <svg\r\n      className=\"animate-spotlight pointer-events-none fixed -top-80 left-0 z-[99] h-[169%] w-[138%] opacity-0 md:-top-20 md:left-60 lg:w-[84%]\"\r\n      viewBox=\"0 0 3787 2842\"\r\n      fill=\"none\"\r\n    >\r\n      <g filter=\"url(#filter)\">\r\n        <ellipse\r\n          cx=\"1924.71\"\r\n          cy=\"273.501\"\r\n          rx=\"1924.71\"\r\n          ry=\"273.501\"\r\n          transform=\"matrix(-0.822377 -0.568943 -0.568943 0.822377 3631.88 2291.09)\"\r\n          fill=\"white\"\r\n          fillOpacity=\"0.21\"\r\n        ></ellipse>\r\n      </g>\r\n      <defs>\r\n        <filter\r\n          id=\"filter\"\r\n          x=\"0.860352\"\r\n          y=\"0.838989\"\r\n          width=\"3785.16\"\r\n          height=\"2840.26\"\r\n          filterUnits=\"userSpaceOnUse\"\r\n          colorInterpolationFilters=\"sRGB\"\r\n        >\r\n          <feFlood floodOpacity=\"0\" result=\"BackgroundImageFix\"></feFlood>\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in=\"SourceGraphic\"\r\n            in2=\"BackgroundImageFix\"\r\n            result=\"shape\"\r\n          ></feBlend>\r\n          <feGaussianBlur\r\n            stdDeviation=\"151\"\r\n            result=\"effect1_foregroundBlur_1065_8\"\r\n          ></feGaussianBlur>\r\n        </filter>\r\n      </defs>\r\n    </svg>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;;AAExB,SAAS;IACd,qBACE,uVAAC;QACC,WAAU;QACV,SAAQ;QACR,MAAK;;0BAEL,uVAAC;gBAAE,QAAO;0BACR,cAAA,uVAAC;oBACC,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,IAAG;oBACH,WAAU;oBACV,MAAK;oBACL,aAAY;;;;;;;;;;;0BAGhB,uVAAC;0BACC,cAAA,uVAAC;oBACC,IAAG;oBACH,GAAE;oBACF,GAAE;oBACF,OAAM;oBACN,QAAO;oBACP,aAAY;oBACZ,2BAA0B;;sCAE1B,uVAAC;4BAAQ,cAAa;4BAAI,QAAO;;;;;;sCACjC,uVAAC;4BACC,MAAK;4BACL,IAAG;4BACH,KAAI;4BACJ,QAAO;;;;;;sCAET,uVAAC;4BACC,cAAa;4BACb,QAAO;;;;;;;;;;;;;;;;;;;;;;;AAMnB", "debugId": null}}, {"offset": {"line": 651, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/components/magicui/bento-grid.tsx"], "sourcesContent": ["import { ArrowRightIcon } from \"@radix-ui/react-icons\";\r\nimport type { ComponentPropsWithoutRef, ReactNode } from \"react\";\r\n\r\nimport { Button } from \"~/components/ui/button\";\r\nimport { cn } from \"~/lib/utils\";\r\n\r\ninterface BentoGridProps extends ComponentPropsWithoutRef<\"div\"> {\r\n  children: ReactNode;\r\n  className?: string;\r\n}\r\n\r\ninterface BentoCardProps extends ComponentPropsWithoutRef<\"div\"> {\r\n  name: string;\r\n  className: string;\r\n  background?: ReactNode;\r\n  Icon: React.ElementType;\r\n  description: string;\r\n  href: string;\r\n  cta: string;\r\n}\r\n\r\nconst BentoGrid = ({ children, className, ...props }: BentoGridProps) => {\r\n  return (\r\n    <div\r\n      className={cn(\"grid w-full auto-rows-auto grid-cols-2 gap-4\", className)}\r\n      {...props}\r\n    >\r\n      {children}\r\n    </div>\r\n  );\r\n};\r\n\r\nconst BentoCard = ({\r\n  name,\r\n  className,\r\n  background,\r\n  Icon,\r\n  description,\r\n  href,\r\n  cta,\r\n  ...props\r\n}: BentoCardProps) => (\r\n  <div\r\n    key={name}\r\n    className={cn(\r\n      \"group relative col-span-3 flex flex-col justify-between overflow-hidden rounded-xl\",\r\n      // light styles\r\n      \"bg-background [box-shadow:0_0_0_1px_rgba(0,0,0,.03),0_2px_4px_rgba(0,0,0,.05),0_12px_24px_rgba(0,0,0,.05)]\",\r\n      // dark styles\r\n      \"dark:bg-background transform-gpu dark:[box-shadow:0_-20px_80px_-20px_#ffffff1f_inset] dark:[border:1px_solid_rgba(255,255,255,.1)]\",\r\n      className,\r\n    )}\r\n    {...props}\r\n  >\r\n    {background && <div>{background}</div>}\r\n    <a\r\n      className=\"z-10 flex transform-gpu flex-col gap-1 p-6 transition-all duration-300 group-hover:-translate-y-5\"\r\n      href={href}\r\n      target=\"_blank\"\r\n    >\r\n      <Icon className=\"h-12 w-12 origin-left transform-gpu text-neutral-700 transition-all duration-300 ease-in-out group-hover:scale-60\" />\r\n      <h3 className=\"text-xl font-semibold text-neutral-700 dark:text-neutral-300\">\r\n        {name}\r\n      </h3>\r\n      <p className=\"max-w-lg text-neutral-400\">{description}</p>\r\n    </a>\r\n\r\n    <div\r\n      className={cn(\r\n        \"pointer-events-none absolute bottom-0 flex w-full translate-y-10 transform-gpu flex-row items-center p-4 opacity-0 transition-all duration-300 group-hover:translate-y-0 group-hover:opacity-100\",\r\n      )}\r\n    >\r\n      <Button variant=\"ghost\" asChild size=\"sm\" className=\"pointer-events-auto\">\r\n        <a href={href}>\r\n          {cta}\r\n          <ArrowRightIcon className=\"ms-2 h-4 w-4 rtl:rotate-180\" />\r\n        </a>\r\n      </Button>\r\n    </div>\r\n    <div className=\"pointer-events-none absolute inset-0 transform-gpu transition-all duration-300 group-hover:bg-black/[.03] group-hover:dark:bg-neutral-800/10\" />\r\n  </div>\r\n);\r\n\r\nexport { BentoCard, BentoGrid };\r\n"], "names": [], "mappings": ";;;;;AAAA;AAGA;AACA;;;;;AAiBA,MAAM,YAAY,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAuB;IAClE,qBACE,uVAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;kBAER;;;;;;AAGP;AAEA,MAAM,YAAY,CAAC,EACjB,IAAI,EACJ,SAAS,EACT,UAAU,EACV,IAAI,EACJ,WAAW,EACX,IAAI,EACJ,GAAG,EACH,GAAG,OACY,iBACf,uVAAC;QAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sFACA,eAAe;QACf,8GACA,cAAc;QACd,sIACA;QAED,GAAG,KAAK;;YAER,4BAAc,uVAAC;0BAAK;;;;;;0BACrB,uVAAC;gBACC,WAAU;gBACV,MAAM;gBACN,QAAO;;kCAEP,uVAAC;wBAAK,WAAU;;;;;;kCAChB,uVAAC;wBAAG,WAAU;kCACX;;;;;;kCAEH,uVAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAG5C,uVAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;0BAGF,cAAA,uVAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,OAAO;oBAAC,MAAK;oBAAK,WAAU;8BAClD,cAAA,uVAAC;wBAAE,MAAM;;4BACN;0CACD,uVAAC,kRAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAIhC,uVAAC;gBAAI,WAAU;;;;;;;OApCV", "debugId": null}}, {"offset": {"line": 775, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/app/landing/components/section-header.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nexport function SectionHeader({\r\n  anchor,\r\n  title,\r\n  description,\r\n}: {\r\n  anchor?: string;\r\n  title: React.ReactNode;\r\n  description: React.ReactNode;\r\n}) {\r\n  return (\r\n    <>\r\n      {anchor && <a id={anchor} className=\"absolute -top-20\" />}\r\n      <div className=\"mb-12 flex flex-col items-center justify-center gap-2\">\r\n        <h2 className=\"mb-4 bg-gradient-to-r from-white via-gray-200 to-gray-400 bg-clip-text text-center text-5xl font-bold text-transparent\">\r\n          {title}\r\n        </h2>\r\n        <p className=\"text-muted-foreground text-center text-xl\">\r\n          {description}\r\n        </p>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;;AAExB,SAAS,cAAc,EAC5B,MAAM,EACN,KAAK,EACL,WAAW,EAKZ;IACC,qBACE;;YACG,wBAAU,uVAAC;gBAAE,IAAI;gBAAQ,WAAU;;;;;;0BACpC,uVAAC;gBAAI,WAAU;;kCACb,uVAAC;wBAAG,WAAU;kCACX;;;;;;kCAEH,uVAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 827, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/app/landing/sections/case-study-section.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { Bike, Building, Film, Github, Ham, Home, Pizza } from \"lucide-react\";\r\nimport { Bo<PERSON> } from \"lucide-react\";\r\n\r\nimport { BentoCard } from \"~/components/magicui/bento-grid\";\r\n\r\nimport { SectionHeader } from \"../components/section-header\";\r\n\r\nconst caseStudies = [\r\n  {\r\n    id: \"eiffel-tower-vs-tallest-building\",\r\n    icon: Building,\r\n    title: \"How tall is Eiffel Tower compared to tallest building?\",\r\n    description:\r\n      \"The research compares the heights and global significance of the Eiffel Tower and Burj Khalifa, and uses Python code to calculate the multiples.\",\r\n  },\r\n  {\r\n    id: \"github-top-trending-repo\",\r\n    icon: Github,\r\n    title: \"What are the top trending repositories on GitHub?\",\r\n    description:\r\n      \"The research utilized MCP services to identify the most popular GitHub repositories and documented them in detail using search engines.\",\r\n  },\r\n  {\r\n    id: \"nanjing-traditional-dishes\",\r\n    icon: Ham,\r\n    title: \"Write an article about Nanjing's traditional dishes\",\r\n    description:\r\n      \"The study vividly showcases Nanjing's famous dishes through rich content and imagery, uncovering their hidden histories and cultural significance.\",\r\n  },\r\n  {\r\n    id: \"rental-apartment-decoration\",\r\n    icon: Home,\r\n    title: \"How to decorate a small rental apartment?\",\r\n    description:\r\n      \"The study provides readers with practical and straightforward methods for decorating apartments, accompanied by inspiring images.\",\r\n  },\r\n  {\r\n    id: \"review-of-the-professional\",\r\n    icon: Film,\r\n    title: \"Introduce the movie 'Léon: The Professional'\",\r\n    description:\r\n      \"The research provides a comprehensive introduction to the movie 'Léon: The Professional', including its plot, characters, and themes.\",\r\n  },\r\n  {\r\n    id: \"china-food-delivery\",\r\n    icon: Bike,\r\n    title: \"How do you view the takeaway war in China? (in Chinese)\",\r\n    description:\r\n      \"The research analyzes the intensifying competition between JD and Meituan, highlighting their strategies, technological innovations, and challenges.\",\r\n  },\r\n  {\r\n    id: \"ultra-processed-foods\",\r\n    icon: Pizza,\r\n    title: \"Are ultra-processed foods linked to health?\",\r\n    description:\r\n      \"The research examines the health risks of rising ultra-processed food consumption, urging more research on long-term effects and individual differences.\",\r\n  },\r\n  {\r\n    id: \"ai-twin-insurance\",\r\n    icon: Bot,\r\n    title: 'Write an article on \"Would you insure your AI twin?\"',\r\n    description:\r\n      \"The research explores the concept of insuring AI twins, highlighting their benefits, risks, ethical considerations, and the evolving regulatory.\",\r\n  },\r\n];\r\n\r\nexport function CaseStudySection() {\r\n  return (\r\n    <section className=\"relative container hidden flex-col items-center justify-center md:flex\">\r\n      <SectionHeader\r\n        anchor=\"case-studies\"\r\n        title=\"Case Studies\"\r\n        description=\"See DeerFlow in action through replays.\"\r\n      />\r\n      <div className=\"grid w-3/4 grid-cols-1 gap-2 sm:w-full sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4\">\r\n        {caseStudies.map((caseStudy) => (\r\n          <div key={caseStudy.title} className=\"w-full p-2\">\r\n            <BentoCard\r\n              {...{\r\n                Icon: caseStudy.icon,\r\n                name: caseStudy.title,\r\n                description: caseStudy.description,\r\n                href: `/chat?replay=${caseStudy.id}`,\r\n                cta: \"Click to watch replay\",\r\n                className: \"w-full h-full\",\r\n              }}\r\n            />\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AAEA;;;;;;AAEA,MAAM,cAAc;IAClB;QACE,IAAI;QACJ,MAAM,8RAAA,CAAA,WAAQ;QACd,OAAO;QACP,aACE;IACJ;IACA;QACE,IAAI;QACJ,MAAM,0RAAA,CAAA,SAAM;QACZ,OAAO;QACP,aACE;IACJ;IACA;QACE,IAAI;QACJ,MAAM,oRAAA,CAAA,MAAG;QACT,OAAO;QACP,aACE;IACJ;IACA;QACE,IAAI;QACJ,MAAM,uRAAA,CAAA,OAAI;QACV,OAAO;QACP,aACE;IACJ;IACA;QACE,IAAI;QACJ,MAAM,sRAAA,CAAA,OAAI;QACV,OAAO;QACP,aACE;IACJ;IACA;QACE,IAAI;QACJ,MAAM,sRAAA,CAAA,OAAI;QACV,OAAO;QACP,aACE;IACJ;IACA;QACE,IAAI;QACJ,MAAM,wRAAA,CAAA,QAAK;QACX,OAAO;QACP,aACE;IACJ;IACA;QACE,IAAI;QACJ,MAAM,oRAAA,CAAA,MAAG;QACT,OAAO;QACP,aACE;IACJ;CACD;AAEM,SAAS;IACd,qBACE,uVAAC;QAAQ,WAAU;;0BACjB,uVAAC,yJAAA,CAAA,gBAAa;gBACZ,QAAO;gBACP,OAAM;gBACN,aAAY;;;;;;0BAEd,uVAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,0BAChB,uVAAC;wBAA0B,WAAU;kCACnC,cAAA,uVAAC,8IAAA,CAAA,YAAS;4BAEN,MAAM,UAAU,IAAI;4BACpB,MAAM,UAAU,KAAK;4BACrB,aAAa,UAAU,WAAW;4BAClC,MAAM,CAAC,aAAa,EAAE,UAAU,EAAE,EAAE;4BACpC,KAAK;4BACL,WAAW;;;;;;uBARP,UAAU,KAAK;;;;;;;;;;;;;;;;AAgBnC", "debugId": null}}, {"offset": {"line": 950, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/app/landing/sections/core-features-section.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { Bird, Microscope, Podcast, Usb, User } from \"lucide-react\";\r\n\r\nimport { BentoCard, BentoGrid } from \"~/components/magicui/bento-grid\";\r\n\r\nimport { <PERSON>Header } from \"../components/section-header\";\r\n\r\nconst features = [\r\n  {\r\n    Icon: Microscope,\r\n    name: \"Dive Deeper and Reach Wider\",\r\n    description:\r\n      \"Unlock deeper insights with advanced tools. Our powerful search + crawling and Python tools gathers comprehensive data, delivering in-depth reports to enhance your study.\",\r\n    href: \"https://github.com/bytedance/deer-flow/blob/main/src/tools\",\r\n    cta: \"Learn more\",\r\n    background: (\r\n      <img alt=\"background\" className=\"absolute -top-20 -right-20 opacity-60\" />\r\n    ),\r\n    className: \"lg:col-start-1 lg:col-end-2 lg:row-start-1 lg:row-end-3\",\r\n  },\r\n  {\r\n    Icon: User,\r\n    name: \"Human-in-the-loop\",\r\n    description:\r\n      \"Refine your research plan, or adjust focus areas all through simple natural language.\",\r\n    href: \"https://github.com/bytedance/deer-flow/blob/main/src/graph/nodes.py\",\r\n    cta: \"Learn more\",\r\n    background: (\r\n      <img alt=\"background\" className=\"absolute -top-20 -right-20 opacity-60\" />\r\n    ),\r\n    className: \"lg:col-start-1 lg:col-end-2 lg:row-start-3 lg:row-end-4\",\r\n  },\r\n  {\r\n    Icon: Bird,\r\n    name: \"Lang Stack\",\r\n    description:\r\n      \"Build with confidence using the LangChain and LangGraph frameworks.\",\r\n    href: \"https://www.langchain.com/\",\r\n    cta: \"Learn more\",\r\n    background: (\r\n      <img alt=\"background\" className=\"absolute -top-20 -right-20 opacity-60\" />\r\n    ),\r\n    className: \"lg:col-start-2 lg:col-end-3 lg:row-start-1 lg:row-end-2\",\r\n  },\r\n  {\r\n    Icon: Usb,\r\n    name: \"MCP Integrations\",\r\n    description:\r\n      \"Supercharge your research workflow and expand your toolkit with seamless MCP integrations.\",\r\n    href: \"https://github.com/bytedance/deer-flow/blob/main/src/graph/nodes.py\",\r\n    cta: \"Learn more\",\r\n    background: (\r\n      <img alt=\"background\" className=\"absolute -top-20 -right-20 opacity-60\" />\r\n    ),\r\n    className: \"lg:col-start-2 lg:col-end-3 lg:row-start-2 lg:row-end-3\",\r\n  },\r\n  {\r\n    Icon: Podcast,\r\n    name: \"Podcast Generation\",\r\n    description:\r\n      \"Instantly generate podcasts from reports. Perfect for on-the-go learning or sharing findings effortlessly.  \",\r\n    href: \"https://github.com/bytedance/deer-flow/blob/main/src/podcast\",\r\n    cta: \"Learn more\",\r\n    background: (\r\n      <img alt=\"background\" className=\"absolute -top-20 -right-20 opacity-60\" />\r\n    ),\r\n    className: \"lg:col-start-2 lg:col-end-3 lg:row-start-3 lg:row-end-4\",\r\n  },\r\n];\r\n\r\nexport function CoreFeatureSection() {\r\n  return (\r\n    <section className=\"relative flex w-full flex-col content-around items-center justify-center\">\r\n      <SectionHeader\r\n        anchor=\"core-features\"\r\n        title=\"Core Features\"\r\n        description=\"Find out what makes DeerFlow effective.\"\r\n      />\r\n      <BentoGrid className=\"w-3/4 lg:grid-cols-2 lg:grid-rows-3\">\r\n        {features.map((feature) => (\r\n          <BentoCard key={feature.name} {...feature} />\r\n        ))}\r\n      </BentoGrid>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAAA;AAAA;AAAA;AAAA;AAEA;AAEA;;;;;AAEA,MAAM,WAAW;IACf;QACE,MAAM,kSAAA,CAAA,aAAU;QAChB,MAAM;QACN,aACE;QACF,MAAM;QACN,KAAK;QACL,0BACE,uVAAC;YAAI,KAAI;YAAa,WAAU;;;;;;QAElC,WAAW;IACb;IACA;QACE,MAAM,sRAAA,CAAA,OAAI;QACV,MAAM;QACN,aACE;QACF,MAAM;QACN,KAAK;QACL,0BACE,uVAAC;YAAI,KAAI;YAAa,WAAU;;;;;;QAElC,WAAW;IACb;IACA;QACE,MAAM,sRAAA,CAAA,OAAI;QACV,MAAM;QACN,aACE;QACF,MAAM;QACN,KAAK;QACL,0BACE,uVAAC;YAAI,KAAI;YAAa,WAAU;;;;;;QAElC,WAAW;IACb;IACA;QACE,MAAM,oRAAA,CAAA,MAAG;QACT,MAAM;QACN,aACE;QACF,MAAM;QACN,KAAK;QACL,0BACE,uVAAC;YAAI,KAAI;YAAa,WAAU;;;;;;QAElC,WAAW;IACb;IACA;QACE,MAAM,4RAAA,CAAA,UAAO;QACb,MAAM;QACN,aACE;QACF,MAAM;QACN,KAAK;QACL,0BACE,uVAAC;YAAI,KAAI;YAAa,WAAU;;;;;;QAElC,WAAW;IACb;CACD;AAEM,SAAS;IACd,qBACE,uVAAC;QAAQ,WAAU;;0BACjB,uVAAC,yJAAA,CAAA,gBAAa;gBACZ,QAAO;gBACP,OAAM;gBACN,aAAY;;;;;;0BAEd,uVAAC,8IAAA,CAAA,YAAS;gBAAC,WAAU;0BAClB,SAAS,GAAG,CAAC,CAAC,wBACb,uVAAC,8IAAA,CAAA,YAAS;wBAAqB,GAAG,OAAO;uBAAzB,QAAQ,IAAI;;;;;;;;;;;;;;;;AAKtC", "debugId": null}}, {"offset": {"line": 1089, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/app/landing/sections/join-community-section.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { GithubFilled } from \"@ant-design/icons\";\r\nimport Link from \"next/link\";\r\n\r\nimport { AuroraText } from \"~/components/magicui/aurora-text\";\r\nimport { <PERSON><PERSON> } from \"~/components/ui/button\";\r\n\r\nimport { SectionHeader } from \"../components/section-header\";\r\n\r\nexport function JoinCommunitySection() {\r\n  return (\r\n    <section className=\"flex w-full flex-col items-center justify-center pb-12\">\r\n      <SectionHeader\r\n        anchor=\"join-community\"\r\n        title={\r\n          <AuroraText colors={[\"#60A5FA\", \"#A5FA60\", \"#A560FA\"]}>\r\n            Join the DeerFlow Community\r\n          </AuroraText>\r\n        }\r\n        description=\"Contribute brilliant ideas to shape the future of DeerFlow. Collaborate, innovate, and make impacts.\"\r\n      />\r\n      <Button className=\"text-xl\" size=\"lg\" asChild>\r\n        <Link href=\"https://github.com/bytedance/deer-flow\" target=\"_blank\">\r\n          <GithubFilled />\r\n          Contribute Now\r\n        </Link>\r\n      </Button>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AACA;AAEA;AACA;AAEA;;;;;;;AAEO,SAAS;IACd,qBACE,uVAAC;QAAQ,WAAU;;0BACjB,uVAAC,yJAAA,CAAA,gBAAa;gBACZ,QAAO;gBACP,qBACE,uVAAC,+IAAA,CAAA,aAAU;oBAAC,QAAQ;wBAAC;wBAAW;wBAAW;qBAAU;8BAAE;;;;;;gBAIzD,aAAY;;;;;;0BAEd,uVAAC,kIAAA,CAAA,SAAM;gBAAC,WAAU;gBAAU,MAAK;gBAAK,OAAO;0BAC3C,cAAA,uVAAC,qQAAA,CAAA,UAAI;oBAAC,MAAK;oBAAyC,QAAO;;sCACzD,uVAAC,4TAAA,CAAA,eAAY;;;;;wBAAG;;;;;;;;;;;;;;;;;;AAM1B", "debugId": null}}, {"offset": {"line": 1168, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/app/landing/components/multi-agent-visualization.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MultiAgentVisualization = registerClientReference(\n    function() { throw new Error(\"Attempted to call MultiAgentVisualization() from the server but MultiAgentVisualization is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/landing/components/multi-agent-visualization.tsx <module evaluation>\",\n    \"MultiAgentVisualization\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,0BAA0B,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACzD;IAAa,MAAM,IAAI,MAAM;AAA8P,GAC3R,0FACA", "debugId": null}}, {"offset": {"line": 1182, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/app/landing/components/multi-agent-visualization.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const MultiAgentVisualization = registerClientReference(\n    function() { throw new Error(\"Attempted to call MultiAgentVisualization() from the server but MultiAgentVisualization is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/landing/components/multi-agent-visualization.tsx\",\n    \"MultiAgentVisualization\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,0BAA0B,CAAA,GAAA,8VAAA,CAAA,0BAAuB,AAAD,EACzD;IAAa,MAAM,IAAI,MAAM;AAA8P,GAC3R,sEACA", "debugId": null}}, {"offset": {"line": 1196, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1206, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/app/landing/sections/multi-agent-section.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { MultiAgentVisualization } from \"../components/multi-agent-visualization\";\r\nimport { SectionHeader } from \"../components/section-header\";\r\n\r\nexport function MultiAgentSection() {\r\n  return (\r\n    <section className=\"relative flex w-full flex-col items-center justify-center\">\r\n      <SectionHeader\r\n        anchor=\"multi-agent-architecture\"\r\n        title=\"Multi-Agent Architecture\"\r\n        description=\"Experience the agent teamwork with our Supervisor + Handoffs design pattern.\"\r\n      />\r\n      <div className=\"flex h-[70vh] w-full flex-col items-center justify-center\">\r\n        <div className=\"h-full w-full\">\r\n          <MultiAgentVisualization />\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AACA;;;;AAEO,SAAS;IACd,qBACE,uVAAC;QAAQ,WAAU;;0BACjB,uVAAC,yJAAA,CAAA,gBAAa;gBACZ,QAAO;gBACP,OAAM;gBACN,aAAY;;;;;;0BAEd,uVAAC;gBAAI,WAAU;0BACb,cAAA,uVAAC;oBAAI,WAAU;8BACb,cAAA,uVAAC,uKAAA,CAAA,0BAAuB;;;;;;;;;;;;;;;;;;;;;AAKlC", "debugId": null}}, {"offset": {"line": 1262, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/repo/deer-flow/web/src/app/page.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { useMemo } from \"react\";\r\n\r\nimport { SiteHeader } from \"./chat/components/site-header\";\r\nimport { Jumbotron } from \"./landing/components/jumbotron\";\r\nimport { <PERSON> } from \"./landing/components/ray\";\r\nimport { CaseStudySection } from \"./landing/sections/case-study-section\";\r\nimport { CoreFeatureSection } from \"./landing/sections/core-features-section\";\r\nimport { JoinCommunitySection } from \"./landing/sections/join-community-section\";\r\nimport { MultiAgentSection } from \"./landing/sections/multi-agent-section\";\r\n\r\nexport default function HomePage() {\r\n  return (\r\n    <div className=\"flex flex-col items-center\">\r\n      <SiteHeader />\r\n      <main className=\"container flex flex-col items-center justify-center gap-56\">\r\n        <Jumbotron />\r\n        <CaseStudySection />\r\n        <MultiAgentSection />\r\n        <CoreFeatureSection />\r\n        <JoinCommunitySection />\r\n      </main>\r\n      <Footer />\r\n      <Ray />\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction Footer() {\r\n  const year = useMemo(() => new Date().getFullYear(), []);\r\n  return (\r\n    <footer className=\"container mt-32 flex flex-col items-center justify-center\">\r\n      <hr className=\"from-border/0 via-border/70 to-border/0 m-0 h-px w-full border-none bg-gradient-to-r\" />\r\n      <div className=\"text-muted-foreground container flex h-20 flex-col items-center justify-center text-sm\">\r\n        <p className=\"text-center font-serif text-lg md:text-xl\">\r\n          &quot;Originated from Open Source, give back to Open Source.&quot;\r\n        </p>\r\n      </div>\r\n      <div className=\"text-muted-foreground container mb-8 flex flex-col items-center justify-center text-xs\">\r\n        <p>Licensed under MIT License</p>\r\n        <p>&copy; {year} DeerFlow</p>\r\n      </div>\r\n    </footer>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEe,SAAS;IACtB,qBACE,uVAAC;QAAI,WAAU;;0BACb,uVAAC,mJAAA,CAAA,aAAU;;;;;0BACX,uVAAC;gBAAK,WAAU;;kCACd,uVAAC,iJAAA,CAAA,YAAS;;;;;kCACV,uVAAC,8JAAA,CAAA,mBAAgB;;;;;kCACjB,uVAAC,+JAAA,CAAA,oBAAiB;;;;;kCAClB,uVAAC,iKAAA,CAAA,qBAAkB;;;;;kCACnB,uVAAC,kKAAA,CAAA,uBAAoB;;;;;;;;;;;0BAEvB,uVAAC;;;;;0BACD,uVAAC,2IAAA,CAAA,MAAG;;;;;;;;;;;AAGV;AAEA,SAAS;IACP,MAAM,OAAO,CAAA,GAAA,8SAAA,CAAA,UAAO,AAAD,EAAE,IAAM,IAAI,OAAO,WAAW,IAAI,EAAE;IACvD,qBACE,uVAAC;QAAO,WAAU;;0BAChB,uVAAC;gBAAG,WAAU;;;;;;0BACd,uVAAC;gBAAI,WAAU;0BACb,cAAA,uVAAC;oBAAE,WAAU;8BAA4C;;;;;;;;;;;0BAI3D,uVAAC;gBAAI,WAAU;;kCACb,uVAAC;kCAAE;;;;;;kCACH,uVAAC;;4BAAE;4BAAQ;4BAAK;;;;;;;;;;;;;;;;;;;AAIxB", "debugId": null}}]}