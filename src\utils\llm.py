# Add Ollama support
from langchain_community.llms.ollama import Ollama
from langchain_core.language_models.chat_models import ChatOpenAI

def get_llm_by_type(llm_config):
    """Get LLM by type."""
    # Extract the type parameter before passing to any model
    model_type = llm_config.get("type")
    
    # Create a copy of the config without the type parameter
    config_without_type = {k: v for k, v in llm_config.items() if k != "type"}
    
    if model_type == "ollama":
        return Ollama(
            model=config_without_type.get("model"),
            base_url=config_without_type.get("base_url"),
        )
    
    # Default to OpenAI-compatible models
    return ChatOpenAI(**config_without_type)
