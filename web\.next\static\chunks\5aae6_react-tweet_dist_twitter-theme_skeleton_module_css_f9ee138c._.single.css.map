{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/skeleton.module.css"], "sourcesContent": [".skeleton {\n  display: block;\n  width: 100%;\n  border-radius: 5px;\n  background-image: var(--tweet-skeleton-gradient);\n  background-size: 400% 100%;\n  animation: loading 8s ease-in-out infinite;\n}\n\n@media (prefers-reduced-motion: reduce) {\n  .skeleton {\n    animation: none;\n    background-position: 200% 0;\n  }\n}\n\n@keyframes loading {\n  0% {\n    background-position: 200% 0;\n  }\n  100% {\n    background-position: -200% 0;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;AASA;EACE;;;;;;AAMF", "ignoreList": [0]}}]}