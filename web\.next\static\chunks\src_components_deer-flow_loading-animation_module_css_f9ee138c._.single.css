/* [project]/src/components/deer-flow/loading-animation.module.css [app-client] (css) */
@keyframes loading-animation-module__o3q3XG__bouncing-animation {
  to {
    opacity: .1;
    transform: translateY(-8px);
  }
}

.loading-animation-module__o3q3XG__loadingAnimation {
  display: flex;
}

.loading-animation-module__o3q3XG__loadingAnimation > div {
  opacity: 1;
  background-color: #a3a1a1;
  border-radius: 50%;
  width: 8px;
  height: 8px;
  margin: 2px 4px;
  animation: .5s infinite alternate loading-animation-module__o3q3XG__bouncing-animation;
}

.loading-animation-module__o3q3XG__loadingAnimation.loading-animation-module__o3q3XG__sm > div {
  width: 6px;
  height: 6px;
  margin: 1px 2px;
}

.loading-animation-module__o3q3XG__loadingAnimation > div:nth-child(2) {
  animation-delay: .2s;
}

.loading-animation-module__o3q3XG__loadingAnimation > div:nth-child(3) {
  animation-delay: .4s;
}

/*# sourceMappingURL=src_components_deer-flow_loading-animation_module_css_f9ee138c._.single.css.map*/