hoistPattern:
  - '*'
hoistedDependencies:
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@ant-design/colors@8.0.0':
    '@ant-design/colors': private
  '@ant-design/fast-color@3.0.0':
    '@ant-design/fast-color': private
  '@ant-design/icons-svg@4.4.2':
    '@ant-design/icons-svg': private
  '@babel/runtime@7.27.0':
    '@babel/runtime': private
  '@cfcs/core@0.0.6':
    '@cfcs/core': private
  '@daybrush/utils@1.13.0':
    '@daybrush/utils': private
  '@egjs/agent@2.4.4':
    '@egjs/agent': private
  '@egjs/children-differ@1.0.1':
    '@egjs/children-differ': private
  '@egjs/component@3.0.5':
    '@egjs/component': private
  '@egjs/list-differ@1.0.1':
    '@egjs/list-differ': private
  '@eslint-community/eslint-utils@4.6.1(eslint@9.24.0(jiti@2.4.2))':
    '@eslint-community/eslint-utils': public
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': public
  '@eslint/config-array@0.20.0':
    '@eslint/config-array': public
  '@eslint/config-helpers@0.2.1':
    '@eslint/config-helpers': public
  '@eslint/core@0.12.0':
    '@eslint/core': public
  '@eslint/js@9.24.0':
    '@eslint/js': public
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': public
  '@eslint/plugin-kit@0.2.8':
    '@eslint/plugin-kit': public
  '@floating-ui/core@1.6.9':
    '@floating-ui/core': private
  '@floating-ui/dom@1.6.13':
    '@floating-ui/dom': private
  '@floating-ui/react-dom@2.1.2(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@floating-ui/react-dom': private
  '@floating-ui/utils@0.2.9':
    '@floating-ui/utils': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.2':
    '@humanwhocodes/retry': private
  '@img/sharp-darwin-arm64@0.34.1':
    '@img/sharp-darwin-arm64': private
  '@img/sharp-darwin-x64@0.34.1':
    '@img/sharp-darwin-x64': private
  '@img/sharp-libvips-darwin-arm64@1.1.0':
    '@img/sharp-libvips-darwin-arm64': private
  '@img/sharp-libvips-darwin-x64@1.1.0':
    '@img/sharp-libvips-darwin-x64': private
  '@img/sharp-libvips-linux-arm64@1.1.0':
    '@img/sharp-libvips-linux-arm64': private
  '@img/sharp-libvips-linux-arm@1.1.0':
    '@img/sharp-libvips-linux-arm': private
  '@img/sharp-libvips-linux-ppc64@1.1.0':
    '@img/sharp-libvips-linux-ppc64': private
  '@img/sharp-libvips-linux-s390x@1.1.0':
    '@img/sharp-libvips-linux-s390x': private
  '@img/sharp-libvips-linux-x64@1.1.0':
    '@img/sharp-libvips-linux-x64': private
  '@img/sharp-libvips-linuxmusl-arm64@1.1.0':
    '@img/sharp-libvips-linuxmusl-arm64': private
  '@img/sharp-libvips-linuxmusl-x64@1.1.0':
    '@img/sharp-libvips-linuxmusl-x64': private
  '@img/sharp-linux-arm64@0.34.1':
    '@img/sharp-linux-arm64': private
  '@img/sharp-linux-arm@0.34.1':
    '@img/sharp-linux-arm': private
  '@img/sharp-linux-s390x@0.34.1':
    '@img/sharp-linux-s390x': private
  '@img/sharp-linux-x64@0.34.1':
    '@img/sharp-linux-x64': private
  '@img/sharp-linuxmusl-arm64@0.34.1':
    '@img/sharp-linuxmusl-arm64': private
  '@img/sharp-linuxmusl-x64@0.34.1':
    '@img/sharp-linuxmusl-x64': private
  '@img/sharp-wasm32@0.34.1':
    '@img/sharp-wasm32': private
  '@img/sharp-win32-ia32@0.34.1':
    '@img/sharp-win32-ia32': private
  '@img/sharp-win32-x64@0.34.1':
    '@img/sharp-win32-x64': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/source-map@0.3.6':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@next/env@15.3.0':
    '@next/env': private
  '@next/eslint-plugin-next@15.3.0':
    '@next/eslint-plugin-next': public
  '@next/swc-darwin-arm64@15.3.0':
    '@next/swc-darwin-arm64': private
  '@next/swc-darwin-x64@15.3.0':
    '@next/swc-darwin-x64': private
  '@next/swc-linux-arm64-gnu@15.3.0':
    '@next/swc-linux-arm64-gnu': private
  '@next/swc-linux-arm64-musl@15.3.0':
    '@next/swc-linux-arm64-musl': private
  '@next/swc-linux-x64-gnu@15.3.0':
    '@next/swc-linux-x64-gnu': private
  '@next/swc-linux-x64-musl@15.3.0':
    '@next/swc-linux-x64-musl': private
  '@next/swc-win32-arm64-msvc@15.3.0':
    '@next/swc-win32-arm64-msvc': private
  '@next/swc-win32-x64-msvc@15.3.0':
    '@next/swc-win32-x64-msvc': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@nolyfill/is-core-module@1.0.39':
    '@nolyfill/is-core-module': private
  '@opentelemetry/api@1.9.0':
    '@opentelemetry/api': private
  '@popperjs/core@2.11.8':
    '@popperjs/core': private
  '@radix-ui/number@1.1.1':
    '@radix-ui/number': private
  '@radix-ui/primitive@1.1.2':
    '@radix-ui/primitive': private
  '@radix-ui/react-arrow@1.1.3(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-arrow': private
  '@radix-ui/react-collection@1.1.4(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-collection': private
  '@radix-ui/react-compose-refs@1.1.2(@types/react@19.1.2)(react@19.1.0)':
    '@radix-ui/react-compose-refs': private
  '@radix-ui/react-context@1.1.2(@types/react@19.1.2)(react@19.1.0)':
    '@radix-ui/react-context': private
  '@radix-ui/react-direction@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    '@radix-ui/react-direction': private
  '@radix-ui/react-dismissable-layer@1.1.7(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-dismissable-layer': private
  '@radix-ui/react-focus-guards@1.1.2(@types/react@19.1.2)(react@19.1.0)':
    '@radix-ui/react-focus-guards': private
  '@radix-ui/react-focus-scope@1.1.4(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-focus-scope': private
  '@radix-ui/react-id@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    '@radix-ui/react-id': private
  '@radix-ui/react-menu@2.1.11(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-menu': private
  '@radix-ui/react-popper@1.2.4(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-popper': private
  '@radix-ui/react-portal@1.1.6(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-portal': private
  '@radix-ui/react-presence@1.1.4(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-presence': private
  '@radix-ui/react-primitive@2.1.0(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-primitive': private
  '@radix-ui/react-roving-focus@1.1.3(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-roving-focus': private
  '@radix-ui/react-use-callback-ref@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    '@radix-ui/react-use-callback-ref': private
  '@radix-ui/react-use-controllable-state@1.2.2(@types/react@19.1.2)(react@19.1.0)':
    '@radix-ui/react-use-controllable-state': private
  '@radix-ui/react-use-effect-event@0.0.2(@types/react@19.1.2)(react@19.1.0)':
    '@radix-ui/react-use-effect-event': private
  '@radix-ui/react-use-escape-keydown@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    '@radix-ui/react-use-escape-keydown': private
  '@radix-ui/react-use-layout-effect@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    '@radix-ui/react-use-layout-effect': private
  '@radix-ui/react-use-previous@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    '@radix-ui/react-use-previous': private
  '@radix-ui/react-use-rect@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    '@radix-ui/react-use-rect': private
  '@radix-ui/react-use-size@1.1.1(@types/react@19.1.2)(react@19.1.0)':
    '@radix-ui/react-use-size': private
  '@radix-ui/react-visually-hidden@1.2.0(@types/react-dom@19.1.1(@types/react@19.1.2))(@types/react@19.1.2)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@radix-ui/react-visually-hidden': private
  '@radix-ui/rect@1.1.1':
    '@radix-ui/rect': private
  '@rc-component/util@1.2.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    '@rc-component/util': private
  '@remirror/core-constants@3.0.0':
    '@remirror/core-constants': private
  '@rtsao/scc@1.1.0':
    '@rtsao/scc': private
  '@rushstack/eslint-patch@1.11.0':
    '@rushstack/eslint-patch': public
  '@scena/dragscroll@1.4.0':
    '@scena/dragscroll': private
  '@scena/event-emitter@1.0.5':
    '@scena/event-emitter': private
  '@scena/matrix@1.1.1':
    '@scena/matrix': private
  '@standard-schema/utils@0.3.0':
    '@standard-schema/utils': private
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/helpers@0.5.15':
    '@swc/helpers': private
  '@t3-oss/env-core@0.11.1(typescript@5.8.3)(zod@3.24.3)':
    '@t3-oss/env-core': private
  '@tailwindcss/node@4.1.4':
    '@tailwindcss/node': private
  '@tailwindcss/oxide-android-arm64@4.1.4':
    '@tailwindcss/oxide-android-arm64': private
  '@tailwindcss/oxide-darwin-arm64@4.1.4':
    '@tailwindcss/oxide-darwin-arm64': private
  '@tailwindcss/oxide-darwin-x64@4.1.4':
    '@tailwindcss/oxide-darwin-x64': private
  '@tailwindcss/oxide-freebsd-x64@4.1.4':
    '@tailwindcss/oxide-freebsd-x64': private
  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.4':
    '@tailwindcss/oxide-linux-arm-gnueabihf': private
  '@tailwindcss/oxide-linux-arm64-gnu@4.1.4':
    '@tailwindcss/oxide-linux-arm64-gnu': private
  '@tailwindcss/oxide-linux-arm64-musl@4.1.4':
    '@tailwindcss/oxide-linux-arm64-musl': private
  '@tailwindcss/oxide-linux-x64-gnu@4.1.4':
    '@tailwindcss/oxide-linux-x64-gnu': private
  '@tailwindcss/oxide-linux-x64-musl@4.1.4':
    '@tailwindcss/oxide-linux-x64-musl': private
  '@tailwindcss/oxide-wasm32-wasi@4.1.4':
    '@tailwindcss/oxide-wasm32-wasi': private
  '@tailwindcss/oxide-win32-arm64-msvc@4.1.4':
    '@tailwindcss/oxide-win32-arm64-msvc': private
  '@tailwindcss/oxide-win32-x64-msvc@4.1.4':
    '@tailwindcss/oxide-win32-x64-msvc': private
  '@tailwindcss/oxide@4.1.4':
    '@tailwindcss/oxide': private
  '@tiptap/core@2.11.7(@tiptap/pm@2.11.7)':
    '@tiptap/core': private
  '@tiptap/extension-blockquote@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))':
    '@tiptap/extension-blockquote': private
  '@tiptap/extension-bold@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))':
    '@tiptap/extension-bold': private
  '@tiptap/extension-bubble-menu@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))(@tiptap/pm@2.11.7)':
    '@tiptap/extension-bubble-menu': private
  '@tiptap/extension-bullet-list@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))':
    '@tiptap/extension-bullet-list': private
  '@tiptap/extension-character-count@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))(@tiptap/pm@2.11.7)':
    '@tiptap/extension-character-count': private
  '@tiptap/extension-code-block-lowlight@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))(@tiptap/extension-code-block@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))(@tiptap/pm@2.11.7))(@tiptap/pm@2.11.7)(highlight.js@11.11.1)(lowlight@3.3.0)':
    '@tiptap/extension-code-block-lowlight': private
  '@tiptap/extension-code-block@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))(@tiptap/pm@2.11.7)':
    '@tiptap/extension-code-block': private
  '@tiptap/extension-code@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))':
    '@tiptap/extension-code': private
  '@tiptap/extension-color@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))(@tiptap/extension-text-style@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7)))':
    '@tiptap/extension-color': private
  '@tiptap/extension-document@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))':
    '@tiptap/extension-document': private
  '@tiptap/extension-dropcursor@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))(@tiptap/pm@2.11.7)':
    '@tiptap/extension-dropcursor': private
  '@tiptap/extension-floating-menu@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))(@tiptap/pm@2.11.7)':
    '@tiptap/extension-floating-menu': private
  '@tiptap/extension-gapcursor@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))(@tiptap/pm@2.11.7)':
    '@tiptap/extension-gapcursor': private
  '@tiptap/extension-hard-break@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))':
    '@tiptap/extension-hard-break': private
  '@tiptap/extension-heading@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))':
    '@tiptap/extension-heading': private
  '@tiptap/extension-highlight@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))':
    '@tiptap/extension-highlight': private
  '@tiptap/extension-history@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))(@tiptap/pm@2.11.7)':
    '@tiptap/extension-history': private
  '@tiptap/extension-horizontal-rule@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))(@tiptap/pm@2.11.7)':
    '@tiptap/extension-horizontal-rule': private
  '@tiptap/extension-image@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))':
    '@tiptap/extension-image': private
  '@tiptap/extension-italic@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))':
    '@tiptap/extension-italic': private
  '@tiptap/extension-link@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))(@tiptap/pm@2.11.7)':
    '@tiptap/extension-link': private
  '@tiptap/extension-list-item@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))':
    '@tiptap/extension-list-item': private
  '@tiptap/extension-ordered-list@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))':
    '@tiptap/extension-ordered-list': private
  '@tiptap/extension-paragraph@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))':
    '@tiptap/extension-paragraph': private
  '@tiptap/extension-placeholder@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))(@tiptap/pm@2.11.7)':
    '@tiptap/extension-placeholder': private
  '@tiptap/extension-strike@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))':
    '@tiptap/extension-strike': private
  '@tiptap/extension-task-item@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))(@tiptap/pm@2.11.7)':
    '@tiptap/extension-task-item': private
  '@tiptap/extension-task-list@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))':
    '@tiptap/extension-task-list': private
  '@tiptap/extension-text-style@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))':
    '@tiptap/extension-text-style': private
  '@tiptap/extension-text@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))':
    '@tiptap/extension-text': private
  '@tiptap/extension-underline@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))':
    '@tiptap/extension-underline': private
  '@tiptap/extension-youtube@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))':
    '@tiptap/extension-youtube': private
  '@tiptap/pm@2.11.7':
    '@tiptap/pm': private
  '@tiptap/starter-kit@2.11.7':
    '@tiptap/starter-kit': private
  '@tiptap/suggestion@2.11.7(@tiptap/core@2.11.7(@tiptap/pm@2.11.7))(@tiptap/pm@2.11.7)':
    '@tiptap/suggestion': private
  '@types/d3-color@3.1.3':
    '@types/d3-color': private
  '@types/d3-drag@3.0.7':
    '@types/d3-drag': private
  '@types/d3-interpolate@3.0.4':
    '@types/d3-interpolate': private
  '@types/d3-selection@3.0.11':
    '@types/d3-selection': private
  '@types/d3-transition@3.0.9':
    '@types/d3-transition': private
  '@types/d3-zoom@3.0.8':
    '@types/d3-zoom': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/eslint-scope@3.7.7':
    '@types/eslint-scope': public
  '@types/eslint@9.6.1':
    '@types/eslint': public
  '@types/estree-jsx@1.0.5':
    '@types/estree-jsx': private
  '@types/estree@1.0.7':
    '@types/estree': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/json5@0.0.29':
    '@types/json5': private
  '@types/katex@0.16.7':
    '@types/katex': private
  '@types/linkify-it@3.0.5':
    '@types/linkify-it': private
  '@types/markdown-it@13.0.9':
    '@types/markdown-it': private
  '@types/mdast@4.0.4':
    '@types/mdast': private
  '@types/mdurl@1.0.5':
    '@types/mdurl': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/unist@3.0.3':
    '@types/unist': private
  '@types/use-sync-external-store@0.0.6':
    '@types/use-sync-external-store': private
  '@typescript-eslint/eslint-plugin@8.30.1(@typescript-eslint/parser@8.30.1(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/eslint-plugin': public
  '@typescript-eslint/parser@8.30.1(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/parser': public
  '@typescript-eslint/scope-manager@8.30.1':
    '@typescript-eslint/scope-manager': public
  '@typescript-eslint/type-utils@8.30.1(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/type-utils': public
  '@typescript-eslint/types@8.30.1':
    '@typescript-eslint/types': public
  '@typescript-eslint/typescript-estree@8.30.1(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': public
  '@typescript-eslint/utils@8.30.1(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/utils': public
  '@typescript-eslint/visitor-keys@8.30.1':
    '@typescript-eslint/visitor-keys': public
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@unrs/resolver-binding-darwin-arm64@1.5.0':
    '@unrs/resolver-binding-darwin-arm64': private
  '@unrs/resolver-binding-darwin-x64@1.5.0':
    '@unrs/resolver-binding-darwin-x64': private
  '@unrs/resolver-binding-freebsd-x64@1.5.0':
    '@unrs/resolver-binding-freebsd-x64': private
  '@unrs/resolver-binding-linux-arm-gnueabihf@1.5.0':
    '@unrs/resolver-binding-linux-arm-gnueabihf': private
  '@unrs/resolver-binding-linux-arm-musleabihf@1.5.0':
    '@unrs/resolver-binding-linux-arm-musleabihf': private
  '@unrs/resolver-binding-linux-arm64-gnu@1.5.0':
    '@unrs/resolver-binding-linux-arm64-gnu': private
  '@unrs/resolver-binding-linux-arm64-musl@1.5.0':
    '@unrs/resolver-binding-linux-arm64-musl': private
  '@unrs/resolver-binding-linux-ppc64-gnu@1.5.0':
    '@unrs/resolver-binding-linux-ppc64-gnu': private
  '@unrs/resolver-binding-linux-riscv64-gnu@1.5.0':
    '@unrs/resolver-binding-linux-riscv64-gnu': private
  '@unrs/resolver-binding-linux-s390x-gnu@1.5.0':
    '@unrs/resolver-binding-linux-s390x-gnu': private
  '@unrs/resolver-binding-linux-x64-gnu@1.5.0':
    '@unrs/resolver-binding-linux-x64-gnu': private
  '@unrs/resolver-binding-linux-x64-musl@1.5.0':
    '@unrs/resolver-binding-linux-x64-musl': private
  '@unrs/resolver-binding-wasm32-wasi@1.5.0':
    '@unrs/resolver-binding-wasm32-wasi': private
  '@unrs/resolver-binding-win32-arm64-msvc@1.5.0':
    '@unrs/resolver-binding-win32-arm64-msvc': private
  '@unrs/resolver-binding-win32-ia32-msvc@1.5.0':
    '@unrs/resolver-binding-win32-ia32-msvc': private
  '@unrs/resolver-binding-win32-x64-msvc@1.5.0':
    '@unrs/resolver-binding-win32-x64-msvc': private
  '@webassemblyjs/ast@1.14.1':
    '@webassemblyjs/ast': private
  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    '@webassemblyjs/floating-point-hex-parser': private
  '@webassemblyjs/helper-api-error@1.13.2':
    '@webassemblyjs/helper-api-error': private
  '@webassemblyjs/helper-buffer@1.14.1':
    '@webassemblyjs/helper-buffer': private
  '@webassemblyjs/helper-numbers@1.13.2':
    '@webassemblyjs/helper-numbers': private
  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    '@webassemblyjs/helper-wasm-bytecode': private
  '@webassemblyjs/helper-wasm-section@1.14.1':
    '@webassemblyjs/helper-wasm-section': private
  '@webassemblyjs/ieee754@1.13.2':
    '@webassemblyjs/ieee754': private
  '@webassemblyjs/leb128@1.13.2':
    '@webassemblyjs/leb128': private
  '@webassemblyjs/utf8@1.13.2':
    '@webassemblyjs/utf8': private
  '@webassemblyjs/wasm-edit@1.14.1':
    '@webassemblyjs/wasm-edit': private
  '@webassemblyjs/wasm-gen@1.14.1':
    '@webassemblyjs/wasm-gen': private
  '@webassemblyjs/wasm-opt@1.14.1':
    '@webassemblyjs/wasm-opt': private
  '@webassemblyjs/wasm-parser@1.14.1':
    '@webassemblyjs/wasm-parser': private
  '@webassemblyjs/wast-printer@1.14.1':
    '@webassemblyjs/wast-printer': private
  '@xtuc/ieee754@1.2.0':
    '@xtuc/ieee754': private
  '@xtuc/long@4.2.2':
    '@xtuc/long': private
  '@xyflow/system@0.0.57':
    '@xyflow/system': private
  acorn-jsx@5.3.2(acorn@8.14.1):
    acorn-jsx: private
  acorn@8.14.1:
    acorn: private
  ajv-formats@2.1.1(ajv@8.17.1):
    ajv-formats: private
  ajv-keywords@3.5.2(ajv@6.12.6):
    ajv-keywords: private
  ajv@6.12.6:
    ajv: private
  ansi-styles@4.3.0:
    ansi-styles: private
  argparse@2.0.1:
    argparse: private
  aria-hidden@1.2.4:
    aria-hidden: private
  aria-query@5.3.2:
    aria-query: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-includes@3.1.8:
    array-includes: private
  array.prototype.findlast@1.2.5:
    array.prototype.findlast: private
  array.prototype.findlastindex@1.2.6:
    array.prototype.findlastindex: private
  array.prototype.flat@1.3.3:
    array.prototype.flat: private
  array.prototype.flatmap@1.3.3:
    array.prototype.flatmap: private
  array.prototype.tosorted@1.1.4:
    array.prototype.tosorted: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  ast-types-flow@0.0.8:
    ast-types-flow: private
  async-function@1.0.0:
    async-function: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  axe-core@4.10.3:
    axe-core: private
  axobject-query@4.1.0:
    axobject-query: private
  bail@2.0.2:
    bail: private
  balanced-match@1.0.2:
    balanced-match: private
  big.js@5.2.2:
    big.js: private
  brace-expansion@1.1.11:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.24.4:
    browserslist: private
  buffer-from@1.1.2:
    buffer-from: private
  busboy@1.6.0:
    busboy: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  caniuse-lite@1.0.30001714:
    caniuse-lite: private
  ccount@2.0.1:
    ccount: private
  chalk@4.1.2:
    chalk: private
  character-entities-html4@2.1.0:
    character-entities-html4: private
  character-entities-legacy@1.1.4:
    character-entities-legacy: private
  character-entities@1.2.4:
    character-entities: private
  character-reference-invalid@1.1.4:
    character-reference-invalid: private
  chrome-trace-event@1.0.4:
    chrome-trace-event: private
  classcat@5.0.5:
    classcat: private
  classnames@2.5.1:
    classnames: private
  client-only@0.0.1:
    client-only: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  comma-separated-tokens@2.0.3:
    comma-separated-tokens: private
  commander@8.3.0:
    commander: private
  concat-map@0.0.1:
    concat-map: private
  crelt@1.0.6:
    crelt: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-styled@1.0.8:
    css-styled: private
  css-to-mat@1.1.1:
    css-to-mat: private
  cssesc@3.0.0:
    cssesc: private
  csstype@3.1.3:
    csstype: private
  d3-color@3.1.0:
    d3-color: private
  d3-dispatch@3.0.1:
    d3-dispatch: private
  d3-drag@3.0.0:
    d3-drag: private
  d3-ease@3.0.1:
    d3-ease: private
  d3-interpolate@3.0.1:
    d3-interpolate: private
  d3-selection@3.0.0:
    d3-selection: private
  d3-timer@3.0.1:
    d3-timer: private
  d3-transition@3.0.1(d3-selection@3.0.0):
    d3-transition: private
  d3-zoom@3.0.0:
    d3-zoom: private
  damerau-levenshtein@1.0.8:
    damerau-levenshtein: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  debug@4.4.0:
    debug: private
  decode-named-character-reference@1.1.0:
    decode-named-character-reference: private
  deep-is@0.1.4:
    deep-is: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  dequal@2.0.3:
    dequal: private
  detect-libc@2.0.3:
    detect-libc: private
  detect-node-es@1.1.0:
    detect-node-es: private
  devlop@1.1.0:
    devlop: private
  doctrine@2.1.0:
    doctrine: private
  dunder-proto@1.0.1:
    dunder-proto: private
  electron-to-chromium@1.5.140:
    electron-to-chromium: private
  emoji-regex@9.2.2:
    emoji-regex: private
  emojis-list@3.0.0:
    emojis-list: private
  enhanced-resolve@5.18.1:
    enhanced-resolve: private
  entities@4.5.0:
    entities: private
  es-abstract@1.23.9:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-iterator-helpers@1.2.1:
    es-iterator-helpers: private
  es-module-lexer@1.6.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-shim-unscopables@1.1.0:
    es-shim-unscopables: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  escalade@3.2.0:
    escalade: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-import-resolver-node@0.3.9:
    eslint-import-resolver-node: public
  eslint-import-resolver-typescript@3.10.0(eslint-plugin-import@2.31.0)(eslint@9.24.0(jiti@2.4.2)):
    eslint-import-resolver-typescript: public
  eslint-module-utils@2.12.0(@typescript-eslint/parser@8.30.1(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.10.0)(eslint@9.24.0(jiti@2.4.2)):
    eslint-module-utils: public
  eslint-plugin-import@2.31.0(@typescript-eslint/parser@8.30.1(eslint@9.24.0(jiti@2.4.2))(typescript@5.8.3))(eslint-import-resolver-typescript@3.10.0)(eslint@9.24.0(jiti@2.4.2)):
    eslint-plugin-import: public
  eslint-plugin-jsx-a11y@6.10.2(eslint@9.24.0(jiti@2.4.2)):
    eslint-plugin-jsx-a11y: public
  eslint-plugin-react-hooks@5.2.0(eslint@9.24.0(jiti@2.4.2)):
    eslint-plugin-react-hooks: public
  eslint-plugin-react@7.37.5(eslint@9.24.0(jiti@2.4.2)):
    eslint-plugin-react: public
  eslint-scope@8.3.0:
    eslint-scope: public
  eslint-visitor-keys@4.2.0:
    eslint-visitor-keys: public
  espree@10.3.0:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-util-is-identifier-name@3.0.0:
    estree-util-is-identifier-name: private
  esutils@2.0.3:
    esutils: private
  events@3.3.0:
    events: private
  extend@3.0.2:
    extend: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-glob@3.3.1:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-uri@3.0.6:
    fast-uri: private
  fastq@1.19.1:
    fastq: private
  fault@1.0.4:
    fault: private
  fdir@6.4.3(picomatch@4.0.2):
    fdir: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  for-each@0.3.5:
    for-each: private
  format@0.2.2:
    format: private
  framework-utils@1.1.0:
    framework-utils: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  gesto@1.19.4:
    gesto: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-nonce@1.0.1:
    get-nonce: private
  get-proto@1.0.1:
    get-proto: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  get-tsconfig@4.10.0:
    get-tsconfig: private
  glob-parent@6.0.2:
    glob-parent: private
  glob-to-regexp@0.4.1:
    glob-to-regexp: private
  globals@14.0.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  hast-util-from-dom@5.0.1:
    hast-util-from-dom: private
  hast-util-from-html-isomorphic@2.0.0:
    hast-util-from-html-isomorphic: private
  hast-util-from-html@2.0.3:
    hast-util-from-html: private
  hast-util-from-parse5@8.0.3:
    hast-util-from-parse5: private
  hast-util-is-element@3.0.0:
    hast-util-is-element: private
  hast-util-parse-selector@2.2.5:
    hast-util-parse-selector: private
  hast-util-to-jsx-runtime@2.3.6:
    hast-util-to-jsx-runtime: private
  hast-util-to-text@4.0.2:
    hast-util-to-text: private
  hast-util-whitespace@3.0.0:
    hast-util-whitespace: private
  hastscript@6.0.0:
    hastscript: private
  highlightjs-vue@1.0.0:
    highlightjs-vue: private
  html-url-attributes@3.0.1:
    html-url-attributes: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inline-style-parser@0.2.4:
    inline-style-parser: private
  internal-slot@1.1.0:
    internal-slot: private
  is-alphabetical@1.0.4:
    is-alphabetical: private
  is-alphanumerical@1.0.4:
    is-alphanumerical: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-bun-module@2.0.0:
    is-bun-module: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-decimal@1.0.4:
    is-decimal: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-hexadecimal@1.0.4:
    is-hexadecimal: private
  is-map@2.0.3:
    is-map: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-plain-obj@4.1.0:
    is-plain-obj: private
  is-regex@1.2.1:
    is-regex: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  isarray@2.0.5:
    isarray: private
  isexe@2.0.0:
    isexe: private
  iterator.prototype@1.1.5:
    iterator.prototype: private
  jest-worker@27.5.1:
    jest-worker: private
  jiti@2.4.2:
    jiti: private
  jotai@2.12.3(@types/react@19.1.2)(react@19.1.0):
    jotai: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@2.2.3:
    json5: private
  jsx-ast-utils@3.3.5:
    jsx-ast-utils: private
  keycode@2.2.1:
    keycode: private
  keycon@1.4.0:
    keycon: private
  keyv@4.5.4:
    keyv: private
  language-subtag-registry@0.3.23:
    language-subtag-registry: private
  language-tags@1.0.9:
    language-tags: private
  levn@0.4.1:
    levn: private
  lightningcss-darwin-arm64@1.29.2:
    lightningcss-darwin-arm64: private
  lightningcss-darwin-x64@1.29.2:
    lightningcss-darwin-x64: private
  lightningcss-freebsd-x64@1.29.2:
    lightningcss-freebsd-x64: private
  lightningcss-linux-arm-gnueabihf@1.29.2:
    lightningcss-linux-arm-gnueabihf: private
  lightningcss-linux-arm64-gnu@1.29.2:
    lightningcss-linux-arm64-gnu: private
  lightningcss-linux-arm64-musl@1.29.2:
    lightningcss-linux-arm64-musl: private
  lightningcss-linux-x64-gnu@1.29.2:
    lightningcss-linux-x64-gnu: private
  lightningcss-linux-x64-musl@1.29.2:
    lightningcss-linux-x64-musl: private
  lightningcss-win32-arm64-msvc@1.29.2:
    lightningcss-win32-arm64-msvc: private
  lightningcss-win32-x64-msvc@1.29.2:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.29.2:
    lightningcss: private
  linkify-it@5.0.0:
    linkify-it: private
  linkifyjs@4.2.0:
    linkifyjs: private
  loader-runner@4.3.0:
    loader-runner: private
  loader-utils@2.0.4:
    loader-utils: private
  locate-path@6.0.0:
    locate-path: private
  lodash.castarray@4.4.0:
    lodash.castarray: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.merge@4.6.2:
    lodash.merge: private
  longest-streak@3.1.0:
    longest-streak: private
  loose-envify@1.4.0:
    loose-envify: private
  markdown-it-task-lists@2.1.1:
    markdown-it-task-lists: private
  markdown-it@14.1.0:
    markdown-it: private
  markdown-table@3.0.4:
    markdown-table: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mdast-util-find-and-replace@3.0.2:
    mdast-util-find-and-replace: private
  mdast-util-from-markdown@2.0.2:
    mdast-util-from-markdown: private
  mdast-util-gfm-autolink-literal@2.0.1:
    mdast-util-gfm-autolink-literal: private
  mdast-util-gfm-footnote@2.1.0:
    mdast-util-gfm-footnote: private
  mdast-util-gfm-strikethrough@2.0.0:
    mdast-util-gfm-strikethrough: private
  mdast-util-gfm-table@2.0.0:
    mdast-util-gfm-table: private
  mdast-util-gfm-task-list-item@2.0.0:
    mdast-util-gfm-task-list-item: private
  mdast-util-gfm@3.1.0:
    mdast-util-gfm: private
  mdast-util-math@3.0.0:
    mdast-util-math: private
  mdast-util-mdx-expression@2.0.1:
    mdast-util-mdx-expression: private
  mdast-util-mdx-jsx@3.2.0:
    mdast-util-mdx-jsx: private
  mdast-util-mdxjs-esm@2.0.1:
    mdast-util-mdxjs-esm: private
  mdast-util-phrasing@4.1.0:
    mdast-util-phrasing: private
  mdast-util-to-hast@13.2.0:
    mdast-util-to-hast: private
  mdast-util-to-markdown@2.1.2:
    mdast-util-to-markdown: private
  mdast-util-to-string@4.0.0:
    mdast-util-to-string: private
  mdurl@2.0.0:
    mdurl: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  micromark-core-commonmark@2.0.3:
    micromark-core-commonmark: private
  micromark-extension-gfm-autolink-literal@2.1.0:
    micromark-extension-gfm-autolink-literal: private
  micromark-extension-gfm-footnote@2.1.0:
    micromark-extension-gfm-footnote: private
  micromark-extension-gfm-strikethrough@2.1.0:
    micromark-extension-gfm-strikethrough: private
  micromark-extension-gfm-table@2.1.1:
    micromark-extension-gfm-table: private
  micromark-extension-gfm-tagfilter@2.0.0:
    micromark-extension-gfm-tagfilter: private
  micromark-extension-gfm-task-list-item@2.1.0:
    micromark-extension-gfm-task-list-item: private
  micromark-extension-gfm@3.0.0:
    micromark-extension-gfm: private
  micromark-extension-math@3.1.0:
    micromark-extension-math: private
  micromark-factory-destination@2.0.1:
    micromark-factory-destination: private
  micromark-factory-label@2.0.1:
    micromark-factory-label: private
  micromark-factory-space@2.0.1:
    micromark-factory-space: private
  micromark-factory-title@2.0.1:
    micromark-factory-title: private
  micromark-factory-whitespace@2.0.1:
    micromark-factory-whitespace: private
  micromark-util-character@2.1.1:
    micromark-util-character: private
  micromark-util-chunked@2.0.1:
    micromark-util-chunked: private
  micromark-util-classify-character@2.0.1:
    micromark-util-classify-character: private
  micromark-util-combine-extensions@2.0.1:
    micromark-util-combine-extensions: private
  micromark-util-decode-numeric-character-reference@2.0.2:
    micromark-util-decode-numeric-character-reference: private
  micromark-util-decode-string@2.0.1:
    micromark-util-decode-string: private
  micromark-util-encode@2.0.1:
    micromark-util-encode: private
  micromark-util-html-tag-name@2.0.1:
    micromark-util-html-tag-name: private
  micromark-util-normalize-identifier@2.0.1:
    micromark-util-normalize-identifier: private
  micromark-util-resolve-all@2.0.1:
    micromark-util-resolve-all: private
  micromark-util-sanitize-uri@2.0.1:
    micromark-util-sanitize-uri: private
  micromark-util-subtokenize@2.1.0:
    micromark-util-subtokenize: private
  micromark-util-symbol@2.0.1:
    micromark-util-symbol: private
  micromark-util-types@2.0.2:
    micromark-util-types: private
  micromark@4.0.2:
    micromark: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  motion-dom@12.7.4:
    motion-dom: private
  motion-utils@12.7.2:
    motion-utils: private
  ms@2.1.3:
    ms: private
  natural-compare@1.4.0:
    natural-compare: private
  neo-async@2.6.2:
    neo-async: private
  node-releases@2.0.19:
    node-releases: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.7:
    object.assign: private
  object.entries@1.1.9:
    object.entries: private
  object.fromentries@2.0.8:
    object.fromentries: private
  object.groupby@1.0.3:
    object.groupby: private
  object.values@1.2.1:
    object.values: private
  optionator@0.9.4:
    optionator: private
  orderedmap@2.1.1:
    orderedmap: private
  overlap-area@1.1.0:
    overlap-area: private
  own-keys@1.0.1:
    own-keys: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  parent-module@1.0.1:
    parent-module: private
  parse-entities@2.0.0:
    parse-entities: private
  parse5@7.2.1:
    parse5: private
  path-exists@4.0.0:
    path-exists: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss-selector-parser@6.0.10:
    postcss-selector-parser: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prismjs@1.30.0:
    prismjs: private
  prop-types@15.8.1:
    prop-types: private
  property-information@7.0.0:
    property-information: private
  prosemirror-changeset@2.2.1:
    prosemirror-changeset: private
  prosemirror-collab@1.3.1:
    prosemirror-collab: private
  prosemirror-commands@1.7.1:
    prosemirror-commands: private
  prosemirror-dropcursor@1.8.2:
    prosemirror-dropcursor: private
  prosemirror-gapcursor@1.3.2:
    prosemirror-gapcursor: private
  prosemirror-history@1.4.1:
    prosemirror-history: private
  prosemirror-inputrules@1.5.0:
    prosemirror-inputrules: private
  prosemirror-keymap@1.2.2:
    prosemirror-keymap: private
  prosemirror-markdown@1.13.2:
    prosemirror-markdown: private
  prosemirror-menu@1.2.5:
    prosemirror-menu: private
  prosemirror-model@1.25.1:
    prosemirror-model: private
  prosemirror-schema-basic@1.2.4:
    prosemirror-schema-basic: private
  prosemirror-schema-list@1.5.1:
    prosemirror-schema-list: private
  prosemirror-state@1.4.3:
    prosemirror-state: private
  prosemirror-tables@1.7.1:
    prosemirror-tables: private
  prosemirror-trailing-node@3.0.0(prosemirror-model@1.25.1)(prosemirror-state@1.4.3)(prosemirror-view@1.39.2):
    prosemirror-trailing-node: private
  prosemirror-transform@1.10.4:
    prosemirror-transform: private
  prosemirror-view@1.39.2:
    prosemirror-view: private
  punycode.js@2.3.1:
    punycode.js: private
  punycode@2.3.1:
    punycode: private
  queue-microtask@1.2.3:
    queue-microtask: private
  randombytes@2.1.0:
    randombytes: private
  react-css-styled@1.1.9:
    react-css-styled: private
  react-is@18.3.1:
    react-is: private
  react-moveable@0.56.0:
    react-moveable: private
  react-remove-scroll-bar@2.3.8(@types/react@19.1.2)(react@19.1.0):
    react-remove-scroll-bar: private
  react-remove-scroll@2.6.3(@types/react@19.1.2)(react@19.1.0):
    react-remove-scroll: private
  react-selecto@1.26.3:
    react-selecto: private
  react-style-singleton@2.2.3(@types/react@19.1.2)(react@19.1.0):
    react-style-singleton: private
  react-tweet@3.2.2(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    react-tweet: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  refractor@3.6.0:
    refractor: private
  regenerator-runtime@0.14.1:
    regenerator-runtime: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  remark-parse@11.0.0:
    remark-parse: private
  remark-rehype@11.1.2:
    remark-rehype: private
  remark-stringify@11.0.0:
    remark-stringify: private
  require-from-string@2.0.2:
    require-from-string: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve@1.22.10:
    resolve: private
  reusify@1.1.0:
    reusify: private
  rope-sequence@1.3.4:
    rope-sequence: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  scheduler@0.26.0:
    scheduler: private
  schema-utils@3.3.0:
    schema-utils: private
  selecto@1.26.3:
    selecto: private
  semver@6.3.1:
    semver: private
  serialize-javascript@6.0.2:
    serialize-javascript: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  sharp@0.34.1:
    sharp: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  space-separated-tokens@2.0.2:
    space-separated-tokens: private
  stable-hash@0.0.5:
    stable-hash: private
  streamsearch@1.1.0:
    streamsearch: private
  string.prototype.includes@2.0.1:
    string.prototype.includes: private
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: private
  string.prototype.repeat@1.0.0:
    string.prototype.repeat: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  stringify-entities@4.0.4:
    stringify-entities: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  style-to-js@1.1.16:
    style-to-js: private
  style-to-object@1.0.8:
    style-to-object: private
  styled-jsx@5.1.6(react@19.1.0):
    styled-jsx: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  swr@2.3.3(react@19.1.0):
    swr: private
  tapable@2.2.1:
    tapable: private
  terser-webpack-plugin@5.3.14(webpack@5.99.6):
    terser-webpack-plugin: private
  terser@5.39.0:
    terser: private
  tinyglobby@0.2.12:
    tinyglobby: private
  tippy.js@6.3.7:
    tippy.js: private
  tiptap-extension-global-drag-handle@0.1.18:
    tiptap-extension-global-drag-handle: private
  to-regex-range@5.0.1:
    to-regex-range: private
  trim-lines@3.0.1:
    trim-lines: private
  trough@2.2.0:
    trough: private
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: private
  tsconfig-paths@3.15.0:
    tsconfig-paths: private
  tslib@2.8.1:
    tslib: private
  tunnel-rat@0.1.2(@types/react@19.1.2)(immer@10.1.1)(react@19.1.0):
    tunnel-rat: private
  type-check@0.4.0:
    type-check: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  uc.micro@2.1.0:
    uc.micro: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  undici-types@6.19.8:
    undici-types: private
  unified@11.0.5:
    unified: private
  unist-util-find-after@5.0.0:
    unist-util-find-after: private
  unist-util-is@6.0.0:
    unist-util-is: private
  unist-util-position@5.0.0:
    unist-util-position: private
  unist-util-remove-position@5.0.0:
    unist-util-remove-position: private
  unist-util-stringify-position@4.0.0:
    unist-util-stringify-position: private
  unist-util-visit-parents@6.0.1:
    unist-util-visit-parents: private
  unrs-resolver@1.5.0:
    unrs-resolver: private
  update-browserslist-db@1.1.3(browserslist@4.24.4):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  use-callback-ref@1.3.3(@types/react@19.1.2)(react@19.1.0):
    use-callback-ref: private
  use-sidecar@1.1.3(@types/react@19.1.2)(react@19.1.0):
    use-sidecar: private
  use-sync-external-store@1.5.0(react@19.1.0):
    use-sync-external-store: private
  util-deprecate@1.0.2:
    util-deprecate: private
  vfile-location@5.0.3:
    vfile-location: private
  vfile-message@4.0.2:
    vfile-message: private
  vfile@6.0.3:
    vfile: private
  w3c-keyname@2.2.8:
    w3c-keyname: private
  watchpack@2.4.2:
    watchpack: private
  web-namespaces@2.0.1:
    web-namespaces: private
  webpack-sources@3.2.3:
    webpack-sources: private
  webpack@5.99.6:
    webpack: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  xtend@4.0.2:
    xtend: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zwitch@2.0.4:
    zwitch: private
ignoredBuilds: []
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.6.5
pendingBuilds: []
prunedAt: Sun, 25 May 2025 10:18:47 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/core@1.4.1'
  - '@emnapi/runtime@1.4.1'
  - '@emnapi/wasi-threads@1.0.1'
  - '@img/sharp-darwin-arm64@0.34.1'
  - '@img/sharp-darwin-x64@0.34.1'
  - '@img/sharp-libvips-darwin-arm64@1.1.0'
  - '@img/sharp-libvips-darwin-x64@1.1.0'
  - '@img/sharp-libvips-linux-arm64@1.1.0'
  - '@img/sharp-libvips-linux-arm@1.1.0'
  - '@img/sharp-libvips-linux-ppc64@1.1.0'
  - '@img/sharp-libvips-linux-s390x@1.1.0'
  - '@img/sharp-libvips-linux-x64@1.1.0'
  - '@img/sharp-libvips-linuxmusl-arm64@1.1.0'
  - '@img/sharp-libvips-linuxmusl-x64@1.1.0'
  - '@img/sharp-linux-arm64@0.34.1'
  - '@img/sharp-linux-arm@0.34.1'
  - '@img/sharp-linux-s390x@0.34.1'
  - '@img/sharp-linux-x64@0.34.1'
  - '@img/sharp-linuxmusl-arm64@0.34.1'
  - '@img/sharp-linuxmusl-x64@0.34.1'
  - '@img/sharp-wasm32@0.34.1'
  - '@img/sharp-win32-ia32@0.34.1'
  - '@napi-rs/wasm-runtime@0.2.9'
  - '@next/swc-darwin-arm64@15.3.0'
  - '@next/swc-darwin-x64@15.3.0'
  - '@next/swc-linux-arm64-gnu@15.3.0'
  - '@next/swc-linux-arm64-musl@15.3.0'
  - '@next/swc-linux-x64-gnu@15.3.0'
  - '@next/swc-linux-x64-musl@15.3.0'
  - '@next/swc-win32-arm64-msvc@15.3.0'
  - '@tailwindcss/oxide-android-arm64@4.1.4'
  - '@tailwindcss/oxide-darwin-arm64@4.1.4'
  - '@tailwindcss/oxide-darwin-x64@4.1.4'
  - '@tailwindcss/oxide-freebsd-x64@4.1.4'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.4'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.4'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.4'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.4'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.4'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.4'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.4'
  - '@tybys/wasm-util@0.9.0'
  - '@unrs/resolver-binding-darwin-arm64@1.5.0'
  - '@unrs/resolver-binding-darwin-x64@1.5.0'
  - '@unrs/resolver-binding-freebsd-x64@1.5.0'
  - '@unrs/resolver-binding-linux-arm-gnueabihf@1.5.0'
  - '@unrs/resolver-binding-linux-arm-musleabihf@1.5.0'
  - '@unrs/resolver-binding-linux-arm64-gnu@1.5.0'
  - '@unrs/resolver-binding-linux-arm64-musl@1.5.0'
  - '@unrs/resolver-binding-linux-ppc64-gnu@1.5.0'
  - '@unrs/resolver-binding-linux-riscv64-gnu@1.5.0'
  - '@unrs/resolver-binding-linux-s390x-gnu@1.5.0'
  - '@unrs/resolver-binding-linux-x64-gnu@1.5.0'
  - '@unrs/resolver-binding-linux-x64-musl@1.5.0'
  - '@unrs/resolver-binding-wasm32-wasi@1.5.0'
  - '@unrs/resolver-binding-win32-arm64-msvc@1.5.0'
  - '@unrs/resolver-binding-win32-ia32-msvc@1.5.0'
  - lightningcss-darwin-arm64@1.29.2
  - lightningcss-darwin-x64@1.29.2
  - lightningcss-freebsd-x64@1.29.2
  - lightningcss-linux-arm-gnueabihf@1.29.2
  - lightningcss-linux-arm64-gnu@1.29.2
  - lightningcss-linux-arm64-musl@1.29.2
  - lightningcss-linux-x64-gnu@1.29.2
  - lightningcss-linux-x64-musl@1.29.2
  - lightningcss-win32-arm64-msvc@1.29.2
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\repo\deer-flow\web\node_modules\.pnpm
virtualStoreDirMaxLength: 60
