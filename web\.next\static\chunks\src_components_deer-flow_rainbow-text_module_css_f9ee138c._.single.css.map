{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/deer-flow/rainbow-text.module.css"], "sourcesContent": [".animated {\r\n  background: linear-gradient(\r\n    to right,\r\n    rgb(from var(--card-foreground) r g b / 0.3) 15%,\r\n    rgb(from var(--card-foreground) r g b / 0.75) 35%,\r\n    rgb(from var(--card-foreground) r g b / 0.75) 65%,\r\n    rgb(from var(--card-foreground) r g b / 0.3) 85%\r\n  );\r\n  -webkit-background-clip: text;\r\n  background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  text-fill-color: transparent;\r\n  background-size: 500% auto;\r\n  animation: textShine 2s ease-in-out infinite alternate;\r\n}\r\n\r\n@keyframes textShine {\r\n  0% {\r\n    background-position: 0% 50%;\r\n  }\r\n  100% {\r\n    background-position: 100% 50%;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;AAgBA"}}]}