{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-media.module.css"], "sourcesContent": [".root {\n  margin-top: 0.75rem;\n  overflow: hidden;\n  position: relative;\n}\n.rounded {\n  border: var(--tweet-border);\n  border-radius: 12px;\n}\n.mediaWrapper {\n  display: grid;\n  grid-auto-rows: 1fr;\n  gap: 2px;\n  height: 100%;\n  width: 100%;\n}\n.grid2Columns {\n  grid-template-columns: repeat(2, 1fr);\n}\n.grid3 > a:first-child {\n  grid-row: span 2;\n}\n.grid2x2 {\n  grid-template-rows: repeat(2, 1fr);\n}\n.mediaContainer {\n  position: relative;\n  height: 100%;\n  width: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.mediaLink {\n  text-decoration: none;\n  outline-style: none;\n}\n.skeleton {\n  padding-bottom: 56.25%;\n  width: 100%;\n  display: block;\n}\n.image {\n  position: absolute;\n  top: 0px;\n  left: 0px;\n  bottom: 0px;\n  height: 100%;\n  width: 100%;\n  margin: 0;\n  object-fit: cover;\n  object-position: center;\n}\n"], "names": [], "mappings": "AAAA;;;;;;AAKA;;;;;AAIA;;;;;;;;AAOA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;;;AAQA;;;;;AAIA;;;;;;AAKA", "ignoreList": [0]}}]}