{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/react-tweet@3.2.2_react-dom_a2b6e01c7594fd9ef81716433bc4e3ba/node_modules/react-tweet/dist/twitter-theme/tweet-media-video.module.css"], "sourcesContent": [".anchor {\n  display: flex;\n  align-items: center;\n  color: white;\n  padding: 0 1rem;\n  border: 1px solid transparent;\n  border-radius: 9999px;\n  font-weight: 700;\n  transition: background-color 0.2s;\n  cursor: pointer;\n  user-select: none;\n  outline-style: none;\n  text-decoration: none;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.videoButton {\n  position: relative;\n  height: 67px;\n  width: 67px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: var(--tweet-color-blue-primary);\n  transition-property: background-color;\n  transition-duration: 0.2s;\n  border: 4px solid #fff;\n  border-radius: 9999px;\n  cursor: pointer;\n}\n.videoButton:hover,\n.videoButton:focus-visible {\n  background-color: var(--tweet-color-blue-primary-hover);\n}\n.videoButtonIcon {\n  margin-left: 3px;\n  width: calc(50% + 4px);\n  height: calc(50% + 4px);\n  max-width: 100%;\n  color: #fff;\n  fill: currentColor;\n  user-select: none;\n}\n.watchOnTwitter {\n  position: absolute;\n  top: 12px;\n  right: 8px;\n}\n.watchOnTwitter > a {\n  min-width: 2rem;\n  min-height: 2rem;\n  font-size: 0.875rem;\n  line-height: 1rem;\n  backdrop-filter: blur(4px);\n  background-color: rgba(15, 20, 25, 0.75);\n}\n.watchOnTwitter > a:hover {\n  background-color: rgba(39, 44, 48, 0.75);\n}\n.viewReplies {\n  position: relative;\n  min-height: 2rem;\n  background-color: var(--tweet-color-blue-primary);\n  border-color: var(--tweet-color-blue-primary);\n  font-size: 0.9375rem;\n  line-height: 1.25rem;\n}\n.viewReplies:hover {\n  background-color: var(--tweet-color-blue-primary-hover);\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;AAgBA;;;;;;;;;;;;;;;AAcA;;;;AAIA;;;;;;;;;;AASA;;;;;;AAKA;;;;;;;;;AAQA;;;;AAGA;;;;;;;;;AAQA", "ignoreList": [0]}}]}